import { Logger } from './logger';

export interface RetryOptions {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
}

export class ErrorHandler {
  private logger: Logger;
  private defaultRetryOptions: RetryOptions;

  constructor() {
    this.logger = Logger.getInstance();
    this.defaultRetryOptions = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 2,
    };
  }

  /**
   * Execute a function with retry logic and exponential backoff
   */
  async withRetry<T>(
    fn: () => Promise<T>,
    context: string,
    options: Partial<RetryOptions> = {},
  ): Promise<T> {
    const opts = { ...this.defaultRetryOptions, ...options };
    let lastError: Error;

    for (let attempt = 1; attempt <= opts.maxRetries + 1; attempt++) {
      try {
        const result = await fn();
        if (attempt > 1) {
          this.logger.success(`${context} succeeded on attempt ${attempt}`);
        }
        return result;
      } catch (error) {
        lastError = error as Error;

        if (attempt <= opts.maxRetries) {
          const delay = Math.min(
            opts.baseDelay * opts.backoffFactor ** (attempt - 1),
            opts.maxDelay,
          );

          this.logger.warn(
            `${context} failed on attempt ${attempt}/${opts.maxRetries + 1}: ${lastError.message}. Retrying in ${delay}ms...`,
          );

          await this.sleep(delay);
        } else {
          this.logger.error(
            `${context} failed after ${opts.maxRetries + 1} attempts: ${lastError.message}`,
          );
        }
      }
    }

    throw lastError!;
  }

  /**
   * Handle and log errors gracefully
   */
  handleError(error: unknown, context: string): Error {
    let errorMessage: string;
    let errorStack: string | undefined;

    if (error instanceof Error) {
      errorMessage = error.message;
      errorStack = error.stack;
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else {
      errorMessage = 'Unknown error occurred';
    }

    const formattedError = new Error(`${context}: ${errorMessage}`);
    formattedError.stack = errorStack;

    this.logger.error(`Error in ${context}:`, {
      message: errorMessage,
      stack: errorStack,
      timestamp: new Date().toISOString(),
    });

    return formattedError;
  }

  /**
   * Wrap async functions with error handling
   */
  async safeExecute<T>(
    fn: () => Promise<T>,
    context: string,
    fallbackValue?: T,
  ): Promise<T | undefined> {
    try {
      return await fn();
    } catch (error) {
      this.handleError(error, context);
      return fallbackValue;
    }
  }

  /**
   * Check if error is retryable
   */
  isRetryableError(error: Error): boolean {
    const retryablePatterns = [
      /timeout/i,
      /network/i,
      /connection/i,
      /ECONNRESET/i,
      /ENOTFOUND/i,
      /ETIMEDOUT/i,
      /502/,
      /503/,
      /504/,
      /rate limit/i,
    ];

    return retryablePatterns.some(
      (pattern) => pattern.test(error.message) || pattern.test(error.name),
    );
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Create a timeout wrapper for promises
   */
  withTimeout<T>(
    promise: Promise<T>,
    timeoutMs: number,
    context: string,
  ): Promise<T> {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`${context} timed out after ${timeoutMs}ms`));
        }, timeoutMs);
      }),
    ]);
  }

  /**
   * Validate required environment variables
   */
  validateEnvironment(requiredVars: string[]): void {
    const missing = requiredVars.filter((varName) => !process.env[varName]);

    if (missing.length > 0) {
      const error = new Error(
        `Missing required environment variables: ${missing.join(', ')}`,
      );
      this.logger.error('Environment validation failed:', error);
      throw error;
    }

    this.logger.success('Environment validation passed');
  }

  /**
   * Create a circuit breaker for repeated failures
   */
  createCircuitBreaker(maxFailures = 5, resetTimeoutMs = 60000) {
    let failures = 0;
    let lastFailureTime = 0;
    let isOpen = false;

    return {
      async execute<T>(fn: () => Promise<T>, context: string): Promise<T> {
        const now = Date.now();

        // Reset circuit breaker if enough time has passed
        if (isOpen && now - lastFailureTime > resetTimeoutMs) {
          isOpen = false;
          failures = 0;
          this.logger.info(`Circuit breaker reset for ${context}`);
        }

        // Reject if circuit is open
        if (isOpen) {
          throw new Error(
            `Circuit breaker is open for ${context}. Too many recent failures.`,
          );
        }

        try {
          const result = await fn();
          // Reset failure count on success
          if (failures > 0) {
            failures = 0;
            this.logger.info(
              `Circuit breaker success for ${context}, failures reset`,
            );
          }
          return result;
        } catch (error) {
          failures++;
          lastFailureTime = now;

          if (failures >= maxFailures) {
            isOpen = true;
            this.logger.error(
              `Circuit breaker opened for ${context} after ${failures} failures`,
            );
          }

          throw error;
        }
      },

      getStatus() {
        return { isOpen, failures, lastFailureTime };
      },
    };
  }
}
