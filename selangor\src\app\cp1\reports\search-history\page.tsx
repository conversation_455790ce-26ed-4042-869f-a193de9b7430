'use client';

import {
  ArrowR<PERSON>,
  ChevronRight,
  Clock,
  Download,
  Filter,
  Heart,
  Repeat,
  Star,
  TrendingUp,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { AdminLayout } from '@/components/admin/AdminLayout';
import {
  generateRefinementFunnel,
  generateRepeatSearches,
} from '@/lib/mockData';

export default function SearchHistoryReport() {
  const [refinementData, setRefinementData] = useState<any[]>([]);
  const [repeatData, setRepeatData] = useState<any[]>([]);
  const [timeRange, setTimeRange] = useState<string>('30d');
  const [viewType, setViewType] = useState<string>('refinement');

  useEffect(() => {
    setRefinementData(generateRefinementFunnel());
    setRepeatData(generateRepeatSearches());
  }, []);

  const getLoyaltyColor = (loyalty: string) => {
    switch (loyalty) {
      case 'high':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-red-100 text-red-800';
    }
  };

  const getLoyaltyIcon = (loyalty: string) => {
    switch (loyalty) {
      case 'high':
        return <Heart className="h-4 w-4" />;
      case 'medium':
        return <Star className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Search History & User Journey
            </h1>
            <p className="text-gray-600">
              Analysis of search refinement patterns and user loyalty metrics
            </p>
          </div>
          <button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </button>
        </div>

        {/* Filters */}
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                Filters:
              </span>
            </div>

            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>

            <select
              value={viewType}
              onChange={(e) => setViewType(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            >
              <option value="refinement">Refinement Funnel</option>
              <option value="repeat">Repeat Searches</option>
              <option value="combined">Combined View</option>
            </select>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Avg Refinement Steps
                </p>
                <p className="text-2xl font-bold text-gray-900">2.8</p>
                <p className="text-sm text-gray-500">
                  Steps per search session
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <ArrowRight className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Repeat Searches
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {repeatData.length}
                </p>
                <p className="text-sm text-gray-500">
                  Frequently searched terms
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Repeat className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  High Loyalty Users
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {
                    repeatData.filter((item) => item.userLoyalty === 'high')
                      .length
                  }
                </p>
                <p className="text-sm text-gray-500">
                  Queries with high loyalty
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Avg Return Frequency
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {(
                    repeatData.reduce(
                      (sum, item) => sum + item.avgDaysBetween,
                      0,
                    ) / repeatData.length
                  ).toFixed(1)}
                </p>
                <p className="text-sm text-gray-500">Days between searches</p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <TrendingUp className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Search Refinement Funnel */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Search Refinement Funnel
          </h3>
          <p className="text-sm text-gray-600 mb-6">
            Typical user journey showing how searches are refined to find
            specific results
          </p>

          <div className="space-y-4">
            {refinementData.map((step, index) => (
              <div key={step.step} className="flex items-center">
                <div className="flex-1">
                  <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                        <span className="text-sm font-medium text-blue-600">
                          {step.step}
                        </span>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">
                          "{step.query}"
                        </h4>
                        <p className="text-sm text-gray-500">
                          {step.resultsCount} results found
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{
                            width: `${(step.resultsCount / refinementData[0].resultsCount) * 100}%`,
                          }}
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {(
                          (step.resultsCount / refinementData[0].resultsCount) *
                          100
                        ).toFixed(1)}
                        % of initial results
                      </p>
                    </div>
                  </div>
                </div>

                {step.nextQuery && (
                  <div className="flex items-center justify-center w-12">
                    <ChevronRight className="h-6 w-6 text-gray-400" />
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">
              Refinement Insights
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
              <div>
                <strong>Search Narrowing:</strong> Users typically refine their
                searches by adding location or cuisine type to find more
                specific results.
              </div>
              <div>
                <strong>Result Optimization:</strong> The funnel shows a 99%
                reduction from initial broad search to final specific query,
                indicating effective refinement.
              </div>
            </div>
          </div>
        </div>

        {/* Repeat Search Analysis */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              Repeat Search Analysis
            </h3>
            <p className="text-sm text-gray-600">
              Queries that users search for repeatedly, indicating trust and
              recurring needs
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Search Query
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Frequency
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Avg Days Between
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User Loyalty
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pattern Type
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {repeatData.map((item, index) => (
                  <tr key={item.query} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {item.query}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Repeat className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-sm text-gray-900">
                          {item.frequency} times
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.avgDaysBetween} days
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${getLoyaltyColor(item.userLoyalty)}`}
                      >
                        {getLoyaltyIcon(item.userLoyalty)}
                        <span className="ml-1 capitalize">
                          {item.userLoyalty}
                        </span>
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.avgDaysBetween <= 7
                        ? 'Weekly'
                        : item.avgDaysBetween <= 14
                          ? 'Bi-weekly'
                          : item.avgDaysBetween <= 30
                            ? 'Monthly'
                            : 'Occasional'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* User Journey Insights */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            User Journey Insights
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                </div>
                <h4 className="font-medium text-gray-900">Search Refinement</h4>
              </div>
              <p className="text-sm text-gray-600">
                Users typically start with broad terms like "halal food" and
                progressively narrow down to specific locations and cuisine
                types.
              </p>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <Repeat className="h-5 w-5 text-blue-600" />
                </div>
                <h4 className="font-medium text-gray-900">Repeat Behavior</h4>
              </div>
              <p className="text-sm text-gray-600">
                High-loyalty users frequently search for suppliers and
                certification status, indicating business and compliance needs.
              </p>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                  <Users className="h-5 w-5 text-purple-600" />
                </div>
                <h4 className="font-medium text-gray-900">User Loyalty</h4>
              </div>
              <p className="text-sm text-gray-600">
                Business-related searches show higher loyalty patterns, while
                consumer searches tend to be more sporadic and location-based.
              </p>
            </div>
          </div>
        </div>

        {/* Recommendations */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Recommendations
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">
                Search Experience Improvements
              </h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-green-600 rounded-full mt-2" />
                  <span>
                    Implement search suggestions based on refinement patterns
                  </span>
                </li>
                <li className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-green-600 rounded-full mt-2" />
                  <span>
                    Add location-based auto-complete for faster refinement
                  </span>
                </li>
                <li className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-green-600 rounded-full mt-2" />
                  <span>Create saved searches for high-loyalty users</span>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-3">
                Content Strategy
              </h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2" />
                  <span>
                    Focus on supplier and certification content for business
                    users
                  </span>
                </li>
                <li className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2" />
                  <span>Enhance location-specific restaurant listings</span>
                </li>
                <li className="flex items-start space-x-2">
                  <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2" />
                  <span>Develop cuisine-specific halal guides</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
