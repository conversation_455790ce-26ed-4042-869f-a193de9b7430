import type { <PERSON> } from 'puppeteer';
import type { ScrapedCompany, ScrapingConfig } from '../types';

export class DataExtractor {
  private config: ScrapingConfig;

  constructor(config: ScrapingConfig) {
    this.config = config;
  }

  async extractCompaniesFromPage(
    page: Page,
    pageNumber: number,
    onCompanyFound?: (company: ScrapedCompany) => Promise<void>,
  ): Promise<ScrapedCompany[]> {
    try {
      if (this.config.debug) {
        console.log(
          `Starting extraction for page ${pageNumber}, URL: ${page.url()}`,
        );
      }

      // Wait for the table to load
      await page.waitForSelector('table', { timeout: 10000 });

      if (this.config.debug) {
        console.log('Table selector found, proceeding with extraction');
      }

      // Get table information first
      const tableInfo = await page.evaluate(() => {
        const tables = document.querySelectorAll('table');
        const info = {
          tableCount: tables.length,
          tables: [] as any[],
        };

        for (let i = 0; i < tables.length; i++) {
          const table = tables[i];
          const rows = table.querySelectorAll('tr');
          const tableData = {
            index: i,
            rowCount: rows.length,
            rows: [] as any[],
          };

          // Get first few rows for analysis
          for (let j = 0; j < Math.min(3, rows.length); j++) {
            const row = rows[j];
            const cells = row.querySelectorAll('td, th');
            const cellTexts = Array.from(cells).map((cell) =>
              cell.textContent?.trim().substring(0, 100),
            );
            tableData.rows.push({
              rowIndex: j,
              cellCount: cells.length,
              cells: cellTexts,
            });
          }

          info.tables.push(tableData);
        }

        return info;
      });

      if (this.config.debug) {
        console.log('Table analysis:', JSON.stringify(tableInfo, null, 2));
      }

      const companies = await page.evaluate((pageNum) => {
        const results: any[] = [];

        // Find the main data table (first table with company data)
        const tables = document.querySelectorAll('table');
        let dataTable = null;

        // Look for the table that contains company data (has more than 1 row)
        for (let i = 0; i < tables.length; i++) {
          const table = tables[i];
          const rows = table.querySelectorAll('tr');

          if (rows.length > 1) {
            // Check if this looks like a data table by examining the first data row
            const firstDataRow = rows[1];
            const cells = firstDataRow?.querySelectorAll('td');

            if (cells && cells.length >= 3) {
              dataTable = table;
              break;
            }
          }
        }

        if (!dataTable) {
          console.log('No data table found');
          return results;
        }

        const rows = dataTable.querySelectorAll('tr');
        console.log(`Processing ${rows.length} rows from data table`);

        // Skip header row (index 0) and process data rows
        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];
          const cells = row.querySelectorAll('td');

          // Skip if not enough cells or if this is the pagination row
          if (cells.length < 3) continue;

          // Check if this is the pagination row by looking for page numbers
          const cellText = cells[0]?.textContent?.trim() || '';
          if (
            cellText.includes('[') ||
            cellText.includes('Next') ||
            cellText.includes('Total Record')
          ) {
            continue;
          }

          const companyData: any = {
            pageNumber: pageNum,
            rawData: row.innerHTML,
          };

          // Extract data from the table structure:
          // Column 0: Number (Bil)
          // Column 1: Company Name & Address (combined in one cell)
          // Column 2: Expiry Date

          // Extract company name and address from the second column
          const nameAddressCell = cells[1];
          if (nameAddressCell) {
            // Get the HTML content to preserve line breaks
            const cellHTML = nameAddressCell.innerHTML || '';

            // Look for company name in span with class "company-name"
            const companyNameMatch = cellHTML.match(
              /<span class="company-name">(.*?)<\/span>/,
            );
            if (companyNameMatch) {
              companyData.companyName = companyNameMatch[1].trim();
            }

            // Look for address in span with class "company-address"
            const addressMatch = cellHTML.match(
              /<span class="company-address">(.*?)<\/span>/,
            );
            if (addressMatch) {
              // Replace <br> tags with spaces and clean up
              let address = addressMatch[1]
                .replace(/<br\s*\/?>/gi, ', ')
                .replace(/<[^>]*>/g, '') // Remove any other HTML tags
                .replace(/\s+/g, ' ')
                .trim();

              // Remove trailing comma if present
              address = address.replace(/,\s*$/, '');
              companyData.address = address;

              // Extract state from the address
              const states = [
                'Johor',
                'Kedah',
                'Kelantan',
                'Melaka',
                'Negeri Sembilan',
                'Pahang',
                'Pulau Pinang',
                'Perak',
                'Perlis',
                'Selangor',
                'Terengganu',
                'Sabah',
                'Sarawak',
                'Wilayah Persekutuan',
              ];

              for (const state of states) {
                if (address.includes(state)) {
                  companyData.state = state;
                  break;
                }
              }

              // Try to extract postcode (5 digits)
              const postcodeMatch = address.match(/\b(\d{5})\b/);
              if (postcodeMatch) {
                companyData.postcode = postcodeMatch[1];
              }
            }

            // Fallback: if spans not found, use the old method
            if (!companyData.companyName) {
              const fullText = nameAddressCell.textContent?.trim() || '';
              const parts = fullText.split(/\t+\s*\n\s*/);

              if (parts.length >= 2) {
                companyData.companyName = parts[0].trim();
                if (!companyData.address) {
                  companyData.address = parts[1].trim();
                }
              } else {
                const lineParts = fullText.split(/\n/);
                if (lineParts.length >= 2) {
                  companyData.companyName = lineParts[0].trim();
                  if (!companyData.address) {
                    companyData.address = lineParts.slice(1).join(', ').trim();
                  }
                } else {
                  companyData.companyName = fullText;
                }
              }
            }
          }

          // Extract expiry date from the third column
          const expiryCell = cells[2];
          if (expiryCell) {
            // Get the HTML content to properly handle <br> tags
            const cellHTML = expiryCell.innerHTML || '';

            if (cellHTML) {
              // Split by <br> tags and clean up
              const dates = cellHTML
                .split(/<br\s*\/?>/gi)
                .map((d) => d.trim())
                .filter((d) => d.length > 0 && d.match(/\d{2}\/\d{2}\/\d{4}/)); // Only keep valid date patterns

              if (dates.length > 0) {
                // Take the first date (usually the latest expiry)
                companyData.expiryDate = dates[0];
              }
            } else {
              // Fallback to textContent if innerHTML is not available
              const expiryText = expiryCell.textContent?.trim() || '';
              if (expiryText) {
                // Try to extract the first valid date pattern
                const dateMatch = expiryText.match(/\d{2}\/\d{2}\/\d{4}/);
                if (dateMatch) {
                  companyData.expiryDate = dateMatch[0];
                } else {
                  companyData.expiryDate = expiryText.substring(0, 50); // Limit length as fallback
                }
              }
            }
          }

          // Look for any links in the row for email/website
          const links = row.querySelectorAll('a');
          for (const link of links) {
            const href = link.getAttribute('href');
            if (href && href.includes('mailto:')) {
              companyData.email = href.replace('mailto:', '');
            } else if (
              href &&
              (href.includes('http') || href.includes('www'))
            ) {
              companyData.website = href;
            }
          }

          // Only add if we have a valid company name
          if (companyData.companyName && companyData.companyName.length > 2) {
            results.push(companyData);
          }
        }

        return results;
      }, pageNumber);

      // Clean and validate the extracted data, emitting each company as it's processed
      const cleanedCompanies: ScrapedCompany[] = [];

      for (const company of companies) {
        const cleanedCompany = this.cleanCompanyData(
          company,
          page.url(),
          pageNumber,
        );
        cleanedCompanies.push(cleanedCompany);

        // Emit the company immediately if callback is provided
        if (onCompanyFound) {
          await onCompanyFound(cleanedCompany);
        }
      }

      if (this.config.debug) {
        console.log(
          `📊 Extracted ${cleanedCompanies.length} companies from page ${pageNumber}`,
        );
      }

      return cleanedCompanies;
    } catch (error) {
      console.error(`❌ Error extracting data from page ${pageNumber}:`, error);
      return [];
    }
  }

  private cleanCompanyData(
    rawData: any,
    sourceUrl: string,
    pageNumber: number,
  ): ScrapedCompany {
    return {
      companyName: this.truncateText(
        this.cleanText(rawData.companyName) || 'Unknown Company',
        500,
      ),
      registrationNumber: this.truncateText(
        this.cleanText(rawData.registrationNumber),
        255,
      ),
      businessType: this.truncateText(
        this.cleanText(rawData.businessType),
        255,
      ),
      category: this.truncateText(this.cleanText(rawData.category), 255),
      subcategory: this.truncateText(this.cleanText(rawData.subcategory), 255),
      address: this.cleanText(rawData.address), // text field, no length limit
      state: this.truncateText(this.cleanText(rawData.state), 255),
      postcode: this.truncateText(this.cleanText(rawData.postcode), 20),
      city: this.truncateText(this.cleanText(rawData.city), 255),
      country: this.truncateText(rawData.country || 'Malaysia', 255),
      phone: this.truncateText(this.cleanPhone(rawData.phone), 50),
      fax: this.truncateText(this.cleanPhone(rawData.fax), 50),
      email: this.truncateText(this.cleanEmail(rawData.email), 255),
      website: this.truncateText(this.cleanUrl(rawData.website), 500),
      contactPerson: this.truncateText(
        this.cleanText(rawData.contactPerson),
        255,
      ),
      certificateNumber: this.truncateText(
        this.cleanText(rawData.certificateNumber),
        255,
      ),
      certificateType: this.truncateText(
        this.cleanText(rawData.certificateType),
        255,
      ),
      certificateStatus: this.truncateText(
        this.cleanText(rawData.certificateStatus),
        100,
      ),
      issuedDate: this.truncateText(this.cleanText(rawData.issuedDate), 255),
      expiryDate: this.truncateText(this.cleanText(rawData.expiryDate), 255),
      sourceUrl: this.truncateText(sourceUrl, 1000),
      pageNumber,
      rawData: JSON.stringify(rawData),
    };
  }

  private cleanText(text: string | undefined): string | undefined {
    if (!text) return undefined;
    return text.trim().replace(/\s+/g, ' ').replace(/\n/g, ' ') || undefined;
  }

  private truncateText(
    text: string | undefined,
    maxLength: number,
  ): string | undefined {
    if (!text) return undefined;
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim();
  }

  private cleanPhone(phone: string | undefined): string | undefined {
    if (!phone) return undefined;
    // Remove common phone formatting and keep only numbers, +, -, and spaces
    return phone.replace(/[^\d+\-\s()]/g, '').trim() || undefined;
  }

  private cleanEmail(email: string | undefined): string | undefined {
    if (!email) return undefined;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const cleaned = email.trim().toLowerCase();
    return emailRegex.test(cleaned) ? cleaned : undefined;
  }

  private cleanUrl(url: string | undefined): string | undefined {
    if (!url) return undefined;
    const cleaned = url.trim();
    if (cleaned.startsWith('http')) return cleaned;
    if (cleaned.startsWith('www.')) return `https://${cleaned}`;
    return undefined;
  }

  async hasNextPage(page: Page): Promise<boolean> {
    try {
      // Check if there are more pages by looking at the pagination info
      // The page shows "Total Record : 9996 - Page X From 500"
      const paginationInfo = await page.evaluate(() => {
        const allElements = document.querySelectorAll('*');
        for (const element of allElements) {
          const text = element.textContent?.trim();
          if (
            text &&
            text.includes('Total Record') &&
            text.includes('Page') &&
            text.includes('From')
          ) {
            // Extract current page and total pages
            const pageMatch = text.match(/Page (\d+) From (\d+)/);
            if (pageMatch) {
              const currentPage = Number.parseInt(pageMatch[1], 10);
              const totalPages = Number.parseInt(pageMatch[2], 10);
              return {
                currentPage,
                totalPages,
                hasNext: currentPage < totalPages,
              };
            }
          }
        }
        return { currentPage: 1, totalPages: 1, hasNext: false };
      });

      if (this.config.debug) {
        console.log(
          `Pagination info: Page ${paginationInfo.currentPage} of ${paginationInfo.totalPages}`,
        );
      }

      return paginationInfo.hasNext;
    } catch (error) {
      if (this.config.debug) {
        console.log('Error checking pagination:', error);
      }
      return false;
    }
  }

  async goToNextPage(page: Page): Promise<boolean> {
    // This method is no longer used for navigation
    // Pagination is handled in the main crawler with different methods for page 1 vs subsequent pages
    if (this.config.debug) {
      console.log('goToNextPage called - pagination handled in main crawler');
    }
    return true;
  }

  async clickPageNumber(page: Page, pageNumber: number): Promise<boolean> {
    try {
      if (this.config.debug) {
        console.log(`Attempting to click page number ${pageNumber}`);
      }

      // Look for pagination links and click the specific page number
      const pageClicked = await page.evaluate(
        (targetPage, debugMode) => {
          // Look for pagination links in various possible formats
          const selectors = [
            'a', // All links
            '.pagination a', // Pagination specific links
            '[href*="page"]', // Links containing "page"
          ];

          for (const selector of selectors) {
            const links = document.querySelectorAll(selector);
            for (const link of links) {
              const linkText = link.textContent?.trim();
              const href = link.getAttribute('href');

              // Check if this link represents the target page
              if (
                linkText === targetPage.toString() ||
                (href &&
                  (href.includes(`page=${targetPage}`) ||
                    href.includes(`p=${targetPage}`)))
              ) {
                if (debugMode) {
                  console.log(`Found page link: ${linkText}, href: ${href}`);
                }
                (link as HTMLElement).click();
                return true;
              }
            }
          }

          // If no direct page link found, look for "Next" button if we're going to page 2
          if (targetPage === 2) {
            const nextLinks = document.querySelectorAll('a');
            for (const link of nextLinks) {
              const linkText = link.textContent?.trim().toLowerCase();
              if (
                linkText.includes('next') ||
                linkText.includes('>>') ||
                linkText === '>'
              ) {
                (link as HTMLElement).click();
                return true;
              }
            }
          }

          return false;
        },
        pageNumber,
        this.config.debug,
      );

      if (pageClicked) {
        // Wait for navigation after clicking
        await page.waitForNavigation({
          waitUntil: 'networkidle0',
          timeout: this.config.timeoutMs || 30000,
        });

        if (this.config.debug) {
          console.log(`Successfully navigated to page ${pageNumber}`);
        }
        return true;
      }

      if (this.config.debug) {
        console.log(`Could not find clickable element for page ${pageNumber}`);
      }
      return false;
    } catch (error) {
      if (this.config.debug) {
        console.log(`Error clicking page number ${pageNumber}:`, error);
      }
      return false;
    }
  }
}
