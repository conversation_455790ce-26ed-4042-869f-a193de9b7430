#!/usr/bin/env node

const { execSync } = require('node:child_process');
const fs = require('node:fs');
const path = require('node:path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n${description}...`, 'cyan');
  try {
    const _output = execSync(command, {
      stdio: 'inherit',
      cwd: process.cwd(),
    });
    log(`✅ ${description} completed successfully`, 'green');
    return true;
  } catch (error) {
    log(`❌ ${description} failed`, 'red');
    console.error(error.message);
    return false;
  }
}

function checkTestCoverage() {
  const coveragePath = path.join(
    process.cwd(),
    'coverage',
    'coverage-summary.json',
  );

  if (!fs.existsSync(coveragePath)) {
    log('⚠️  Coverage report not found', 'yellow');
    return false;
  }

  try {
    const coverage = JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
    const total = coverage.total;

    log('\n📊 Test Coverage Summary:', 'bright');
    log(
      `Lines: ${total.lines.pct}%`,
      total.lines.pct >= 80 ? 'green' : 'yellow',
    );
    log(
      `Functions: ${total.functions.pct}%`,
      total.functions.pct >= 80 ? 'green' : 'yellow',
    );
    log(
      `Branches: ${total.branches.pct}%`,
      total.branches.pct >= 70 ? 'green' : 'yellow',
    );
    log(
      `Statements: ${total.statements.pct}%`,
      total.statements.pct >= 80 ? 'green' : 'yellow',
    );

    // Check if coverage meets minimum thresholds
    const meetsThreshold =
      total.lines.pct >= 80 &&
      total.functions.pct >= 80 &&
      total.branches.pct >= 70 &&
      total.statements.pct >= 80;

    if (meetsThreshold) {
      log('✅ Coverage thresholds met', 'green');
    } else {
      log('⚠️  Coverage below recommended thresholds', 'yellow');
    }

    return meetsThreshold;
  } catch (_error) {
    log('❌ Failed to read coverage report', 'red');
    return false;
  }
}

function generateTestReport() {
  const reportPath = path.join(process.cwd(), 'test-reports');

  if (!fs.existsSync(reportPath)) {
    fs.mkdirSync(reportPath, { recursive: true });
  }

  const timestamp = new Date().toISOString();
  const report = {
    timestamp,
    testSuites: {
      unit: { status: 'unknown', duration: 0 },
      integration: { status: 'unknown', duration: 0 },
      e2e: { status: 'unknown', duration: 0 },
      performance: { status: 'unknown', duration: 0 },
    },
    coverage: null,
    summary: {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
    },
  };

  // This would be populated by actual test results
  // For now, we'll create a basic structure

  fs.writeFileSync(
    path.join(reportPath, `test-report-${Date.now()}.json`),
    JSON.stringify(report, null, 2),
  );

  log(`📄 Test report generated in ${reportPath}`, 'blue');
}

function main() {
  const args = process.argv.slice(2);
  const testType = args[0] || 'all';

  log('🧪 JAKIM Halal Portal Test Runner', 'bright');
  log('=====================================', 'bright');

  let allTestsPassed = true;

  switch (testType) {
    case 'unit':
      log('\n🔬 Running Unit Tests', 'magenta');
      allTestsPassed = runCommand(
        'npm run test -- --coverage --watchAll=false',
        'Unit tests',
      );
      checkTestCoverage();
      break;

    case 'integration':
      log('\n🔗 Running Integration Tests', 'magenta');
      allTestsPassed = runCommand(
        'npm run test -- --testPathPattern="integration" --watchAll=false',
        'Integration tests',
      );
      break;

    case 'e2e':
      log('\n🌐 Running End-to-End Tests', 'magenta');
      allTestsPassed = runCommand('npx playwright test', 'End-to-end tests');
      break;

    case 'performance':
      log('\n⚡ Running Performance Tests', 'magenta');
      allTestsPassed = runCommand(
        'npm run test -- --testPathPattern="performance" --watchAll=false',
        'Performance tests',
      );
      break;

    case 'lint':
      log('\n🔍 Running Linting', 'magenta');
      allTestsPassed =
        runCommand('npm run lint', 'ESLint') &&
        runCommand('npm run type-check', 'TypeScript check');
      break;
    default: {
      log('\n🚀 Running All Tests', 'magenta');

      // Run linting first
      log('\n1️⃣ Linting and Type Checking', 'blue');
      const lintPassed =
        runCommand('npm run lint', 'ESLint') &&
        runCommand('npm run type-check', 'TypeScript check');

      if (!lintPassed) {
        log('❌ Linting failed. Fix issues before running tests.', 'red');
        process.exit(1);
      }

      // Run unit tests with coverage
      log('\n2️⃣ Unit Tests', 'blue');
      const unitPassed = runCommand(
        'npm run test -- --coverage --watchAll=false --passWithNoTests',
        'Unit tests with coverage',
      );

      // Check coverage
      const _coveragePassed = checkTestCoverage();

      // Run integration tests
      log('\n3️⃣ Integration Tests', 'blue');
      const integrationPassed = runCommand(
        'npm run test -- --testPathPattern="integration" --watchAll=false --passWithNoTests',
        'Integration tests',
      );

      // Run performance tests
      log('\n4️⃣ Performance Tests', 'blue');
      const performancePassed = runCommand(
        'npm run test -- --testPathPattern="performance" --watchAll=false --passWithNoTests',
        'Performance tests',
      );

      // Run E2E tests (optional, might not be available in all environments)
      log('\n5️⃣ End-to-End Tests', 'blue');
      let e2ePassed = true;
      try {
        e2ePassed = runCommand(
          'npx playwright test --reporter=line',
          'End-to-end tests',
        );
      } catch (_error) {
        log('⚠️  E2E tests skipped (Playwright not available)', 'yellow');
      }

      allTestsPassed =
        unitPassed && integrationPassed && performancePassed && e2ePassed;
      break;
    }
  }

  // Generate test report
  generateTestReport();

  // Final summary
  log('\n📋 Test Summary', 'bright');
  log('===============', 'bright');

  if (allTestsPassed) {
    log('🎉 All tests passed!', 'green');
    log('✅ Ready for deployment', 'green');
  } else {
    log('💥 Some tests failed', 'red');
    log('❌ Fix issues before deployment', 'red');
  }

  // Exit with appropriate code
  process.exit(allTestsPassed ? 0 : 1);
}

// Handle command line arguments
if (require.main === module) {
  main();
}

module.exports = {
  runCommand,
  checkTestCoverage,
  generateTestReport,
};
