import { afterEach, beforeEach, describe, expect, it } from 'bun:test';
import { getTestDatabaseConfig } from '../../config/database.config.js';
import { DatabaseManager } from '../../src/crawlers/base/DatabaseManager.js';
import { ReportGenerator } from '../../src/reporting/ReportGenerator.js';
import { performanceMonitor } from '../../src/utils/performance.js';

describe('Crawler Integration Tests', () => {
  let database: DatabaseManager;
  let reportGenerator: ReportGenerator;

  beforeEach(() => {
    database = new DatabaseManager(getTestDatabaseConfig());
    reportGenerator = new ReportGenerator(database);
    performanceMonitor.clearMetrics();
  });

  afterEach(() => {
    database.close();
  });

  describe('End-to-End Workflow', () => {
    it('should handle complete crawl workflow', async () => {
      // Create a test session
      const sessionId = database.createSession({
        platform: 'douyin',
        keywords: JSON.stringify(['sabah', 'tourism']),
        status: 'running',
        config: JSON.stringify({ maxPosts: 10 }),
      });

      expect(sessionId).toBeGreaterThan(0);

      // Add test posts
      const testPosts = [
        {
          platform: 'douyin',
          post_id: 'test_post_1',
          url: 'https://douyin.com/video/test_post_1',
          title: 'Beautiful Sabah Tourism Video',
          content: 'Amazing views of Mount Kinabalu #sabah #tourism #travel',
          author_username: 'sabah_explorer',
          author_display_name: 'Sabah Explorer',
          likes_count: 1500,
          comments_count: 120,
          shares_count: 45,
          views_count: 25000,
        },
        {
          platform: 'douyin',
          post_id: 'test_post_2',
          url: 'https://douyin.com/video/test_post_2',
          title: 'Sipadan Diving Experience',
          content: 'Underwater paradise in Sabah #diving #sipadan #sabah',
          author_username: 'dive_master',
          author_display_name: 'Dive Master',
          likes_count: 2300,
          comments_count: 180,
          shares_count: 67,
          views_count: 38000,
        },
      ];

      const postIds: number[] = [];
      for (const post of testPosts) {
        const postId = database.insertPost(post);
        postIds.push(postId);

        // Add test media files
        database.insertMediaFile({
          post_id: postId,
          media_type: 'video',
          file_name: `${post.post_id}.mp4`,
          file_path: `./output/douyin/${post.post_id}/${post.post_id}.mp4`,
          original_url: `https://example.com/${post.post_id}.mp4`,
          file_size: 5242880, // 5MB
          duration: 30,
          width: 1920,
          height: 1080,
        });

        // Add crawl attempt
        database.insertAttempt({
          session_id: sessionId,
          post_url: post.url,
          keyword: 'sabah',
          status: 'success',
        });
      }

      // Update session as completed
      database.updateSession(sessionId, {
        status: 'completed',
        completed_at: new Date(),
        total_posts: testPosts.length,
        successful_posts: testPosts.length,
        failed_posts: 0,
      });

      // Test database queries
      const stats = database.getStats();
      expect(stats.totalPosts).toBe(2);
      expect(stats.totalMediaFiles).toBe(2);
      expect(stats.totalSessions).toBe(1);

      // Test advanced queries
      const sabahPosts = database.getPostsByKeyword('sabah');
      expect(sabahPosts.length).toBe(2);

      const topAuthors = database.getTopAuthors('douyin', 5);
      expect(topAuthors.length).toBe(2);
      expect(topAuthors[0]?.author_username).toBe('dive_master'); // Higher likes

      const engagementStats = database.getEngagementStats('douyin');
      expect(engagementStats.totalLikes).toBe(3800);
      expect(engagementStats.totalComments).toBe(300);

      // Test report generation
      const reportData = await reportGenerator.generateReport({
        platform: 'douyin',
        includeMedia: true,
      });

      expect(reportData.posts.length).toBe(2);
      expect(reportData.summary.totalPosts).toBe(2);
      expect(reportData.summary.totalAuthors).toBe(2);
      expect(reportData.summary.totalMedia).toBe(2);
      expect(reportData.summary.totalEngagement.likes).toBe(3800);

      // Check that media files are included
      expect((reportData.posts[0] as any).mediaFiles).toBeDefined();
      expect((reportData.posts[0] as any).mediaFiles.length).toBe(1);

      // Test top keywords extraction
      expect(reportData.summary.topKeywords.length).toBeGreaterThan(0);
      const sahabKeyword = reportData.summary.topKeywords.find(
        (k) => k.keyword === 'sabah',
      );
      expect(sahabKeyword?.count).toBe(2);

      // Test top authors
      expect(reportData.summary.topAuthors.length).toBe(2);
      expect(reportData.summary.topAuthors[0]?.author).toBe('dive_master');
      expect(reportData.summary.topAuthors[0]?.totalLikes).toBe(2300);
    });

    it('should handle resume functionality', async () => {
      // Create session with pending attempts
      const sessionId = database.createSession({
        platform: 'douyin',
        keywords: JSON.stringify(['test']),
        status: 'paused',
        config: JSON.stringify({}),
      });

      // Add pending attempts
      const attemptIds = [
        database.insertAttempt({
          session_id: sessionId,
          post_url: 'https://douyin.com/video/pending1',
          keyword: 'test',
          status: 'pending',
        }),
        database.insertAttempt({
          session_id: sessionId,
          post_url: 'https://douyin.com/video/pending2',
          keyword: 'test',
          status: 'pending',
        }),
      ];

      // Test getting pending attempts
      const pendingAttempts = database.getPendingAttempts(sessionId);
      expect(pendingAttempts.length).toBe(2);

      // Simulate processing one attempt
      database.updateAttempt(attemptIds[0], {
        status: 'success',
        completed_at: new Date(),
      });

      // Check that only one is still pending
      const remainingPending = database.getPendingAttempts(sessionId);
      expect(remainingPending.length).toBe(1);
      expect(remainingPending[0]?.post_url).toBe(
        'https://douyin.com/video/pending2',
      );
    });

    it('should handle performance monitoring', async () => {
      // Test performance monitoring
      performanceMonitor.startOperation('test-operation', { testData: true });

      // Simulate some work
      await new Promise((resolve) => setTimeout(resolve, 100));

      const metrics = performanceMonitor.endOperation('test-operation', {
        completed: true,
      });

      expect(metrics).toBeTruthy();
      expect(metrics!.operation).toBe('test-operation');
      expect(metrics!.duration).toBeGreaterThan(90); // Should be around 100ms
      expect(metrics!.metadata?.testData).toBe(true);
      expect(metrics!.metadata?.completed).toBe(true);

      // Test async measurement
      const { result, metrics: asyncMetrics } =
        await performanceMonitor.measureAsync(
          'async-test',
          async () => {
            await new Promise((resolve) => setTimeout(resolve, 50));
            return 'test-result';
          },
          { async: true },
        );

      expect(result).toBe('test-result');
      expect(asyncMetrics.operation).toBe('async-test');
      expect(asyncMetrics.duration).toBeGreaterThan(40);

      // Test summary
      const summary = performanceMonitor.getSummary();
      expect(summary.totalOperations).toBe(2);
      expect(summary.operationBreakdown['test-operation']).toBe(1);
      expect(summary.operationBreakdown['async-test']).toBe(1);
    });

    it('should handle error scenarios gracefully', async () => {
      // Test with invalid session
      const invalidSession = database.getSession(99999);
      expect(invalidSession).toBeNull();

      // Test with invalid post
      const invalidPost = database.getPost('invalid', 'invalid');
      expect(invalidPost).toBeNull();

      // Test report generation with no data
      const emptyReport = await reportGenerator.generateReport({
        platform: 'nonexistent',
      });

      expect(emptyReport.posts.length).toBe(0);
      expect(emptyReport.summary.totalPosts).toBe(0);

      // Test performance monitoring with non-existent operation
      const invalidMetrics = performanceMonitor.endOperation('non-existent');
      expect(invalidMetrics).toBeNull();
    });
  });

  describe('Data Integrity', () => {
    it('should maintain referential integrity', async () => {
      // Create post
      const postId = database.insertPost({
        platform: 'douyin',
        post_id: 'integrity_test',
        url: 'https://douyin.com/video/integrity_test',
      });

      // Add media file
      const mediaId = database.insertMediaFile({
        post_id: postId,
        media_type: 'video',
        file_name: 'test.mp4',
        file_path: './test.mp4',
      });

      // Verify relationships
      const mediaFiles = database.getMediaFiles(postId);
      expect(mediaFiles.length).toBe(1);
      expect(mediaFiles[0]?.id).toBe(mediaId);
      expect(mediaFiles[0]?.post_id).toBe(postId);
    });

    it('should handle duplicate posts correctly', async () => {
      const postData = {
        platform: 'douyin',
        post_id: 'duplicate_test',
        url: 'https://douyin.com/video/duplicate_test',
        title: 'Original Title',
        likes_count: 100,
      };

      // Insert first time
      const firstId = database.insertPost(postData);

      // Insert again with updated data
      const updatedData = {
        ...postData,
        title: 'Updated Title',
        likes_count: 200,
      };
      const secondId = database.insertPost(updatedData);

      // Should update existing record, not create new one
      const post = database.getPost('douyin', 'duplicate_test');
      expect(post?.title).toBe('Updated Title');
      expect(post?.likes_count).toBe(200);

      // Verify only one post exists
      const allPosts = database['db']
        .prepare('SELECT COUNT(*) as count FROM social_posts WHERE post_id = ?')
        .get('duplicate_test') as { count: number };
      expect(allPosts.count).toBe(1);
    });
  });
});
