#!/usr/bin/env node

const FRONTEND_URL = 'http://localhost:16000';

async function testFrontendPages() {
  console.log('🚀 Starting Frontend Page Tests\n');
  
  const testUrls = [
    { path: '/', name: 'Home Page' },
    { path: '/en/admin', name: 'Ad<PERSON> (EN)' },
    { path: '/en/admin/collections', name: 'Collections Page (EN)' },
    { path: '/en/admin/users', name: 'Users Page (EN)' },
    { path: '/en/admin/bots', name: 'Bots Page (EN)' },
    { path: '/en/admin/services', name: 'Services Page (EN)' },
    { path: '/en/admin/sessions', name: '<PERSON> Page (EN)' },
    { path: '/en/admin/s3-configurations', name: 'S3 Configurations Page (EN)' },
    { path: '/en/admin/bots/new', name: 'New Bot Form (EN)' },
    { path: '/en/admin/collections/new', name: 'New Collection Form (EN)' },
    // Test without locale prefix too
    { path: '/admin', name: '<PERSON><PERSON>gin (No Locale)' },
    { path: '/admin/collections', name: 'Collections Page (No Locale)' },
  ];
  
  for (const { path, name } of testUrls) {
    try {
      console.log(`📋 Testing ${name}: ${path}`);
      
      const response = await fetch(`${FRONTEND_URL}${path}`, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; AdminTest/1.0)',
        },
      });
      
      const statusColor = response.ok ? '✅' : '❌';
      console.log(`${statusColor} ${name}: ${response.status} ${response.statusText}`);
      
      if (!response.ok) {
        const text = await response.text();
        if (text.includes('error') || text.includes('Error')) {
          const errorMatch = text.match(/<title>(.*?)<\/title>/);
          const title = errorMatch ? errorMatch[1] : 'Unknown error';
          console.log(`   Error: ${title}`);
        }
      }
      
    } catch (error) {
      console.log(`❌ ${name}: Network error - ${error.message}`);
    }
  }
  
  console.log('\n📋 Testing API endpoints from frontend perspective:');
  
  // Test if frontend can reach API
  const apiTests = [
    { path: '/api/admin/login', method: 'POST', name: 'Admin Login API' },
    { path: '/api/sites/1/admin/collections', method: 'GET', name: 'Collections API' },
    { path: '/api/sites/1/admin/users', method: 'GET', name: 'Users API' },
    { path: '/api/sites/1/admin/bots', method: 'GET', name: 'Bots API' },
  ];
  
  for (const { path, method, name } of apiTests) {
    try {
      console.log(`📡 Testing ${name}: ${method} ${path}`);
      
      let options = { method };
      
      if (method === 'POST' && path.includes('login')) {
        options.headers = { 'Content-Type': 'application/json' };
        options.body = JSON.stringify({ username: 'admin', password: 'admin123' });
      }
      
      const response = await fetch(`${FRONTEND_URL}${path}`, options);
      const statusColor = response.ok ? '✅' : '❌';
      console.log(`${statusColor} ${name}: ${response.status} ${response.statusText}`);
      
    } catch (error) {
      console.log(`❌ ${name}: Network error - ${error.message}`);
    }
  }
  
  console.log('\n✅ Frontend tests completed');
}

testFrontendPages().catch(console.error);
