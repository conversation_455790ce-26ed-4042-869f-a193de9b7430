import { create } from 'zustand';
import type { Bo<PERSON> } from '@/types/bot';
import { useAuthStore } from './auth';

interface BotsState {
  bots: Bot[];
  currentBot: Bot | null;
  isLoading: boolean;
  error: string | null;
}

interface BotsActions {
  fetchBots: () => Promise<void>;
  fetchBotById: (id: number) => Promise<Bot | null>;
  createBot: (
    data: Omit<Bot, 'id' | 'siteId' | 'createdAt' | 'updatedAt'>,
  ) => Promise<boolean>;
  updateBot: (
    id: number,
    data: Partial<Omit<Bot, 'id' | 'createdAt' | 'updatedAt'>>,
  ) => Promise<boolean>;
  deleteBot: (id: number) => Promise<boolean>;
  setCurrentBot: (bot: Bot | null) => void;
  clearError: () => void;
}

type BotsStore = BotsState & BotsActions;

// Use frontend API proxy instead of direct backend calls
const API_BASE_URL = '';

export const useBotsStore = create<BotsStore>((set, get) => ({
  bots: [],
  currentBot: null,
  isLoading: false,
  error: null,

  fetchBots: async () => {
    set({ isLoading: true, error: null });

    try {
      const { adminToken } = useAuthStore.getState();
      if (!adminToken) {
        set({ error: 'No authentication token', isLoading: false });
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/bots`, {
        headers: {
          Authorization: `Bearer ${adminToken}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          set({ bots: result.data, isLoading: false });
        } else {
          set({
            error: result.error || 'Failed to fetch bots',
            isLoading: false,
          });
        }
      } else {
        const errorData = await response.json();
        set({
          error: errorData.error || 'Failed to fetch bots',
          isLoading: false,
        });
      }
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Network error',
        isLoading: false,
      });
    }
  },

  fetchBotById: async (id: number) => {
    set({ isLoading: true, error: null });

    try {
      const { adminToken } = useAuthStore.getState();
      if (!adminToken) {
        set({ error: 'No authentication token', isLoading: false });
        return null;
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/bots/${id}`, {
        headers: {
          Authorization: `Bearer ${adminToken}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          set({ currentBot: result.data, isLoading: false });
          return result.data;
        } else {
          set({
            error: result.error || 'Failed to fetch bot',
            isLoading: false,
          });
          return null;
        }
      }
      const errorData = await response.json();
      set({
        error: errorData.error || 'Failed to fetch bot',
        isLoading: false,
      });
      return null;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Network error',
        isLoading: false,
      });
      return null;
    }
  },

  createBot: async (
    data: Omit<Bot, 'id' | 'siteId' | 'createdAt' | 'updatedAt'>,
  ) => {
    set({ isLoading: true, error: null });

    try {
      const { adminToken } = useAuthStore.getState();
      if (!adminToken) {
        set({ error: 'No authentication token', isLoading: false });
        return false;
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/bots`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${adminToken}`,
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          await get().fetchBots();
          set({ isLoading: false });
          return true;
        } else {
          set({
            error: result.error || 'Failed to create bot',
            isLoading: false,
          });
          return false;
        }
      }
      const errorData = await response.json();
      set({
        error: errorData.error || 'Failed to create bot',
        isLoading: false,
      });
      return false;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Network error',
        isLoading: false,
      });
      return false;
    }
  },

  updateBot: async (
    id: number,
    data: Partial<Omit<Bot, 'id' | 'createdAt' | 'updatedAt'>>,
  ) => {
    set({ isLoading: true, error: null });

    try {
      const { adminToken } = useAuthStore.getState();
      if (!adminToken) {
        set({ error: 'No authentication token', isLoading: false });
        return false;
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/bots/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${adminToken}`,
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          await get().fetchBots();
          set({ isLoading: false });
          return true;
        } else {
          set({
            error: result.error || 'Failed to update bot',
            isLoading: false,
          });
          return false;
        }
      }
      const errorData = await response.json();
      set({
        error: errorData.error || 'Failed to update bot',
        isLoading: false,
      });
      return false;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Network error',
        isLoading: false,
      });
      return false;
    }
  },

  deleteBot: async (id: number) => {
    set({ isLoading: true, error: null });

    try {
      const { adminToken } = useAuthStore.getState();
      if (!adminToken) {
        set({ error: 'No authentication token', isLoading: false });
        return false;
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/bots/${id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${adminToken}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          await get().fetchBots();
          set({ isLoading: false });
          return true;
        } else {
          set({
            error: result.error || 'Failed to delete bot',
            isLoading: false,
          });
          return false;
        }
      }
      const errorData = await response.json();
      set({
        error: errorData.error || 'Failed to delete bot',
        isLoading: false,
      });
      return false;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Network error',
        isLoading: false,
      });
      return false;
    }
  },

  setCurrentBot: (bot: Bot | null) => {
    set({ currentBot: bot });
  },

  clearError: () => {
    set({ error: null });
  },
}));
