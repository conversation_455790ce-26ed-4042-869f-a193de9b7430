'use client';

import {
  ArcElement,
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
} from 'chart.js';
import { useEffect, useState } from 'react';
import { <PERSON>, Doughnut, Line } from 'react-chartjs-2';
import { api } from '@/lib/api';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
);

interface AgentPerformanceData {
  agentId: number;
  agentName: string;
  totalSessions: number;
  completedSessions: number;
  averageResponseTime: number; // in seconds
  averageSessionDuration: number; // in minutes
  customerSatisfactionScore: number; // 1-5 scale
  onlineHours: number;
  handoverRate: number; // percentage
}

interface AnalyticsData {
  agentPerformance: AgentPerformanceData[];
  sessionTrends: {
    date: string;
    totalSessions: number;
    completedSessions: number;
    averageResponseTime: number;
  }[];
  workloadDistribution: {
    agentName: string;
    activeSessions: number;
    pendingSessions: number;
  }[];
  satisfactionTrends: {
    date: string;
    averageScore: number;
    totalRatings: number;
  }[];
}

interface AgentAnalyticsProps {
  timeRange?: '7d' | '30d' | '90d';
  agentId?: number;
}

export function AgentAnalytics({
  timeRange = '30d',
  agentId,
}: AgentAnalyticsProps) {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAnalyticsData();
  }, [timeRange, agentId]);

  const loadAnalyticsData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Mock data for now - replace with actual API calls
      const mockData: AnalyticsData = {
        agentPerformance: [
          {
            agentId: 1,
            agentName: 'John Doe',
            totalSessions: 45,
            completedSessions: 42,
            averageResponseTime: 120,
            averageSessionDuration: 15.5,
            customerSatisfactionScore: 4.2,
            onlineHours: 160,
            handoverRate: 6.7,
          },
          {
            agentId: 2,
            agentName: 'Jane Smith',
            totalSessions: 38,
            completedSessions: 36,
            averageResponseTime: 95,
            averageSessionDuration: 12.3,
            customerSatisfactionScore: 4.5,
            onlineHours: 155,
            handoverRate: 5.3,
          },
          {
            agentId: 3,
            agentName: 'Mike Johnson',
            totalSessions: 52,
            completedSessions: 48,
            averageResponseTime: 140,
            averageSessionDuration: 18.2,
            customerSatisfactionScore: 3.9,
            onlineHours: 170,
            handoverRate: 7.7,
          },
        ],
        sessionTrends: Array.from({ length: 30 }, (_, i) => ({
          date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000)
            .toISOString()
            .split('T')[0],
          totalSessions: Math.floor(Math.random() * 20) + 10,
          completedSessions: Math.floor(Math.random() * 18) + 8,
          averageResponseTime: Math.floor(Math.random() * 60) + 80,
        })),
        workloadDistribution: [
          { agentName: 'John Doe', activeSessions: 3, pendingSessions: 1 },
          { agentName: 'Jane Smith', activeSessions: 2, pendingSessions: 0 },
          { agentName: 'Mike Johnson', activeSessions: 4, pendingSessions: 2 },
        ],
        satisfactionTrends: Array.from({ length: 30 }, (_, i) => ({
          date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000)
            .toISOString()
            .split('T')[0],
          averageScore: Math.random() * 2 + 3, // 3-5 range
          totalRatings: Math.floor(Math.random() * 10) + 5,
        })),
      };

      setData(mockData);
    } catch (error) {
      console.error('Error loading analytics data:', error);
      setError('Failed to load analytics data');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
        <span className="ml-2 text-gray-600">Loading analytics...</span>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">{error || 'No data available'}</p>
        <button
          onClick={loadAnalyticsData}
          className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    );
  }

  // Chart configurations
  const sessionTrendsData = {
    labels: data.sessionTrends.map((d) =>
      new Date(d.date).toLocaleDateString(),
    ),
    datasets: [
      {
        label: 'Total Sessions',
        data: data.sessionTrends.map((d) => d.totalSessions),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.1,
      },
      {
        label: 'Completed Sessions',
        data: data.sessionTrends.map((d) => d.completedSessions),
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.1,
      },
    ],
  };

  const responseTimeData = {
    labels: data.agentPerformance.map((a) => a.agentName),
    datasets: [
      {
        label: 'Average Response Time (seconds)',
        data: data.agentPerformance.map((a) => a.averageResponseTime),
        backgroundColor: [
          'rgba(59, 130, 246, 0.8)',
          'rgba(34, 197, 94, 0.8)',
          'rgba(251, 191, 36, 0.8)',
          'rgba(239, 68, 68, 0.8)',
        ],
      },
    ],
  };

  const satisfactionData = {
    labels: data.agentPerformance.map((a) => a.agentName),
    datasets: [
      {
        data: data.agentPerformance.map((a) => a.customerSatisfactionScore),
        backgroundColor: ['#10B981', '#3B82F6', '#F59E0B', '#EF4444'],
      },
    ],
  };

  const workloadData = {
    labels: data.workloadDistribution.map((w) => w.agentName),
    datasets: [
      {
        label: 'Active Sessions',
        data: data.workloadDistribution.map((w) => w.activeSessions),
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
      },
      {
        label: 'Pending Sessions',
        data: data.workloadDistribution.map((w) => w.pendingSessions),
        backgroundColor: 'rgba(251, 191, 36, 0.8)',
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const doughnutOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">Total Sessions</h3>
          <p className="text-2xl font-bold text-gray-900">
            {data.agentPerformance.reduce(
              (sum, agent) => sum + agent.totalSessions,
              0,
            )}
          </p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">
            Avg Response Time
          </h3>
          <p className="text-2xl font-bold text-gray-900">
            {Math.round(
              data.agentPerformance.reduce(
                (sum, agent) => sum + agent.averageResponseTime,
                0,
              ) / data.agentPerformance.length,
            )}
            s
          </p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">
            Avg Satisfaction
          </h3>
          <p className="text-2xl font-bold text-gray-900">
            {(
              data.agentPerformance.reduce(
                (sum, agent) => sum + agent.customerSatisfactionScore,
                0,
              ) / data.agentPerformance.length
            ).toFixed(1)}
          </p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500">Completion Rate</h3>
          <p className="text-2xl font-bold text-gray-900">
            {Math.round(
              (data.agentPerformance.reduce(
                (sum, agent) => sum + agent.completedSessions,
                0,
              ) /
                data.agentPerformance.reduce(
                  (sum, agent) => sum + agent.totalSessions,
                  0,
                )) *
                100,
            )}
            %
          </p>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Session Trends */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Session Trends
          </h3>
          <Line data={sessionTrendsData} options={chartOptions} />
        </div>

        {/* Response Time by Agent */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Response Time by Agent
          </h3>
          <Bar data={responseTimeData} options={chartOptions} />
        </div>

        {/* Customer Satisfaction */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Customer Satisfaction
          </h3>
          <Doughnut data={satisfactionData} options={doughnutOptions} />
        </div>

        {/* Current Workload */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Current Workload
          </h3>
          <Bar data={workloadData} options={chartOptions} />
        </div>
      </div>
    </div>
  );
}
