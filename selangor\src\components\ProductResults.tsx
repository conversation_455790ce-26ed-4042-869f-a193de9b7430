'use client';

import {
  ChevronLeft,
  ChevronRight,
  Loader2,
  Package,
  Search,
  ShoppingBag,
  Zap,
} from 'lucide-react';
import type {
  Product,
  ProductSearchResponse,
  ProductWithSimilarity,
  SemanticProductSearchResponse,
} from '@/types/product';

interface ProductResultsProps {
  productResponse: ProductSearchResponse | SemanticProductSearchResponse;
  onPageChange?: (page: number) => void;
  isLoading?: boolean;
}

interface ProductItemProps {
  product: Product | ProductWithSimilarity;
  query: string;
  searchType?:
    | 'keyword'
    | 'semantic'
    | 'multilingual'
    | 'multilingual-semantic';
}

function ProductItem({
  product,
  query,
  searchType = 'keyword',
}: ProductItemProps) {
  const {
    productName,
    companyName,
    category,
    subcategory,
    certificateNumber,
    certificateType,
    status,
    state,
    country,
    website,
    contactInfo,
  } = product;

  // Check if this is a semantic search result with similarity score and search term
  const similarityScore =
    'similarityScore' in product ? product.similarityScore : undefined;
  const foundBySearchTerm =
    'foundBySearchTerm' in product ? product.foundBySearchTerm : undefined;

  // Highlight search terms in the text
  const highlightText = (text: string, query: string) => {
    if (!query) return text;

    const words = query.toLowerCase().split(/\s+/);
    let highlightedText = text;

    words.forEach((word) => {
      if (word.length > 2) {
        const regex = new RegExp(`(${word})`, 'gi');
        highlightedText = highlightedText.replace(
          regex,
          '<mark class="bg-yellow-200 px-1 rounded">$1</mark>',
        );
      }
    });

    return highlightedText;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex gap-4">
        {/* Product Image */}
        <div className="w-24 h-24 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
          <Package className="w-8 h-8 text-gray-400" />
        </div>

        {/* Product Details */}
        <div className="flex-1">
          {/* Product Header */}
          <div className="flex items-start justify-between mb-2">
            <div>
              <h3
                className="text-lg font-semibold text-gray-900"
                dangerouslySetInnerHTML={{
                  __html: highlightText(productName, query),
                }}
              />
              <p
                className="text-sm text-gray-600"
                dangerouslySetInnerHTML={{
                  __html: highlightText(
                    `${companyName}${category ? ` • ${category}` : ''}${subcategory ? ` • ${subcategory}` : ''}`,
                    query,
                  ),
                }}
              />
            </div>
            <div className="text-right">
              {status && (
                <div className="text-sm font-medium text-green-600">
                  {status}
                </div>
              )}
              {state && <div className="text-xs text-gray-500">{state}</div>}
            </div>
          </div>

          {/* Contact Info */}
          {contactInfo && (
            <p
              className="text-gray-700 text-sm mb-3"
              dangerouslySetInnerHTML={{
                __html: highlightText(contactInfo, query),
              }}
            />
          )}

          {/* Certification */}
          <div className="flex items-center gap-2 mb-3">
            {certificateNumber && (
              <div className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                ✓ HALAL {certificateNumber}
              </div>
            )}
            {certificateType && (
              <div className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                {certificateType}
              </div>
            )}
            {/* Similarity Score for Semantic Search (including multilingual) */}
            {(searchType === 'semantic' ||
              searchType === 'multilingual-semantic') &&
              similarityScore !== undefined && (
                <div className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full flex items-center">
                  <Zap className="w-3 h-3 mr-1" />
                  {Math.round(similarityScore * 100)}% match
                </div>
              )}
            {/* Search Term Tag for Semantic Search */}
            {(searchType === 'semantic' ||
              searchType === 'multilingual-semantic') &&
              foundBySearchTerm &&
              typeof foundBySearchTerm === 'string' &&
              foundBySearchTerm !== query && (
                <div className="bg-purple-100 text-purple-800 text-xs font-medium px-2 py-1 rounded-full flex items-center">
                  <span className="mr-1">🔍</span>
                  Found by: "{foundBySearchTerm}"
                </div>
              )}
          </div>

          {/* Additional Info */}
          <div className="flex flex-wrap gap-2 mb-3 text-xs text-gray-600">
            {country && (
              <span className="bg-gray-100 px-2 py-1 rounded">
                📍 {country}
              </span>
            )}
            {website && (
              <span className="bg-gray-100 px-2 py-1 rounded">🌐 Website</span>
            )}
          </div>

          {/* Actions */}
          {false && (
            <div className="flex items-center gap-2">
              <button
                type="button"
                className="bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors"
              >
                View Details
              </button>
              <button
                type="button"
                className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors"
              >
                <ShoppingBag className="w-4 h-4 mr-2 inline" />
                Add to Cart
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function ProductPagination({
  productResponse,
  onPageChange,
}: {
  productResponse: ProductSearchResponse | SemanticProductSearchResponse;
  onPageChange?: (page: number) => void;
}) {
  const { pagination } = productResponse;

  if (!pagination) return null;

  const { page, total, hasMore, totalPages } = pagination;

  if (totalPages <= 1) return null;

  return (
    <div className="flex items-center justify-between mt-8">
      <div className="text-sm text-gray-700">
        Showing page {page} of {totalPages} ({total} total products)
      </div>

      <div className="flex items-center space-x-2">
        <button
          type="button"
          onClick={() => onPageChange?.(page - 1)}
          disabled={page <= 1}
          className="flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft className="w-4 h-4 mr-1" />
          Previous
        </button>

        <span className="px-3 py-2 text-sm font-medium text-gray-700">
          {page}
        </span>

        <button
          type="button"
          onClick={() => onPageChange?.(page + 1)}
          disabled={!hasMore}
          className="flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
          <ChevronRight className="w-4 h-4 ml-1" />
        </button>
      </div>
    </div>
  );
}

export function ProductResults({
  productResponse,
  onPageChange,
  isLoading,
}: ProductResultsProps) {
  const { query, products } = productResponse;
  const searchType =
    'searchType' in productResponse ? productResponse.searchType : 'keyword';

  // Check if this is a multilingual search with translated terms
  const detectedLanguage = (
    productResponse as ProductSearchResponse & { detectedLanguage: string }
  ).detectedLanguage;
  const isMultilingual = detectedLanguage && detectedLanguage !== 'en';
  const translatedTerms =
    (productResponse as ProductSearchResponse & { translatedTerms: string[] })
      .translatedTerms ?? [];

  if (isLoading) {
    return (
      <div className="text-center py-12">
        <Loader2 className="w-12 h-12 text-green-600 mx-auto mb-4 animate-spin" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Searching products...
        </h3>
        <p className="text-gray-600">
          Please wait while we search for halal products.
        </p>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No products found
        </h3>
        <p className="text-gray-600">
          {query ? (
            <>
              No halal products found for "
              <span className="font-medium text-gray-800">{query}</span>"
              {isMultilingual && translatedTerms.length > 0 && (
                <>
                  {' '}
                  and translated terms "
                  <span className="font-medium text-gray-800">
                    {translatedTerms.join('", "')}
                  </span>
                  "
                </>
              )}
              .
              <br />
              Try adjusting your search terms or browse our categories.
            </>
          ) : (
            'Try adjusting your search terms or browse our categories.'
          )}
        </p>
      </div>
    );
  }

  return (
    <div>
      {/* Results summary */}
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          <h2 className="text-xl font-semibold text-gray-900">
            {query ? `Products for "${query}"` : 'All Halal Products'}
          </h2>
          {(searchType === 'semantic' ||
            searchType === 'multilingual-semantic') && (
            <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full flex items-center">
              <Zap className="w-3 h-3 mr-1" />
              {searchType === 'multilingual-semantic'
                ? 'Multilingual AI Search'
                : 'AI Search'}
            </span>
          )}
          {(searchType === 'keyword' || searchType === 'multilingual') &&
            query && (
              <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full flex items-center">
                <Search className="w-3 h-3 mr-1" />
                {searchType === 'multilingual'
                  ? 'Multilingual Search'
                  : 'Keyword Search'}
              </span>
            )}
        </div>
        <p className="text-gray-600">
          {query
            ? `Found ${products.length} halal products`
            : `Showing ${products.length} halal products`}
          {(searchType === 'semantic' ||
            searchType === 'multilingual-semantic') &&
            query && (
              <span className="text-sm text-gray-500 ml-2">
                • Results ranked by AI similarity
                {searchType === 'multilingual-semantic' && ' (multilingual)'}
              </span>
            )}
          {translatedTerms.length > 0 && (
            <span className="text-sm text-gray-500 ml-2">
              • Also searching for: {translatedTerms.join(', ')}
            </span>
          )}
        </p>
      </div>

      {/* Products list */}
      <div className="space-y-4">
        {products.map((product) => (
          <ProductItem
            key={product.id}
            product={product}
            query={query}
            searchType={searchType}
          />
        ))}
      </div>

      {/* Pagination */}
      <ProductPagination
        productResponse={productResponse}
        onPageChange={onPageChange}
      />
    </div>
  );
}
