// Navigation data for Halal Malaysia Portal

export interface NavItem {
  id: string;
  label: string;
  labelBM: string; // Bahasa Malaysia
  href: string;
  children?: NavItem[];
}

export const mainNavigation: NavItem[] = [
  {
    id: 'corporate',
    label: 'Corporate',
    labelBM: 'Korporat',
    href: '/corporate',
    children: [
      {
        id: 'about',
        label: 'About Us',
        labelBM: 'Tentang Kami',
        href: '/corporate/about',
      },
      {
        id: 'vision-mission',
        label: 'Vision & Mission',
        labelBM: 'Visi & Misi',
        href: '/corporate/vision-mission',
      },
      {
        id: 'organization',
        label: 'Organization Chart',
        labelBM: 'Carta Organisasi',
        href: '/corporate/organization',
      },
    ],
  },
  {
    id: 'certification',
    label: 'Certification',
    labelBM: 'Pensijilan',
    href: '/certification',
    children: [
      {
        id: 'domestic',
        label: 'Domestic',
        labelBM: 'Domestik',
        href: '/certification/domestic',
      },
      {
        id: 'international',
        label: 'International',
        labelBM: 'Antarabangsa',
        href: '/certification/international',
      },
      {
        id: 'recognition',
        label: 'Recognition',
        labelBM: 'Pengiktirafan',
        href: '/certification/recognition',
      },
    ],
  },
  {
    id: 'procedure',
    label: 'Procedure',
    labelBM: 'Prosedur',
    href: '/procedure',
    children: [
      {
        id: 'application',
        label: 'Application Process',
        labelBM: 'Proses Permohonan',
        href: '/procedure/application',
      },
      {
        id: 'requirements',
        label: 'Requirements',
        labelBM: 'Keperluan',
        href: '/procedure/requirements',
      },
      {
        id: 'guidelines',
        label: 'Guidelines',
        labelBM: 'Garis Panduan',
        href: '/procedure/guidelines',
      },
    ],
  },
  {
    id: 'contact',
    label: 'Contact Us',
    labelBM: 'Hubungi Kami',
    href: '/contact',
  },
];

export const quickLinks = [
  {
    id: 'myehalal-domestic',
    title: 'MYeHALAL Domestic',
    titleBM: 'MYeHALAL Domestik',
    description: 'Application system for domestic organizations',
    descriptionBM: 'Sistem permohonan untuk organisasi domestik',
    href: 'https://myehalal.halal.gov.my/domestik/v1/',
    icon: '/images/icons/monitor.png',
    external: true,
  },
  {
    id: 'myehalal-international',
    title: 'MYeHALAL International',
    titleBM: 'MYeHALAL Antarabangsa',
    description: 'Application system for international organizations',
    descriptionBM: 'Sistem permohonan untuk organisasi antarabangsa',
    href: 'https://myehalal.halal.gov.my/international/v1/pemohon/',
    icon: '/images/icons/monitor.png',
    external: true,
  },
  {
    id: 'fhcb',
    title: 'Foreign Halal Certification Body',
    titleBM: 'Badan Pensijilan Halal Asing',
    description: 'Information about foreign certification bodies',
    descriptionBM: 'Maklumat mengenai badan pensijilan asing',
    href: '/fhcb',
    icon: '/images/icons/fhcb.jpeg',
  },
  {
    id: 'halal-info',
    title: 'Halal Information',
    titleBM: 'Maklumat Halal',
    description: 'General information about Halal certification',
    descriptionBM: 'Maklumat umum mengenai pensijilan Halal',
    href: '/info',
    icon: '/images/logos/halal_logo.png',
  },
  {
    id: 'circular',
    title: 'Circular',
    titleBM: 'Pekeliling',
    description: 'Official circulars and documents',
    descriptionBM: 'Pekeliling dan dokumen rasmi',
    href: '/circular',
    icon: '/images/icons/pekeliling.png',
  },
  {
    id: 'press-statement',
    title: 'Press Statement',
    titleBM: 'Kenyataan Akhbar',
    description: 'Latest press releases and statements',
    descriptionBM: 'Siaran akhbar dan kenyataan terkini',
    href: '/press',
    icon: '/images/icons/megaphone.png',
  },
  {
    id: 'journal',
    title: 'Journal',
    titleBM: 'Jurnal',
    description: 'Academic journals and publications',
    descriptionBM: 'Jurnal akademik dan penerbitan',
    href: '/journal',
    icon: '/images/icons/book.png',
  },
  {
    id: 'myhac',
    title: 'Info MyHAC',
    titleBM: 'Info MyHAC',
    description: 'MyHAC system information',
    descriptionBM: 'Maklumat sistem MyHAC',
    href: '/myhac',
    icon: '/images/icons/myhac.jpeg',
  },
  {
    id: 'ai-chat',
    title: 'AI Chat Assistant',
    titleBM: 'Pembantu Chat AI',
    description: 'Chat with AI assistant for Halal inquiries',
    descriptionBM: 'Berbual dengan pembantu AI untuk pertanyaan Halal',
    href: '/chat',
    icon: '/images/icons/chat.svg',
  },
];

export const footerLinks = {
  government: [
    {
      title: 'Malaysia Government',
      titleBM: 'Kerajaan Malaysia',
      href: 'http://www.malaysia.gov.my',
      logo: '/images/logos/gov.png',
    },
    {
      title: 'JAKIM',
      titleBM: 'JAKIM',
      href: 'http://www.islam.gov.my',
      logo: '/images/logos/jakim_logo.png',
    },
    {
      title: 'KPDN',
      titleBM: 'KPDN',
      href: 'https://www.kpdn.gov.my',
      logo: '/images/logos/kpdnkk.png',
    },
    {
      title: 'Suruhanjaya Syarikat Malaysia',
      titleBM: 'Suruhanjaya Syarikat Malaysia',
      href: 'http://www.ssm.com.my',
      logo: '/images/logos/ssm.png',
    },
    {
      title: 'Kementerian Kesihatan Malaysia',
      titleBM: 'Kementerian Kesihatan Malaysia',
      href: 'http://www.moh.gov.my',
      logo: '/images/logos/kkm.png',
    },
  ],
  social: [
    {
      platform: 'Facebook',
      href: 'https://www.facebook.com/HabHalalJakim/',
      icon: '/images/social/facebook.png',
    },
    {
      platform: 'X (Twitter)',
      href: 'https://x.com/halal_malaysia',
      icon: '/images/social/x.png',
    },
  ],
};

export const contactInfo = {
  address: {
    en: `BAHAGIAN PENGURUSAN HALAL,
JABATAN KEMAJUAN ISLAM MALAYSIA,
Aras 6 & 7, Blok D,
Kompleks Islam Putrajaya (KIP),
No. 3 Jalan Tun Abdul Razak,
Presint 3, 62100 Putrajaya, Malaysia.`,
    bm: `BAHAGIAN PENGURUSAN HALAL,
JABATAN KEMAJUAN ISLAM MALAYSIA,
Aras 6 & 7, Blok D,
Kompleks Islam Putrajaya (KIP),
No. 3 Jalan Tun Abdul Razak,
Presint 3, 62100 Putrajaya, Malaysia.`,
  },
  phone: '03-8892 5000',
  fax: '03-8892 5005',
  email: '<EMAIL>',
};

export const operationHours = {
  weekdays: {
    en: {
      morning: '8:30 AM until 12:30 PM',
      afternoon: '2:30 PM until 4:30 PM',
    },
    bm: {
      morning: '8:30 Pagi sehingga 12.30 Tengahari',
      afternoon: '2:30 Petang sehingga 4.30 Petang',
    },
  },
  friday: {
    en: {
      morning: '8:30 AM until 12:00 PM',
      afternoon: '2:45 PM until 4:30 PM',
    },
    bm: {
      morning: '8:30 Pagi sehingga 12.00 Tengahari',
      afternoon: '2:45 Petang sehingga 4.30 Petang',
    },
  },
  weekend: {
    en: 'Closed',
    bm: 'Tutup',
  },
};
