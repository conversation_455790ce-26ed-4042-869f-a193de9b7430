'use client';

import { ChevronLeft, ChevronRight, Pause, Play } from 'lucide-react';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';
import { sliderItems } from '@/data/content';
import { Link } from '@/i18n/navigation';
import { useLanguage } from '@/lib/language-context';
import { cn } from '@/lib/utils';

interface HeroCarouselProps {
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showControls?: boolean;
  showIndicators?: boolean;
  className?: string;
}

export function HeroCarousel({
  autoPlay = true,
  autoPlayInterval = 5000,
  showControls = true,
  showIndicators = true,
  className,
}: HeroCarouselProps) {
  const { language } = useLanguage();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [isHovered, setIsHovered] = useState(false);

  const nextSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev + 1) % sliderItems.length);
  }, []);

  const prevSlide = useCallback(() => {
    setCurrentSlide(
      (prev) => (prev - 1 + sliderItems.length) % sliderItems.length,
    );
  }, []);

  const goToSlide = useCallback((index: number) => {
    setCurrentSlide(index);
  }, []);

  const togglePlayPause = useCallback(() => {
    setIsPlaying((prev) => !prev);
  }, []);

  // Auto-play functionality
  useEffect(() => {
    if (!isPlaying || isHovered) {
      return;
    }

    const interval = setInterval(nextSlide, autoPlayInterval);
    return () => clearInterval(interval);
  }, [isPlaying, isHovered, nextSlide, autoPlayInterval]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') {
        prevSlide();
      } else if (e.key === 'ArrowRight') {
        nextSlide();
      } else if (e.key === ' ') {
        e.preventDefault();
        togglePlayPause();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [nextSlide, prevSlide, togglePlayPause]);

  if (sliderItems.length === 0) {
    return null;
  }

  const _currentItem = sliderItems[currentSlide];

  return (
    <div
      className={cn(
        'relative w-full h-64 sm:h-80 md:h-96 lg:h-[500px] xl:h-[600px] overflow-hidden bg-gray-900',
        className,
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      role="region"
      aria-label={language === 'en' ? 'Image carousel' : 'Karusel imej'}
    >
      {/* Slides */}
      <div className="relative w-full h-full">
        {sliderItems.map((item, index) => (
          <div
            key={item.id}
            className={cn(
              'absolute inset-0 transition-opacity duration-500 ease-in-out',
              index === currentSlide ? 'opacity-100' : 'opacity-0',
            )}
          >
            <div className="relative w-full h-full">
              <Image
                src={item.image}
                alt={language === 'bm' ? item.titleBM : item.title}
                fill
                className="object-cover object-center"
                priority={index === 0}
                sizes="(max-width: 640px) 100vw, (max-width: 768px) 100vw, (max-width: 1024px) 100vw, 100vw"
                quality={90}
              />

              {/* Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-40" />

              {/* Content */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-white px-4 max-w-4xl">
                  <h2 className="text-2xl md:text-4xl lg:text-5xl font-bold mb-4">
                    {language === 'bm' ? item.titleBM : item.title}
                  </h2>
                  {item.description && (
                    <p className="text-lg md:text-xl mb-6 max-w-2xl mx-auto">
                      {language === 'bm'
                        ? item.descriptionBM
                        : item.description}
                    </p>
                  )}
                  {item.href && (
                    <div className="flex justify-center">
                      {item.isExternal ? (
                        <a
                          href={item.href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="btn-primary"
                        >
                          {language === 'en'
                            ? 'Learn More'
                            : 'Ketahui Lebih Lanjut'}
                        </a>
                      ) : (
                        <Link href={item.href} className="btn-primary">
                          {language === 'en'
                            ? 'Learn More'
                            : 'Ketahui Lebih Lanjut'}
                        </Link>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Navigation Controls */}
      {showControls && sliderItems.length > 1 && (
        <>
          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black"
            aria-label={
              language === 'en' ? 'Previous slide' : 'Slaid sebelumnya'
            }
          >
            <ChevronLeft className="w-6 h-6" />
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black"
            aria-label={language === 'en' ? 'Next slide' : 'Slaid seterusnya'}
          >
            <ChevronRight className="w-6 h-6" />
          </button>
        </>
      )}

      {/* Play/Pause Button */}
      {autoPlay && (
        <button
          onClick={togglePlayPause}
          className="absolute top-4 right-4 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black"
          aria-label={
            isPlaying
              ? language === 'en'
                ? 'Pause slideshow'
                : 'Jeda tayangan slaid'
              : language === 'en'
                ? 'Play slideshow'
                : 'Main tayangan slaid'
          }
        >
          {isPlaying ? (
            <Pause className="w-5 h-5" />
          ) : (
            <Play className="w-5 h-5" />
          )}
        </button>
      )}

      {/* Indicators */}
      {showIndicators && sliderItems.length > 1 && (
        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
          {sliderItems.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={cn(
                'w-3 h-3 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black',
                index === currentSlide
                  ? 'bg-white'
                  : 'bg-white bg-opacity-50 hover:bg-opacity-75',
              )}
              aria-label={`${language === 'en' ? 'Go to slide' : 'Pergi ke slaid'} ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Progress Bar */}
      {autoPlay && isPlaying && !isHovered && (
        <div className="absolute bottom-0 left-0 w-full h-1 bg-black bg-opacity-30">
          <div
            className="h-full bg-white transition-all duration-100 ease-linear"
            style={{
              width: `${((Date.now() % autoPlayInterval) / autoPlayInterval) * 100}%`,
            }}
          />
        </div>
      )}
    </div>
  );
}

// Compact carousel for smaller spaces
export function CompactCarousel({ className }: { className?: string }) {
  return (
    <HeroCarousel
      autoPlayInterval={4000}
      showControls={false}
      className={cn('h-48 md:h-64', className)}
    />
  );
}
