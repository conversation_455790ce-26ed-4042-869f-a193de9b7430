'use client';

export const runtime = 'edge';

import {
  Building,
  Edit3,
  Eye,
  Globe,
  Mail,
  MapPin,
  Phone,
  PlusCircle,
  Search,
  Trash2,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Link, useRouter } from '@/i18n/navigation';
import { api } from '@/lib/api';

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

interface Company {
  id: number;
  name: string;
  registrationNumber?: string;
  businessType?: string;
  category?: string;
  subcategory?: string;
  address?: string;
  state?: string;
  postcode?: string;
  city?: string;
  country?: string;
  phone?: string;
  email?: string;
  website?: string;
  contactPerson?: string;
  certificateNumber?: string;
  certificateType?: string;
  certificateStatus?: string;
  createdAt: string;
  updatedAt: string;
}

interface CompanyResponse {
  companies: Company[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
    totalPages: number;
  };
}

export default function CorporatePage() {
  const router = useRouter();
  const [companies, setCompanies] = useState<Company[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    hasMore: false,
    totalPages: 0,
  });

  useEffect(() => {
    fetchCompanies();
  }, [pagination.page, searchTerm]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchCompanies = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });

      if (searchTerm) {
        params.append('search', searchTerm);
      }

      const response = await api.get<CompanyResponse>(
        `/admin/companies?${params}`,
      );
      setCompanies(response.data.companies);
      setPagination(response.data.pagination);
    } catch (err) {
      console.error('Error fetching companies:', err);
      setError('Failed to load companies');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this company?')) {
      return;
    }

    try {
      await api.delete(`/admin/companies/${id}`);
      await fetchCompanies(); // Refresh the list
    } catch (err) {
      console.error('Error deleting company:', err);
      setError('Failed to delete company');
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchCompanies();
  };

  const getStatusColor = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'valid':
        return 'text-green-600 bg-green-50';
      case 'expired':
        return 'text-red-600 bg-red-50';
      case 'suspended':
        return 'text-yellow-600 bg-yellow-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  if (isLoading && companies.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">
            Corporate Management
          </h1>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading companies...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <Button variant="ghost" size="sm" onClick={() => setError(null)}>
              ×
            </Button>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Corporate Management
          </h1>
          <p className="text-gray-600 mt-2">
            Manage halal certified companies and corporate information
          </p>
        </div>
        <Button onClick={() => router.push('/admin/corporate/new')}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Add Company
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search Companies</CardTitle>
          <CardDescription>
            Find companies by name, registration number, or certificate number
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search companies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button type="submit">
              <Search className="mr-2 h-4 w-4" />
              Search
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Companies Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {companies.map((company) => (
          <Card key={company.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Building className="h-5 w-5 text-blue-600" />
                  <CardTitle className="text-lg truncate">
                    {company.name}
                  </CardTitle>
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      router.push(`/admin/corporate/${company.id}`)
                    }
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      router.push(`/admin/corporate/${company.id}/edit`)
                    }
                  >
                    <Edit3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDelete(company.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardDescription>
                {company.certificateStatus && (
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(company.certificateStatus)}`}
                  >
                    {company.certificateStatus}
                  </span>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                {company.registrationNumber && (
                  <div>
                    <span className="font-medium">Registration:</span>{' '}
                    <span className="text-gray-600">
                      {company.registrationNumber}
                    </span>
                  </div>
                )}
                {company.businessType && (
                  <div>
                    <span className="font-medium">Type:</span>{' '}
                    <span className="text-gray-600">
                      {company.businessType}
                    </span>
                  </div>
                )}
                {company.category && (
                  <div>
                    <span className="font-medium">Category:</span>{' '}
                    <span className="text-gray-600">{company.category}</span>
                  </div>
                )}
                {company.address && (
                  <div className="flex items-start gap-1">
                    <MapPin className="h-3 w-3 mt-0.5 text-gray-400" />
                    <span className="text-gray-600 text-xs">
                      {company.address}
                    </span>
                  </div>
                )}
                {company.phone && (
                  <div className="flex items-center gap-1">
                    <Phone className="h-3 w-3 text-gray-400" />
                    <span className="text-gray-600 text-xs">
                      {company.phone}
                    </span>
                  </div>
                )}
                {company.email && (
                  <div className="flex items-center gap-1">
                    <Mail className="h-3 w-3 text-gray-400" />
                    <span className="text-gray-600 text-xs">
                      {company.email}
                    </span>
                  </div>
                )}
                {company.website && (
                  <div className="flex items-center gap-1">
                    <Globe className="h-3 w-3 text-gray-400" />
                    <a
                      href={company.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-700 text-xs"
                    >
                      Website
                    </a>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}

        {companies.length === 0 && !isLoading && (
          <Card className="col-span-full">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Building className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Companies Found
              </h3>
              <p className="text-gray-600 text-center mb-4">
                {searchTerm
                  ? `No companies found matching "${searchTerm}"`
                  : 'Get started by adding your first company.'}
              </p>
              <Button onClick={() => router.push('/admin/corporate/new')}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Company
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <Card>
          <CardContent className="flex items-center justify-between py-4">
            <div className="text-sm text-gray-600">
              Showing {(pagination.page - 1) * pagination.limit + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)}{' '}
              of {pagination.total} companies
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setPagination((prev) => ({ ...prev, page: prev.page - 1 }))
                }
                disabled={pagination.page === 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
                }
                disabled={pagination.page === pagination.totalPages}
              >
                Next
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Building className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Total Companies
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {pagination.total}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Active Certificates
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {
                    companies.filter(
                      (c) =>
                        c.certificateStatus === 'active' ||
                        c.certificateStatus === 'valid',
                    ).length
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Globe className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  With Websites
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {companies.filter((c) => c.website).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Mail className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">With Email</p>
                <p className="text-2xl font-bold text-gray-900">
                  {companies.filter((c) => c.email).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
