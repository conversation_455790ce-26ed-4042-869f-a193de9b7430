const WebSocket = require('ws');

console.log('Testing WebSocket connection...');

const ws = new WebSocket('ws://localhost:16001/ws');

ws.on('open', function open() {
  console.log('✅ WebSocket connection opened successfully!');
  
  // Test registration as user
  console.log('📝 Registering as user...');
  ws.send(JSON.stringify({
    type: 'register',
    data: {
      connectionType: 'user',
      sessionId: 'test-session-123'
    }
  }));
  
  // Test ping
  setTimeout(() => {
    console.log('🏓 Sending ping...');
    ws.send(JSON.stringify({
      type: 'ping',
      data: { timestamp: new Date().toISOString() }
    }));
  }, 1000);
  
  // Test user message
  setTimeout(() => {
    console.log('💬 Sending user message...');
    ws.send(JSON.stringify({
      type: 'user_message',
      data: {
        sessionId: 'test-session-123',
        content: 'Hello from test client!',
        userId: 'test-user'
      }
    }));
  }, 2000);
  
  // Close connection after tests
  setTimeout(() => {
    console.log('👋 Closing connection...');
    ws.close();
  }, 3000);
});

ws.on('message', function message(data) {
  try {
    const message = JSON.parse(data);
    console.log('📨 Received message:', JSON.stringify(message, null, 2));
  } catch (error) {
    console.log('📨 Received raw message:', data.toString());
  }
});

ws.on('close', function close() {
  console.log('❌ WebSocket connection closed');
  process.exit(0);
});

ws.on('error', function error(err) {
  console.error('💥 WebSocket error:', err);
  process.exit(1);
});

// Timeout to prevent hanging
setTimeout(() => {
  console.log('⏰ Test timeout - closing connection');
  ws.close();
  process.exit(1);
}, 10000);
