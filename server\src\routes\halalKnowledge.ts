import express, { type Request, type Response } from 'express';
import HalalKnowledgeService from '../services/halalKnowledge';
import type { HalalKnowledgeRequest, HalalKnowledgeResponse } from '../types';

const router: express.Router = express.Router();

// Halal knowledge search and answer endpoint
router.post(
  '/ask',
  async (
    req: Request,
    res: Response<HalalKnowledgeResponse>,
  ): Promise<void> => {
    try {
      const {
        query,
        sessionId,
        maxResults = 5,
        minScore = 0.3,
        includeContext = true,
      }: HalalKnowledgeRequest = req.body;

      // Validate required fields
      if (!query || typeof query !== 'string' || query.trim().length === 0) {
        res.status(400).json({
          success: false,
          error: 'Query is required and must be a non-empty string',
          query: query || '',
          sessionId,
        });
        return;
      }

      // Check if the query is related to halal topics
      const halalKnowledgeService = new HalalKnowledgeService();
      if (!halalKnowledgeService.isHalalRelatedQuery(query)) {
        res.json({
          success: true,
          answer:
            "I specialize in answering questions about halal and Islamic matters. Your question doesn't seem to be related to halal topics. Please ask me about Islamic jurisprudence, halal food, Islamic practices, or other religious matters.",
          sources: [],
          query,
          sessionId,
        });
        return;
      }

      console.log('Processing halal knowledge request:', {
        query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
        sessionId,
        maxResults,
        minScore,
        includeContext,
      });

      // Process the request
      const response = await halalKnowledgeService.searchAndAnswer({
        query,
        sessionId,
        maxResults,
        minScore,
        includeContext,
      });

      console.log('Halal knowledge response:', {
        success: response.success,
        hasAnswer: !!response.answer,
        sourcesCount: response.sources?.length || 0,
        error: response.error,
      });

      res.json(response);
    } catch (error) {
      console.error('Halal knowledge endpoint error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        query: req.body?.query || '',
        sessionId: req.body?.sessionId,
      });
    }
  },
);

// Health check endpoint for halal knowledge service
router.get('/health', async (_req: Request, res: Response): Promise<void> => {
  try {
    // Test R2R connection
    const testQuery = 'halal';
    const halalKnowledgeService = new HalalKnowledgeService();
    const testResponse = await halalKnowledgeService.searchAndAnswer({
      query: testQuery,
      maxResults: 1,
      minScore: 0.1,
      includeContext: false,
    });

    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        r2r: testResponse.success ? 'connected' : 'error',
        openai: 'available',
      },
      message: 'Halal knowledge service is operational',
    });
  } catch (error) {
    console.error('Halal knowledge health check error:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Halal knowledge service is experiencing issues',
    });
  }
});

// Get information about the halal knowledge service
router.get('/info', (_req: Request, res: Response): void => {
  res.json({
    service: 'Halal Knowledge Search',
    description: 'AI-powered halal knowledge search using R2R and OpenAI',
    version: '1.0.0',
    capabilities: [
      'Search halal knowledge base',
      'Generate AI-powered answers',
      'Provide source citations',
      'Filter by relevance score',
      'Support for multiple Islamic topics',
    ],
    supportedTopics: [
      'Halal and Haram rulings',
      'Islamic jurisprudence (Fiqh)',
      'Food and dietary laws',
      'Business and finance',
      'Prayer and worship',
      'Marriage and family',
      'Clothing and modesty',
      'General Islamic guidance',
    ],
    endpoints: {
      ask: 'POST /halal-knowledge/ask - Ask a halal-related question',
      health: 'GET /halal-knowledge/health - Check service health',
      info: 'GET /halal-knowledge/info - Get service information',
    },
  });
});

export default router;
