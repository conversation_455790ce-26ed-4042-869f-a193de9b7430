'use client';

import { Info, Search, Zap } from 'lucide-react';
import { useState } from 'react';

export type SearchType = 'keyword' | 'semantic';

interface SearchTypeSelectorProps {
  searchType: SearchType;
  onSearchTypeChange: (type: SearchType) => void;
  className?: string;
}

export function SearchTypeSelector({
  searchType,
  onSearchTypeChange,
  className = '',
}: SearchTypeSelectorProps) {
  const [showTooltip, setShowTooltip] = useState<SearchType | null>(null);

  const searchTypes = [
    {
      id: 'keyword' as SearchType,
      label: 'Keyword Search',
      icon: Search,
      description: 'Traditional search using exact word matches',
      details:
        'Searches for exact matches in product names, company names, categories, and other fields.',
    },
    {
      id: 'semantic' as SearchType,
      label: 'Semantic Search',
      icon: Zap,
      description: 'AI-powered search that understands meaning and context',
      details:
        "Uses AI to understand the meaning of your search and find relevant products even if they don't contain the exact words.",
    },
  ];

  return (
    <div className={`relative ${className} flex`}>
      <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
        {searchTypes.map((type) => {
          const Icon = type.icon;
          const isActive = searchType === type.id;

          return (
            <div key={type.id} className="relative">
              <button
                type="button"
                onClick={() => onSearchTypeChange(type.id)}
                onMouseEnter={() => setShowTooltip(type.id)}
                onMouseLeave={() => setShowTooltip(null)}
                className={`
                  flex items-center px-3 py-2 rounded-md text-sm font-medium transition-all duration-200
                  ${
                    isActive
                      ? 'bg-white text-green-700 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }
                `}
              >
                <Icon className="w-4 h-4 mr-2" />
                {type.label}
                <Info className="w-3 h-3 ml-1 opacity-50" />
              </button>

              {/* Tooltip */}
              {showTooltip === type.id && (
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50">
                  <div className="bg-gray-900 text-white text-xs rounded-lg py-2 px-3 max-w-xs">
                    <div className="font-medium mb-1">{type.label}</div>
                    <div className="text-gray-300">{type.details}</div>
                    {/* Arrow */}
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2">
                      <div className="border-4 border-transparent border-t-gray-900" />
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Search type description */}
      <div className="mt-2 text-xs text-gray-600 flex-2 pl-2 items-center">
        {searchTypes.find((type) => type.id === searchType)?.description}
      </div>
    </div>
  );
}

// Enhanced search box with search type selector
interface EnhancedSearchBoxProps {
  placeholder?: string;
  onSearch: (query: string, searchType: SearchType) => void;
  onChange?: (value: string) => void;
  value?: string;
  defaultValue?: string;
  autoFocus?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  searchType?: SearchType;
  onSearchTypeChange?: (type: SearchType) => void;
  showSearchTypeSelector?: boolean;
}

export function EnhancedSearchBox({
  placeholder = 'Search products...',
  onSearch,
  onChange,
  value,
  defaultValue = '',
  autoFocus = false,
  size = 'md',
  className = '',
  searchType = 'keyword',
  onSearchTypeChange,
  showSearchTypeSelector = true,
}: EnhancedSearchBoxProps) {
  const [internalQuery, setInternalQuery] = useState(defaultValue);
  const [internalSearchType, setInternalSearchType] =
    useState<SearchType>(searchType);
  const [isLoading, setIsLoading] = useState(false);

  // Use controlled values if provided, otherwise use internal state
  const query = value !== undefined ? value : internalQuery;
  const currentSearchType = searchType;

  const sizeClasses = {
    sm: 'h-8 text-sm',
    md: 'h-10 text-base',
    lg: 'h-12 text-lg',
  };

  const handleSearch = async () => {
    setIsLoading(true);
    try {
      onSearch(query, currentSearchType);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSearch();
  };

  const handleSearchTypeChange = (type: SearchType) => {
    if (onSearchTypeChange) {
      onSearchTypeChange(type);
    } else {
      setInternalSearchType(type);
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Search Input */}
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <input
            type="text"
            value={query}
            onChange={(e) => {
              const newValue = e.target.value;
              if (value === undefined) {
                setInternalQuery(newValue);
              } else if (onChange) {
                onChange(newValue);
              }
            }}
            placeholder={placeholder}
            autoFocus={autoFocus}
            disabled={isLoading}
            className={`
              block w-full pl-4 pr-12 border border-gray-300 rounded-lg
              focus:ring-2 focus:ring-green-500 focus:border-green-500
              placeholder-gray-500 bg-white disabled:bg-gray-50
              ${sizeClasses[size]}
            `}
          />

          {/* Search button */}
          <button
            type="submit"
            disabled={isLoading || !query.trim()}
            className={`
              absolute inset-y-0 right-0 flex items-center pr-3
              text-green-600 hover:text-green-700 disabled:text-gray-400
              disabled:cursor-not-allowed
            `}
          >
            {currentSearchType === 'semantic' ? (
              <Zap className="w-5 h-5" />
            ) : (
              <Search className="w-5 h-5" />
            )}
          </button>
        </div>
      </form>

      {/* Search Type Selector */}
      {showSearchTypeSelector && (
        <SearchTypeSelector
          searchType={currentSearchType}
          onSearchTypeChange={handleSearchTypeChange}
          className="flex justify-center"
        />
      )}
    </div>
  );
}
