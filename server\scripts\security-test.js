#!/usr/bin/env node

/**
 * Security Testing Script for Halal Chatbot
 *
 * This script tests various security aspects of the chatbot API
 * including rate limiting, input validation, authentication, and more.
 */

const https = require('https');
const http = require('http');

// Configuration
const config = {
  baseUrl: process.env.TEST_BASE_URL || 'http://localhost:16001',
  timeout: 5000,
  maxRetries: 3,
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// HTTP request helper
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const client = options.protocol === 'https:' ? https : http;

    const req = client.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => (body += chunk));
      res.on('end', () => {
        try {
          const parsedBody = body ? JSON.parse(body) : {};
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: parsedBody,
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body,
          });
        }
      });
    });

    req.on('error', reject);
    req.setTimeout(config.timeout, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (data) {
      req.write(typeof data === 'string' ? data : JSON.stringify(data));
    }

    req.end();
  });
}

// Test functions
async function testRateLimiting() {
  log('\n🔄 Testing Rate Limiting...', 'blue');

  const url = new URL('/api/chat/session', config.baseUrl);
  const options = {
    hostname: url.hostname,
    port: url.port,
    path: url.pathname,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  };

  let successCount = 0;
  let rateLimitedCount = 0;
  const totalRequests = 70; // Should trigger rate limit at 60

  log(`Sending ${totalRequests} requests to test rate limiting...`);

  for (let i = 0; i < totalRequests; i++) {
    try {
      const response = await makeRequest(options, {});
      if (response.statusCode === 200) {
        successCount++;
      } else if (response.statusCode === 429) {
        rateLimitedCount++;
        log(`✅ Rate limit triggered at request ${i + 1}`, 'green');
        break;
      }
    } catch (error) {
      log(`❌ Request ${i + 1} failed: ${error.message}`, 'red');
    }
  }

  log(`Results: ${successCount} successful, ${rateLimitedCount} rate limited`);

  if (rateLimitedCount > 0) {
    log('✅ Rate limiting is working correctly', 'green');
  } else {
    log('⚠️  Rate limiting may not be working as expected', 'yellow');
  }
}

async function testInputValidation() {
  log('\n🔍 Testing Input Validation...', 'blue');

  // Create a session first
  const sessionResponse = await makeRequest(
    {
      hostname: new URL(config.baseUrl).hostname,
      port: new URL(config.baseUrl).port,
      path: '/api/chat/session',
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    },
    {},
  );

  if (sessionResponse.statusCode !== 200) {
    log('❌ Failed to create session for input validation tests', 'red');
    return;
  }

  const sessionId = sessionResponse.body.sessionId;
  log(`Created session: ${sessionId}`);

  const testCases = [
    {
      name: 'Empty message',
      data: { sessionId, message: '' },
      expectedStatus: 400,
    },
    {
      name: 'Very long message (>4000 chars)',
      data: { sessionId, message: 'A'.repeat(4001) },
      expectedStatus: 400,
    },
    {
      name: 'Invalid session ID',
      data: { sessionId: 'invalid-id', message: 'Hello' },
      expectedStatus: 400,
    },
    {
      name: 'Missing session ID',
      data: { message: 'Hello' },
      expectedStatus: 400,
    },
    {
      name: 'XSS attempt',
      data: { sessionId, message: '<script>alert("xss")</script>' },
      expectedStatus: 200, // Should be sanitized, not rejected
    },
    {
      name: 'SQL injection attempt',
      data: { sessionId, message: "'; DROP TABLE users; --" },
      expectedStatus: 200, // Should be handled safely
    },
  ];

  for (const testCase of testCases) {
    try {
      const response = await makeRequest(
        {
          hostname: new URL(config.baseUrl).hostname,
          port: new URL(config.baseUrl).port,
          path: '/api/chat/message',
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
        },
        testCase.data,
      );

      if (response.statusCode === testCase.expectedStatus) {
        log(
          `✅ ${testCase.name}: Handled correctly (${response.statusCode})`,
          'green',
        );
      } else {
        log(
          `⚠️  ${testCase.name}: Unexpected status ${response.statusCode} (expected ${testCase.expectedStatus})`,
          'yellow',
        );
      }
    } catch (error) {
      log(`❌ ${testCase.name}: Request failed - ${error.message}`, 'red');
    }
  }
}

async function testSecurityHeaders() {
  log('\n🛡️  Testing Security Headers...', 'blue');

  try {
    const response = await makeRequest({
      hostname: new URL(config.baseUrl).hostname,
      port: new URL(config.baseUrl).port,
      path: '/health',
      method: 'GET',
    });

    const headers = response.headers;
    const requiredHeaders = [
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection',
      'referrer-policy',
    ];

    let headerCount = 0;
    for (const header of requiredHeaders) {
      if (headers[header]) {
        log(`✅ ${header}: ${headers[header]}`, 'green');
        headerCount++;
      } else {
        log(`❌ Missing header: ${header}`, 'red');
      }
    }

    // Check HSTS for HTTPS
    if (config.baseUrl.startsWith('https')) {
      if (headers['strict-transport-security']) {
        log(
          `✅ strict-transport-security: ${headers['strict-transport-security']}`,
          'green',
        );
        headerCount++;
      } else {
        log('❌ Missing HSTS header for HTTPS', 'red');
      }
    }

    log(
      `Security headers: ${headerCount}/${requiredHeaders.length + (config.baseUrl.startsWith('https') ? 1 : 0)} present`,
    );
  } catch (error) {
    log(`❌ Failed to test security headers: ${error.message}`, 'red');
  }
}

async function testImageValidation() {
  log('\n🖼️  Testing Image Validation...', 'blue');

  // Create a session first
  const sessionResponse = await makeRequest(
    {
      hostname: new URL(config.baseUrl).hostname,
      port: new URL(config.baseUrl).port,
      path: '/api/chat/session',
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    },
    {},
  );

  if (sessionResponse.statusCode !== 200) {
    log('❌ Failed to create session for image validation tests', 'red');
    return;
  }

  const sessionId = sessionResponse.body.sessionId;

  const testCases = [
    {
      name: 'Invalid URL',
      data: { sessionId, imageUrl: 'not-a-url' },
      expectedStatus: 400,
    },
    {
      name: 'Non-image URL',
      data: { sessionId, imageUrl: 'https://example.com/document.pdf' },
      expectedStatus: 400,
    },
    {
      name: 'Valid image URL',
      data: { sessionId, imageUrl: 'https://example.com/image.jpg' },
      expectedStatus: 200, // May fail due to OpenAI, but validation should pass
    },
  ];

  for (const testCase of testCases) {
    try {
      const response = await makeRequest(
        {
          hostname: new URL(config.baseUrl).hostname,
          port: new URL(config.baseUrl).port,
          path: '/api/chat/image',
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
        },
        testCase.data,
      );

      if (
        response.statusCode === testCase.expectedStatus ||
        (testCase.expectedStatus === 200 && response.statusCode === 500)
      ) {
        log(
          `✅ ${testCase.name}: Validation working (${response.statusCode})`,
          'green',
        );
      } else {
        log(
          `⚠️  ${testCase.name}: Unexpected status ${response.statusCode}`,
          'yellow',
        );
      }
    } catch (error) {
      log(`❌ ${testCase.name}: Request failed - ${error.message}`, 'red');
    }
  }
}

async function testSessionSecurity() {
  log('\n🔐 Testing Session Security...', 'blue');

  // Test session creation
  const sessionResponse = await makeRequest(
    {
      hostname: new URL(config.baseUrl).hostname,
      port: new URL(config.baseUrl).port,
      path: '/api/chat/session',
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    },
    {},
  );

  if (sessionResponse.statusCode === 200) {
    const sessionId = sessionResponse.body.sessionId;
    log(`✅ Session created: ${sessionId}`, 'green');

    // Validate session ID format (UUID v4)
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(sessionId)) {
      log('✅ Session ID format is valid UUID v4', 'green');
    } else {
      log('❌ Session ID format is not UUID v4', 'red');
    }

    // Test session retrieval
    const getResponse = await makeRequest({
      hostname: new URL(config.baseUrl).hostname,
      port: new URL(config.baseUrl).port,
      path: `/api/chat/session/${sessionId}`,
      method: 'GET',
    });

    if (getResponse.statusCode === 200) {
      log('✅ Session retrieval working', 'green');
    } else {
      log(`❌ Session retrieval failed: ${getResponse.statusCode}`, 'red');
    }
  } else {
    log(`❌ Session creation failed: ${sessionResponse.statusCode}`, 'red');
  }
}

// Main test runner
async function runSecurityTests() {
  log('🔒 Starting Security Tests for Halal Chatbot', 'magenta');
  log(`Testing against: ${config.baseUrl}`, 'blue');

  try {
    await testSecurityHeaders();
    await testSessionSecurity();
    await testInputValidation();
    await testImageValidation();
    await testRateLimiting();

    log('\n✅ Security testing completed!', 'green');
    log('Review the results above and address any issues found.', 'blue');
  } catch (error) {
    log(`\n❌ Security testing failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run tests if called directly
if (require.main === module) {
  runSecurityTests().catch((error) => {
    log(`Fatal error: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = {
  runSecurityTests,
  testRateLimiting,
  testInputValidation,
  testSecurityHeaders,
  testImageValidation,
  testSessionSecurity,
};
