# Design System - Halal Malaysia Portal

## Color Palette

### Primary Colors
```css
/* Government Green - Primary brand color */
--primary-green: #2d5a27;
--primary-green-light: #4a7c59;
--primary-green-dark: #1a3d1a;

/* Islamic Green - Secondary brand color */
--islamic-green: #008000;
--islamic-green-light: #33a033;
--islamic-green-dark: #006600;

/* White - Background and text */
--white: #ffffff;
--off-white: #fafafa;

/* Text Colors */
--text-primary: #333333;
--text-secondary: #666666;
--text-muted: #999999;
```

### Secondary Colors
```css
/* Blue - Links and interactive elements */
--link-blue: #0066cc;
--link-blue-hover: #0052a3;

/* Gray Scale */
--gray-50: #f9fafb;
--gray-100: #f3f4f6;
--gray-200: #e5e7eb;
--gray-300: #d1d5db;
--gray-400: #9ca3af;
--gray-500: #6b7280;
--gray-600: #4b5563;
--gray-700: #374151;
--gray-800: #1f2937;
--gray-900: #111827;
```

### Accent Colors
```css
/* Status Colors */
--success: #10b981;
--warning: #f59e0b;
--error: #ef4444;
--info: #3b82f6;

/* Background Variants */
--bg-light-green: #f0f9f0;
--bg-light-blue: #f0f8ff;
--border-light: #e2e8f0;
```

## Typography

### Font Families
```css
/* Primary Font Stack */
--font-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

/* Headings Font */
--font-headings: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

/* Monospace for code/data */
--font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;
```

### Font Sizes
```css
/* Heading Sizes */
--text-xs: 0.75rem;     /* 12px */
--text-sm: 0.875rem;    /* 14px */
--text-base: 1rem;      /* 16px */
--text-lg: 1.125rem;    /* 18px */
--text-xl: 1.25rem;     /* 20px */
--text-2xl: 1.5rem;     /* 24px */
--text-3xl: 1.875rem;   /* 30px */
--text-4xl: 2.25rem;    /* 36px */
--text-5xl: 3rem;       /* 48px */
```

### Font Weights
```css
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
--font-extrabold: 800;
```

### Line Heights
```css
--leading-tight: 1.25;
--leading-snug: 1.375;
--leading-normal: 1.5;
--leading-relaxed: 1.625;
--leading-loose: 2;
```

## Spacing System

### Spacing Scale
```css
--space-1: 0.25rem;   /* 4px */
--space-2: 0.5rem;    /* 8px */
--space-3: 0.75rem;   /* 12px */
--space-4: 1rem;      /* 16px */
--space-5: 1.25rem;   /* 20px */
--space-6: 1.5rem;    /* 24px */
--space-8: 2rem;      /* 32px */
--space-10: 2.5rem;   /* 40px */
--space-12: 3rem;     /* 48px */
--space-16: 4rem;     /* 64px */
--space-20: 5rem;     /* 80px */
--space-24: 6rem;     /* 96px */
```

## Component Styles

### Buttons
```css
/* Primary Button */
.btn-primary {
  background-color: var(--primary-green);
  color: var(--white);
  padding: var(--space-3) var(--space-6);
  border-radius: 0.375rem;
  font-weight: var(--font-medium);
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background-color: var(--primary-green-dark);
  transform: translateY(-1px);
}

/* Secondary Button */
.btn-secondary {
  background-color: transparent;
  color: var(--primary-green);
  border: 2px solid var(--primary-green);
  padding: var(--space-3) var(--space-6);
  border-radius: 0.375rem;
  font-weight: var(--font-medium);
}
```

### Cards
```css
.card {
  background-color: var(--white);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: var(--space-6);
  border: 1px solid var(--border-light);
}

.card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
  transition: all 0.2s ease;
}
```

### Navigation
```css
.nav-link {
  color: var(--text-primary);
  font-weight: var(--font-medium);
  padding: var(--space-2) var(--space-4);
  text-decoration: none;
  transition: color 0.2s ease;
}

.nav-link:hover {
  color: var(--primary-green);
}

.nav-link.active {
  color: var(--primary-green);
  border-bottom: 2px solid var(--primary-green);
}
```

## Layout System

### Container Sizes
```css
--container-sm: 640px;
--container-md: 768px;
--container-lg: 1024px;
--container-xl: 1280px;
--container-2xl: 1536px;
```

### Grid System
```css
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }
```

## Responsive Breakpoints

```css
/* Mobile First Approach */
@media (min-width: 640px) { /* sm */ }
@media (min-width: 768px) { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
@media (min-width: 1536px) { /* 2xl */ }
```

## Animation & Transitions

### Standard Transitions
```css
--transition-fast: 0.15s ease;
--transition-normal: 0.2s ease;
--transition-slow: 0.3s ease;

/* Common Animations */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
```

## Accessibility

### Focus States
```css
.focus-visible {
  outline: 2px solid var(--primary-green);
  outline-offset: 2px;
}
```

### High Contrast Support
```css
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --primary-green: #004d00;
    --border-light: #666666;
  }
}
```

## Usage Guidelines

1. **Colors**: Use primary green for main actions, secondary colors for supporting elements
2. **Typography**: Maintain consistent hierarchy with defined font sizes
3. **Spacing**: Use the spacing scale for consistent layouts
4. **Components**: Follow established patterns for buttons, cards, and navigation
5. **Responsive**: Design mobile-first, enhance for larger screens
6. **Accessibility**: Ensure sufficient contrast and focus states
