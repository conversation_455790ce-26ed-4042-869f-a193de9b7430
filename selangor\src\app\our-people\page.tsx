import { Suspense } from 'react';
import { fetchApiData } from '@/lib/api-client';

export const runtime = 'edge';

async function OurPeopleContent() {
  try {
    const data = await fetchApiData(
      `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:16010'}/api/people`,
      { cache: 'no-store' },
      { people: [] }
    );

    const people = data?.people || [];

    return (
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-green-600 to-green-800 text-white">
          <div className="container mx-auto px-4 py-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Our People</h1>
            <p className="text-xl text-green-100 max-w-2xl">
              Meet the dedicated team behind Halal International Selangor
            </p>
          </div>
        </div>

        {/* People Grid */}
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-6xl mx-auto">
            {people.length > 0 ? (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {people.map(
                  (person: {
                    id: number;
                    name: string;
                    role?: string;
                    bio?: string;
                    slug: string;
                  }) => (
                    <div
                      key={person.id}
                      className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
                    >
                      <div className="p-6">
                        <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                          <span className="text-white text-2xl font-bold">
                            {person.name
                              .split(' ')
                              .map((n) => n[0])
                              .join('')
                              .slice(0, 2)}
                          </span>
                        </div>
                        <h3 className="text-xl font-bold mb-2 text-gray-800 text-center">
                          {person.name}
                        </h3>
                        {person.role && (
                          <p className="text-green-600 font-medium text-center mb-3">
                            {person.role}
                          </p>
                        )}
                        {person.bio && (
                          <p className="text-gray-600 mb-4 text-sm leading-relaxed text-center">
                            {person.bio}
                          </p>
                        )}
                      </div>
                    </div>
                  ),
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">👥</div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">
                  Meet Our Team
                </h3>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Our dedicated team of professionals is committed to
                  maintaining the highest standards of Halal certification and
                  ensuring the trust of our community.
                </p>
              </div>
            )}

            {/* Leadership Section */}
            <div className="mt-16 bg-white rounded-lg shadow-lg p-8">
              <h2 className="text-3xl font-bold text-center mb-8">
                Leadership Team
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div className="text-center">
                  <div className="w-32 h-32 bg-gradient-to-br from-green-400 to-green-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span className="text-white text-4xl font-bold">CEO</span>
                  </div>
                  <h3 className="text-xl font-bold mb-2">
                    Chief Executive Officer
                  </h3>
                  <p className="text-gray-600">
                    Leading the organization with vision and commitment to
                    excellence in Halal certification.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-32 h-32 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span className="text-white text-4xl font-bold">CTO</span>
                  </div>
                  <h3 className="text-xl font-bold mb-2">
                    Chief Technical Officer
                  </h3>
                  <p className="text-gray-600">
                    Overseeing technical operations and ensuring compliance with
                    international standards.
                  </p>
                </div>
              </div>
            </div>

            {/* Departments */}
            <div className="mt-12 grid md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-lg p-6 text-center">
                <div className="text-green-600 text-4xl mb-4">🔬</div>
                <h3 className="text-xl font-bold mb-2">Certification Team</h3>
                <p className="text-gray-600 text-sm">
                  Our experts ensure rigorous compliance with Halal standards
                </p>
              </div>

              <div className="bg-white rounded-lg shadow-lg p-6 text-center">
                <div className="text-green-600 text-4xl mb-4">📋</div>
                <h3 className="text-xl font-bold mb-2">Audit Department</h3>
                <p className="text-gray-600 text-sm">
                  Conducting thorough inspections and maintaining quality
                  assurance
                </p>
              </div>

              <div className="bg-white rounded-lg shadow-lg p-6 text-center">
                <div className="text-green-600 text-4xl mb-4">🤝</div>
                <h3 className="text-xl font-bold mb-2">Customer Service</h3>
                <p className="text-gray-600 text-sm">
                  Providing support and guidance to our valued clients
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  } catch {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">Our People</h1>
          <p className="text-gray-600">Loading team information...</p>
        </div>
      </div>
    );
  }
}

export default function OurPeoplePage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600" />
        </div>
      }
    >
      <OurPeopleContent />
    </Suspense>
  );
}
