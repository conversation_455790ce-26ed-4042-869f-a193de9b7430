'use client';

export const runtime = 'edge';

import { Calendar, ExternalLink, Eye } from 'lucide-react';
import type { BreadcrumbItem } from '@/components/breadcrumb';
import { PageWrapper } from '@/components/page-wrapper';
import { useLanguage } from '@/lib/language-context';

export default function PressPage() {
  const { language } = useLanguage();

  const breadcrumbs: BreadcrumbItem[] = [
    {
      label: language === 'en' ? 'Home' : 'Laman <PERSON>',
      href: '/',
    },
    {
      label: language === 'en' ? 'Press Statement' : 'Kenyataan Akhbar',
      href: '/press',
    },
  ];

  const pressReleases = [
    {
      id: 'PR-001-2024',
      title: 'JAKIM Strengthens International Halal Certification Cooperation',
      titleBM: 'JAKIM Kukuhkan Kerjasama Pensijilan Halal Antarabangsa',
      date: '2024-03-15',
      category: 'International Cooperation',
      categoryBM: 'Kerjasama Antarabangsa',
      excerpt:
        'JAKIM signs new mutual recognition agreements with three countries to enhance global Halal trade.',
      excerptBM:
        'JAKIM menandatangani perjanjian pengiktirafan bersama baru dengan tiga negara untuk meningkatkan perdagangan Halal global.',
      content: 'Full press release content...',
      contentBM: 'Kandungan penuh siaran akhbar...',
      featured: true,
      views: 1250,
    },
    {
      id: 'PR-002-2024',
      title: 'New Guidelines for Pharmaceutical Halal Certification Released',
      titleBM: 'Garis Panduan Baru Pensijilan Halal Farmaseutikal Dikeluarkan',
      date: '2024-03-10',
      category: 'Policy Update',
      categoryBM: 'Kemas Kini Dasar',
      excerpt:
        'Updated guidelines aim to streamline the certification process for pharmaceutical products.',
      excerptBM:
        'Garis panduan terkini bertujuan untuk memperlancar proses pensijilan produk farmaseutikal.',
      content: 'Full press release content...',
      contentBM: 'Kandungan penuh siaran akhbar...',
      featured: true,
      views: 980,
    },
    {
      id: 'PR-003-2024',
      title: 'JAKIM Launches Digital Halal Verification System',
      titleBM: 'JAKIM Melancarkan Sistem Pengesahan Halal Digital',
      date: '2024-02-28',
      category: 'Technology',
      categoryBM: 'Teknologi',
      excerpt:
        'New digital system allows consumers to verify Halal status instantly using QR codes.',
      excerptBM:
        'Sistem digital baru membolehkan pengguna mengesahkan status Halal secara serta-merta menggunakan kod QR.',
      content: 'Full press release content...',
      contentBM: 'Kandungan penuh siaran akhbar...',
      featured: false,
      views: 1450,
    },
    {
      id: 'PR-004-2024',
      title: 'Record Number of Halal Certificates Issued in 2023',
      titleBM: 'Rekod Bilangan Sijil Halal Dikeluarkan pada 2023',
      date: '2024-01-20',
      category: 'Statistics',
      categoryBM: 'Statistik',
      excerpt:
        'JAKIM issued over 5,000 new Halal certificates in 2023, marking a 15% increase from the previous year.',
      excerptBM:
        'JAKIM mengeluarkan lebih 5,000 sijil Halal baru pada 2023, menandakan peningkatan 15% dari tahun sebelumnya.',
      content: 'Full press release content...',
      contentBM: 'Kandungan penuh siaran akhbar...',
      featured: false,
      views: 750,
    },
    {
      id: 'PR-005-2023',
      title: 'JAKIM Participates in World Halal Summit 2023',
      titleBM: 'JAKIM Menyertai Sidang Kemuncak Halal Dunia 2023',
      date: '2023-12-15',
      category: 'Events',
      categoryBM: 'Acara',
      excerpt:
        'JAKIM showcases Malaysian Halal standards and innovations at the international summit.',
      excerptBM:
        'JAKIM mempamerkan piawaian dan inovasi Halal Malaysia di sidang kemuncak antarabangsa.',
      content: 'Full press release content...',
      contentBM: 'Kandungan penuh siaran akhbar...',
      featured: false,
      views: 620,
    },
  ];

  const categories = [
    { id: 'all', label: 'All Categories', labelBM: 'Semua Kategori' },
    { id: 'policy', label: 'Policy Update', labelBM: 'Kemas Kini Dasar' },
    {
      id: 'international',
      label: 'International Cooperation',
      labelBM: 'Kerjasama Antarabangsa',
    },
    { id: 'technology', label: 'Technology', labelBM: 'Teknologi' },
    { id: 'statistics', label: 'Statistics', labelBM: 'Statistik' },
    { id: 'events', label: 'Events', labelBM: 'Acara' },
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'en' ? 'en-US' : 'ms-MY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <PageWrapper
      title="Press Statement"
      titleBM="Kenyataan Akhbar"
      description="Latest press releases and statements from JAKIM regarding Halal certification developments and announcements."
      descriptionBM="Siaran akhbar dan kenyataan terkini daripada JAKIM mengenai perkembangan pensijilan Halal dan pengumuman."
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-8">
        {/* Introduction */}
        <div className="card">
          <h2 className="text-2xl font-bold mb-4 text-gray-900">
            {language === 'en'
              ? 'Press Releases & Statements'
              : 'Siaran Akhbar & Kenyataan'}
          </h2>
          <p className="text-gray-600 leading-relaxed">
            {language === 'en'
              ? 'Stay informed with the latest news, announcements, and developments from JAKIM. Our press releases provide official information about policy updates, international cooperation, technological advancements, and important milestones in Halal certification.'
              : 'Kekal termaklum dengan berita, pengumuman, dan perkembangan terkini daripada JAKIM. Siaran akhbar kami menyediakan maklumat rasmi mengenai kemas kini dasar, kerjasama antarabangsa, kemajuan teknologi, dan pencapaian penting dalam pensijilan Halal.'}
          </p>
        </div>

        {/* Filter Categories */}
        <div className="card">
          <h3 className="text-lg font-semibold mb-4 text-gray-900">
            {language === 'en'
              ? 'Filter by Category'
              : 'Tapis mengikut Kategori'}
          </h3>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category.id}
                className="px-4 py-2 text-sm border border-gray-300 rounded-lg hover:border-primary-green hover:bg-bg-light-green transition-colors"
              >
                {language === 'bm' ? category.labelBM : category.label}
              </button>
            ))}
          </div>
        </div>

        {/* Featured Press Releases */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Latest Press Releases'
              : 'Siaran Akhbar Terkini'}
          </h3>
          <div className="space-y-6">
            {pressReleases
              .filter((release) => release.featured)
              .map((release) => (
                <article
                  key={release.id}
                  className="p-6 border border-gray-200 rounded-lg hover:border-primary-green hover:shadow-md transition-all"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-green text-white">
                        {language === 'en' ? 'Featured' : 'Pilihan'}
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {language === 'bm'
                          ? release.categoryBM
                          : release.category}
                      </span>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(release.date)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Eye className="w-4 h-4" />
                        <span>{release.views.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-3">
                    {language === 'bm' ? release.titleBM : release.title}
                  </h4>
                  <p className="text-gray-600 leading-relaxed mb-4">
                    {language === 'bm' ? release.excerptBM : release.excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      {language === 'en'
                        ? 'Press Release ID:'
                        : 'ID Siaran Akhbar:'}{' '}
                      {release.id}
                    </span>
                    <button className="inline-flex items-center gap-2 px-4 py-2 text-primary-green border border-primary-green rounded-lg hover:bg-primary-green hover:text-white transition-colors">
                      {language === 'en' ? 'Read More' : 'Baca Lagi'}
                      <ExternalLink className="w-4 h-4" />
                    </button>
                  </div>
                </article>
              ))}
          </div>
        </div>

        {/* All Press Releases */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'All Press Releases' : 'Semua Siaran Akhbar'}
          </h3>
          <div className="space-y-4">
            {pressReleases.map((release) => (
              <div
                key={release.id}
                className="p-4 border border-gray-200 rounded-lg hover:border-primary-green transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <span className="text-sm font-mono text-primary-green">
                        {release.id}
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {language === 'bm'
                          ? release.categoryBM
                          : release.category}
                      </span>
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <Calendar className="w-3 h-3" />
                        <span>{formatDate(release.date)}</span>
                      </div>
                    </div>
                    <h5 className="font-semibold text-gray-900 mb-2">
                      {language === 'bm' ? release.titleBM : release.title}
                    </h5>
                    <p className="text-sm text-gray-600 mb-2">
                      {language === 'bm' ? release.excerptBM : release.excerpt}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <Eye className="w-3 h-3" />
                        <span>
                          {release.views.toLocaleString()}{' '}
                          {language === 'en' ? 'views' : 'tontonan'}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="ml-4">
                    <button className="inline-flex items-center gap-1 text-primary-green hover:text-primary-green-dark text-sm">
                      {language === 'en' ? 'Read' : 'Baca'}
                      <ExternalLink className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Media Contact */}
        <div className="card bg-bg-light-green">
          <h3 className="text-xl font-bold mb-4 text-gray-900">
            {language === 'en' ? 'Media Contact' : 'Hubungan Media'}
          </h3>
          <p className="text-gray-600 mb-4">
            {language === 'en'
              ? 'For media inquiries, interview requests, or additional information about our press releases, please contact our Public Relations team.'
              : 'Untuk pertanyaan media, permintaan temu bual, atau maklumat tambahan mengenai siaran akhbar kami, sila hubungi pasukan Perhubungan Awam kami.'}
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en'
                  ? 'Public Relations Officer'
                  : 'Pegawai Perhubungan Awam'}
              </h4>
              <div className="space-y-1 text-sm text-gray-600">
                <p>
                  <span className="font-medium">
                    {language === 'en' ? 'Phone:' : 'Telefon:'}
                  </span>{' '}
                  03-8892 5000
                </p>
                <p>
                  <span className="font-medium">
                    {language === 'en' ? 'Email:' : 'E-mel:'}
                  </span>{' '}
                  <EMAIL>
                </p>
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en' ? 'Office Hours' : 'Waktu Pejabat'}
              </h4>
              <div className="space-y-1 text-sm text-gray-600">
                <p>
                  <span className="font-medium">
                    {language === 'en'
                      ? 'Monday - Thursday:'
                      : 'Isnin - Khamis:'}
                  </span>{' '}
                  8:30 AM - 4:30 PM
                </p>
                <p>
                  <span className="font-medium">
                    {language === 'en' ? 'Friday:' : 'Jumaat:'}
                  </span>{' '}
                  8:30 AM - 4:30 PM
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Newsletter Subscription */}
        <div className="card border-l-4 border-primary-green bg-green-50">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg
                className="w-5 h-5 text-primary-green mt-0.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
            </div>
            <div>
              <h4 className="font-semibold text-primary-green mb-2">
                {language === 'en' ? 'Stay Updated' : 'Kekal Terkini'}
              </h4>
              <p className="text-green-700 text-sm">
                {language === 'en'
                  ? 'Subscribe to our press release notifications to receive the latest news and announcements directly in your inbox.'
                  : 'Langgan pemberitahuan siaran akhbar kami untuk menerima berita dan pengumuman terkini terus ke peti mel anda.'}
              </p>
            </div>
          </div>
        </div>

        {/* Archive Notice */}
        <div className="card">
          <h3 className="text-lg font-semibold mb-3 text-gray-900">
            {language === 'en' ? 'Archive' : 'Arkib'}
          </h3>
          <p className="text-gray-600 text-sm">
            {language === 'en'
              ? 'Looking for older press releases? Visit our archive section or contact our media team for historical press materials.'
              : 'Mencari siaran akhbar yang lebih lama? Lawati bahagian arkib kami atau hubungi pasukan media kami untuk bahan akhbar bersejarah.'}
          </p>
        </div>
      </div>
    </PageWrapper>
  );
}
