import * as schema from './schema';

// Check if we're in a build environment
const isBuildTime =
  process.env.NODE_ENV === 'production' && !process.env.DATABASE_URL;

let db: any;

if (isBuildTime) {
  // Mock database for build time only
  const mockDb = {
    select: () => ({
      from: () => ({
        where: () => Promise.resolve([]),
        limit: () => Promise.resolve([]),
      }),
    }),
    insert: () => ({
      into: () => ({
        values: () => Promise.resolve({ insertId: 1 }),
      }),
    }),
    update: () => ({
      set: () => ({
        where: () => Promise.resolve({ affectedRows: 1 }),
      }),
    }),
    delete: () => ({
      from: () => ({
        where: () => Promise.resolve({ affectedRows: 1 }),
      }),
    }),
  };
  db = mockDb;
} else {
  // Real database for both Node.js and edge runtime
  const { drizzle } = require('drizzle-orm/postgres-js');
  const postgres = require('postgres');

  const connectionString =
    process.env.DATABASE_URL ||
    'postgresql://root:000000@localhost:5432/halal_chat';

  // Frontend-optimized connection settings
  const isProduction = process.env.NODE_ENV === 'production';
  const client = postgres(connectionString, {
    prepare: false,
    max: isProduction ? 5 : 3, // Slightly higher for production frontend
    idle_timeout: 15, // Faster cleanup for frontend
    max_lifetime: 60 * 20, // 20 minutes max lifetime for frontend
    connect_timeout: 8, // Reasonable timeout for frontend
    debug: false,
    // Frontend-specific options
    connection: {
      application_name: 'halal-selangor-frontend',
    },
    transform: {
      undefined: null,
    },
  });
  db = drizzle(client, { schema });
}

export { db };

// Export schema for use in other files
export * from './schema';
