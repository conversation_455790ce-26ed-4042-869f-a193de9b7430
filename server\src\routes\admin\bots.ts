import { Hono } from 'hono';
import {
  createBot,
  deleteBot,
  getBot,
  getBots,
  updateBot,
} from '@/controllers/adminBotsController';
import { authenticateAdmin } from '@/middleware/auth';

const botsRouter = new Hono();

// Apply authentication middleware to all routes
botsRouter.use('*', authenticateAdmin);

botsRouter.post('/', createBot);
botsRouter.get('/', getBots);
botsRouter.get('/:id', getBot);
botsRouter.put('/:id', updateBot);
botsRouter.delete('/:id', deleteBot);

export default botsRouter;
