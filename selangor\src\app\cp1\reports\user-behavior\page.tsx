'use client';

import {
  Calendar,
  Clock,
  Download,
  Filter,
  MapPin,
  Star,
  TrendingUp,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { generateGeographicData, generateTemporalData } from '@/lib/mockData';

export default function UserBehaviorReport() {
  const [geoData, setGeoData] = useState<any[]>([]);
  const [timeData, setTimeData] = useState<any>(null);
  const [timeRange, setTimeRange] = useState<string>('30d');
  const [viewType, setViewType] = useState<string>('geographic');

  useEffect(() => {
    setGeoData(generateGeographicData());
    setTimeData(generateTemporalData());
  }, []);

  if (!timeData) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto" />
            <p className="mt-4 text-gray-600">Loading user behavior data...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  const totalSearches = geoData.reduce(
    (sum, location) => sum + location.searchCount,
    0,
  );

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              User Behavior & Context
            </h1>
            <p className="text-gray-600">
              Geographic and temporal analysis of user search patterns
            </p>
          </div>
          <button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </button>
        </div>

        {/* Filters */}
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                Filters:
              </span>
            </div>

            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>

            <select
              value={viewType}
              onChange={(e) => setViewType(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            >
              <option value="geographic">Geographic View</option>
              <option value="temporal">Temporal View</option>
              <option value="combined">Combined View</option>
            </select>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Top Location
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {geoData[0]?.location}
                </p>
                <p className="text-sm text-gray-500">
                  {geoData[0]?.percentage}% of searches
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <MapPin className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Peak Day</p>
                <p className="text-2xl font-bold text-gray-900">
                  {timeData.peakDays[0]?.day}
                </p>
                <p className="text-sm text-gray-500">
                  {timeData.peakDays[0]?.count} searches
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Calendar className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Peak Hour</p>
                <p className="text-2xl font-bold text-gray-900">
                  {timeData.peakHours[0]?.hour}:00
                </p>
                <p className="text-sm text-gray-500">
                  {timeData.peakHours[0]?.count} searches
                </p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Active Locations
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {geoData.length}
                </p>
                <p className="text-sm text-gray-500">Areas with searches</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Geographic Analysis */}
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Geographic Distribution
              </h3>
              <p className="text-sm text-gray-600">
                Search volume by location in Selangor
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <MapPin className="h-5 w-5 text-gray-500" />
              <span className="text-sm text-gray-600">
                Total: {totalSearches.toLocaleString()} searches
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Location List */}
            <div className="space-y-4">
              {geoData.map((location, index) => (
                <div
                  key={location.location}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                      <span className="text-sm font-medium text-blue-600">
                        #{index + 1}
                      </span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {location.location}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {location.searchCount} searches
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-gray-900">
                      {location.percentage}%
                    </p>
                    <div className="w-20 bg-gray-200 rounded-full h-2 mt-1">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${location.percentage}%` }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Geographic Insights */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Geographic Insights</h4>

              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="flex items-start space-x-2">
                  <Star className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h5 className="font-medium text-blue-900">
                      Top Search Hotspots
                    </h5>
                    <p className="text-sm text-blue-700 mt-1">
                      Shah Alam and Petaling Jaya account for over 50% of all
                      searches, indicating strong urban demand for halal
                      services.
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-green-50 rounded-lg">
                <div className="flex items-start space-x-2">
                  <TrendingUp className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <h5 className="font-medium text-green-900">
                      Emerging Markets
                    </h5>
                    <p className="text-sm text-green-700 mt-1">
                      Subang Jaya shows significant search volume (18.7%),
                      representing a growing market for halal businesses.
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-yellow-50 rounded-lg">
                <div className="flex items-start space-x-2">
                  <MapPin className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div>
                    <h5 className="font-medium text-yellow-900">
                      Expansion Opportunities
                    </h5>
                    <p className="text-sm text-yellow-700 mt-1">
                      Areas like Selayang and other regions show lower search
                      volumes, indicating potential for market expansion.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Temporal Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Peak Days */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Peak Days Analysis
            </h3>
            <div className="space-y-3">
              {timeData.peakDays.map((day: any, index: number) => (
                <div
                  key={day.day}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className={`w-3 h-3 rounded-full ${
                        index === 0
                          ? 'bg-green-500'
                          : index === 1
                            ? 'bg-blue-500'
                            : index === 2
                              ? 'bg-yellow-500'
                              : 'bg-gray-400'
                      }`}
                    />
                    <span className="text-sm font-medium text-gray-900">
                      {day.day}
                    </span>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {day.count}
                    </p>
                    <div className="w-16 bg-gray-200 rounded-full h-2 mt-1">
                      <div
                        className={`h-2 rounded-full ${
                          index === 0
                            ? 'bg-green-500'
                            : index === 1
                              ? 'bg-blue-500'
                              : index === 2
                                ? 'bg-yellow-500'
                                : 'bg-gray-400'
                        }`}
                        style={{
                          width: `${(day.count / timeData.peakDays[0].count) * 100}%`,
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4 p-3 bg-green-50 rounded-lg">
              <p className="text-sm text-green-800">
                <strong>Insight:</strong> Friday shows the highest search
                volume, likely due to weekend meal planning and Jummah prayer
                preparations.
              </p>
            </div>
          </div>

          {/* Peak Hours */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Peak Hours Analysis
            </h3>
            <div className="space-y-3">
              {timeData.peakHours
                .slice(0, 7)
                .map((hour: any, index: number) => (
                  <div
                    key={hour.hour}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className={`w-3 h-3 rounded-full ${
                          index === 0
                            ? 'bg-orange-500'
                            : index === 1
                              ? 'bg-red-500'
                              : index === 2
                                ? 'bg-purple-500'
                                : 'bg-gray-400'
                        }`}
                      />
                      <span className="text-sm font-medium text-gray-900">
                        {hour.hour}:00 - {hour.hour + 1}:00
                      </span>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        {hour.count}
                      </p>
                      <div className="w-16 bg-gray-200 rounded-full h-2 mt-1">
                        <div
                          className={`h-2 rounded-full ${
                            index === 0
                              ? 'bg-orange-500'
                              : index === 1
                                ? 'bg-red-500'
                                : index === 2
                                  ? 'bg-purple-500'
                                  : 'bg-gray-400'
                          }`}
                          style={{
                            width: `${(hour.count / timeData.peakHours[0].count) * 100}%`,
                          }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
            </div>

            <div className="mt-4 p-3 bg-orange-50 rounded-lg">
              <p className="text-sm text-orange-800">
                <strong>Insight:</strong> Peak search times align with lunch
                hours (11 AM - 1 PM), indicating meal planning behavior.
              </p>
            </div>
          </div>
        </div>

        {/* Behavioral Patterns */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            User Behavior Patterns
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 border rounded-lg">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
              <h4 className="font-medium text-gray-900 mb-2">
                Weekday Preference
              </h4>
              <p className="text-sm text-gray-600">
                Users are most active during weekdays, with Friday being the
                peak day for halal searches.
              </p>
            </div>

            <div className="text-center p-4 border rounded-lg">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
              <h4 className="font-medium text-gray-900 mb-2">
                Lunch Hour Peak
              </h4>
              <p className="text-sm text-gray-600">
                Search activity peaks during lunch hours (11 AM - 1 PM),
                indicating meal planning behavior.
              </p>
            </div>

            <div className="text-center p-4 border rounded-lg">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <MapPin className="h-6 w-6 text-green-600" />
              </div>
              <h4 className="font-medium text-gray-900 mb-2">
                Urban Concentration
              </h4>
              <p className="text-sm text-gray-600">
                Search activity is concentrated in major urban areas, with Shah
                Alam leading at 28% of total searches.
              </p>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
