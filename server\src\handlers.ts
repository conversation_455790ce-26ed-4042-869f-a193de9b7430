// Extract handlers from index.ts for reuse in both Cloudflare Workers and Node.js
import { v4 as uuidv4 } from 'uuid';
import authService from './middleware/auth';
import { handleTwilioRoutes } from './routes/twilio';
import type DatabaseService from './services/database';
import facebookService from './services/facebook';
import HalalKnowledgeService from './services/halalKnowledge';
import { createMessageHandlerService } from './services/messageHandler';
import openaiService from './services/openai';
import R2RService from './services/r2r';
import whatsappService from './services/whatsapp';
import type {
  AgentLoginRequest,
  ChatMessage,
  ChatSession,
  FacebookWebhookPayload,
  HalalKnowledgeResponse,
  ParseR2rOptions,
  SearchResponse,
} from './types';

// Store WhatsApp chat sessions (in production, use a database)
const whatsappSessions = new Map<string, ChatSession>();

// WhatsApp route handler
export async function handleWhatsAppRequest(
  request: Request,
  env: any,
  _dbService: any,
  siteId: string,
): Promise<Response> {
  const url = new URL(request.url);
  const path = url.pathname;

  // Remove site prefix from path for route matching
  const normalizedPath = path.includes(`/api/sites/${siteId}`)
    ? path.replace(`/api/sites/${siteId}`, '/api')
    : path;

  // Webhook verification endpoint
  if (path === '/api/whatsapp/webhook' && request.method === 'GET') {
    try {
      const mode = url.searchParams.get('hub.mode');
      const token = url.searchParams.get('hub.verify_token');
      const challenge = url.searchParams.get('hub.challenge');

      console.log('Webhook verification request:', { mode, token, challenge });

      const verificationResult = whatsappService.verifyWebhook(
        mode,
        token,
        challenge,
      );

      if (verificationResult) {
        console.log('Webhook verified successfully');
        return new Response(verificationResult, {
          headers: { 'Content-Type': 'text/plain' },
          status: 200,
        });
      }
      console.log('Webhook verification failed');
      return new Response('Forbidden', {
        headers: { 'Content-Type': 'text/plain' },
        status: 403,
      });
    } catch (error) {
      console.error('Webhook verification error:', error);
      return new Response('Internal Server Error', {
        headers: { 'Content-Type': 'text/plain' },
        status: 500,
      });
    }
  }

  // Webhook message endpoint
  if (path === '/api/whatsapp/webhook' && request.method === 'POST') {
    try {
      const body = await request.json();
      console.log('Received WhatsApp webhook:', JSON.stringify(body, null, 2));

      // Process webhook payload
      const messages = await whatsappService.processWebhookMessage(body as any);

      if (messages && messages.length > 0) {
        // Handle each incoming message
        for (const message of messages) {
          await handleIncomingMessage(message, env);
        }
      }

      return new Response('OK', {
        headers: { 'Content-Type': 'text/plain' },
        status: 200,
      });
    } catch (error) {
      console.error('Webhook processing error:', error);
      return new Response('Internal Server Error', {
        headers: { 'Content-Type': 'text/plain' },
        status: 500,
      });
    }
  }

  // Get session history for a phone number (admin endpoint)
  if (path.startsWith('/api/whatsapp/sessions/') && request.method === 'GET') {
    try {
      const phoneNumber = path.split('/').pop();
      if (!phoneNumber) {
        return new Response(
          JSON.stringify({
            error: 'Phone number required',
            message: 'Phone number parameter is missing',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        );
      }

      const session = whatsappSessions.get(phoneNumber);

      if (!session) {
        return new Response(
          JSON.stringify({
            error: 'Session not found',
            message: 'No chat session found for this phone number',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 404,
          },
        );
      }

      return new Response(
        JSON.stringify({
          session: {
            id: session.id,
            phoneNumber,
            messageCount: session.messages.length,
            createdAt: session.createdAt,
            messages: session.messages.slice(-20), // Return last 20 messages
          },
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      );
    } catch (error) {
      console.error('Error getting session:', error);
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to get session',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      );
    }
  }

  // Get all active sessions (admin endpoint)
  if (path === '/api/whatsapp/sessions' && request.method === 'GET') {
    try {
      const sessions = Array.from(whatsappSessions.entries()).map(
        ([phoneNumber, session]) => ({
          phoneNumber,
          sessionId: session.id,
          messageCount: session.messages.length,
          createdAt: session.createdAt,
          lastActivity:
            session.messages[session.messages.length - 1]?.timestamp,
        }),
      );

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            sessions,
            totalSessions: sessions.length,
          },
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      );
    } catch (error) {
      console.error('Error getting sessions:', error);
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Internal server error',
          message: 'Failed to get sessions',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      );
    }
  }

  // Default response for unknown WhatsApp routes
  return new Response(JSON.stringify({
    success: false,
    error: 'WhatsApp route not found'
  }), {
    headers: { 'Content-Type': 'application/json' },
    status: 404,
  });
}

// Handle incoming WhatsApp message using consolidated handler
async function handleIncomingMessage(message: any, env: any): Promise<void> {
  try {
    const phoneNumber = message.from;
    const messageText = message.content;

    // Use consolidated message handler
    const response = await createMessageHandlerService().handleIncomingMessage(
      {
        message: messageText,
        sessionId: phoneNumber, // Use phone number as session ID for WhatsApp
        platform: 'whatsapp',
        messageType: message.type === 'image' ? 'image' : 'text',
        mediaUrl: message.mediaUrl,
        userId: phoneNumber,
        config: {
          maxMessageHistory: 10,
          enableToolCalling: true,
          defaultModel: 'gpt-4o-mini',
        },
      },
      env,
    );

    if (response.success && response.message) {
      // Send response back to WhatsApp
      const sendResult = await whatsappService.sendTextMessage(
        phoneNumber,
        response.message,
      );

      if (!sendResult.success) {
        console.error('Failed to send WhatsApp response:', sendResult.error);
      } else {
        console.log(`Sent response to ${phoneNumber}: ${response.message}`);
      }
    } else {
      console.error('Failed to get AI response:', response.error);
    }
  } catch (error) {
    console.error('Error handling incoming message:', error);
  }
}

// Store Facebook chat sessions in memory (in production, use a database)
const facebookSessions = new Map<string, ChatSession>();

// Facebook route handler
export async function handleFacebookRequest(
  request: Request,
  env: any,
  dbService: DatabaseService,
  siteId: string,
): Promise<Response> {
  const url = new URL(request.url);
  const path = url.pathname;

  // Initialize Facebook service with database service for this request
  facebookService.initialize(dbService, Number.parseInt(siteId, 10));

  // Remove site prefix from path for route matching
  const normalizedPath = path.includes(`/api/sites/${siteId}`)
    ? path.replace(`/api/sites/${siteId}`, '/api')
    : path;

  // Webhook verification endpoint
  if (path === '/api/facebook/webhook' && request.method === 'GET') {
    try {
      const mode = url.searchParams.get('hub.mode');
      const token = url.searchParams.get('hub.verify_token');
      const challenge = url.searchParams.get('hub.challenge');

      console.log('Facebook webhook verification request:', {
        mode,
        token,
        challenge,
      });

      const verificationResult = facebookService.verifyWebhookChallenge(
        mode || '',
        token || '',
        challenge || '',
      );

      if (verificationResult) {
        console.log('Facebook webhook verified successfully');
        return new Response(verificationResult, {
          headers: { 'Content-Type': 'text/plain' },
          status: 200,
        });
      }
      console.log('Facebook webhook verification failed');
      return new Response('Forbidden', {
        headers: { 'Content-Type': 'text/plain' },
        status: 403,
      });
    } catch (error) {
      console.error('Facebook webhook verification error:', error);
      return new Response('Internal Server Error', {
        headers: { 'Content-Type': 'text/plain' },
        status: 500,
      });
    }
  }

  // Webhook message endpoint
  if (path === '/api/facebook/webhook' && request.method === 'POST') {
    try {
      const signature = request.headers.get('x-hub-signature-256') || '';
      const body = await request.text();

      // Verify webhook signature for security
      if (!facebookService.verifyWebhookSignature(body, signature)) {
        console.log('Facebook webhook signature verification failed');
        return new Response('Forbidden', {
          headers: { 'Content-Type': 'text/plain' },
          status: 403,
        });
      }

      const webhookPayload: FacebookWebhookPayload = JSON.parse(body);
      console.log(
        'Received Facebook webhook:',
        JSON.stringify(webhookPayload, null, 2),
      );

      // Process each entry in the webhook payload
      for (const entry of webhookPayload.entry) {
        for (const messagingEvent of entry.messaging) {
          if (messagingEvent.message && !messagingEvent.message.is_echo) {
            // Handle incoming message
            await handleIncomingFacebookMessage(messagingEvent, env);
          } else if (messagingEvent.postback) {
            // Handle postback (button clicks, etc.)
            await handleFacebookPostback(messagingEvent, env);
          }
        }
      }

      return new Response('OK', {
        headers: { 'Content-Type': 'text/plain' },
        status: 200,
      });
    } catch (error) {
      console.error('Facebook webhook processing error:', error);
      return new Response('Internal Server Error', {
        headers: { 'Content-Type': 'text/plain' },
        status: 500,
      });
    }
  }

  // Default response for unknown Facebook routes
  return new Response(JSON.stringify({ error: 'Facebook route not found' }), {
    headers: { 'Content-Type': 'application/json' },
    status: 404,
  });
}

// Handle incoming Facebook message using consolidated handler
async function handleIncomingFacebookMessage(
  messagingEvent: any,
  env: any,
): Promise<void> {
  try {
    const senderId = messagingEvent.sender.id;
    const message = messagingEvent.message;

    let messageText = '';
    let messageType = 'text';
    let mediaUrl: string | undefined;

    if (message.text) {
      messageText = message.text;
      messageType = 'text';
    } else if (message.attachments && message.attachments.length > 0) {
      const attachment = message.attachments[0];
      messageType = attachment.type;
      mediaUrl = attachment.payload.url;
      messageText = `[${attachment.type.toUpperCase()} received]`;
    } else if (message.quick_reply) {
      messageText = message.quick_reply.payload;
      messageType = 'quick_reply';
    }

    // Use consolidated message handler
    const response = await createMessageHandlerService().handleIncomingMessage(
      {
        message: messageText,
        sessionId: senderId, // Use sender ID as session ID for Facebook
        platform: 'facebook',
        messageType: messageType === 'image' ? 'image' : 'text',
        mediaUrl,
        userId: senderId,
        config: {
          maxMessageHistory: 10,
          enableToolCalling: true,
          defaultModel: 'gpt-4o-mini',
        },
      },
      env,
    );

    if (response.success && response.message) {
      // Send response back to Facebook Messenger
      const sendResult = await facebookService.sendTextMessage(
        senderId,
        response.message,
      );

      if (!sendResult.success) {
        console.error('Failed to send Facebook message:', sendResult.error);
      } else {
        console.log(
          `Sent Facebook response to ${senderId}: ${response.message}`,
        );
      }
    } else {
      console.error('AI request failed:', response.error);

      // Send error message to user
      await facebookService.sendTextMessage(
        senderId,
        'Sorry, I encountered an error processing your message. Please try again later.',
      );
    }
  } catch (error) {
    console.error('Error handling Facebook message:', error);
  }
}

// Handle Facebook postback events (button clicks, etc.)
async function handleFacebookPostback(
  messagingEvent: any,
  env: any,
): Promise<void> {
  try {
    const senderId = messagingEvent.sender.id;
    const postback = messagingEvent.postback;

    console.log(
      `Processing Facebook postback from ${senderId}: ${postback.payload}`,
    );

    // Handle different postback payloads
    switch (postback.payload) {
      case 'GET_STARTED':
        await facebookService.sendTextMessage(
          senderId,
          'Welcome to Halal Malaysia! I can help you with halal certification inquiries. How can I assist you today?',
        );
        break;

      case 'HELP':
        await facebookService.sendTextMessage(
          senderId,
          'I can help you with:\n• Halal certification information\n• Application procedures\n• Requirements and guidelines\n• General halal inquiries\n\nJust ask me anything!',
        );
        break;

      default:
        // Treat unknown postbacks as regular messages
        await handleIncomingFacebookMessage(
          {
            sender: { id: senderId },
            message: { text: postback.payload },
          },
          env,
        );
        break;
    }
  } catch (error) {
    console.error('Error handling Facebook postback:', error);
  }
}

// Export other handlers as placeholders - you'll need to copy the implementations from index.ts
export async function handleChatRequest(
  request: Request,
  env: any,
  dbService: any,
  siteId: string,
): Promise<Response> {
  // TODO: Copy implementation from index.ts
  return new Response('Handler not implemented yet', { status: 501 });
}

export async function handleUploadRequest(
  request: Request,
  env: any,
  dbService: any,
  siteId: string,
): Promise<Response> {
  // TODO: Copy implementation from index.ts
  return new Response('Handler not implemented yet', { status: 501 });
}

export async function handleAdminRequest(
  request: Request,
  env: any,
  dbService: any,
  siteId: string,
): Promise<Response> {
  // TODO: Copy implementation from index.ts
  return new Response('Handler not implemented yet', { status: 501 });
}

export async function handleAgentRequest(
  request: Request,
  env: any,
  dbService: any,
  siteId: string,
): Promise<Response> {
  // TODO: Copy implementation from index.ts
  return new Response('Handler not implemented yet', { status: 501 });
}

export async function handleSessionRequest(
  request: Request,
  env: any,
  dbService: any,
  siteId: string,
): Promise<Response> {
  // TODO: Copy implementation from index.ts
  return new Response('Handler not implemented yet', { status: 501 });
}

export async function handleSearchRequest(
  request: Request,
  env: any,
): Promise<Response> {
  const url = new URL(request.url);
  const path = url.pathname;

  // Handle search endpoint
  if (path === '/api/search' || path === '/api/search/') {
    if (request.method === 'GET') {
      const query = url.searchParams.get('query');
      const retrieveDocument =
        url.searchParams.get('retrieveDocument') || 'true';
      const maxWordCount = url.searchParams.get('maxWordCount') || '3000';
      const includeGraph = url.searchParams.get('includeGraph') || 'true';

      console.log('Received search query:', query);

      if (!query) {
        return new Response(
          JSON.stringify({ error: 'Query parameter is missing' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } },
        );
      }

      const { R2R_USERNAME, R2R_PASSWORD, R2R_URL, R2R_COLLECTION_ID } = env;

      if (!R2R_USERNAME || !R2R_PASSWORD || !R2R_URL) {
        console.error('R2R credentials not configured');
        return new Response(
          JSON.stringify({ error: 'R2R credentials not configured' }),
          { status: 500, headers: { 'Content-Type': 'application/json' } },
        );
      }

      try {
        const r2rService = new R2RService();

        // Perform search
        const { chunks, graph } = await r2rService.search(
          query,
          {
            retrieveDocument: retrieveDocument === 'true',
            maxWordCount: Number.parseInt(maxWordCount, 10),
            includeGraph: includeGraph === 'true',
            collectionId: R2R_COLLECTION_ID,
          },
          env,
        );

        console.log('Extracted chunks:', chunks.length);
        console.log('Extracted graph results:', graph.length);

        // Parse options
        const options = {
          retrieveDocument: retrieveDocument === 'true',
          maxWordCount: Number.parseInt(maxWordCount, 10),
          includeGraph: includeGraph === 'true',
          minScore: 0.1,
          limit: 20,
        };

        // Get the R2R client for parsing
        const client = r2rService.getClient();
        const r2rResult = await r2rService.parseR2rResult(
          client,
          chunks,
          options.includeGraph ? graph : [],
          options,
        );

        const searchResponse = {
          query,
          results: r2rResult.texts,
          totalChunks: chunks.length,
          totalGraphResults: graph.length,
          options,
        };

        console.log('Final search response:', {
          query,
          resultsCount: r2rResult.texts.length,
          totalChunks: chunks.length,
          totalGraphResults: graph.length,
        });

        return new Response(JSON.stringify(searchResponse), {
          headers: { 'Content-Type': 'application/json' },
        });
      } catch (error) {
        console.error('R2R search error:', error);
        return new Response(
          JSON.stringify({
            error: 'Failed to fetch search results',
            details: error instanceof Error ? error.message : 'Unknown error',
          }),
          { status: 500, headers: { 'Content-Type': 'application/json' } },
        );
      }
    }
  }

  return new Response(JSON.stringify({ error: 'Search route not found' }), {
    status: 404,
    headers: { 'Content-Type': 'application/json' },
  });
}

export async function handleHalalKnowledgeRequest(
  request: Request,
  env: any,
): Promise<Response> {
  const url = new URL(request.url);
  const path = url.pathname;

  // Handle halal knowledge ask endpoint
  if (path === '/api/halal-knowledge/ask' && request.method === 'POST') {
    try {
      const body = await request.json();
      const {
        query,
        sessionId,
        maxResults = 5,
        minScore = 0.3,
        includeContext = true,
      } = body;

      // Validate required fields
      if (!query || typeof query !== 'string' || query.trim().length === 0) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Query is required and must be a non-empty string',
            query: query || '',
            sessionId,
          }),
          { status: 400, headers: { 'Content-Type': 'application/json' } },
        );
      }

      // Check if the query is related to halal topics
      const halalKnowledgeService = new HalalKnowledgeService();
      if (!halalKnowledgeService.isHalalRelatedQuery(query)) {
        return new Response(
          JSON.stringify({
            success: true,
            answer:
              "I specialize in answering questions about halal and Islamic matters. Your question doesn't seem to be related to halal topics. Please ask me about Islamic jurisprudence, halal food, Islamic practices, or other religious matters.",
            sources: [],
            query,
            sessionId,
          }),
          { headers: { 'Content-Type': 'application/json' } },
        );
      }

      console.log('Processing halal knowledge request:', {
        query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
        sessionId,
        maxResults,
        minScore,
        includeContext,
      });

      // Process the request
      const response = await halalKnowledgeService.searchAndAnswer(
        {
          query,
          sessionId,
          maxResults,
          minScore,
          includeContext,
        },
        env,
      );

      console.log('Halal knowledge response:', {
        success: response.success,
        hasAnswer: !!response.answer,
        sourcesCount: response.sources?.length || 0,
        error: response.error,
      });

      return new Response(JSON.stringify(response), {
        headers: { 'Content-Type': 'application/json' },
      });
    } catch (error) {
      console.error('Halal knowledge endpoint error:', error);
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Internal server error',
          query: '',
          sessionId: '',
        }),
        { status: 500, headers: { 'Content-Type': 'application/json' } },
      );
    }
  }

  // Handle health check endpoint
  if (path === '/api/halal-knowledge/health' && request.method === 'GET') {
    try {
      const testQuery = 'halal';
      const halalKnowledgeService = new HalalKnowledgeService();
      const testResponse = await halalKnowledgeService.searchAndAnswer(
        {
          query: testQuery,
          maxResults: 1,
          minScore: 0.1,
          includeContext: false,
        },
        env,
      );

      return new Response(
        JSON.stringify({
          status: 'healthy',
          timestamp: new Date().toISOString(),
          services: {
            r2r: testResponse.success ? 'connected' : 'error',
            openai: 'available',
          },
          message: 'Halal knowledge service is operational',
        }),
        { headers: { 'Content-Type': 'application/json' } },
      );
    } catch (error) {
      console.error('Halal knowledge health check error:', error);
      return new Response(
        JSON.stringify({
          status: 'error',
          timestamp: new Date().toISOString(),
          services: {
            r2r: 'error',
            openai: 'unknown',
          },
          message: 'Halal knowledge service is not operational',
          error: error instanceof Error ? error.message : 'Unknown error',
        }),
        { status: 503, headers: { 'Content-Type': 'application/json' } },
      );
    }
  }

  // Handle info endpoint
  if (path === '/api/halal-knowledge/info' && request.method === 'GET') {
    return new Response(
      JSON.stringify({
        service: 'Halal Knowledge Search',
        description: 'AI-powered halal knowledge search using R2R and OpenAI',
        version: '1.0.0',
        capabilities: [
          'Search halal knowledge base',
          'Generate AI-powered answers',
          'Provide source citations',
          'Filter by relevance score',
          'Support for multiple Islamic topics',
        ],
        supportedTopics: [
          'Halal and Haram rulings',
          'Islamic jurisprudence (Fiqh)',
          'Food and dietary laws',
          'Business and finance',
          'Prayer and worship',
          'Marriage and family',
          'Clothing and modesty',
          'General Islamic guidance',
        ],
        endpoints: {
          ask: 'POST /api/halal-knowledge/ask - Ask a halal-related question',
          health: 'GET /api/halal-knowledge/health - Check service health',
          info: 'GET /api/halal-knowledge/info - Get service information',
        },
      }),
      { headers: { 'Content-Type': 'application/json' } },
    );
  }

  // Default response for unknown halal knowledge routes
  return new Response(
    JSON.stringify({ error: 'Halal knowledge route not found' }),
    { status: 404, headers: { 'Content-Type': 'application/json' } },
  );
}

export async function handleSecurityRoutes(
  request: Request,
  env: any,
): Promise<Response> {
  const url = new URL(request.url);
  const path = url.pathname;

  // Handle security stats endpoint
  if (path === '/api/security/stats' && request.method === 'GET') {
    try {
      // Basic security stats - in a real implementation, this would come from monitoring systems
      const securityStats = {
        sessions: {
          totalSessions: 0,
          activeSessions: 0,
          expiredSessions: 0,
        },
        system: {
          uptime: process.uptime ? process.uptime() : 0,
          memoryUsage: {
            heapUsed: 0,
            heapTotal: 0,
            external: 0,
          },
          timestamp: new Date().toISOString(),
        },
        security: {
          sessionTimeout: 30 * 60 * 1000, // 30 minutes
          cleanupInterval: 5 * 60 * 1000, // 5 minutes
          maxSessionsPerUser: 5,
          rateLimiting: {
            general: { windowMs: 15 * 60 * 1000, max: 1000 },
            chat: { windowMs: 1 * 60 * 1000, max: 60 },
            auth: { windowMs: 1 * 60 * 1000, max: 5 },
            upload: { windowMs: 60 * 60 * 1000, max: 10 },
          },
          securityHeaders: true,
          inputValidation: true,
          errorHandling: true,
        },
      };

      return new Response(
        JSON.stringify({
          success: true,
          data: securityStats,
        }),
        { headers: { 'Content-Type': 'application/json' } },
      );
    } catch (error) {
      console.error('Security stats error:', error);
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to retrieve security stats',
        }),
        { status: 500, headers: { 'Content-Type': 'application/json' } },
      );
    }
  }

  // Handle security health check endpoint
  if (path === '/api/security/health' && request.method === 'GET') {
    try {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        checks: {
          sessions: {
            status: 'healthy',
            message: 'Session management operational',
          },
          rateLimit: { status: 'healthy', message: 'Rate limiting active' },
          security: {
            status: 'healthy',
            message: 'Security headers configured',
          },
          validation: { status: 'healthy', message: 'Input validation active' },
        },
      };

      return new Response(JSON.stringify(health), {
        headers: { 'Content-Type': 'application/json' },
      });
    } catch (error) {
      console.error('Security health check error:', error);
      return new Response(
        JSON.stringify({
          status: 'error',
          timestamp: new Date().toISOString(),
          error: error instanceof Error ? error.message : 'Unknown error',
        }),
        { status: 503, headers: { 'Content-Type': 'application/json' } },
      );
    }
  }

  // Handle security events endpoint
  if (path === '/api/security/events' && request.method === 'GET') {
    try {
      // In a real implementation, this would fetch from a logging service or database
      const events = [
        {
          id: '1',
          event: 'SECURITY_HEALTH_CHECK',
          level: 'info',
          timestamp: new Date().toISOString(),
          ip: '127.0.0.1',
          details: {
            healthStatus: 'healthy',
            endpoint: '/api/security/health',
          },
        },
      ];

      return new Response(
        JSON.stringify({
          success: true,
          events,
          total: events.length,
        }),
        { headers: { 'Content-Type': 'application/json' } },
      );
    } catch (error) {
      console.error('Security events error:', error);
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to retrieve security events',
        }),
        { status: 500, headers: { 'Content-Type': 'application/json' } },
      );
    }
  }

  // Default response for unknown security routes
  return new Response(JSON.stringify({ error: 'Security route not found' }), {
    status: 404,
    headers: { 'Content-Type': 'application/json' },
  });
}
