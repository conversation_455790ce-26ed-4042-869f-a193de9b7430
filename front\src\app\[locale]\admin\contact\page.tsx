'use client';

export const runtime = 'edge';

import {
  Building,
  Clock,
  Contact,
  Edit3,
  Eye,
  Globe,
  Mail,
  MapPin,
  Phone,
  PlusCircle,
  Search,
  Trash2,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Link, useRouter } from '@/i18n/navigation';
import { api } from '@/lib/api';

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

interface ContactInfo {
  id: number;
  name: string;
  title: string;
  department: string;
  type: 'general' | 'support' | 'sales' | 'technical' | 'emergency';
  email: string;
  phone?: string;
  mobile?: string;
  address?: string;
  city?: string;
  state?: string;
  postcode?: string;
  country?: string;
  website?: string;
  workingHours?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ContactResponse {
  contacts: ContactInfo[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
    totalPages: number;
  };
}

export default function ContactPage() {
  const router = useRouter();
  const [contacts, setContacts] = useState<ContactInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [departmentFilter, setDepartmentFilter] = useState<string>('all');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    hasMore: false,
    totalPages: 0,
  });

  useEffect(() => {
    fetchContacts();
  }, [pagination.page, searchTerm, typeFilter, departmentFilter]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchContacts = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });

      if (searchTerm) {
        params.append('search', searchTerm);
      }
      if (typeFilter !== 'all') {
        params.append('type', typeFilter);
      }
      if (departmentFilter !== 'all') {
        params.append('department', departmentFilter);
      }

      const response = await api.get<ContactResponse>(
        `/admin/contacts?${params}`,
      );
      setContacts(response.data.contacts);
      setPagination(response.data.pagination);
    } catch (err) {
      console.error('Error fetching contacts:', err);
      setError('Failed to load contacts');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this contact?')) {
      return;
    }

    try {
      await api.delete(`/admin/contacts/${id}`);
      await fetchContacts(); // Refresh the list
    } catch (err) {
      console.error('Error deleting contact:', err);
      setError('Failed to delete contact');
    }
  };

  const handleToggleActive = async (id: number, isActive: boolean) => {
    try {
      await api.patch(`/admin/contacts/${id}`, { isActive: !isActive });
      await fetchContacts(); // Refresh the list
    } catch (err) {
      console.error('Error updating contact status:', err);
      setError('Failed to update contact status');
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchContacts();
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'general':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'support':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'sales':
        return 'text-purple-600 bg-purple-50 border-purple-200';
      case 'technical':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'emergency':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (isLoading && contacts.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">
            Contact Management
          </h1>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading contacts...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <Button variant="ghost" size="sm" onClick={() => setError(null)}>
              ×
            </Button>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Contact Management
          </h1>
          <p className="text-gray-600 mt-2">
            Manage contact information, support channels, and communication
            details
          </p>
        </div>
        <Button onClick={() => router.push('/admin/contact/new')}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Add Contact
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter Contacts</CardTitle>
          <CardDescription>
            Find contacts by name, department, or contact type
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search contacts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Button type="submit">
                <Search className="mr-2 h-4 w-4" />
                Search
              </Button>
            </div>
            <div className="flex gap-4">
              <div className="flex-1">
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="support">Support</SelectItem>
                    <SelectItem value="sales">Sales</SelectItem>
                    <SelectItem value="technical">Technical</SelectItem>
                    <SelectItem value="emergency">Emergency</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex-1">
                <Select
                  value={departmentFilter}
                  onValueChange={setDepartmentFilter}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by department" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    <SelectItem value="administration">
                      Administration
                    </SelectItem>
                    <SelectItem value="certification">Certification</SelectItem>
                    <SelectItem value="audit">Audit</SelectItem>
                    <SelectItem value="compliance">Compliance</SelectItem>
                    <SelectItem value="customer_service">
                      Customer Service
                    </SelectItem>
                    <SelectItem value="technical_support">
                      Technical Support
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Contacts Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {contacts.map((contact) => (
          <Card key={contact.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Contact className="h-5 w-5 text-blue-600" />
                  <CardTitle className="text-lg truncate">
                    {contact.name}
                  </CardTitle>
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => router.push(`/admin/contact/${contact.id}`)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      router.push(`/admin/contact/${contact.id}/edit`)
                    }
                  >
                    <Edit3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDelete(contact.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardDescription>
                <div className="flex items-center gap-2">
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium border ${getTypeColor(contact.type)}`}
                  >
                    {contact.type.toUpperCase()}
                  </span>
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${
                      contact.isActive
                        ? 'text-green-600 bg-green-50 border border-green-200'
                        : 'text-gray-600 bg-gray-50 border border-gray-200'
                    }`}
                  >
                    {contact.isActive ? 'ACTIVE' : 'INACTIVE'}
                  </span>
                </div>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Title:</span>{' '}
                  <span className="text-gray-600">{contact.title}</span>
                </div>
                <div>
                  <span className="font-medium">Department:</span>{' '}
                  <span className="text-gray-600">{contact.department}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Mail className="h-3 w-3 text-gray-400" />
                  <a
                    href={`mailto:${contact.email}`}
                    className="text-blue-600 hover:text-blue-700 text-xs"
                  >
                    {contact.email}
                  </a>
                </div>
                {contact.phone && (
                  <div className="flex items-center gap-1">
                    <Phone className="h-3 w-3 text-gray-400" />
                    <a
                      href={`tel:${contact.phone}`}
                      className="text-blue-600 hover:text-blue-700 text-xs"
                    >
                      {contact.phone}
                    </a>
                  </div>
                )}
                {contact.address && (
                  <div className="flex items-start gap-1">
                    <MapPin className="h-3 w-3 mt-0.5 text-gray-400" />
                    <span className="text-gray-600 text-xs">
                      {contact.address}
                    </span>
                  </div>
                )}
                {contact.workingHours && (
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3 text-gray-400" />
                    <span className="text-gray-600 text-xs">
                      {contact.workingHours}
                    </span>
                  </div>
                )}
                {contact.website && (
                  <div className="flex items-center gap-1">
                    <Globe className="h-3 w-3 text-gray-400" />
                    <a
                      href={contact.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-700 text-xs"
                    >
                      Website
                    </a>
                  </div>
                )}
              </div>
              <div className="mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    handleToggleActive(contact.id, contact.isActive)
                  }
                  className="w-full"
                >
                  {contact.isActive ? 'Deactivate' : 'Activate'}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}

        {contacts.length === 0 && !isLoading && (
          <Card className="col-span-full">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Contact className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Contacts Found
              </h3>
              <p className="text-gray-600 text-center mb-4">
                {searchTerm
                  ? `No contacts found matching "${searchTerm}"`
                  : 'Get started by adding your first contact.'}
              </p>
              <Button onClick={() => router.push('/admin/contact/new')}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Contact
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
