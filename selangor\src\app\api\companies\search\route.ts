import { count, ilike, or, eq, and, inArray } from 'drizzle-orm';
import { type NextRequest } from 'next/server';
import {
  extractAnalyticsFromRequest,
  getHalalSelangorSiteId,
  trackSearchAnalytics,
} from '@/lib/analytics';
import { db } from '@/lib/db';
import { companies, categories, companyCategories } from '@/lib/db/schema';
import { createSuccessResponse, handleApiError } from '@/lib/api-response';

// Remove edge runtime to use Node.js runtime for database access

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('q');
  const page = Number.parseInt(searchParams.get('page') || '1');
  const limit = Number.parseInt(searchParams.get('limit') || '10');
  const category = searchParams.get('category');
  const state = searchParams.get('state');

  console.log('Company search query:', query, 'length:', query?.length);
  console.log('Company search filters - category:', category, 'state:', state);

  // Helper function to convert state code to state name
  const getStateName = (stateCode: string) => {
    const stateMap: { [key: string]: string } = {
      'MY-01': 'Johor', 'MY-02': 'Kedah', 'MY-03': 'Kelantan',
      'MY-04': 'Melaka', 'MY-05': 'Negeri Sembilan', 'MY-06': 'Pahang',
      'MY-07': 'Penang', 'MY-08': 'Perak', 'MY-09': 'Perlis',
      'MY-10': 'Selangor', 'MY-11': 'Terengganu', 'MY-12': 'Sabah',
      'MY-13': 'Sarawak', 'MY-14': 'Kuala Lumpur', 'MY-15': 'Labuan',
      'MY-16': 'Putrajaya'
    };
    return stateMap[stateCode] || stateCode;
  };

  // Build filter conditions
  const buildFilterConditions = async () => {
    const conditions = [eq(companies.siteId, getHalalSelangorSiteId())];

    if (category) {
      // First, find the category ID by name
      const categoryRecord = await db
        .select({ id: categories.id })
        .from(categories)
        .where(eq(categories.categoryName, category))
        .limit(1);

      if (categoryRecord.length > 0) {
        const categoryId = categoryRecord[0].id;

        // Get company IDs that belong to this category
        const companyCategoryLinks = await db
          .select({ companyId: companyCategories.companyId })
          .from(companyCategories)
          .where(eq(companyCategories.categoryId, categoryId));

        const companyIdsFromCategory = companyCategoryLinks.map((link: any) => link.companyId);

        if (companyIdsFromCategory.length > 0) {
          conditions.push(inArray(companies.id, companyIdsFromCategory));
        } else {
          // No companies found for this category, also check legacy category field
          const legacyConditions = [
            ilike(companies.category, `%${category}%`),
            ilike(companies.subcategory, `%${category}%`)
          ];
          const orCondition = or(...legacyConditions);
          if (orCondition) {
            conditions.push(orCondition);
          }
        }
      } else {
        // Category not found in categories table, fall back to legacy search
        const legacyConditions = [
          ilike(companies.category, `%${category}%`),
          ilike(companies.subcategory, `%${category}%`)
        ];
        const orCondition = or(...legacyConditions);
        if (orCondition) {
          conditions.push(orCondition);
        }
      }
    }

    if (state) {
      conditions.push(eq(companies.state, state));
    }

    return and(...conditions);
  };

  // If no query provided, return all companies with pagination
  if (!query || query.trim() === '') {
    try {
      // Get total count for pagination (filtered by site_id and other filters)
      const filterConditions = await buildFilterConditions();
      const totalResults = await db
        .select({ count: count() })
        .from(companies)
        .where(filterConditions);

      const total = totalResults[0]?.count || 0;

      // Calculate pagination
      const offset = (page - 1) * limit;
      const hasMore = offset + limit < total;

      // Get paginated results (filtered by site_id)
      const allCompanies = await db
        .select({
          id: companies.id,
          companyName: companies.companyName,
          registrationNumber: companies.registrationNumber,
          businessType: companies.businessType,
          category: companies.category,
          subcategory: companies.subcategory,
          address: companies.address,
          state: companies.state,
          postcode: companies.postcode,
          city: companies.city,
          country: companies.country,
          phone: companies.phone,
          fax: companies.fax,
          email: companies.email,
          website: companies.website,
          contactPerson: companies.contactPerson,
          certificateNumber: companies.certificateNumber,
          certificateType: companies.certificateType,
          certificateStatus: companies.certificateStatus,
          issuedDate: companies.issuedDate,
          expiryDate: companies.expiryDate,
          sourceUrl: companies.sourceUrl,
          createdAt: companies.createdAt,
          updatedAt: companies.updatedAt,
        })
        .from(companies)
        .where(filterConditions)
        .limit(limit)
        .offset(offset)
        .orderBy(companies.companyName);

      console.log(
        `Company list (no search) found ${total} results, returning ${allCompanies.length} for page ${page}`,
      );

      // Track analytics for listing all companies
      const analyticsData = extractAnalyticsFromRequest(request);
      await trackSearchAnalytics({
        siteId: getHalalSelangorSiteId(),
        searchQuery: '',
        searchType: 'companies',
        resultsCount: allCompanies.length,
        hasResults: allCompanies.length > 0,
        searchFilters: JSON.stringify({ page, limit, category, state }),
        ...analyticsData,
      });

      return createSuccessResponse({
        companies: allCompanies.map((company: any) => ({
          id: company.id,
          name: company.companyName,
          registrationNumber: company.registrationNumber,
          businessType: company.businessType,
          category: company.category,
          subcategory: company.subcategory,
          address: company.address,
          state: getStateName(company.state),
          postcode: company.postcode,
          city: company.city,
          country: company.country,
          phone: company.phone,
          fax: company.fax,
          email: company.email,
          website: company.website,
          contactPerson: company.contactPerson,
          certificateNumber: company.certificateNumber,
          certificateType: company.certificateType,
          certificateStatus: company.certificateStatus,
          issuedDate: company.issuedDate,
          expiryDate: company.expiryDate,
          sourceUrl: company.sourceUrl,
          createdAt: company.createdAt,
          updatedAt: company.updatedAt,
        })),
        pagination: {
          page,
          limit,
          total,
          hasMore,
          totalPages: Math.ceil(total / limit),
        },
        query: '',
      });
    } catch (error) {
      return handleApiError(error, 'Internal server error');
    }
  }

  try {
    // Build search conditions with whole word matching to avoid irrelevant partial matches
    const searchTerm = query.trim();

    // Create word boundary patterns for whole word matching
    const wholeWordPatterns = [
      `${searchTerm}`, // Exact match
      `${searchTerm} %`, // Word at start
      `% ${searchTerm}`, // Word at end
      `% ${searchTerm} %`, // Word in middle
    ];

    // For registration numbers and certificate numbers, allow partial matching
    const partialSearchTerm = `%${searchTerm}%`;

    // Build base search conditions
    const baseConditions = [eq(companies.siteId, getHalalSelangorSiteId())];

    // Add category filter if provided using junction table
    if (category) {
      // First, find the category ID by name
      const categoryRecord = await db
        .select({ id: categories.id })
        .from(categories)
        .where(eq(categories.categoryName, category))
        .limit(1);

      if (categoryRecord.length > 0) {
        const categoryId = categoryRecord[0].id;

        // Get company IDs that belong to this category
        const companyCategoryLinks = await db
          .select({ companyId: companyCategories.companyId })
          .from(companyCategories)
          .where(eq(companyCategories.categoryId, categoryId));

        const companyIdsFromCategory = companyCategoryLinks.map((link: any) => link.companyId);

        if (companyIdsFromCategory.length > 0) {
          baseConditions.push(inArray(companies.id, companyIdsFromCategory));
        } else {
          // No companies found for this category, also check legacy category field
          const legacyConditions = [
            ilike(companies.category, `%${category}%`),
            ilike(companies.subcategory, `%${category}%`)
          ];
          const orCondition = or(...legacyConditions);
          if (orCondition) {
            baseConditions.push(orCondition);
          }
        }
      } else {
        // Category not found in categories table, fall back to legacy search
        const legacyConditions = [
          ilike(companies.category, `%${category}%`),
          ilike(companies.subcategory, `%${category}%`)
        ];
        const orCondition = or(...legacyConditions);
        if (orCondition) {
          baseConditions.push(orCondition);
        }
      }
    }

    // Add state filter if provided
    if (state) {
      baseConditions.push(eq(companies.state, state));
    }

    // Add search query conditions
    const searchQueryConditions = [
      // Primary fields with whole word matching
      ...wholeWordPatterns.map(pattern => ilike(companies.companyName, pattern)),

      // Registration and certificate numbers can use partial matching
      ilike(companies.registrationNumber, partialSearchTerm),
      ilike(companies.certificateNumber, partialSearchTerm),

      // Business type and category with whole word matching
      ...wholeWordPatterns.map(pattern => ilike(companies.businessType, pattern)),
      ...wholeWordPatterns.map(pattern => ilike(companies.category, pattern)),
      ...wholeWordPatterns.map(pattern => ilike(companies.subcategory, pattern)),

      // For longer queries (4+ chars), include address fields with whole word matching
      ...(searchTerm.length >= 4 ? [
        ...wholeWordPatterns.map(pattern => ilike(companies.address, pattern)),
        ...wholeWordPatterns.map(pattern => ilike(companies.city, pattern)),
        ...wholeWordPatterns.map(pattern => ilike(companies.state, pattern)),
        ...wholeWordPatterns.map(pattern => ilike(companies.contactPerson, pattern)),
        ...wholeWordPatterns.map(pattern => ilike(companies.certificateType, pattern)),
        ...wholeWordPatterns.map(pattern => ilike(companies.certificateStatus, pattern)),
      ] : []),
    ];

    if (searchQueryConditions.length > 0) {
      const orCondition = or(...searchQueryConditions);
      if (orCondition) {
        baseConditions.push(orCondition);
      }
    }

    const searchConditions = and(...baseConditions);

    console.log('Company search strategy:', {
      query: searchTerm,
      queryLength: searchTerm.length,
      searchStrategy: 'whole-word-matching',
      includeAddressFields: searchTerm.length >= 4,
    });

    // Get total count for pagination
    const totalResults = await db
      .select({ count: count() })
      .from(companies)
      .where(searchConditions);

    const total = totalResults[0]?.count || 0;

    // Calculate pagination
    const offset = (page - 1) * limit;
    const hasMore = offset + limit < total;

    // Get paginated results
    const searchResults = await db
      .select({
        id: companies.id,
        companyName: companies.companyName,
        registrationNumber: companies.registrationNumber,
        businessType: companies.businessType,
        category: companies.category,
        subcategory: companies.subcategory,
        address: companies.address,
        state: companies.state,
        postcode: companies.postcode,
        city: companies.city,
        country: companies.country,
        phone: companies.phone,
        fax: companies.fax,
        email: companies.email,
        website: companies.website,
        contactPerson: companies.contactPerson,
        certificateNumber: companies.certificateNumber,
        certificateType: companies.certificateType,
        certificateStatus: companies.certificateStatus,
        issuedDate: companies.issuedDate,
        expiryDate: companies.expiryDate,
        sourceUrl: companies.sourceUrl,
        createdAt: companies.createdAt,
        updatedAt: companies.updatedAt,
      })
      .from(companies)
      .where(searchConditions)
      .limit(limit)
      .offset(offset)
      .orderBy(companies.companyName);

    console.log(
      `Company search found ${total} results, returning ${searchResults.length} for page ${page}`,
    );

    // Track search analytics
    const analyticsData = extractAnalyticsFromRequest(request);
    await trackSearchAnalytics({
      siteId: getHalalSelangorSiteId(),
      searchQuery: query,
      searchType: 'companies',
      resultsCount: searchResults.length,
      hasResults: searchResults.length > 0,
      searchFilters: JSON.stringify({ page, limit, category, state }),
      ...analyticsData,
    });

    return createSuccessResponse({
      companies: searchResults.map((company: any) => ({
        id: company.id,
        name: company.companyName,
        registrationNumber: company.registrationNumber,
        businessType: company.businessType,
        category: company.category,
        subcategory: company.subcategory,
        address: company.address,
        state: getStateName(company.state),
        postcode: company.postcode,
        city: company.city,
        country: company.country,
        phone: company.phone,
        fax: company.fax,
        email: company.email,
        website: company.website,
        contactPerson: company.contactPerson,
        certificateNumber: company.certificateNumber,
        certificateType: company.certificateType,
        certificateStatus: company.certificateStatus,
        issuedDate: company.issuedDate,
        expiryDate: company.expiryDate,
        sourceUrl: company.sourceUrl,
        createdAt: company.createdAt,
        updatedAt: company.updatedAt,
      })),
      pagination: {
        page,
        limit,
        total,
        hasMore,
        totalPages: Math.ceil(total / limit),
      },
      query,
    });
  } catch (error) {
    return handleApiError(error, 'Company search error');
  }
}
