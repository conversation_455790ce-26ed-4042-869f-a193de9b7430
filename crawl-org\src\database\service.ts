import { and, eq, sql } from 'drizzle-orm';
import {
  type Company,
  companies,
  initializeDatabase,
  type NewCompany,
} from '../db/connection';
import type { ScrapedCompany, ScrapingStats } from '../types';
import { DataValidator } from '../utils/validation';

export class DatabaseService {
  private db: ReturnType<typeof initializeDatabase>;

  constructor() {
    this.db = initializeDatabase();
  }

  /**
   * Save a single company to database with duplicate detection
   */
  async saveCompany(
    scrapedCompany: ScrapedCompany,
    siteId: number,
  ): Promise<{
    saved: boolean;
    duplicate: boolean;
    error?: string;
  }> {
    try {
      // Debug: Check the company object
      console.log(
        'Database service received company:',
        typeof scrapedCompany,
        scrapedCompany,
      );
      console.log('Company name from object:', scrapedCompany?.companyName);

      // Validate the company data
      const validationResult = DataValidator.validateCompany(scrapedCompany);
      console.log('Validation result:', validationResult);

      if (!validationResult.isValid) {
        console.log('Validation failed with errors:', validationResult.errors);
        return {
          saved: false,
          duplicate: false,
          error: `Validation failed: ${validationResult.errors.join(', ')}`,
        };
      }

      console.log('Validation passed, getting normalized data...');
      const normalizedCompany = validationResult.normalizedData!;
      console.log('Normalized company:', normalizedCompany);

      // Generate data hash for duplicate detection
      const dataHash = DataValidator.generateDataHash(normalizedCompany);

      // Check for duplicates
      const existingCompany = await this.findCompanyByHash(dataHash);
      if (existingCompany) {
        return {
          saved: false,
          duplicate: true,
        };
      }

      // Prepare company data for insertion
      const companyData: NewCompany = {
        siteId,
        companyName: normalizedCompany.companyName,
        registrationNumber: normalizedCompany.registrationNumber,
        businessType: normalizedCompany.businessType,
        category: normalizedCompany.category,
        subcategory: normalizedCompany.subcategory,
        address: normalizedCompany.address,
        state: normalizedCompany.state,
        postcode: normalizedCompany.postcode,
        city: normalizedCompany.city,
        country: normalizedCompany.country,
        phone: normalizedCompany.phone,
        fax: normalizedCompany.fax,
        email: normalizedCompany.email,
        website: normalizedCompany.website,
        contactPerson: normalizedCompany.contactPerson,
        certificateNumber: normalizedCompany.certificateNumber,
        certificateType: normalizedCompany.certificateType,
        certificateStatus: normalizedCompany.certificateStatus,
        issuedDate: normalizedCompany.issuedDate,
        expiryDate: normalizedCompany.expiryDate,
        sourceUrl: normalizedCompany.sourceUrl,
        pageNumber: normalizedCompany.pageNumber,
        rawData: normalizedCompany.rawData,
        dataHash,
      };

      // Insert the company
      await this.db.insert(companies).values(companyData);

      return {
        saved: true,
        duplicate: false,
      };
    } catch (error) {
      return {
        saved: false,
        duplicate: false,
        error: `Error saving ${scrapedCompany.companyName}: ${error}`,
      };
    }
  }

  /**
   * Save companies to database with duplicate detection (batch operation)
   */
  async saveCompanies(
    scrapedCompanies: ScrapedCompany[],
    siteId: number,
  ): Promise<{
    saved: number;
    duplicates: number;
    errors: number;
    errorMessages: string[];
  }> {
    let saved = 0;
    let duplicates = 0;
    let errors = 0;
    const errorMessages: string[] = [];

    for (const scrapedCompany of scrapedCompanies) {
      try {
        // Validate the company data
        const validation = DataValidator.validateCompany(scrapedCompany);
        if (!validation.isValid) {
          errors++;
          errorMessages.push(
            `Invalid data for ${scrapedCompany.companyName}: ${validation.errors.join(', ')}`,
          );
          continue;
        }

        // Normalize the data
        const normalizedCompany =
          DataValidator.normalizeCompany(scrapedCompany);

        // Generate hash for duplicate detection
        const dataHash = DataValidator.generateDataHash(normalizedCompany);

        // Check if company already exists
        const existingCompany = await this.findCompanyByHash(dataHash);
        if (existingCompany) {
          duplicates++;
          continue;
        }

        // Prepare company data for insertion
        const companyData: NewCompany = {
          siteId,
          companyName: normalizedCompany.companyName!,
          registrationNumber: normalizedCompany.registrationNumber,
          businessType: normalizedCompany.businessType,
          category: normalizedCompany.category,
          subcategory: normalizedCompany.subcategory,
          address: normalizedCompany.address,
          state: normalizedCompany.state,
          postcode: normalizedCompany.postcode,
          city: normalizedCompany.city,
          country: normalizedCompany.country,
          phone: normalizedCompany.phone,
          fax: normalizedCompany.fax,
          email: normalizedCompany.email,
          website: normalizedCompany.website,
          contactPerson: normalizedCompany.contactPerson,
          certificateNumber: normalizedCompany.certificateNumber,
          certificateType: normalizedCompany.certificateType,
          certificateStatus: normalizedCompany.certificateStatus,
          issuedDate: normalizedCompany.issuedDate,
          expiryDate: normalizedCompany.expiryDate,
          sourceUrl: normalizedCompany.sourceUrl,
          pageNumber: normalizedCompany.pageNumber,
          rawData: normalizedCompany.rawData,
          dataHash,
        };

        // Insert the company
        await this.db.insert(companies).values(companyData);
        saved++;
      } catch (error) {
        errors++;
        errorMessages.push(
          `Error saving ${scrapedCompany.companyName}: ${error}`,
        );
        console.error(
          `Error saving company ${scrapedCompany.companyName}:`,
          error,
        );
      }
    }

    return { saved, duplicates, errors, errorMessages };
  }

  /**
   * Find company by data hash
   */
  async findCompanyByHash(dataHash: string): Promise<Company | null> {
    try {
      const result = await this.db
        .select()
        .from(companies)
        .where(eq(companies.dataHash, dataHash))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      console.error('Error finding company by hash:', error);
      return null;
    }
  }

  /**
   * Get companies by site ID
   */
  async getCompaniesBySite(
    siteId: number,
    limit = 100,
    offset = 0,
  ): Promise<Company[]> {
    try {
      return await this.db
        .select()
        .from(companies)
        .where(eq(companies.siteId, siteId))
        .limit(limit)
        .offset(offset);
    } catch (error) {
      console.error('Error getting companies by site:', error);
      return [];
    }
  }

  /**
   * Get total count of companies for a site
   */
  async getCompanyCount(siteId: number): Promise<number> {
    try {
      const result = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(companies)
        .where(eq(companies.siteId, siteId));

      return result[0]?.count || 0;
    } catch (error) {
      console.error('Error getting company count:', error);
      return 0;
    }
  }

  /**
   * Search companies by name
   */
  async searchCompaniesByName(
    siteId: number,
    searchTerm: string,
    limit = 50,
  ): Promise<Company[]> {
    try {
      return await this.db
        .select()
        .from(companies)
        .where(
          and(
            eq(companies.siteId, siteId),
            sql`${companies.companyName} ILIKE ${`%${searchTerm}%`}`,
          ),
        )
        .limit(limit);
    } catch (error) {
      console.error('Error searching companies:', error);
      return [];
    }
  }

  /**
   * Get scraping statistics
   */
  async getScrapingStats(siteId: number): Promise<{
    totalCompanies: number;
    companiesByState: Record<string, number>;
    companiesByCategory: Record<string, number>;
    recentlyAdded: number;
  }> {
    try {
      const totalCompanies = await this.getCompanyCount(siteId);

      // Get companies by state
      const stateResults = await this.db
        .select({
          state: companies.state,
          count: sql<number>`count(*)`,
        })
        .from(companies)
        .where(eq(companies.siteId, siteId))
        .groupBy(companies.state);

      const companiesByState: Record<string, number> = {};
      stateResults.forEach((row) => {
        if (row.state) {
          companiesByState[row.state] = row.count;
        }
      });

      // Get companies by category
      const categoryResults = await this.db
        .select({
          category: companies.category,
          count: sql<number>`count(*)`,
        })
        .from(companies)
        .where(eq(companies.siteId, siteId))
        .groupBy(companies.category);

      const companiesByCategory: Record<string, number> = {};
      categoryResults.forEach((row) => {
        if (row.category) {
          companiesByCategory[row.category] = row.count;
        }
      });

      // Get recently added (last 24 hours)
      const recentResults = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(companies)
        .where(
          and(
            eq(companies.siteId, siteId),
            sql`${companies.createdAt} > NOW() - INTERVAL '24 hours'`,
          ),
        );

      const recentlyAdded = recentResults[0]?.count || 0;

      return {
        totalCompanies,
        companiesByState,
        companiesByCategory,
        recentlyAdded,
      };
    } catch (error) {
      console.error('Error getting scraping stats:', error);
      return {
        totalCompanies: 0,
        companiesByState: {},
        companiesByCategory: {},
        recentlyAdded: 0,
      };
    }
  }

  /**
   * Test database connection
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.db.select().from(companies).limit(1);
      return true;
    } catch (error) {
      console.error('Database connection test failed:', error);
      return false;
    }
  }

  /**
   * Clean up old duplicate entries (optional maintenance function)
   */
  async cleanupDuplicates(siteId: number): Promise<number> {
    try {
      // This is a complex operation that would identify and remove duplicates
      // For now, we'll just return 0 as this would need careful implementation
      console.log(
        `Cleanup duplicates for site ${siteId} - not implemented yet`,
      );
      return 0;
    } catch (error) {
      console.error('Error cleaning up duplicates:', error);
      return 0;
    }
  }
}
