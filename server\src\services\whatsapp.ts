import crypto from 'node:crypto';
import axios from 'axios';
import type {
  WhatsAppConfig,
  WhatsAppMessage,
  WhatsAppSendMessageRequest,
  WhatsAppSendMessageResponse,
  WhatsAppWebhookPayload,
} from '../types';

class WhatsAppService {
  private config: WhatsAppConfig | null = null;
  private readonly baseUrl = 'https://graph.facebook.com/v18.0';

  constructor() {
    this.loadConfig();
  }

  private async loadConfig(): Promise<void> {
    try {
      // For now, we'll skip loading config from database to avoid circular dependencies
      // This will be loaded when needed
      this.config = null;
    } catch (error) {
      console.error('Failed to load WhatsApp config:', error);
    }
  }

  async updateConfig(
    newConfig: Omit<WhatsAppConfig, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<void> {
    try {
      // For now, just store in memory - database integration will be added later
      this.config = {
        ...newConfig,
        id: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    } catch (error) {
      console.error('Failed to update WhatsApp config:', error);
      throw error;
    }
  }

  isConfigured(): boolean {
    return this.config?.isActive;
  }

  getConfig(): WhatsAppConfig | null {
    return this.config;
  }

  // Webhook verification for WhatsApp Business API
  verifyWebhook(mode: string, token: string, challenge: string): string | null {
    if (!this.config) {
      console.error('WhatsApp not configured');
      return null;
    }

    if (mode === 'subscribe' && token === this.config.webhookVerifyToken) {
      console.log('Webhook verified successfully');
      return challenge;
    }

    console.error('Webhook verification failed');
    return null;
  }

  // Verify webhook signature for security
  verifyWebhookSignature(payload: string, signature: string): boolean {
    if (!this.config) {
      return false;
    }

    const expectedSignature = crypto
      .createHmac('sha256', this.config.webhookVerifyToken)
      .update(payload)
      .digest('hex');

    return crypto.timingSafeEqual(
      Buffer.from(`sha256=${expectedSignature}`),
      Buffer.from(signature),
    );
  }

  // Send text message via WhatsApp Business API
  async sendTextMessage(
    to: string,
    message: string,
  ): Promise<WhatsAppSendMessageResponse> {
    if (!this.isConfigured()) {
      return {
        success: false,
        error: 'WhatsApp not configured',
      };
    }

    try {
      const requestData: WhatsAppSendMessageRequest = {
        to: to.replace(/\D/g, ''), // Remove non-digits
        type: 'text',
        text: {
          body: message,
        },
      };

      const response = await axios.post(
        `${this.baseUrl}/${this.config?.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          ...requestData,
        },
        {
          headers: {
            Authorization: `Bearer ${this.config?.accessToken}`,
            'Content-Type': 'application/json',
          },
        },
      );

      const messageId = response.data.messages[0].id;

      // Log the outbound message
      // await dbService.logWhatsAppMessage(
      //   messageId,
      //   this.config!.phoneNumberId,
      //   to,
      //   'text',
      //   message,
      //   'outbound'
      // );

      return {
        success: true,
        messageId,
      };
    } catch (error: any) {
      console.error(
        'Failed to send WhatsApp message:',
        error.response?.data || error.message,
      );
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
      };
    }
  }

  // Send image message via WhatsApp Business API
  async sendImageMessage(
    to: string,
    imageUrl: string,
    caption?: string,
  ): Promise<WhatsAppSendMessageResponse> {
    if (!this.isConfigured()) {
      return {
        success: false,
        error: 'WhatsApp not configured',
      };
    }

    try {
      const requestData: WhatsAppSendMessageRequest = {
        to: to.replace(/\D/g, ''),
        type: 'image',
        image: {
          link: imageUrl,
          caption,
        },
      };

      const response = await axios.post(
        `${this.baseUrl}/${this.config?.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          ...requestData,
        },
        {
          headers: {
            Authorization: `Bearer ${this.config?.accessToken}`,
            'Content-Type': 'application/json',
          },
        },
      );

      const messageId = response.data.messages[0].id;

      // Log the outbound message
      // await dbService.logWhatsAppMessage(
      //   messageId,
      //   this.config!.phoneNumberId,
      //   to,
      //   'image',
      //   caption || 'Image',
      //   'outbound',
      //   imageUrl
      // );

      return {
        success: true,
        messageId,
      };
    } catch (error: any) {
      console.error(
        'Failed to send WhatsApp image:',
        error.response?.data || error.message,
      );
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
      };
    }
  }

  // Process incoming webhook messages
  async processWebhookMessage(
    payload: WhatsAppWebhookPayload,
  ): Promise<WhatsAppMessage[]> {
    const messages: WhatsAppMessage[] = [];

    try {
      for (const entry of payload.entry) {
        for (const change of entry.changes) {
          if (change.field === 'messages' && change.value.messages) {
            for (const message of change.value.messages) {
              const whatsappMessage: WhatsAppMessage = {
                id: message.id,
                from: message.from,
                to: change.value.metadata.phone_number_id,
                type: message.type as 'text' | 'image' | 'audio' | 'document',
                content: message.text?.body || `${message.type} message`,
                timestamp: new Date(Number.parseInt(message.timestamp) * 1000),
              };

              // Handle different message types
              if (message.image) {
                whatsappMessage.mediaUrl = await this.getMediaUrl(
                  message.image.id,
                );
              } else if (message.audio) {
                whatsappMessage.mediaUrl = await this.getMediaUrl(
                  message.audio.id,
                );
              }

              // Log the inbound message
              // await dbService.logWhatsAppMessage(
              //   whatsappMessage.id,
              //   whatsappMessage.from,
              //   whatsappMessage.to,
              //   whatsappMessage.type,
              //   whatsappMessage.content,
              //   'inbound',
              //   whatsappMessage.mediaUrl
              // );

              messages.push(whatsappMessage);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error processing webhook message:', error);
    }

    return messages;
  }

  // Get media URL from WhatsApp Business API
  private async getMediaUrl(mediaId: string): Promise<string | undefined> {
    if (!this.isConfigured()) {
      return undefined;
    }

    try {
      const response = await axios.get(`${this.baseUrl}/${mediaId}`, {
        headers: {
          Authorization: `Bearer ${this.config?.accessToken}`,
        },
      });

      return response.data.url;
    } catch (error) {
      console.error('Failed to get media URL:', error);
      return undefined;
    }
  }

  // Test WhatsApp configuration
  async testConfiguration(): Promise<{ success: boolean; error?: string }> {
    if (!this.isConfigured()) {
      return {
        success: false,
        error: 'WhatsApp not configured',
      };
    }

    try {
      // Test by getting phone number info
      const _response = await axios.get(
        `${this.baseUrl}/${this.config?.phoneNumberId}`,
        {
          headers: {
            Authorization: `Bearer ${this.config?.accessToken}`,
          },
        },
      );

      return {
        success: true,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
      };
    }
  }
}

export default new WhatsAppService();
