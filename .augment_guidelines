
monorepo of:

# front

- nextjs frontend
- use pnpm for package management
- call backend api via API_BASE_URL set in nextjs config 
- when using useeffect, please always check to ensure the useeffect is called after all the dependencies are loaded or declared
- on dev, start on port 16000
- uses tailwind4
- admin url prefix: http://localhost:16000/[locale]/admin (also login admin here)
- agent url prefix: http://localhost:16000/[locale]/agent (also login agent here)
- do not use frontend/nextjs proxy to backend server

# server

- use pnpm for package management
- use typescript
- use drizzle for ORM (schema: server/src/db/schema.ts)
- when implementing new table or change on table schema, also generate the migration via ```drizzle-kit generate```, otherwise, always remember to add the migration entry into server/drizzle/meta/_journal.json
- use postgres for database
- environment files located in server/ (.env, .dev.vars, .env.production)
- all api should return { success: true/false, data: <data to return > }
- on dev, start on port 8787
- always refer to available script commands in server/package.json for server operations

# selangor

- uses nextjs, tailwind4
- uses drizzle for ORM (schema: server/src/db/schema.ts)
- uses postgres for database
- use pnpm for package management
- development user for postgres: root, password: "000000", host: 127.0.0.1 (hosted on external docker )
- use bun  for package management
- semantic search is for product and company only.
- do not use r2r for product / company semantic search. it does not contain product or company data.
- - do not use frontend/nextjs proxy to backend server

# crawler

- uses python in docker

# admin
- uses nextjs, tailwind4


# rules

- when making any changes, make sure bun run lint:fix and fix all errors
- use biome for all linting, type and formatting
- sample admin login: admin, password: admin123
- sample agent login: agent, password: agent123
- if you think a bigger change would be beneficial, explain why
- always suggest best practices even if user requests something suboptimal
- if user asks for something that could be done better, explain the better approach and ask which way to proceed
- explain everything in simple, clear language - no fancy technical jargon
- break down complex concepts into easy-to-understand parts
- server uses Cloudflare Workers - avoid Node.js specific features (fs, path, etc.)
- if user suggests something that won't work in Workers, suggest Workers-compatible alternatives
- remind user about Workers limitations when relevant
- when changing db schema, make sure to update the migration script via ```drizzle-kit generate```
- ensure not sqlite db, log files are in git
