'use client';

export const runtime = 'edge';

import type { BreadcrumbItem } from '@/components/breadcrumb';
import { PageWrapper } from '@/components/page-wrapper';
import { useLanguage } from '@/lib/language-context';

export default function HalalInfoPage() {
  const { language } = useLanguage();

  const breadcrumbs: BreadcrumbItem[] = [
    {
      label: language === 'en' ? 'Home' : 'Laman <PERSON>',
      href: '/',
    },
    {
      label: language === 'en' ? 'Halal Information' : 'Maklumat Halal',
      href: '/info',
    },
  ];

  const halalCategories = [
    {
      title: 'Food Products',
      titleBM: 'Produk Makanan',
      description:
        'Meat, poultry, seafood, dairy, beverages, and processed foods',
      descriptionBM:
        'Daging, ayam, makanan laut, tenusu, minuman, dan makanan diproses',
      icon: '🍽️',
    },
    {
      title: 'Cosmetics & Personal Care',
      titleBM: 'Kosmetik & Penjagaan Diri',
      description: 'Skincare, makeup, perfumes, and personal hygiene products',
      descriptionBM:
        'Penjagaan kulit, solekan, minyak wangi, dan produk kebersihan diri',
      icon: '💄',
    },
    {
      title: 'Pharmaceuticals',
      titleBM: 'Farmaseutikal',
      description: 'Medicines, supplements, and healthcare products',
      descriptionBM:
        'Ubat-ubatan, makanan tambahan, dan produk penjagaan kesihatan',
      icon: '💊',
    },
    {
      title: 'Food Services',
      titleBM: 'Perkhidmatan Makanan',
      description: 'Restaurants, catering, and food service establishments',
      descriptionBM: 'Restoran, katering, dan pertubuhan perkhidmatan makanan',
      icon: '🏪',
    },
  ];

  const prohibitedItems = [
    {
      item: 'Pork and pork derivatives',
      itemBM: 'Daging babi dan terbitan babi',
      reason: 'Explicitly forbidden in Islam',
      reasonBM: 'Dilarang secara jelas dalam Islam',
    },
    {
      item: 'Alcohol and alcoholic beverages',
      itemBM: 'Alkohol dan minuman beralkohol',
      reason: 'Intoxicating substances are prohibited',
      reasonBM: 'Bahan memabukkan adalah dilarang',
    },
    {
      item: 'Non-Halal animal derivatives',
      itemBM: 'Terbitan haiwan bukan Halal',
      reason: 'From animals not slaughtered according to Islamic law',
      reasonBM: 'Daripada haiwan yang tidak disembelih mengikut hukum Islam',
    },
    {
      item: 'Blood and blood products',
      itemBM: 'Darah dan produk darah',
      reason: 'Consumption of blood is forbidden',
      reasonBM: 'Pengambilan darah adalah dilarang',
    },
  ];

  return (
    <PageWrapper
      title="Halal Information"
      titleBM="Maklumat Halal"
      description="Comprehensive information about Halal certification, requirements, and Islamic dietary laws."
      descriptionBM="Maklumat komprehensif mengenai pensijilan Halal, keperluan, dan undang-undang pemakanan Islam."
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-8">
        {/* Introduction */}
        <div className="card">
          <h2 className="text-2xl font-bold mb-4 text-gray-900">
            {language === 'en' ? 'Understanding Halal' : 'Memahami Halal'}
          </h2>
          <p className="text-gray-600 leading-relaxed mb-4">
            {language === 'en'
              ? 'Halal is an Arabic term meaning "permissible" or "lawful" in Islam. It refers to anything that is allowed under Islamic law (Shariah). In the context of food and products, Halal certification ensures that items comply with Islamic dietary laws and are suitable for consumption by Muslims.'
              : 'Halal adalah istilah Arab yang bermaksud "dibenarkan" atau "halal" dalam Islam. Ia merujuk kepada apa-apa yang dibenarkan di bawah undang-undang Islam (Syariah). Dalam konteks makanan dan produk, pensijilan Halal memastikan bahawa item mematuhi undang-undang pemakanan Islam dan sesuai untuk dimakan oleh umat Islam.'}
          </p>
          <p className="text-gray-600 leading-relaxed">
            {language === 'en'
              ? 'JAKIM, as the sole authority for Halal certification in Malaysia, ensures that all certified products meet the highest standards of Halal integrity, providing confidence to Muslim consumers worldwide.'
              : 'JAKIM, sebagai pihak berkuasa tunggal untuk pensijilan Halal di Malaysia, memastikan bahawa semua produk yang disijilkan memenuhi piawaian integriti Halal tertinggi, memberikan keyakinan kepada pengguna Islam di seluruh dunia.'}
          </p>
        </div>

        {/* Halal Principles */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Fundamental Halal Principles'
              : 'Prinsip Asas Halal'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en'
                      ? 'Source Compliance'
                      : 'Pematuhan Sumber'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'All ingredients and materials must come from Halal sources'
                      : 'Semua ramuan dan bahan mesti datang daripada sumber Halal'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en'
                      ? 'Processing Methods'
                      : 'Kaedah Pemprosesan'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Manufacturing processes must not contaminate Halal products'
                      : 'Proses pembuatan tidak boleh mencemarkan produk Halal'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en'
                      ? 'Cleanliness & Hygiene'
                      : 'Kebersihan & Kebersihan'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Strict hygiene and cleanliness standards must be maintained'
                      : 'Piawaian kebersihan dan kebersihan yang ketat mesti dikekalkan'}
                  </p>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en'
                      ? 'Slaughter Requirements'
                      : 'Keperluan Penyembelihan'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Animals must be slaughtered according to Islamic law'
                      : 'Haiwan mesti disembelih mengikut hukum Islam'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en' ? 'Segregation' : 'Pengasingan'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Halal products must be kept separate from non-Halal items'
                      : 'Produk Halal mesti diasingkan daripada item bukan Halal'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en' ? 'Traceability' : 'Kebolehsurihan'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Complete traceability of ingredients and supply chain'
                      : 'Kebolehsurihan lengkap ramuan dan rantaian bekalan'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Certification Categories */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Halal Certification Categories'
              : 'Kategori Pensijilan Halal'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {halalCategories.map((category, index) => (
              <div
                key={index}
                className="p-4 border border-gray-200 rounded-lg hover:border-primary-green transition-colors"
              >
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">{category.icon}</div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 mb-2">
                      {language === 'bm' ? category.titleBM : category.title}
                    </h4>
                    <p className="text-gray-600 text-sm">
                      {language === 'bm'
                        ? category.descriptionBM
                        : category.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Prohibited Items */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Prohibited (Haram) Items'
              : 'Item yang Dilarang (Haram)'}
          </h3>
          <div className="space-y-4">
            {prohibitedItems.map((item, index) => (
              <div
                key={index}
                className="p-4 bg-red-50 border border-red-200 rounded-lg"
              >
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0" />
                  <div className="flex-1">
                    <h4 className="font-semibold text-red-800 mb-1">
                      {language === 'bm' ? item.itemBM : item.item}
                    </h4>
                    <p className="text-red-700 text-sm">
                      {language === 'bm' ? item.reasonBM : item.reason}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Halal Logo */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Malaysian Halal Logo' : 'Logo Halal Malaysia'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
            <div>
              <p className="text-gray-600 leading-relaxed mb-4">
                {language === 'en'
                  ? 'The Malaysian Halal logo is a symbol of trust and quality, recognized globally. Products bearing this logo have undergone rigorous certification processes and comply with Malaysian Halal standards.'
                  : 'Logo Halal Malaysia adalah simbol kepercayaan dan kualiti, diiktiraf secara global. Produk yang mempunyai logo ini telah melalui proses pensijilan yang ketat dan mematuhi piawaian Halal Malaysia.'}
              </p>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-primary-green rounded-full" />
                  <span className="text-sm text-gray-600">
                    {language === 'en'
                      ? 'Internationally recognized'
                      : 'Diiktiraf di peringkat antarabangsa'}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-primary-green rounded-full" />
                  <span className="text-sm text-gray-600">
                    {language === 'en'
                      ? 'Rigorous certification process'
                      : 'Proses pensijilan yang ketat'}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-primary-green rounded-full" />
                  <span className="text-sm text-gray-600">
                    {language === 'en'
                      ? 'Regular monitoring and surveillance'
                      : 'Pemantauan dan pengawasan berkala'}
                  </span>
                </div>
              </div>
            </div>
            <div className="text-center">
              <div className="w-32 h-32 bg-primary-green bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-4xl">🏷️</span>
              </div>
              <p className="text-sm text-gray-500">
                {language === 'en'
                  ? 'Malaysian Halal Logo'
                  : 'Logo Halal Malaysia'}
              </p>
            </div>
          </div>
        </div>

        {/* Consumer Guidelines */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Consumer Guidelines' : 'Panduan Pengguna'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">
                {language === 'en'
                  ? 'What to Look For'
                  : 'Apa yang Perlu Dicari'}
              </h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Valid Halal certificate displayed'
                    : 'Sijil Halal yang sah dipamerkan'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Malaysian Halal logo on products'
                    : 'Logo Halal Malaysia pada produk'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Certificate expiry date'
                    : 'Tarikh tamat tempoh sijil'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Proper storage and handling'
                    : 'Penyimpanan dan pengendalian yang betul'}
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">
                {language === 'en' ? 'How to Verify' : 'Cara Mengesahkan'}
              </h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Check certificate authenticity online'
                    : 'Semak keaslian sijil dalam talian'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Verify company registration'
                    : 'Sahkan pendaftaran syarikat'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Report suspicious products'
                    : 'Laporkan produk yang mencurigakan'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Contact JAKIM for verification'
                    : 'Hubungi JAKIM untuk pengesahan'}
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Frequently Asked Questions' : 'Soalan Lazim'}
          </h3>
          <div className="space-y-4">
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en'
                  ? 'How long is a Halal certificate valid?'
                  : 'Berapa lama sijil Halal sah?'}
              </h4>
              <p className="text-gray-600 text-sm">
                {language === 'en'
                  ? 'Halal certificates are typically valid for 2 years from the date of issuance and must be renewed before expiry.'
                  : 'Sijil Halal biasanya sah selama 2 tahun dari tarikh pengeluaran dan mesti diperbaharui sebelum tamat tempoh.'}
              </p>
            </div>
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en'
                  ? 'Can non-Muslim companies apply for Halal certification?'
                  : 'Bolehkah syarikat bukan Islam memohon pensijilan Halal?'}
              </h4>
              <p className="text-gray-600 text-sm">
                {language === 'en'
                  ? 'Yes, non-Muslim companies can apply for Halal certification, but they must appoint a Muslim Halal executive and comply with all Halal requirements.'
                  : 'Ya, syarikat bukan Islam boleh memohon pensijilan Halal, tetapi mereka mesti melantik eksekutif Halal Muslim dan mematuhi semua keperluan Halal.'}
              </p>
            </div>
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en'
                  ? 'What happens if a certified product is found to be non-compliant?'
                  : 'Apa yang berlaku jika produk yang disijilkan didapati tidak mematuhi?'}
              </h4>
              <p className="text-gray-600 text-sm">
                {language === 'en'
                  ? 'JAKIM will investigate the issue and may suspend or revoke the certificate depending on the severity of non-compliance.'
                  : 'JAKIM akan menyiasat isu tersebut dan mungkin menggantung atau membatalkan sijil bergantung kepada keterukan ketidakpatuhan.'}
              </p>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="card bg-bg-light-green">
          <h3 className="text-xl font-bold mb-4 text-gray-900">
            {language === 'en'
              ? 'Need More Information?'
              : 'Perlukan Maklumat Lanjut?'}
          </h3>
          <p className="text-gray-600 mb-4">
            {language === 'en'
              ? 'For more detailed information about Halal certification or to clarify any doubts, please contact our information center.'
              : 'Untuk maklumat lebih terperinci mengenai pensijilan Halal atau untuk menjelaskan sebarang keraguan, sila hubungi pusat maklumat kami.'}
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="text-sm">
              <span className="font-medium">
                {language === 'en' ? 'Phone:' : 'Telefon:'}
              </span>{' '}
              03-8892 5000
            </div>
            <div className="text-sm">
              <span className="font-medium">
                {language === 'en' ? 'Email:' : 'E-mel:'}
              </span>{' '}
              <EMAIL>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}
