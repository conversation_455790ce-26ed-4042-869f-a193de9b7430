<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .message-log {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        input, select {
            margin: 5px;
            padding: 5px;
        }
    </style>
</head>
<body>
    <h1>WebSocket Test Client</h1>
    
    <div id="status" class="status disconnected">
        Disconnected
    </div>
    
    <div class="controls">
        <button id="connectBtn" onclick="connect()">Connect</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
        <button onclick="ping()">Send Ping</button>
    </div>
    
    <div class="controls">
        <h3>Registration</h3>
        <select id="connectionType">
            <option value="user">User</option>
            <option value="agent">Agent</option>
        </select>
        <input type="text" id="sessionId" placeholder="Session ID" value="test-session-123">
        <input type="text" id="agentId" placeholder="Agent ID" value="1">
        <button onclick="register()">Register</button>
    </div>
    
    <div class="controls">
        <h3>Send Messages</h3>
        <input type="text" id="messageContent" placeholder="Message content" value="Hello from test client">
        <button onclick="sendUserMessage()">Send User Message</button>
        <button onclick="sendAgentMessage()">Send Agent Message</button>
    </div>
    
    <h3>Message Log</h3>
    <div id="messageLog" class="message-log"></div>
    <button onclick="clearLog()">Clear Log</button>

    <script>
        let ws = null;
        let isConnected = false;

        function updateStatus(connected) {
            isConnected = connected;
            const statusEl = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            if (connected) {
                statusEl.textContent = 'Connected';
                statusEl.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusEl.textContent = 'Disconnected';
                statusEl.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        function logMessage(message, type = 'info') {
            const log = document.getElementById('messageLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<strong>[${timestamp}] ${type.toUpperCase()}:</strong> ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }

        function connect() {
            if (ws) {
                ws.close();
            }

            const wsUrl = 'ws://localhost:16001/ws';
            logMessage(`Connecting to ${wsUrl}...`);
            
            ws = new WebSocket(wsUrl);

            ws.onopen = function(event) {
                logMessage('WebSocket connection opened');
                updateStatus(true);
            };

            ws.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    logMessage(`Received: ${JSON.stringify(message, null, 2)}`, 'received');
                } catch (error) {
                    logMessage(`Received (raw): ${event.data}`, 'received');
                }
            };

            ws.onclose = function(event) {
                logMessage(`WebSocket connection closed (code: ${event.code})`);
                updateStatus(false);
            };

            ws.onerror = function(error) {
                logMessage(`WebSocket error: ${error}`, 'error');
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function sendMessage(message) {
            if (!isConnected || !ws) {
                logMessage('Not connected to WebSocket', 'error');
                return;
            }

            const messageStr = JSON.stringify(message);
            ws.send(messageStr);
            logMessage(`Sent: ${messageStr}`, 'sent');
        }

        function ping() {
            sendMessage({
                type: 'ping',
                data: { timestamp: new Date().toISOString() }
            });
        }

        function register() {
            const connectionType = document.getElementById('connectionType').value;
            const sessionId = document.getElementById('sessionId').value;
            const agentId = document.getElementById('agentId').value;

            const data = {
                connectionType: connectionType,
                sessionId: sessionId
            };

            if (connectionType === 'agent') {
                data.agentId = parseInt(agentId);
            }

            sendMessage({
                type: 'register',
                data: data
            });
        }

        function sendUserMessage() {
            const content = document.getElementById('messageContent').value;
            const sessionId = document.getElementById('sessionId').value;

            sendMessage({
                type: 'user_message',
                data: {
                    sessionId: sessionId,
                    content: content,
                    userId: 'test-user'
                }
            });
        }

        function sendAgentMessage() {
            const content = document.getElementById('messageContent').value;
            const sessionId = document.getElementById('sessionId').value;
            const agentId = document.getElementById('agentId').value;

            sendMessage({
                type: 'agent_message',
                data: {
                    sessionId: sessionId,
                    content: content,
                    agentId: parseInt(agentId)
                }
            });
        }

        function clearLog() {
            document.getElementById('messageLog').innerHTML = '';
        }

        // Auto-connect on page load
        window.onload = function() {
            connect();
        };
    </script>
</body>
</html>
