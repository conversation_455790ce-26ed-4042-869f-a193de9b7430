# Halal Selangor Search Functionality

This document describes the R2R-based search functionality implemented in the Halal Selangor repository.

## Features

- **R2R Integration**: Uses R2R (Retrieval-Augmented Generation) for semantic search
- **Search Box in Header**: Prominently placed search box for easy access
- **Dedicated Search Results Page**: Comprehensive search results with scoring and pagination
- **Chunk-based Results**: Shows relevant text chunks with highlighting
- **Scoring Display**: Shows relevance scores as percentages
- **Original URL Links**: Links back to source documents
- **Responsive Design**: Works on desktop and mobile devices
- **No Graph Search**: Focuses on vector search only as requested

## Components

### SearchBox Component
- Located in `src/components/SearchBox.tsx`
- Responsive search input with loading states
- Supports different sizes (sm, md, lg)
- Auto-navigation to search results page

### SearchResults Component
- Located in `src/components/SearchResults.tsx`
- Displays search results with highlighting
- Shows relevance scores and metadata
- Includes pagination controls
- Links to original sources

### Search API Endpoint
- Located in `src/app/api/search/route.ts`
- Handles both GET and POST requests
- Supports pagination and filtering
- Returns structured search responses

## Configuration

### Environment Variables

Add these to your `.env.local` file:

```env
# R2R Configuration
R2R_URL=http://localhost:7272
R2R_COLLECTION_ID=selangor-halal

# Next.js Configuration
NEXT_PUBLIC_BASE_URL=http://localhost:16010
```

### R2R Setup

1. Ensure R2R server is running on the configured URL
2. Create a collection for Selangor content
3. Ingest your halal-related documents into the collection
4. Update the `R2R_COLLECTION_ID` with your collection ID

## Usage

### Search Box in Header
- Available on all pages in the header
- Type your search query and press Enter
- Automatically navigates to `/search?q=your-query`

### Search Results Page
- Access via `/search?q=your-query`
- Shows paginated results with scores
- Click on "View Original" links to see source documents
- Use pagination controls to navigate through results

### API Usage

```javascript
// GET request
const response = await fetch('/api/search?q=halal&page=1&limit=10');
const data = await response.json();

// POST request
const response = await fetch('/api/search', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    query: 'halal certification',
    page: 1,
    limit: 10,
  })
});
```

## Search Parameters

- `q` (string): Search query
- `page` (number): Page number for pagination (default: 1)
- `limit` (number): Results per page (default: 10)
- `minScore` (number): Minimum relevance score (default: 0.2)
- `maxWordCount` (number): Maximum words per result (default: 3000)
- `retrieveDocument` (boolean): Include document metadata (default: true)

## Response Format

```json
{
  "query": "halal certification",
  "results": [
    {
      "text": "Content chunk with search terms...",
      "score": 0.85,
      "metadata": {
        "title": "Document Title",
        "url": "https://source-url.com",
        "source": "document.pdf"
      },
      "wordCount": 150
    }
  ],
  "totalChunks": 25,
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "hasMore": true
  }
}
```

## Styling

The search functionality uses Tailwind CSS classes consistent with the rest of the application:
- Green color scheme (`green-600`, `green-700`)
- Responsive design with mobile-first approach
- Consistent spacing and typography
- Hover states and transitions

## Dependencies

- `r2r-js`: R2R client library
- `lucide-react`: Icons for search interface
- `next`: Next.js framework
- `tailwindcss`: Styling

## Troubleshooting

### Common Issues

1. **R2R Connection Error**: Ensure R2R server is running and accessible
2. **No Results**: Check if documents are properly ingested in R2R
3. **Slow Search**: Consider adjusting `maxWordCount` and `limit` parameters
4. **Missing Metadata**: Ensure documents have proper metadata when ingested

### Debug Mode

Enable debug logging by checking the browser console and server logs for detailed search information.
