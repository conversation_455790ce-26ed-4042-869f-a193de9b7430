'use client';

export const runtime = 'edge';

import type { BreadcrumbItem } from '@/components/breadcrumb';
import { PageWrapper } from '@/components/page-wrapper';
import { useLanguage } from '@/lib/language-context';

export default function AboutPage() {
  const { language } = useLanguage();

  const breadcrumbs: BreadcrumbItem[] = [
    {
      label: language === 'en' ? 'Home' : 'Laman <PERSON>',
      href: '/',
    },
    {
      label: language === 'en' ? 'Corporate' : 'Korporat',
      href: '/corporate',
    },
    {
      label: language === 'en' ? 'About Us' : 'Tentang Kami',
      href: '/corporate/about',
    },
  ];

  return (
    <PageWrapper
      title="About Us"
      titleBM="Tentang Kami"
      description="Learn about JAKIM's Halal Management Division, our history, and our commitment to Halal integrity."
      descriptionBM="Ketahui tentang Bahagian Pengurusan Halal JAKIM, sejarah kami, dan komitmen kami terhadap integriti Halal."
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-8">
        {/* Introduction Section */}
        <div className="card">
          <h2 className="text-2xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'About JAKIM Halal Management Division'
              : 'Tentang Bahagian Pengurusan Halal JAKIM'}
          </h2>
          <div className="prose prose-lg max-w-none">
            <p className="text-gray-600 leading-relaxed mb-4">
              {language === 'en'
                ? 'The Halal Management Division (Bahagian Pengurusan Halal) of the Department of Islamic Development Malaysia (JAKIM) is the sole authority responsible for Halal certification in Malaysia. Established to ensure the integrity and authenticity of Halal products and services, we have been serving the Muslim community and industry stakeholders since our inception.'
                : 'Bahagian Pengurusan Halal Jabatan Kemajuan Islam Malaysia (JAKIM) adalah pihak berkuasa tunggal yang bertanggungjawab untuk pensijilan Halal di Malaysia. Ditubuhkan untuk memastikan integriti dan keaslian produk dan perkhidmatan Halal, kami telah berkhidmat kepada masyarakat Islam dan pihak berkepentingan industri sejak penubuhan kami.'}
            </p>
            <p className="text-gray-600 leading-relaxed">
              {language === 'en'
                ? 'Our division plays a crucial role in maintaining consumer confidence in Halal products, both domestically and internationally. We work closely with manufacturers, food service providers, and other stakeholders to ensure compliance with Islamic dietary laws and Malaysian Halal standards.'
                : 'Bahagian kami memainkan peranan penting dalam mengekalkan keyakinan pengguna terhadap produk Halal, baik di dalam negara mahupun di peringkat antarabangsa. Kami bekerjasama rapat dengan pengilang, penyedia perkhidmatan makanan, dan pihak berkepentingan lain untuk memastikan pematuhan kepada undang-undang pemakanan Islam dan piawaian Halal Malaysia.'}
            </p>
          </div>
        </div>

        {/* Our Role Section */}
        <div className="card">
          <h2 className="text-2xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Our Role & Responsibilities'
              : 'Peranan & Tanggungjawab Kami'}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {language === 'en'
                      ? 'Halal Certification'
                      : 'Pensijilan Halal'}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Issuing Halal certificates for food products, cosmetics, pharmaceuticals, and services'
                      : 'Mengeluarkan sijil Halal untuk produk makanan, kosmetik, farmaseutikal, dan perkhidmatan'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {language === 'en'
                      ? 'Standards Development'
                      : 'Pembangunan Piawaian'}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Developing and maintaining Malaysian Halal standards and guidelines'
                      : 'Membangun dan mengekalkan piawaian dan garis panduan Halal Malaysia'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {language === 'en'
                      ? 'Monitoring & Enforcement'
                      : 'Pemantauan & Penguatkuasaan'}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Regular monitoring and enforcement to ensure continued compliance'
                      : 'Pemantauan dan penguatkuasaan berkala untuk memastikan pematuhan berterusan'}
                  </p>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {language === 'en'
                      ? 'International Recognition'
                      : 'Pengiktirafan Antarabangsa'}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Promoting Malaysian Halal certification globally and establishing mutual recognition'
                      : 'Mempromosikan pensijilan Halal Malaysia secara global dan mewujudkan pengiktirafan bersama'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {language === 'en'
                      ? 'Training & Education'
                      : 'Latihan & Pendidikan'}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Providing training and education on Halal requirements and best practices'
                      : 'Menyediakan latihan dan pendidikan mengenai keperluan Halal dan amalan terbaik'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {language === 'en'
                      ? 'Research & Development'
                      : 'Penyelidikan & Pembangunan'}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Conducting research to improve Halal standards and certification processes'
                      : 'Menjalankan penyelidikan untuk meningkatkan piawaian Halal dan proses pensijilan'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* History Section */}
        <div className="card">
          <h2 className="text-2xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Our History' : 'Sejarah Kami'}
          </h2>
          <div className="prose prose-lg max-w-none">
            <p className="text-gray-600 leading-relaxed mb-4">
              {language === 'en'
                ? 'The journey of Halal certification in Malaysia began with the recognition of the need for a standardized and credible Halal certification system. Over the years, we have evolved from a small department to become one of the most recognized Halal certification bodies globally.'
                : 'Perjalanan pensijilan Halal di Malaysia bermula dengan pengiktirafan keperluan untuk sistem pensijilan Halal yang piawai dan boleh dipercayai. Selama bertahun-tahun, kami telah berkembang daripada sebuah jabatan kecil menjadi salah satu badan pensijilan Halal yang paling diiktiraf di peringkat global.'}
            </p>
            <p className="text-gray-600 leading-relaxed">
              {language === 'en'
                ? 'Today, Malaysian Halal certification is recognized and trusted by Muslim consumers worldwide. Our continuous efforts in maintaining high standards and adapting to industry changes have positioned Malaysia as a global leader in the Halal industry.'
                : 'Hari ini, pensijilan Halal Malaysia diiktiraf dan dipercayai oleh pengguna Islam di seluruh dunia. Usaha berterusan kami dalam mengekalkan piawaian tinggi dan menyesuaikan diri dengan perubahan industri telah meletakkan Malaysia sebagai pemimpin global dalam industri Halal.'}
            </p>
          </div>
        </div>

        {/* Contact Information */}
        <div className="card">
          <h2 className="text-2xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Contact Information' : 'Maklumat Hubungan'}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">
                {language === 'en' ? 'Address' : 'Alamat'}
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                BAHAGIAN PENGURUSAN HALAL,
                <br />
                JABATAN KEMAJUAN ISLAM MALAYSIA,
                <br />
                Aras 6 & 7, Blok D,
                <br />
                Kompleks Islam Putrajaya (KIP),
                <br />
                No. 3 Jalan Tun Abdul Razak,
                <br />
                Presint 3, 62100 Putrajaya, Malaysia.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">
                {language === 'en' ? 'Contact Details' : 'Butiran Hubungan'}
              </h3>
              <div className="space-y-2 text-sm">
                <p className="text-gray-600">
                  <span className="font-medium">
                    {language === 'en' ? 'Phone:' : 'Telefon:'}
                  </span>{' '}
                  03-8892 5000
                </p>
                <p className="text-gray-600">
                  <span className="font-medium">
                    {language === 'en' ? 'Fax:' : 'Faks:'}
                  </span>{' '}
                  03-8892 5005
                </p>
                <p className="text-gray-600">
                  <span className="font-medium">
                    {language === 'en' ? 'Email:' : 'E-mel:'}
                  </span>{' '}
                  <EMAIL>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}
