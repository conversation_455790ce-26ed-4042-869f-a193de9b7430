import { NextRequest } from 'next/server';
import { sql, count, isNotNull, and, ne } from 'drizzle-orm';
import { db } from '@/lib/db';
import { companies, products } from '@/lib/db/schema';
import { createSuccessResponse, handleApiError } from '@/lib/api-response';

// Remove edge runtime to use Node.js runtime for database access

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'all'; // 'all', 'companies', 'products'

    console.log('States API - type:', type);

    let stateData: { state: string; count: number }[] = [];

    if (type === 'companies' || type === 'all') {
      // Get state counts from companies table
      const companyStates = await db
        .select({
          state: companies.state,
          count: count(),
        })
        .from(companies)
        .where(
          and(
            isNotNull(companies.state),
            ne(companies.state, ''),
            ne(companies.state, 'null')
          )
        )
        .groupBy(companies.state)
        .orderBy(companies.state);

      console.log('Company states found:', companyStates.length);

      // Add to state data
      companyStates.forEach(item => {
        if (item.state) {
          const existingIndex = stateData.findIndex(s => s.state === item.state);
          if (existingIndex >= 0) {
            stateData[existingIndex].count += Number(item.count);
          } else {
            stateData.push({
              state: item.state,
              count: Number(item.count)
            });
          }
        }
      });
    }

    if (type === 'products' || type === 'all') {
      // Get state counts from products table
      const productStates = await db
        .select({
          state: products.state,
          count: count(),
        })
        .from(products)
        .where(
          and(
            isNotNull(products.state),
            ne(products.state, ''),
            ne(products.state, 'null')
          )
        )
        .groupBy(products.state)
        .orderBy(products.state);

      console.log('Product states found:', productStates.length);

      // Add to state data
      productStates.forEach(item => {
        if (item.state) {
          const existingIndex = stateData.findIndex(s => s.state === item.state);
          if (existingIndex >= 0) {
            stateData[existingIndex].count += Number(item.count);
          } else {
            stateData.push({
              state: item.state,
              count: Number(item.count)
            });
          }
        }
      });
    }

    // Sort by count (descending) then by state name
    stateData.sort((a, b) => {
      if (b.count !== a.count) {
        return b.count - a.count;
      }
      return a.state.localeCompare(b.state);
    });

    console.log('Final state data:', stateData.length, 'states');

    // Get total count
    const totalCount = stateData.reduce((sum, item) => sum + item.count, 0);

    return createSuccessResponse({
      states: stateData,
      total: totalCount,
      type: type
    });

  } catch (error) {
    return handleApiError(error, 'Failed to fetch state data');
  }
}
