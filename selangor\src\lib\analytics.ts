import type { NextRequest } from 'next/server';
import { db } from './db';
import { searchAnalytics } from './db/schema';

export interface SearchAnalyticsData {
  siteId: number;
  searchQuery: string;
  searchType: 'web' | 'products' | 'companies' | 'semantic';
  resultsCount: number;
  userAgent?: string;
  ipAddress?: string;
  sessionId?: string;
  userId?: number;
  responseTime?: number;
  hasResults: boolean;
  searchFilters?: string;
}

/**
 * Track search analytics for user searches
 */
export async function trackSearchAnalytics(
  data: SearchAnalyticsData,
): Promise<void> {
  try {
    await db.insert(searchAnalytics).values({
      siteId: data.siteId,
      searchQuery: data.searchQuery,
      searchType: data.searchType,
      resultsCount: data.resultsCount,
      userAgent: data.userAgent,
      ipAddress: data.ipAddress,
      sessionId: data.sessionId,
      userId: data.userId,
      responseTime: data.responseTime,
      hasResults: data.hasResults,
      searchFilters: data.searchFilters,
    });
  } catch (error) {
    console.error('Failed to track search analytics:', error);
    // Don't throw error to avoid breaking the search functionality
  }
}

/**
 * Extract analytics data from NextRequest
 */
export function extractAnalyticsFromRequest(request: NextRequest): {
  userAgent?: string;
  ipAddress?: string;
  sessionId?: string;
} {
  const userAgent = request.headers.get('user-agent') || undefined;

  // Get IP address from various headers (considering proxies)
  const ipAddress =
    request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
    request.headers.get('x-real-ip') ||
    request.headers.get('cf-connecting-ip') ||
    undefined;

  // Try to get session ID from cookies or generate one
  const sessionId = request.cookies.get('session-id')?.value || undefined;

  return {
    userAgent,
    ipAddress,
    sessionId,
  };
}

/**
 * Get the site ID for halalselangor
 */
export function getHalalSelangorSiteId(): number {
  return 2; // As defined in the seed script
}
