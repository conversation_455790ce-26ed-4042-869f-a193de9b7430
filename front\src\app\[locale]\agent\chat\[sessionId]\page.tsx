'use client';

export const runtime = 'edge';

import axios from 'axios';
import {
  ArrowLeft,
  CheckCircle,
  Mic,
  MicOff,
  Monitor,
  Paperclip,
  Phone,
  Send,
  Smartphone,
  User,
  X,
} from 'lucide-react';
import { useParams } from 'next/navigation';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useRouter } from '@/i18n/navigation';
import { useAgentAuth } from '@/stores/auth';
import { useWebSocket } from '../../../../../lib/websocket';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'agent';
  content: string;
  timestamp: Date;
  imageUrl?: string;
  audioUrl?: string;
  fileUrl?: string;
  fileName?: string;
  agentName?: string;
}

interface ChatSession {
  id: string;
  userId?: string;
  platform: string;
  platformId?: string;
  status: string;
  isHandedOver: boolean;
  createdAt: Date;
  lastMessageAt?: Date;
  messages: Message[];
}

export default function AgentChatPage() {
  const router = useRouter();
  const params = useParams();
  const sessionId = params.sessionId as string;
  const {
    user: agentUser,
    token,
    logout,
    verify,
    isAuthenticated,
    isLoading: authIsLoading,
  } = useAgentAuth();

  const [session, setSession] = useState<ChatSession | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<{
    url: string;
    file: File;
    type: string;
  } | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  const handleTokenExpiration = useCallback(() => {
    logout();
    router.push('/agent');
  }, [logout, router]);

  const makeAuthenticatedRequest = useCallback(
    async (url: string, options: any = {}) => {
      if (!token) {
        handleTokenExpiration();
        throw new Error('No token available');
      }

      try {
        const response = await axios.get(url, {
          ...options,
          headers: {
            ...options.headers,
            Authorization: `Bearer ${token}`,
          },
        });
        return response;
      } catch (error) {
        if (axios.isAxiosError(error) && error.response?.status === 401) {
          handleTokenExpiration();
          throw new Error('Token expired');
        }
        throw error;
      }
    },
    [token, handleTokenExpiration],
  );

  const loadSession = useCallback(async () => {
    try {
      if (!token) {
        handleTokenExpiration();
        return;
      }

      const response = await makeAuthenticatedRequest(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/sessions/${sessionId}`,
      );

      setSession(response.data);
      setMessages(response.data.messages || []);
    } catch (error) {
      console.error('Error loading session:', error);
      // If session not found, redirect to dashboard
      router.push('/agent/dashboard');
    } finally {
      setIsLoading(false);
    }
  }, [
    sessionId,
    router,
    token,
    makeAuthenticatedRequest,
    handleTokenExpiration,
  ]);

  // Memoized WebSocket callbacks to prevent infinite reconnections
  const onMessage = useCallback((message: any) => {
    handleWebSocketMessage(message);
  }, [handleWebSocketMessage]);

  // WebSocket connection for real-time updates
  const { isConnected: wsConnected, sendMessage: sendWsMessage } = useWebSocket(
    `${process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:16001'}/ws`,
    {
      onMessage,
      onConnect: () => {
        console.log('Agent WebSocket connected');
      },
    },
  );

  // Register agent connection when WebSocket connects and we have the necessary data
  useEffect(() => {
    if (wsConnected && token && agentUser && sendWsMessage) {
      sendWsMessage({
        type: 'register',
        data: {
          connectionType: 'agent',
          agentId: agentUser.id,
          sessionId,
        },
      });
    }
  }, [wsConnected, token, agentUser, sessionId, sendWsMessage]);

  useEffect(() => {
    const authAndLoad = async () => {
      if (authIsLoading) {
        return;
      }
      // Only attempt to verify if not authenticated and a token exists (from persistence)
      if (!isAuthenticated && token) {
        const isValid = await verify();
        if (!isValid) {
          router.push('/agent');
          return;
        }
      } else if (!isAuthenticated && !token) {
        // If no token and not authenticated, redirect to login
        router.push('/agent');
        return;
      }

      await loadSession();
    };

    authAndLoad();
  }, [isAuthenticated, token, verify, router, loadSession, authIsLoading]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleWebSocketMessage = (message: any) => {
    switch (message.type) {
      case 'user_message':
        if (message.data.sessionId === sessionId) {
          const userMessage: Message = {
            id: message.data.messageId || Date.now().toString(),
            role: 'user',
            content: message.data.content,
            timestamp: new Date(message.data.timestamp || Date.now()),
            imageUrl: message.data.imageUrl,
          };
          setMessages((prev) => [...prev, userMessage]);
        }
        break;
      default:
        console.log('Unknown WebSocket message type:', message.type);
    }
  };

  const sendMessage = async (
    messageType: 'text' | 'voice' | 'file' = 'text',
  ) => {
    if ((!inputText.trim() && !uploadedFile) || isSending || !agentUser) {
      return;
    }

    setIsSending(true);
    const messageText = inputText.trim();
    setInputText('');

    try {
      if (!token) {
        handleTokenExpiration();
        return;
      }

      const requestData: any = {
        content:
          messageText || (uploadedFile ? `Sent a ${uploadedFile.type}` : ''),
        type: messageType,
      };

      if (uploadedFile) {
        if (uploadedFile.type === 'file') {
          requestData.fileUrl = uploadedFile.url;
          requestData.fileName = uploadedFile.file.name;
        } else if (uploadedFile.type === 'voice') {
          requestData.audioUrl = uploadedFile.url;
        }
      }

      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/sessions/${sessionId}/message`,
        requestData,
        { headers: { Authorization: `Bearer ${token}` } },
      );

      if (response.data.success) {
        const agentMessage: Message = {
          id: response.data.messageId,
          role: 'agent',
          content: requestData.content,
          timestamp: new Date(),
          agentName: `${agentUser.firstName} ${agentUser.lastName}`,
          audioUrl: requestData.audioUrl,
          fileUrl: requestData.fileUrl,
          fileName: requestData.fileName,
        };
        setMessages((prev) => [...prev, agentMessage]);

        // Notify via WebSocket
        if (wsConnected) {
          sendWsMessage({
            type: 'agent_message',
            data: {
              sessionId,
              messageId: response.data.messageId,
              content: requestData.content,
              agentName: `${agentUser.firstName} ${agentUser.lastName}`,
              timestamp: new Date().toISOString(),
              audioUrl: requestData.audioUrl,
              fileUrl: requestData.fileUrl,
              fileName: requestData.fileName,
            },
          });
        }

        // Clear uploaded file
        setUploadedFile(null);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsSending(false);
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: 'audio/webm',
        });
        const audioUrl = URL.createObjectURL(audioBlob);

        // Create a file object for the audio
        const audioFile = new File([audioBlob], 'voice-message.webm', {
          type: 'audio/webm',
        });

        setUploadedFile({
          url: audioUrl,
          file: audioFile,
          type: 'voice',
        });

        stream.getTracks().forEach((track) => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Error starting recording:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const fileUrl = URL.createObjectURL(file);
      setUploadedFile({
        url: fileUrl,
        file,
        type: 'file',
      });
    }
  };

  const removeUploadedFile = () => {
    if (uploadedFile) {
      URL.revokeObjectURL(uploadedFile.url);
      setUploadedFile(null);
    }
  };

  const completeSession = async () => {
    try {
      if (!token) {
        handleTokenExpiration();
        return;
      }

      await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/sessions/${sessionId}/complete`,
        {},
        { headers: { Authorization: `Bearer ${token}` } },
      );

      // Notify via WebSocket
      if (wsConnected) {
        sendWsMessage({
          type: 'session_completed',
          data: { sessionId },
        });
      }

      router.push('/agent/dashboard');
    } catch (error) {
      console.error('Error completing session:', error);
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'whatsapp':
        return <Phone className="h-4 w-4" />;
      case 'facebook':
        return <Smartphone className="h-4 w-4" />;
      default:
        return <Monitor className="h-4 w-4" />;
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" />
          <p className="mt-4 text-gray-600">Loading session...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Session not found</p>
          <button
            onClick={() => router.push('/agent/dashboard')}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/agent/dashboard')}
                className="p-2 hover:bg-gray-100 rounded-md"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
              <div className="flex items-center space-x-2">
                {getPlatformIcon(session.platform)}
                <div>
                  <h1 className="text-lg font-semibold text-gray-900">
                    Session {session.id.slice(-8)}
                  </h1>
                  <p className="text-sm text-gray-500">
                    {session.platform} • Started{' '}
                    {new Date(session.createdAt).toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
            <button
              onClick={completeSession}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Complete Session
            </button>
          </div>
        </div>
      </header>

      {/* Messages */}
      <div className="flex-1 max-w-4xl mx-auto w-full px-4 py-6">
        <div className="bg-white rounded-lg shadow-sm border h-96 flex flex-col">
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.role === 'agent' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-gray-200 text-gray-800'
                      : message.role === 'agent'
                        ? 'bg-blue-600 text-white'
                        : 'bg-yellow-100 text-yellow-800'
                  }`}
                >
                  {message.role === 'agent' && message.agentName && (
                    <div className="flex items-center mb-1">
                      <User className="w-3 h-3 mr-1" />
                      <span className="text-xs font-medium opacity-75">
                        {message.agentName}
                      </span>
                    </div>
                  )}
                  {message.imageUrl && (
                    <img
                      src={message.imageUrl}
                      alt="Uploaded"
                      className="w-full h-32 object-cover rounded mb-2"
                    />
                  )}
                  {message.audioUrl && (
                    <div className="mb-2">
                      <audio controls className="w-full max-w-xs">
                        <source src={message.audioUrl} type="audio/webm" />
                        Your browser does not support the audio element.
                      </audio>
                    </div>
                  )}
                  {message.fileUrl && message.fileName && (
                    <div className="mb-2 p-2 bg-gray-100 rounded flex items-center space-x-2">
                      <Paperclip className="h-4 w-4 text-gray-500" />
                      <a
                        href={message.fileUrl}
                        download={message.fileName}
                        className="text-sm text-blue-600 hover:text-blue-800 underline"
                      >
                        {message.fileName}
                      </a>
                    </div>
                  )}
                  <p className="text-sm">{message.content}</p>
                  <p className="text-xs opacity-70 mt-1">
                    {message.timestamp?.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="border-t p-4">
            {/* File Preview */}
            {uploadedFile && (
              <div className="mb-3 p-3 bg-gray-50 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Paperclip className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-700">
                      {uploadedFile.type === 'voice'
                        ? 'Voice message recorded'
                        : uploadedFile.file.name}
                    </span>
                    {uploadedFile.type === 'voice' && (
                      <audio controls className="h-8">
                        <source src={uploadedFile.url} type="audio/webm" />
                      </audio>
                    )}
                  </div>
                  <button
                    onClick={removeUploadedFile}
                    className="p-1 hover:bg-gray-200 rounded"
                  >
                    <X className="h-4 w-4 text-gray-500" />
                  </button>
                </div>
              </div>
            )}

            <div className="flex items-center space-x-2">
              <div className="flex-1 relative">
                <textarea
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message..."
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                  rows={1}
                  disabled={isSending}
                />
              </div>

              {/* Voice Recording Button */}
              <button
                onClick={isRecording ? stopRecording : startRecording}
                className={`p-2 rounded-lg ${
                  isRecording
                    ? 'bg-red-600 text-white animate-pulse'
                    : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                }`}
                disabled={isSending}
              >
                {isRecording ? (
                  <MicOff className="w-5 h-5" />
                ) : (
                  <Mic className="w-5 h-5" />
                )}
              </button>

              {/* File Upload Button */}
              <label className="p-2 bg-gray-200 text-gray-600 hover:bg-gray-300 rounded-lg cursor-pointer">
                <Paperclip className="w-5 h-5" />
                <input
                  type="file"
                  onChange={handleFileUpload}
                  className="hidden"
                  accept="image/*,application/pdf,.doc,.docx,.txt"
                  disabled={isSending}
                />
              </label>

              {/* Send Button */}
              <button
                onClick={() =>
                  sendMessage(
                    uploadedFile?.type === 'voice'
                      ? 'voice'
                      : uploadedFile?.type === 'file'
                        ? 'file'
                        : 'text',
                  )
                }
                disabled={(!inputText.trim() && !uploadedFile) || isSending}
                className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Send className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
