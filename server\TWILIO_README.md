# Twilio Integration - Hono.js

Clean MVC architecture for Twilio webhook handling in Hono.js with direct route handling.

## 🏗️ Architecture

```
server.ts (Hono.js entry)
    ↓ (direct call)
routes/twilio.ts (direct route matching)
    ↓ (calls controller)
controllers/twilioController.ts
    ↓ (handles DB init & business logic)
services/twilio.ts + messageHandler.ts
    ↓ (database)
Drizzle ORM
```

## 📁 File Structure

```
src/
├── controllers/
│   └── twilioController.ts    # Request/response handling & DB initialization
├── routes/
│   └── twilio.ts             # Direct route matching (no itty-router)
├── services/
│   └── twilio.ts             # Business logic & Twilio API
└── server.ts                 # Main Hono.js entry point
```

## 🔧 How It Works

### 1. **Webhook Request Flow**
```
POST /api/twilio/webhook
    ↓
handleTwilioRoutes (routes/twilio.ts)
    ↓ (direct route matching)
twilioController.handleWebhook()
    ↓ (initializes DB, verifies signature, processes message)
TwilioService + MessageHandler
    ↓ (AI response, database logging)
Response sent back to user
```

### 2. **Key Components**

**Routes** (`routes/twilio.ts`):
- Direct URL matching (no external router dependencies)
- Clean error handling and logging
- Controller delegation

**Controller** (`controllers/twilioController.ts`):
- Database service initialization
- Webhook signature verification
- Request/response handling
- Comprehensive error handling and logging

**Service** (`services/twilio.ts`):
- Twilio API integration
- Configuration management
- Message sending/receiving

## 🚀 Usage

### Environment Variables
```env
TWILIO_ACCOUNT_SID=ACxxxxx
TWILIO_AUTH_TOKEN=your_token
TWILIO_PHONE_NUMBER=whatsapp:+**********
```

### Webhook URL
```
https://your-hono-server.com/api/twilio/webhook
```

## 🔧 First Setup Guide

### 1. **Twilio Account Setup**

1. **Create Twilio Account**
   - Go to [Twilio Console](https://console.twilio.com/)
   - Sign up for a new account or log in
   - Complete account verification

2. **Get Account Credentials**
   - Navigate to **Console Dashboard**
   - Copy your **Account SID** and **Auth Token**
   - Keep these secure - they'll be used in your .env file

### 2. **WhatsApp Business API Setup**

1. **Access WhatsApp Business API**
   - In Twilio Console, go to **Messaging > Try it out > Send a WhatsApp message**
   - Or navigate to **Messaging > WhatsApp > Senders**

2. **Sandbox Setup (Development)**
   - Use Twilio's WhatsApp Sandbox for testing
   - Get your sandbox number (e.g., `whatsapp:+***********`)
   - Send "join [sandbox-keyword]" to the sandbox number from your WhatsApp

3. **Production Setup**
   - Apply for WhatsApp Business API access
   - Get your business phone number approved
   - Configure your business profile

### 3. **Webhook Configuration**

1. **Get Your Webhook URL**
   - Your webhook URL will be: `https://your-worker.workers.dev/api/twilio/webhook`
   - Replace `your-worker` with your actual Cloudflare Workers subdomain

2. **Configure in Twilio Console**
   - Go to **Messaging > WhatsApp > Sandbox settings** (for sandbox)
   - Or **Phone Numbers > Manage > WhatsApp senders** (for production)
   - Set **Webhook URL** to your webhook endpoint
   - Set **HTTP Method** to `POST`
   - Save configuration

### 4. **Environment Configuration**

The project uses `.env` files for environment configuration:

**`.env`** (Your actual configuration - not in git)
- Contains your actual environment variables
- Environment-specific settings for development and production
- Both sensitive and non-sensitive variables

**`.env.example`** (Template file - in git)
- Template configuration file with example values
- Copy this to create your `.env`
- Contains comments showing required variables

**Key Configuration Variables:**
```env
# Twilio Configuration
TWILIO_ACCOUNT_SID=your_account_sid_here
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=whatsapp:+your_twilio_number
WEBHOOK_BASE_URL=https://your-hono-server.com
```

### 5. **Database Configuration**

The system supports both database and environment variable configuration:

**Option A: Database Configuration (Recommended)**
- Use the admin interface (coming soon) to configure Twilio settings
- Configuration is stored per site in the database
- Allows multiple site configurations
- Twilio credentials stored in `twilio_configs` table

**Option B: Environment Variables (Fallback)**
- Set environment variables in .env file
- Used when database configuration is not available
- Automatically falls back if database config missing

**Database Seeding Process:**
```bash
# Development seeding
# Add to your .env file:
TWILIO_ACCOUNT_SID=ACxxxxx
TWILIO_AUTH_TOKEN=your_token
TWILIO_PHONE_NUMBER=whatsapp:+**********

# Run database seed (will create Twilio config if env vars exist)
pnpm db:seed

# Production seeding
# Add to your .env.production file:
TWILIO_ACCOUNT_SID=ACxxxxx
TWILIO_AUTH_TOKEN=your_token
TWILIO_PHONE_NUMBER=whatsapp:+**********

# Run production database seed
pnpm db:seed:prod
```

### Adding New Endpoints

1. **Add route** in `routes/twilio.ts`:
```typescript
// GET /api/twilio/new-endpoint
if (method === 'GET' && path === '/api/twilio/new-endpoint') {
  console.log('[Twilio Router] New endpoint accessed');
  return await twilioController.newMethod(request, env, ctx, siteId);
}
```

2. **Add controller method** in `controllers/twilioController.ts`:
```typescript
async newMethod(request: Request, env: any, ctx: any, siteId: string) {
  try {
    // Controller handles DB initialization if needed
    // Handle request logic
    return new Response(JSON.stringify({ success: true }));
  } catch (error: any) {
    console.error('[Twilio Controller] Error in newMethod:', { error: error.message });
    return new Response('Internal server error', { status: 500 });
  }
}
```

## 🔐 Environment Variables

### Required Variables

Set these variables in your .env file:

```bash
# Core Twilio Configuration
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=whatsapp:+***********
WEBHOOK_BASE_URL=https://your-hono-server.com
```

### Development Environment

```bash
# For development environment (.env)
TWILIO_ACCOUNT_SID=your_dev_account_sid
TWILIO_AUTH_TOKEN=your_dev_auth_token
TWILIO_PHONE_NUMBER=whatsapp:+your_dev_number
```

### Other Required Variables

```bash
# Database and Authentication
DATABASE_URL=postgresql://user:password@host:port/database
JWT_SECRET=your_jwt_secret_here
OPENAI_API_KEY=your_openai_api_key

# R2R Configuration (for halal knowledge search)
R2R_PASSWORD=your_r2r_password
R2R_COLLECTION_ID=your_collection_id
```

### S3 Configuration (Media Processing)

**Important:** S3 credentials are handled differently - they are stored in `.env` and seeded into the database, not as Cloudflare Workers secrets.

**Setup Process:**
1. **Add S3 credentials to `.env` file:**
```bash
# S3 Configuration (for media processing)
DEFAULT_S3_SERVICE_NAME=AWS S3
S3_ACCESS_KEY_ID=your_access_key_id
S3_SECRET_ACCESS_KEY=your_secret_access_key
S3_BUCKET_NAME=halal-jakim
S3_REGION=ap-southeast-1
S3_ENDPOINT_URL=  # Leave empty for AWS S3, set for Cloudflare R2/DigitalOcean Spaces
```

2. **Seed S3 configuration into database:**
```bash
# Development seeding
pnpm db:seed

# Production seeding (from .env.production)
pnpm db:seed:prod
```

3. **S3 Configuration Management:**
- S3 credentials are stored in `s3_configurations` table
- Managed via admin interface (existing functionality)
- Supports multiple S3 services (AWS S3, Cloudflare R2, DigitalOcean Spaces)
- Twilio media processing automatically detects available S3 service

### Verify Secrets

```bash
# List all secrets
npx wrangler secret list --env production

# Test deployment
npx wrangler deploy --env production
```

## 🔒 Security

- ✅ **Webhook signature verification** using Twilio's `validateRequest`
- ✅ **Request validation** for required fields
- ✅ **Error handling** with proper HTTP status codes
- ✅ **Secure secret management** via Cloudflare Workers secrets
- ✅ **Database configuration encryption** (auth tokens stored securely)

## 🗄️ Database Integration

- Uses **Drizzle ORM** for database operations
- **Site-specific configuration** loaded from database
- **Message logging** for all incoming/outgoing messages
- **Fallback to environment variables** if database config missing

## 📱 Message Types Supported

- ✅ **Text messages**
- ✅ **Images** 
- ✅ **Audio**
- ✅ **Video**
- ✅ **Documents**

## ⚙️ Admin Interface (Coming Soon)

The Twilio admin interface will be implemented later and will include:

### Planned Features
- **Configuration Form**: Account SID, Auth Token, Phone Number input
- **Test Message**: Send test messages to verify configuration
- **Webhook URL Display**: Copy webhook URL with instructions
- **Configuration Validation**: Real-time validation of Twilio credentials
- **Message History**: View sent/received message logs
- **Status Dashboard**: Connection status and health checks

### Planned Admin Endpoints
```typescript
// Configuration Management
GET  /api/admin/twilio/config        // Get current Twilio configuration
POST /api/admin/twilio/config        // Save Twilio configuration

// Testing and Validation
POST /api/admin/twilio/test          // Test Twilio configuration
POST /api/admin/twilio/test-message  // Send test message

// Webhook Management
GET  /api/admin/twilio/webhook-url   // Get webhook URL with instructions

// Message Management
GET  /api/admin/twilio/messages/:phoneNumber  // Get message history
```

### Implementation Notes
- Will follow the same pattern as existing WhatsApp admin interface
- Database configuration will take precedence over environment variables
- Real-time configuration testing and validation
- Integration with existing admin authentication system

## 🤖 AI Integration

- Uses **consolidated MessageHandler** service
- **OpenAI integration** for AI responses
- **Tool calling** support
- **Session management** by phone number

## 🛠️ Development

### TypeScript
```typescript
// Clean ES6 imports
import twilio from 'twilio';

// Proper type safety
private twilioClient: InstanceType<typeof twilio.Twilio> | null = null;
```

### Testing Webhook Locally

Since ngrok might not be available, here are alternative methods:

#### Option 1: Cloudflared Tunnel (Recommended)
```bash
# Install cloudflared
# Download from: https://developers.cloudflare.com/cloudflare-one/connections/connect-apps/install-and-setup/installation/

# Create tunnel
cloudflared tunnel --url http://localhost:8787

# Use the provided URL in Twilio webhook configuration
# Example: https://random-string.trycloudflare.com/api/twilio/webhook
```

#### Option 2: LocalTunnel
```bash
# Install localtunnel
npm install -g localtunnel

# Create tunnel
lt --port 8787 --subdomain your-custom-name

# Use: https://your-custom-name.loca.lt/api/twilio/webhook
```

#### Option 3: Direct Development Deployment
```bash
# Deploy to development environment
pnpm deploy:dev

# Use development URL for webhook:
# https://your-worker-development.your-subdomain.workers.dev/api/twilio/webhook

# Monitor logs
pnpm dev:log
```

## 🚨 Troubleshooting

### Common Issues

#### **Signature Verification Failed**
```
Error: Signature verification failed (invalid signature)
```
**Solutions:**
- Verify webhook URL matches exactly (including https://)
- Check `TWILIO_AUTH_TOKEN` secret is set correctly
- Ensure webhook URL in Twilio Console matches your deployment
- Verify request is coming from Twilio (check headers)

#### **Service Not Configured**
```
Error: Twilio service not configured
```
**Solutions:**
- Check Cloudflare Workers secrets are set: `npx wrangler secret list --env production`
- Verify database configuration exists for your site
- Check site ID extraction in logs
- Ensure `loadConfig()` is called before using service

#### **Message Not Processed**
```
Error: Invalid webhook payload
```
**Solutions:**
- Check webhook payload structure in logs
- Verify required fields: `MessageSid`, `From`, `To`
- Check Twilio webhook configuration (POST method)
- Verify content-type is `application/x-www-form-urlencoded`

#### **Media Processing Failed**
```
Error: Failed to process media message
```
**Solutions:**
- Check S3 configuration and credentials
- Verify media URL is accessible
- Check supported media types (image, audio, document)
- Review S3 bucket permissions

#### **Database Connection Issues**
```
Error: Database connection failed
```
**Solutions:**
- Verify `DATABASE_URL` secret is set correctly
- Check database is accessible from Cloudflare Workers
- Verify database schema is up to date: `pnpm db:migrate`
- Check database connection limits

### Debug Steps

1. **Check Deployment Status**
```bash
# Verify deployment
npx wrangler deployments list --env production

# Check logs
npx wrangler tail --env production
```

2. **Test Webhook Manually**
```bash
# Test webhook endpoint
curl -X GET "https://your-worker.workers.dev/api/twilio/webhook"
# Should return: "Webhook OK"
```

3. **Verify Configuration Files**
```bash
# Check wrangler.toml exists and is configured
cat wrangler.toml

# Verify environment configuration
npx wrangler whoami
```

4. **Verify Secrets**
```bash
# List all secrets
npx wrangler secret list --env production

# Check specific secret (will show if exists, not value)
npx wrangler secret put TWILIO_ACCOUNT_SID --env production
```

5. **Database Verification**
```bash
# Check database connection
pnpm db:studio

# Verify Twilio configuration exists
# Check twilio_configs table for your site

# Verify S3 configuration exists
# Check s3_configurations table
```

### Debug Logs
```typescript
console.log('TwilioController: Processing webhook...');
console.log('TwilioService: Configuration loaded');
console.log('MessageHandler: AI response generated');
```

### Getting Help

If issues persist:
1. Check Hono.js server logs: `pnpm dev:log`
2. Review Twilio Console webhook logs
3. Verify all environment variables are set correctly
4. Test with Twilio's webhook testing tools
5. Check database configuration in admin panel (when available)

## 📋 Deployment Checklist

### Pre-Deployment

- [ ] **Twilio Account Setup Complete**
  - [ ] Account verified and active
  - [ ] WhatsApp Business API access configured
  - [ ] Phone number verified (sandbox or production)

- [ ] **Configuration Files**
  - [ ] Copy `.env.example` to `.env`
  - [ ] Update environment variables in `.env`
  - [ ] Configure environment-specific settings

- [ ] **Environment Variables Configuration**
  - [ ] `TWILIO_ACCOUNT_SID` set in .env
  - [ ] `TWILIO_AUTH_TOKEN` set in .env
  - [ ] `TWILIO_PHONE_NUMBER` set in .env
  - [ ] Other required variables (DATABASE_URL, OPENAI_API_KEY, etc.)

- [ ] **Database Setup**
  - [ ] Database migrations applied: `pnpm db:migrate`
  - [ ] Database seeded if needed: `pnpm db:seed`
  - [ ] Twilio configuration exists in database (optional)
  - [ ] S3 configuration seeded from `.env` (for media processing)

### Deployment

- [ ] **Deploy to Production**
  ```bash
  # Deploy to production
  pnpm deploy:dev

  # Verify deployment
  curl https://your-hono-server.com/health
  ```

- [ ] **Get Webhook URL**
  - [ ] Note your webhook URL: `https://your-worker.workers.dev/api/twilio/webhook`
  - [ ] Test webhook endpoint: `curl -X GET "https://your-worker.workers.dev/api/twilio/webhook"`

### Post-Deployment

- [ ] **Configure Twilio Webhook**
  - [ ] Go to Twilio Console
  - [ ] Set webhook URL in WhatsApp configuration
  - [ ] Verify webhook is receiving requests

- [ ] **Test Integration**
  - [ ] Send test message to your Twilio WhatsApp number
  - [ ] Verify message is processed and response is sent
  - [ ] Check Cloudflare Workers logs: `pnpm dev:log`
  - [ ] Verify database logging (check twilio_messages table)

- [ ] **Monitor and Verify**
  - [ ] Check webhook signature verification is working
  - [ ] Verify media processing (send image/audio)
  - [ ] Test AI responses and tool calling
  - [ ] Monitor error rates and performance

### Production Checklist

- [ ] **Security**
  - [ ] All secrets properly configured (not in code)
  - [ ] Webhook signature verification enabled
  - [ ] Database access secured

- [ ] **Performance**
  - [ ] S3 media processing configured
  - [ ] Database connection optimized
  - [ ] Error handling and logging in place

- [ ] **Monitoring**
  - [ ] Cloudflare Workers analytics enabled
  - [ ] Log monitoring set up
  - [ ] Alert system configured (optional)

## 📋 Migration Notes

**From Express.js to Workers:**
- ✅ Removed Express.js dependencies
- ✅ Removed itty-router dependency for cleaner implementation
- ✅ Direct route handling with better error management
- ✅ Maintained all existing functionality
- ✅ Improved code organization with MVC pattern

**Recent Improvements:**
- 🧹 **Removed itty-router** - Direct route matching for better performance
- 📝 **Enhanced logging** - Structured logging with context
- 🛡️ **Better error handling** - Comprehensive error management
- 🗄️ **Controller-managed DB** - Database initialization handled by controller
- 🚫 **Removed db-test endpoint** - Cleaner production-ready code

**Benefits:**
- 🚀 **Better performance** with Hono.js and direct routing
- 💰 **Lower costs** (efficient Bun runtime)
- 🌍 **Flexible deployment** (Docker, cloud platforms)
- 📈 **Auto-scaling** (with proper deployment)
- 🧹 **Cleaner, more maintainable code structure**

## 🔗 Related Services

- **WhatsApp**: Similar pattern in `index.ts` (not yet refactored)
- **Facebook**: Similar pattern in `index.ts` (not yet refactored)
- **MessageHandler**: Shared AI service across all platforms
- **Database**: Shared Drizzle ORM service

---

**Ready for production!** 🎉
