import { type NextRequest, NextResponse } from 'next/server';
import { DocumentManager } from '@/lib/document-manager';
export const runtime = 'edge';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  const { id } = params;

  try {
    // Get document by ID
    const document = DocumentManager.getDocumentById(id);

    if (!document) {
      return NextResponse.json(
        {
          success: false,
          error: 'Document not found',
        },
        { status: 404 },
      );
    }

    // Check if document is published (unless explicitly requesting draft/archived)
    const { searchParams } = new URL(request.url);
    const includeUnpublished =
      searchParams.get('includeUnpublished') === 'true';

    if (!includeUnpublished && document.status !== 'published') {
      return NextResponse.json(
        {
          success: false,
          error: 'Document not available',
        },
        { status: 404 },
      );
    }

    // Get related documents
    const relatedDocuments = DocumentManager.getRelatedDocuments(id, 5);

    // Increment download count (in a real app, this would be tracked separately)
    // document.downloadCount += 1

    return NextResponse.json({
      success: true,
      data: {
        document,
        relatedDocuments,
        metadata: {
          fileSize: DocumentManager.formatFileSize(document.fileSize),
          fileTypeIcon: DocumentManager.getFileTypeIcon(document.fileType),
          downloadUrl: document.fileUrl,
          lastAccessed: new Date().toISOString(),
        },
      },
    });
  } catch (error) {
    console.error('Document detail API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 },
    );
  }
}

// PATCH endpoint for updating document metadata (admin use)
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  const { id } = params;

  try {
    const body = await request.json();
    const document = DocumentManager.getDocumentById(id);

    if (!document) {
      return NextResponse.json(
        {
          success: false,
          error: 'Document not found',
        },
        { status: 404 },
      );
    }

    // In a real application, you would update the document in the database
    // For now, we'll just return the updated document structure
    const updatedDocument = {
      ...document,
      ...body,
      lastModified: new Date().toISOString().split('T')[0],
    };

    return NextResponse.json({
      success: true,
      data: {
        document: updatedDocument,
        message: 'Document updated successfully',
      },
    });
  } catch (error) {
    console.error('Document update API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 },
    );
  }
}

// DELETE endpoint for removing documents (admin use)
export async function DELETE(_request: NextRequest, { params }: RouteParams) {
  const { id } = params;

  try {
    const document = DocumentManager.getDocumentById(id);

    if (!document) {
      return NextResponse.json(
        {
          success: false,
          error: 'Document not found',
        },
        { status: 404 },
      );
    }

    // In a real application, you would delete the document from the database
    // and remove the file from storage

    return NextResponse.json({
      success: true,
      data: {
        message: 'Document deleted successfully',
        deletedDocument: {
          id: document.id,
          title: document.title,
          fileName: document.fileName,
        },
      },
    });
  } catch (error) {
    console.error('Document delete API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 },
    );
  }
}
