// Search-related types for R2R integration

export interface TextResult {
  id?: string;
  text: string;
  type: 'vector' | 'graph' | string;
  document_id: string | null;
  score: number;
  wordCount: number;
  metadata?: {
    title?: string;
    summary?: string;
    url?: string;
    source?: string;
    chunk_index?: number;
    document_type?: string;
    [key: string]: any;
  };
}

export interface R2rSearchResult {
  texts: TextResult[];
  totalWordCount: number;
}

export interface ParseR2rOptions {
  retrieveDocument: boolean;
  maxWordCount: number;
  includeGraph: boolean;
  minScore: number;
  limit: number;
}

export interface SearchResponse {
  query: string;
  results: TextResult[];
  totalChunks: number;
  totalGraphResults: number;
  options: ParseR2rOptions;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
  translatedQueries?: string[];
}

export interface SearchRequest {
  query: string;
  page?: number;
  limit?: number;
  minScore?: number;
  maxWordCount?: number;
  retrieveDocument?: boolean;
}

export interface SearchFilters {
  documentType?: string;
  source?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface SearchState {
  query: string;
  results: TextResult[];
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
  filters: SearchFilters;
}
