// Mock the schema import
jest.mock('@/lib/db/schema', () => ({
  products: 'mocked-products-table',
  sites: 'mocked-sites-table',
}));

// Mock fetch for OpenAI API calls
global.fetch = jest.fn();

// Mock the database
const mockProduct = {
  id: '1',
  productName: 'Halal Chicken',
  companyName: 'Test Company',
  certificateNumber: 'TEST-001',
  certificateType: 'HALAL',
  status: 'Active',
  category: 'Food',
  subcategory: 'Frozen',
  state: 'Selangor',
  country: 'Malaysia',
  website: 'https://test.com',
  contactInfo: '<EMAIL>',
  issuedDate: '2024-01-01',
  expiryDate: '2025-01-01',
  address: 'Test Address',
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockDb = {
  select: jest.fn().mockImplementation(() => ({
    from: jest.fn().mockImplementation(() => ({
      where: jest.fn().mockImplementation(() => ({
        limit: jest.fn().mockImplementation(() => ({
          offset: jest.fn().mockImplementation(() => ({
            orderBy: jest.fn().mockResolvedValue([mockProduct]),
          })),
        })),
      })),
      limit: jest.fn().mockImplementation(() => ({
        offset: jest.fn().mockImplementation(() => ({
          orderBy: jest.fn().mockResolvedValue([mockProduct]),
        })),
      })),
    })),
  })),
};

jest.mock('@/lib/db', () => ({
  db: mockDb,
}));

describe('/api/products/search', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock OpenAI API response
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: jest.fn().mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify({
                language: 'en',
                confidence: 0.95,
                translatedTerms: [],
              }),
            },
          },
        ],
      }),
    });
    // Mock count query and regular queries
    mockDb.select.mockImplementation((fields) => {
      if (fields && fields.count) {
        return {
          from: jest.fn().mockReturnValue([{ count: 1 }]),
        };
      }
      return {
        from: jest.fn().mockImplementation(() => ({
          where: jest.fn().mockImplementation(() => ({
            orderBy: jest.fn().mockResolvedValue([mockProduct]),
          })),
          limit: jest.fn().mockImplementation(() => ({
            offset: jest.fn().mockImplementation(() => ({
              orderBy: jest.fn().mockResolvedValue([mockProduct]),
            })),
          })),
        })),
      };
    });
  });

  it('should return search results for valid query', async () => {
    const { GET } = await import('../route');
    const request = new Request(
      'http://localhost:16010/api/products/search?q=chicken',
    );
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toHaveProperty('query', 'chicken');
    expect(data).toHaveProperty('products');
    expect(data).toHaveProperty('pagination');
    expect(Array.isArray(data.products)).toBe(true);
  });

  it('should return all products when no query parameter is provided', async () => {
    const { GET } = await import('../route');
    const request = new Request('http://localhost:16010/api/products/search');
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toHaveProperty('query', '');
    expect(data).toHaveProperty('products');
    expect(data).toHaveProperty('pagination');
    expect(Array.isArray(data.products)).toBe(true);
  });

  it('should handle pagination in search results', async () => {
    const { GET } = await import('../route');
    const request = new Request(
      'http://localhost:16010/api/products/search?q=halal&page=2&limit=5',
    );
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.pagination.page).toBe(2);
    expect(data.pagination.limit).toBe(5);
  });

  it('should handle multilingual search with OpenAI translation', async () => {
    // Mock OpenAI response for Malay query
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: jest.fn().mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify({
                language: 'ms',
                confidence: 0.95,
                translatedTerms: ['chicken', 'poultry', 'meat'],
              }),
            },
          },
        ],
      }),
    });

    const { GET } = await import('../route');
    const request = new Request(
      'http://localhost:16010/api/products/search?q=ayam',
    );
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toHaveProperty('query', 'ayam');
    expect(data).toHaveProperty('detectedLanguage', 'ms');
    expect(data).toHaveProperty('translatedTerms');
    expect(data).toHaveProperty('searchType', 'multilingual');
    expect(Array.isArray(data.translatedTerms)).toBe(true);
  });

  it('should handle OpenAI API errors gracefully', async () => {
    // Mock OpenAI API error
    (global.fetch as jest.Mock).mockRejectedValueOnce(
      new Error('OpenAI API error'),
    );

    const { GET } = await import('../route');
    const request = new Request(
      'http://localhost:16010/api/products/search?q=makanan',
    );
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toHaveProperty('query', 'makanan');
    expect(data).toHaveProperty('products');
    // Should still return results even if translation fails
  });
});
