# Enhanced Worker Logging System

This logging system provides comprehensive, structured logging for the Cloudflare Worker environment with better console visibility and debugging capabilities.

## Features

- **Structured Logging**: Consistent log format with timestamps, levels, and context
- **Performance Tracking**: Built-in performance monitoring for operations
- **Request Tracking**: End-to-end request tracing with unique IDs
- **Context-Aware**: Automatic context propagation (session, user, platform)
- **Environment-Based**: Configurable log levels based on environment
- **Console-Friendly**: Clear, readable output with emojis and formatting

## Quick Start

### 1. Initialize Logger

```typescript
import { logger } from '../utils/logger';

// Initialize with environment (usually done once at startup)
logger.initialize(env);
```

### 2. Basic Logging

```typescript
// Different log levels
logger.debug('Debug information', { userId: '123' });
logger.info('Processing started', { sessionId: 'abc123' });
logger.warn('Rate limit approaching', { attempts: 8 });
logger.error('Database connection failed', { error: 'timeout' });
logger.success('Operation completed', { duration: 150 });
```

### 3. Specialized Logging

```typescript
// Message flow tracking
logger.messageFlow('VALIDATION', 'Validating user input');
logger.messageFlow('PROCESSING', 'Sending to OpenAI');
logger.messageFlow('COMPLETE', 'Response generated');

// Tool execution
logger.toolCall('search_halal_knowledge', 'Starting search');
logger.toolCall('search_halal_knowledge', 'Found 5 results');

// Session management
logger.session('CREATE', sessionId);
logger.session('UPDATE', sessionId);
logger.session('EXPIRE', sessionId);

// Database operations
logger.database('SELECT', 'Fetching user session');
logger.database('INSERT', 'Storing message');

// API calls
logger.api('POST', '/api/chat', 200, 150);
logger.api('GET', '/api/search', 404, 50);
```

## Advanced Usage

### Request Tracking

```typescript
import { RequestTracker } from '../utils/logger';

// Create tracker for end-to-end request monitoring
const tracker = new RequestTracker(sessionId, userId, platform);

// Track stages
tracker.stage('VALIDATION', 'Validating input');
tracker.stage('PROCESSING', 'Processing with AI');
tracker.stage('STORAGE', 'Storing results');

// Complete tracking
tracker.complete(true, 'Request processed successfully');
```

### Performance Tracking

```typescript
import { PerformanceTracker } from '../utils/logger';

// Track operation performance
const perf = new PerformanceTracker('database_query', { table: 'users' });

// Add checkpoints
perf.checkpoint('connection_established');
perf.checkpoint('query_executed');

// End tracking
const duration = perf.end('Query completed');
```

### Context Propagation

```typescript
// Create base context
const context = {
  sessionId: 'sess_123',
  userId: 'user_456',
  platform: 'web',
  requestId: 'req_789'
};

// Use context in all related logs
logger.info('Starting message processing', context);
logger.debug('Validating input', { ...context, inputLength: 150 });
logger.success('Message processed', { ...context, responseTime: 250 });
```

## Log Levels

Set log level via environment variable:

```bash
# Environment variables
LOG_LEVEL=DEBUG    # Show all logs
LOG_LEVEL=INFO     # Show info, warn, error (default)
LOG_LEVEL=WARN     # Show warn, error only
LOG_LEVEL=ERROR    # Show error only
```

## Output Examples

### Console Output

```
[2024-01-15T10:30:45.123Z] ℹ️ [INFO] Processing web message from user123 [session:sess_abc1|user:user123|platform:web|req:req_def4]
[2024-01-15T10:30:45.125Z] 🔄 [FLOW] [SESSION_SETUP] Getting or creating chat session [session:sess_abc1|user:user123|platform:web|req:req_def4]
[2024-01-15T10:30:45.130Z] 👤 [SESSION] [FOUND_MEMORY] Session: sess_abc1 [session:sess_abc1|user:user123|platform:web|messageCount:5]
[2024-01-15T10:30:45.135Z] 🔧 [TOOL] [search_halal_knowledge] Starting execution [session:sess_abc1|tool:search_halal_knowledge]
[2024-01-15T10:30:45.250Z] ⚡ [PERF] tool_search_halal_knowledge took 115ms [session:sess_abc1|tool:search_halal_knowledge]
[2024-01-15T10:30:45.255Z] ✅ [SUCCESS] Message processed successfully [session:sess_abc1|user:user123|platform:web|responseLength:245]
```

### Development Mode

In development, additional context details are shown:

```
[2024-01-15T10:30:45.123Z] ℹ️ [INFO] Processing web message from user123
   📋 Context: {
     sessionId: "sess_abc123",
     userId: "user123", 
     platform: "web",
     messageType: "text",
     hasMedia: false
   }
```

## Integration Examples

### Message Handler Integration

```typescript
// In messageHandler.ts
async handleIncomingMessage(request: ConsolidatedMessageRequest, env?: WorkerEnv) {
  // Initialize logger
  logger.initialize(env);
  
  // Create request tracker
  const tracker = new RequestTracker(request.sessionId, request.userId, request.platform);
  
  try {
    tracker.stage('VALIDATION', 'Validating request');
    // ... validation logic
    
    tracker.stage('PROCESSING', 'Processing message');
    // ... processing logic
    
    tracker.complete(true);
    return response;
  } catch (error) {
    logger.error('Message processing failed', tracker.getContext(), error);
    tracker.complete(false, error.message);
    throw error;
  }
}
```

### Database Service Integration

```typescript
// In database service
async getChatSession(sessionId: string) {
  logger.database('SELECT', `Fetching session ${sessionId}`);
  
  const perf = new PerformanceTracker('db_get_session', { sessionId });
  
  try {
    const session = await this.db.select()...;
    perf.end('Session fetched successfully');
    
    logger.database('SELECT', `Found session with ${session.messages.length} messages`);
    return session;
  } catch (error) {
    perf.end('Session fetch failed');
    logger.error('Database query failed', { sessionId }, error);
    throw error;
  }
}
```

## Best Practices

1. **Always initialize logger** at application startup
2. **Use appropriate log levels** - debug for detailed info, info for normal flow
3. **Include relevant context** in all log messages
4. **Use performance tracking** for operations > 100ms
5. **Track request flow** for complex operations
6. **Don't log sensitive data** (passwords, tokens, personal info)
7. **Use structured context** instead of string concatenation

## Environment Configuration

```bash
# .env file
LOG_LEVEL=INFO
NODE_ENV=development
ENVIRONMENT=development
```

The logger automatically adjusts behavior based on environment:
- **Development**: Shows detailed context, more verbose output
- **Production**: Optimized for performance, structured JSON-like output
