'use client';

export const runtime = 'edge';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Off,
  Download,
  Filter,
  RefreshCw,
  Send,
  Trash2,
} from 'lucide-react';
import { useCallback, useState } from 'react';
import { NotificationCenter } from '@/components/notifications/NotificationCenter';
import { NotificationSettings } from '@/components/notifications/NotificationSettings';
import { useAdminAuthGuard } from '@/hooks/useAuthGuard';
import { Link } from '@/i18n/navigation';
import { useNotifications } from '@/lib/notifications';
import { UserRole } from '@/types/roles';

export default function NotificationsManagementPage() {
  const { loading } = useAdminAuthGuard([UserRole.ADMIN, UserRole.SUPERVISOR]);

  const {
    notifications,
    clearAll,
    markAllAsRead,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showUrgent,
  } = useNotifications();

  const [showSettings, setShowSettings] = useState(false);
  const [filterType, setFilterType] = useState<string>('all');
  const [customNotification, setCustomNotification] = useState({
    type: 'info' as const,
    title: '',
    message: '',
    urgent: false,
    actionUrl: '',
  });

  const filteredNotifications = notifications.filter((notification) => {
    if (filterType === 'all') return true;
    return notification.type === filterType;
  });

  const handleSendCustomNotification = useCallback(() => {
    if (
      !customNotification.title.trim() ||
      !customNotification.message.trim()
    ) {
      showError('Validation Error', 'Title and message are required');
      return;
    }

    const notificationFunctions = {
      success: showSuccess,
      error: showError,
      warning: showWarning,
      info: showInfo,
    };

    const sendFunction = customNotification.urgent
      ? showUrgent
      : notificationFunctions[customNotification.type];

    sendFunction(
      customNotification.title,
      customNotification.message,
      customNotification.actionUrl || undefined,
    );

    // Reset form
    setCustomNotification({
      type: 'info',
      title: '',
      message: '',
      urgent: false,
      actionUrl: '',
    });

    showSuccess(
      'Notification Sent',
      'Custom notification has been sent successfully',
    );
  }, [
    customNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showUrgent,
  ]);

  const handleExportNotifications = useCallback(() => {
    const exportData = {
      notifications: filteredNotifications,
      exportedAt: new Date().toISOString(),
      totalCount: filteredNotifications.length,
      filters: { type: filterType },
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `notifications-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [filteredNotifications, filterType]);

  const sendTestNotifications = useCallback(() => {
    showInfo('Test Info', 'This is a test info notification');
    setTimeout(
      () => showSuccess('Test Success', 'This is a test success notification'),
      1000,
    );
    setTimeout(
      () => showWarning('Test Warning', 'This is a test warning notification'),
      2000,
    );
    setTimeout(
      () => showError('Test Error', 'This is a test error notification'),
      3000,
    );
    setTimeout(
      () => showUrgent('Test Urgent', 'This is a test urgent notification'),
      4000,
    );
  }, [showInfo, showSuccess, showWarning, showError, showUrgent]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" />
          <p className="mt-4 text-gray-600">Loading notifications...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Link
                href="/admin/dashboard"
                className="p-2 hover:bg-gray-100 rounded-md"
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
              <div className="flex items-center">
                <Bell className="h-8 w-8 text-blue-600 mr-3" />
                <h1 className="text-2xl font-bold text-gray-900">
                  Notification Management
                </h1>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <NotificationCenter />

              <button
                type="button"
                onClick={() => setShowSettings(!showSettings)}
                className="flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
              >
                <Bell className="h-4 w-4 mr-2" />
                Settings
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Notification List */}
          <div className="lg:col-span-2 space-y-6">
            {/* Controls */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex flex-wrap items-center justify-between gap-4">
                <div className="flex items-center space-x-4">
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Types</option>
                    <option value="info">Info</option>
                    <option value="success">Success</option>
                    <option value="warning">Warning</option>
                    <option value="error">Error</option>
                  </select>

                  <span className="text-sm text-gray-600">
                    {filteredNotifications.length} notification
                    {filteredNotifications.length !== 1 ? 's' : ''}
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    type="button"
                    onClick={sendTestNotifications}
                    className="flex items-center px-3 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 text-sm"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    Test All
                  </button>

                  <button
                    type="button"
                    onClick={handleExportNotifications}
                    className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </button>

                  <button
                    type="button"
                    onClick={markAllAsRead}
                    className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
                  >
                    Mark All Read
                  </button>

                  <button
                    type="button"
                    onClick={clearAll}
                    className="flex items-center px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Clear All
                  </button>
                </div>
              </div>
            </div>

            {/* Notification History */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">
                  Notification History
                </h3>
              </div>

              <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
                {filteredNotifications.length === 0 ? (
                  <div className="p-8 text-center">
                    <BellOff className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">
                      No notifications
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      No notifications match your current filter.
                    </p>
                  </div>
                ) : (
                  filteredNotifications.map((notification) => (
                    <div key={notification.id} className="p-4 hover:bg-gray-50">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span
                              className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                notification.type === 'success'
                                  ? 'bg-green-100 text-green-800'
                                  : notification.type === 'error'
                                    ? 'bg-red-100 text-red-800'
                                    : notification.type === 'warning'
                                      ? 'bg-yellow-100 text-yellow-800'
                                      : 'bg-blue-100 text-blue-800'
                              }`}
                            >
                              {notification.type}
                            </span>
                            {notification.urgent && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Urgent
                              </span>
                            )}
                            {!notification.read && (
                              <span className="w-2 h-2 bg-blue-500 rounded-full" />
                            )}
                          </div>
                          <h4 className="text-sm font-medium text-gray-900 mt-1">
                            {notification.title}
                          </h4>
                          <p className="text-sm text-gray-600 mt-1">
                            {notification.message}
                          </p>
                          <p className="text-xs text-gray-400 mt-2">
                            {new Date(notification.timestamp).toLocaleString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Settings Panel */}
            {showSettings && (
              <NotificationSettings onClose={() => setShowSettings(false)} />
            )}

            {/* Send Custom Notification */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Send Custom Notification
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type
                  </label>
                  <select
                    value={customNotification.type}
                    onChange={(e) =>
                      setCustomNotification((prev) => ({
                        ...prev,
                        type: e.target.value as any,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="info">Info</option>
                    <option value="success">Success</option>
                    <option value="warning">Warning</option>
                    <option value="error">Error</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Title
                  </label>
                  <input
                    type="text"
                    value={customNotification.title}
                    onChange={(e) =>
                      setCustomNotification((prev) => ({
                        ...prev,
                        title: e.target.value,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Notification title..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Message
                  </label>
                  <textarea
                    value={customNotification.message}
                    onChange={(e) =>
                      setCustomNotification((prev) => ({
                        ...prev,
                        message: e.target.value,
                      }))
                    }
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Notification message..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Action URL (optional)
                  </label>
                  <input
                    type="text"
                    value={customNotification.actionUrl}
                    onChange={(e) =>
                      setCustomNotification((prev) => ({
                        ...prev,
                        actionUrl: e.target.value,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="/admin/sessions"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="urgent"
                    checked={customNotification.urgent}
                    onChange={(e) =>
                      setCustomNotification((prev) => ({
                        ...prev,
                        urgent: e.target.checked,
                      }))
                    }
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label
                    htmlFor="urgent"
                    className="ml-2 text-sm text-gray-700"
                  >
                    Mark as urgent
                  </label>
                </div>

                <button
                  type="button"
                  onClick={handleSendCustomNotification}
                  className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  <Send className="h-4 w-4 mr-2" />
                  Send Notification
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
