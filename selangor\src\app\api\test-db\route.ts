import { sql } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { createSuccessResponse, handleApiError } from '@/lib/api-response';

export async function GET() {
  // Create direct database connection with test-optimized settings
  const connectionString =
    process.env.DATABASE_URL ||
    'postgresql://root:000000@localhost:5432/halal_chat';
  const client = postgres(connectionString, {
    prepare: false,
    max: 1, // Single connection for testing
    idle_timeout: 5, // Quick cleanup for test
    max_lifetime: 60 * 5, // 5 minutes max for test connections
    connect_timeout: 5, // Fast timeout for tests
    debug: false,
    connection: {
      application_name: 'halal-test-endpoint',
    },
    transform: {
      undefined: null,
    },
  });
  const db = drizzle(client);

  try {
    // Test basic database connection
    const result = await db.execute(sql`SELECT 1 as test`);

    // Test current database
    const currentDb = await db.execute(
      sql`SELECT current_database() as db_name`,
    );

    // List all tables
    const allTables = await db.execute(sql`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);

    // Test if products table exists
    const tableCheck = await db.execute(sql`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_name = 'products'
    `);

    let productsCount = null;
    try {
      productsCount = await db.execute(
        sql`SELECT COUNT(*) as count FROM products`,
      );
    } catch (e) {
      productsCount = {
        error: e instanceof Error ? e.message : 'Unknown error',
      };
    }

    const responseData = {
      status: 'success',
      connection: 'working',
      testQuery: result,
      currentDatabase: currentDb[0]?.db_name,
      allTables: allTables.map((t) => t.table_name),
      productsTableExists: tableCheck.length > 0,
      tableCheck,
      productsCount,
      databaseUrl: connectionString.replace(/:[^:]*@/, ':***@'), // Hide password
    };

    // Clean up the connection
    await client.end();

    return createSuccessResponse(responseData);
  } catch (error) {
    console.error('Database test error:', error);

    // Ensure connection is closed even on error
    try {
      await client.end();
    } catch (closeError) {
      console.error('Error closing database connection:', closeError);
    }

    return handleApiError(error, 'Database test failed');
  }
}
