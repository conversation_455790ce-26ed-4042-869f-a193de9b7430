# Selangor Halal Search Platform

This is a [Next.js](https://nextjs.org) project for the Selangor Halal certification search platform, featuring comprehensive search capabilities and analytics tracking.

## Features

- **Multi-type Search**: Web content, products, companies, and semantic search
- **Bilingual Support**: Automatic language detection and translation (Malay/English)
- **Search Analytics**: Comprehensive tracking and reporting of search activities
- **Admin Dashboard**: Analytics dashboard for monitoring search performance
- **Site-specific Data**: Configured for Selangor Halal (Site ID: 2)

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL database (shared with server/ directory)
- R2R service for semantic search

### Environment Variables

Create a `.env.local` file with:

```bash
# Database
DATABASE_URL=postgresql://root:000000@localhost:5432/halal

# R2R Configuration
R2R_URL=http://localhost:7272
R2R_COLLECTION_ID=selangor-halal

# OpenAI (for translation)
OPENAI_API_KEY=your_openai_api_key
```

### Development Server

```bash
npm run dev
```

Open [http://localhost:16010](http://localhost:16010) with your browser to see the result.

## Search Analytics

The platform includes comprehensive search analytics tracking for monitoring user search behavior and system performance.

### Admin Access

- **URL**: `/cp1` (admin login)
- **Credentials**:
  - Username: `cp1`
  - Password: `cp1admin123`
  - Site ID: 2 (halalselangor)

### Analytics Dashboard

Access the analytics dashboard at `/analytics` to view:

- **Summary Metrics**: Total searches, success rates, average response times
- **Search Types**: Breakdown by web, products, companies, semantic searches
- **Top Queries**: Most popular search terms with success rates
- **Recent Activity**: Latest search activities with details
- **Time Filters**: View data for 1 day, 7 days, 30 days, or all time

### Analytics API Endpoints

#### GET `/api/analytics/search`
Retrieve detailed search logs with filtering options.

**Query Parameters:**
- `page` - Page number (default: 1)
- `limit` - Results per page (default: 50)
- `searchType` - Filter by type: 'web', 'products', 'companies', 'semantic'
- `startDate` - ISO date string for start date
- `endDate` - ISO date string for end date
- `hasResults` - Filter by success: 'true', 'false', or omit for all

**Response:**
```json
{
  "analytics": [
    {
      "id": 1,
      "searchQuery": "halal chicken",
      "searchType": "products",
      "resultsCount": 15,
      "hasResults": true,
      "responseTime": 245,
      "userAgent": "Mozilla/5.0...",
      "ipAddress": "***********",
      "sessionId": "sess_123",
      "userId": null,
      "searchFilters": "{\"page\":1,\"limit\":10}",
      "createdAt": "2025-01-01T12:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 100,
    "hasMore": true,
    "totalPages": 2
  }
}
```

#### GET `/api/analytics/summary`
Get aggregated analytics data and insights.

**Query Parameters:**
- `period` - Time period: '1d', '7d', '30d', 'all' (default: '7d')
- `startDate` - Custom start date (overrides period)
- `endDate` - Custom end date (overrides period)

**Response:**
```json
{
  "totalSearches": 1250,
  "searchesWithResults": 1100,
  "successRate": 0.88,
  "avgResponseTime": 234.5,
  "searchesByType": [
    { "type": "products", "count": 650 },
    { "type": "web", "count": 400 },
    { "type": "companies", "count": 150 },
    { "type": "semantic", "count": 50 }
  ],
  "topQueries": [
    {
      "query": "halal chicken",
      "count": 45,
      "avgResultsCount": 12.3,
      "successRate": 0.95
    }
  ],
  "searchesByHour": [
    { "hour": 9, "count": 25 },
    { "hour": 10, "count": 35 }
  ]
}
```

### Tracked Data

The analytics system automatically tracks:

- **Search Queries**: Actual search terms used
- **Search Types**: Web content, products, companies, semantic
- **Results**: Count and success status
- **Performance**: Response times in milliseconds
- **User Context**: IP address, user agent, session ID
- **Filters**: Applied search parameters and filters
- **Timestamps**: When searches were performed

### Database Schema

The `search_analytics` table stores:

```sql
CREATE TABLE search_analytics (
  id SERIAL PRIMARY KEY,
  site_id INTEGER NOT NULL,
  search_query TEXT NOT NULL,
  search_type VARCHAR(50) NOT NULL,
  results_count INTEGER DEFAULT 0 NOT NULL,
  has_results BOOLEAN DEFAULT false NOT NULL,
  response_time INTEGER,
  user_agent TEXT,
  ip_address VARCHAR(45),
  session_id VARCHAR(255),
  user_id INTEGER,
  search_filters TEXT,
  created_at TIMESTAMP DEFAULT now() NOT NULL
);
```

## Search Features

### Available Search Types

1. **Web Content Search** (`/api/search`)
   - Searches through R2R document collection
   - Supports multilingual queries with automatic translation
   - Returns document chunks with relevance scores

2. **Product Search** (`/api/products/search`)
   - Searches halal-certified products
   - Supports keyword matching on product names and companies
   - Includes multilingual search with Malay-English translation

3. **Company Search** (`/api/companies/search`)
   - Searches halal certification companies
   - Matches company names, registration numbers, and addresses
   - No translation (as requested for Selangor branch)

4. **Semantic Product Search** (`/api/products/semantic-search`)
   - Vector-based semantic search using embeddings
   - Finds products by meaning rather than exact keywords
   - Supports multilingual queries

### Bilingual Support

- **Automatic Detection**: Detects Malay vs English queries
- **Translation**: Translates Malay terms to English for broader search
- **Combined Results**: Merges results from original and translated queries
- **Deduplication**: Removes duplicate results while preserving relevance

## Learn More

- [Search Documentation](./SEARCH_README.md)
- [Bilingual Search](./BILINGUAL_SEARCH_README.md)
- [Semantic Search](./SEMANTIC_SEARCH_README.md)
- [Next.js Documentation](https://nextjs.org/docs)
