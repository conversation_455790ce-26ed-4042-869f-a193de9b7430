'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Save } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { AdminLayout } from '@/components/admin/AdminLayout';
import {
  Button,
  Input,
  Select,
  type SelectOption,
  Textarea,
} from '@/components/ui';

const businessTypeOptions: SelectOption[] = [
  { value: 'Sdn Bhd', label: 'Sdn Bhd' },
  { value: 'Bhd', label: 'Bhd' },
  { value: 'Enterprise', label: 'Enterprise' },
  { value: 'Partnership', label: 'Partnership' },
  { value: 'Sole Proprietorship', label: 'Sole Proprietorship' },
];

const certificateStatusOptions: SelectOption[] = [
  { value: 'Active', label: 'Active' },
  { value: 'Inactive', label: 'Inactive' },
  { value: 'Pending', label: 'Pending' },
  { value: 'Expired', label: 'Expired' },
  { value: 'Suspended', label: 'Suspended' },
];

const certificateTypeOptions: SelectOption[] = [
  { value: 'HALAL', label: 'HALAL' },
  { value: 'HALAL JAKIM', label: 'HALAL JAKIM' },
  { value: 'HALAL SELANGOR', label: 'HALAL SELANGOR' },
];

const categoryOptions: SelectOption[] = [
  { value: 'Food Manufacturing', label: 'Food Manufacturing' },
  { value: 'Food Processing', label: 'Food Processing' },
  { value: 'Restaurant', label: 'Restaurant' },
  { value: 'Catering', label: 'Catering' },
  { value: 'Retail', label: 'Retail' },
  { value: 'Wholesale', label: 'Wholesale' },
  { value: 'Import/Export', label: 'Import/Export' },
  { value: 'Cosmetics', label: 'Cosmetics' },
  { value: 'Pharmaceutical', label: 'Pharmaceutical' },
  { value: 'Others', label: 'Others' },
];

const stateOptions: SelectOption[] = [
  { value: 'Selangor', label: 'Selangor' },
  { value: 'Kuala Lumpur', label: 'Kuala Lumpur' },
  { value: 'Johor', label: 'Johor' },
  { value: 'Penang', label: 'Penang' },
  { value: 'Perak', label: 'Perak' },
  { value: 'Kedah', label: 'Kedah' },
  { value: 'Kelantan', label: 'Kelantan' },
  { value: 'Terengganu', label: 'Terengganu' },
  { value: 'Pahang', label: 'Pahang' },
  { value: 'Negeri Sembilan', label: 'Negeri Sembilan' },
  { value: 'Melaka', label: 'Melaka' },
  { value: 'Perlis', label: 'Perlis' },
  { value: 'Sabah', label: 'Sabah' },
  { value: 'Sarawak', label: 'Sarawak' },
];

interface EditCompanyPageProps {
  params: Promise<{ id: string }>;
}

export default function EditCompanyPage({ params }: EditCompanyPageProps) {
  const router = useRouter();
  const [companyId, setCompanyId] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingCompany, setIsLoadingCompany] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formData, setFormData] = useState({
    companyName: '',
    registrationNumber: '',
    businessType: '',
    category: '',
    subcategory: '',
    address: '',
    state: '',
    postcode: '',
    city: '',
    country: 'Malaysia',
    phone: '',
    fax: '',
    email: '',
    website: '',
    contactPerson: '',
    certificateNumber: '',
    certificateType: '',
    certificateStatus: '',
    issuedDate: '',
    expiryDate: '',
    sourceUrl: '',
  });

  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params;
      setCompanyId(resolvedParams.id);
    };
    getParams();
  }, [params]);

  useEffect(() => {
    if (companyId) {
      fetchCompany();
    }
  }, [companyId]);

  const fetchCompany = async () => {
    setIsLoadingCompany(true);
    try {
      const response = await fetch(`/api/companies/${companyId}`);
      if (response.ok) {
        const data = await response.json();
        const company = data.company;

        setFormData({
          companyName: company.companyName || '',
          registrationNumber: company.registrationNumber || '',
          businessType: company.businessType || '',
          category: company.category || '',
          subcategory: company.subcategory || '',
          address: company.address || '',
          state: company.state || '',
          postcode: company.postcode || '',
          city: company.city || '',
          country: company.country || 'Malaysia',
          phone: company.phone || '',
          fax: company.fax || '',
          email: company.email || '',
          website: company.website || '',
          contactPerson: company.contactPerson || '',
          certificateNumber: company.certificateNumber || '',
          certificateType: company.certificateType || '',
          certificateStatus: company.certificateStatus || '',
          issuedDate: company.issuedDate || '',
          expiryDate: company.expiryDate || '',
          sourceUrl: company.sourceUrl || '',
        });
      } else {
        console.error('Failed to fetch company');
        router.push('/cp1/companies');
      }
    } catch (error) {
      console.error('Error fetching company:', error);
      router.push('/cp1/companies');
    } finally {
      setIsLoadingCompany(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.companyName.trim()) {
      newErrors.companyName = 'Company name is required';
    }

    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (formData.website && !formData.website.startsWith('http')) {
      newErrors.website = 'Website URL must start with http:// or https://';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/companies', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: Number.parseInt(companyId), ...formData }),
      });

      if (response.ok) {
        router.push('/cp1/companies');
      } else {
        const errorData = await response.json();
        console.error('Failed to update company:', errorData);
      }
    } catch (error) {
      console.error('Error updating company:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingCompany) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading company...</div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/cp1/companies">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Companies
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Edit Company</h1>
            <p className="text-gray-600">Update company information</p>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Basic Information
                </h3>

                <Input
                  label="Company Name *"
                  value={formData.companyName}
                  onChange={(e) =>
                    handleInputChange('companyName', e.target.value)
                  }
                  error={errors.companyName}
                  placeholder="Enter company name"
                />

                <Input
                  label="Registration Number"
                  value={formData.registrationNumber}
                  onChange={(e) =>
                    handleInputChange('registrationNumber', e.target.value)
                  }
                  placeholder="Enter registration number"
                />

                <Select
                  label="Business Type"
                  value={formData.businessType}
                  onChange={(e) =>
                    handleInputChange('businessType', e.target.value)
                  }
                  options={businessTypeOptions}
                  placeholder="Select business type"
                />

                <Select
                  label="Category"
                  value={formData.category}
                  onChange={(e) =>
                    handleInputChange('category', e.target.value)
                  }
                  options={categoryOptions}
                  placeholder="Select category"
                />

                <Input
                  label="Subcategory"
                  value={formData.subcategory}
                  onChange={(e) =>
                    handleInputChange('subcategory', e.target.value)
                  }
                  placeholder="Enter subcategory"
                />
              </div>

              {/* Certificate Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Certificate Information
                </h3>

                <Input
                  label="Certificate Number"
                  value={formData.certificateNumber}
                  onChange={(e) =>
                    handleInputChange('certificateNumber', e.target.value)
                  }
                  placeholder="Enter certificate number"
                />

                <Select
                  label="Certificate Type"
                  value={formData.certificateType}
                  onChange={(e) =>
                    handleInputChange('certificateType', e.target.value)
                  }
                  options={certificateTypeOptions}
                  placeholder="Select certificate type"
                />

                <Select
                  label="Certificate Status"
                  value={formData.certificateStatus}
                  onChange={(e) =>
                    handleInputChange('certificateStatus', e.target.value)
                  }
                  options={certificateStatusOptions}
                  placeholder="Select certificate status"
                />

                <Input
                  label="Issued Date"
                  type="date"
                  value={formData.issuedDate}
                  onChange={(e) =>
                    handleInputChange('issuedDate', e.target.value)
                  }
                />

                <Input
                  label="Expiry Date"
                  type="date"
                  value={formData.expiryDate}
                  onChange={(e) =>
                    handleInputChange('expiryDate', e.target.value)
                  }
                />
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <Link href="/cp1/companies">
                <Button variant="outline">Cancel</Button>
              </Link>
              <Button type="submit" isLoading={isLoading}>
                <Save className="h-4 w-4 mr-2" />
                Update Company
              </Button>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
