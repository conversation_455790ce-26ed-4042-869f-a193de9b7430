# Halal Malaysia AI Chat System - Agent Quick Reference

## Commands
- **Build**: `bun lint:fix` (uses Biome for linting/formatting)
- **Test All**: `bun test` (root), `cd server && bun test` (server), `cd front && bun test` (frontend)
- **Single Test**: `cd server && bun test <test-file.test.ts>` or `cd front && bun test <test-file.test.js>`
- **Dev Start**: `bun dev` (starts all: frontend:16000, server:8787, admin)
- **DB Operations**: `bun db:migrate`, `bun db:seed`, `bun db:studio`

## Architecture
- **Monorepo**: `front/` (Next.js 15 + Tailwind 4), `server/` (Cloudflare Workers/Hono), `admin/` (admin dashboard)
- **Database**: PostgreSQL with Drizzle ORM (`server/src/db/schema.ts`)
- **Package Manager**: Uses `bun` globally, some scripts use `pnpm` (legacy)
- **Runtime**: Cloudflare Workers (prod), Node.js/Bun (dev) - avoid Node.js-specific features (fs, path)

## Code Style (Biome + .clinerules)
- TypeScript everywhere, single quotes, 2-space indent, 80 char line width
- Components: PascalCase, hooks at top, handlers after, render last
- DB changes: always run `drizzle-kit generate` to create migrations
- Always run `bun lint:fix` before committing
- Use Drizzle ORM for all database operations, no raw SQL
