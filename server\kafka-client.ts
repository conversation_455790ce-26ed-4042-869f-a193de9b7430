#!/usr/bin/env tsx

import {
  type Admin,
  type Consumer,
  Kafka,
  Partitioners,
  type Producer,
} from 'kafkajs';

interface TestMessage {
  id: number;
  message: string;
  timestamp: string;
  type?: string;
}

interface KafkaClientConfig {
  clientId: string;
  brokers: string[];
  sasl?: {
    mechanism: string;
    username: string;
    password: string;
  };
}

export class KafkaClient {
  private kafka: Kafka;
  private producer: Producer;
  private consumer: Consumer;
  private admin: Admin;

  constructor(config: KafkaClientConfig) {
    this.kafka = new Kafka({
      ...config,
      requestTimeout: 30000,
      connectionTimeout: 10000,
      retry: {
        initialRetryTime: 100,
        retries: 8,
      },
    });
    this.producer = this.kafka.producer({
      createPartitioner: Partitioners.LegacyPartitioner,
    });
    this.consumer = this.kafka.consumer({ groupId: 'test-group' });
    this.admin = this.kafka.admin();
  }

  async publishMessage(
    topic: string,
    key: string,
    message: TestMessage,
  ): Promise<void> {
    try {
      await this.producer.send({
        topic,
        messages: [
          {
            key,
            value: JSON.stringify(message),
            timestamp: Date.now().toString(),
          },
        ],
      });
      console.log(`✅ Message published to ${topic}:`, message);
    } catch (error) {
      console.error('❌ Error publishing message:', error);
      throw error;
    }
  }

  async subscribeToTopic(topic: string): Promise<void> {
    try {
      await this.consumer.subscribe({ topic, fromBeginning: true });

      await this.consumer.run({
        eachMessage: async ({ topic, partition, message }) => {
          const value = message.value
            ? JSON.parse(message.value.toString())
            : null;
          console.log(`📨 Received message from ${topic}:`, {
            partition,
            offset: message.offset,
            key: message.key?.toString(),
            value,
            timestamp: message.timestamp,
          });
        },
      });
    } catch (error) {
      console.error('❌ Error subscribing to topic:', error);
      throw error;
    }
  }

  async createTopic(topicName: string): Promise<void> {
    try {
      await this.admin.createTopics({
        topics: [
          {
            topic: topicName,
            numPartitions: 3,
            replicationFactor: 1,
          },
        ],
        timeout: 10000,
        validateOnly: false,
      });
      console.log(`✅ Topic '${topicName}' created successfully`);
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'type' in error) {
        if (error.type === 'TOPIC_ALREADY_EXISTS') {
          console.log(`ℹ️  Topic '${topicName}' already exists`);
          return;
        }
      }
      console.error('❌ Error creating topic:', error);
      throw error;
    }
  }

  async listTopics(): Promise<string[]> {
    try {
      const topics = await this.admin.listTopics();
      console.log('📋 Available topics:', topics);
      return topics;
    } catch (error) {
      console.error('❌ Error listing topics:', error);
      throw error;
    }
  }

  async connect(): Promise<void> {
    await Promise.all([
      this.producer.connect(),
      this.consumer.connect(),
      this.admin.connect(),
    ]);
  }

  async disconnect(): Promise<void> {
    await Promise.all([
      this.producer.disconnect(),
      this.consumer.disconnect(),
      this.admin.disconnect(),
    ]);
  }

  async connectProducer(): Promise<void> {
    await this.producer.connect();
  }

  async connectConsumer(): Promise<void> {
    await this.consumer.connect();
  }

  async disconnectProducer(): Promise<void> {
    await this.producer.disconnect();
  }

  async disconnectConsumer(): Promise<void> {
    await this.consumer.disconnect();
  }
}

// CLI functionality
async function main(): Promise<void> {
  const command = process.argv[2];
  const topic = process.argv[3] || 'test-topic';

  console.log('🚀 Halal Kafka Client starting...');
  console.log('🔗 Connecting to RedPanda at localhost:19092');

  const client = new KafkaClient({
    clientId: 'halal-test-client',
    brokers: ['localhost:19092'],
    // Add authentication if needed
    // sasl: {
    //   mechanism: 'plain',
    //   username: 'myuser',
    //   password: 'mypassword'
    // }
  });

  try {
    switch (command) {
      case 'create':
        await client.admin.connect();
        await client.createTopic(topic);
        await client.admin.disconnect();
        break;

      case 'list':
        await client.admin.connect();
        await client.listTopics();
        await client.admin.disconnect();
        break;

      case 'publish':
        await client.connectProducer();
        await client.admin.connect();
        await client.createTopic(topic);
        await client.admin.disconnect();

        // Publish test messages
        for (let i = 1; i <= 5; i++) {
          await client.publishMessage(topic, `key-${i}`, {
            id: i,
            message: `Test message ${i}`,
            timestamp: new Date().toISOString(),
            type: 'test',
          });
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }

        await client.disconnectProducer();
        break;

      case 'subscribe':
        await client.connectConsumer();
        await client.admin.connect();
        await client.createTopic(topic);
        await client.admin.disconnect();

        console.log(`👂 Subscribing to topic '${topic}'...`);
        console.log('Press Ctrl+C to stop');

        // Handle graceful shutdown
        process.on('SIGINT', async () => {
          console.log('\n🛑 Shutting down consumer...');
          await client.disconnectConsumer();
          process.exit(0);
        });

        await client.subscribeToTopic(topic);
        break;

      case 'test': {
        // Run a full test - publish and subscribe
        await client.connect();
        await client.createTopic(topic);

        console.log(`🧪 Running full test on topic '${topic}'...`);

        // Start subscribing in background
        const subscribePromise = client.subscribeToTopic(topic);

        // Wait a bit then publish messages
        setTimeout(async () => {
          for (let i = 1; i <= 3; i++) {
            await client.publishMessage(topic, `test-${i}`, {
              id: i,
              message: `Full test message ${i}`,
              timestamp: new Date().toISOString(),
            });
            await new Promise((resolve) => setTimeout(resolve, 2000));
          }
        }, 2000);

        // Let it run for 15 seconds then cleanup
        setTimeout(async () => {
          console.log('\n✅ Test completed. Cleaning up...');
          await client.disconnect();
          process.exit(0);
        }, 15000);

        await subscribePromise;
        break;
      }

      default:
        console.log(`
Usage: tsx kafka-client.ts <command> [topic]

Commands:
  create [topic]     - Create a topic (default: test-topic)
  list              - List all topics
  publish [topic]   - Publish 5 test messages to topic
  subscribe [topic] - Subscribe to topic and listen for messages
  test [topic]      - Run full test (publish + subscribe)

Examples:
  tsx kafka-client.ts create my-topic
  tsx kafka-client.ts list
  tsx kafka-client.ts publish my-topic
  tsx kafka-client.ts subscribe my-topic
  tsx kafka-client.ts test my-topic
        `);
        break;
    }
  } catch (error) {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  }
}

// Run the client if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}
