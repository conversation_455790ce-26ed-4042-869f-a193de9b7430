'use client';

export const runtime = 'edge';

import {
  Calendar,
  CheckCircle,
  Clock,
  Filter,
  MapPin,
  Search,
  XCircle,
} from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { Suspense, useCallback, useEffect, useState } from 'react';
import type { BreadcrumbItem } from '@/components/breadcrumb';
import { PageWrapper } from '@/components/page-wrapper';
import { SearchWidget } from '@/components/search-widget';
import { useSearch } from '@/hooks/use-api';
import { useLanguage } from '@/lib/language-context';

function SearchContent() {
  const { language } = useLanguage();
  const searchParams = useSearchParams();
  const { search, results, loading, error } = useSearch();

  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState({
    status: '',
    category: '',
    country: '',
  });
  const [sortBy, setSortBy] = useState('companyName');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  const breadcrumbs: BreadcrumbItem[] = [
    {
      label: language === 'en' ? 'Home' : 'Laman Utama',
      href: '/',
    },
    {
      label: language === 'en' ? 'Search Results' : 'Hasil Carian',
      href: '/search',
    },
  ];

  const handleSearch = useCallback(
    async (searchQuery: string = query) => {
      if (!searchQuery.trim()) {
        return;
      }

      await search({
        query: searchQuery,
        ...filters,
        sortBy,
        sortOrder,
        page: 1,
        limit: 20,
      });
    },
    [query, filters, sortBy, sortOrder, search],
  );

  useEffect(() => {
    const q = searchParams.get('q');
    if (q) {
      setQuery(q);
      handleSearch(q);
    }
  }, [searchParams, handleSearch]);

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    if (query) {
      search({
        query,
        ...newFilters,
        sortBy,
        sortOrder,
        page: 1,
        limit: 20,
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'valid':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'expired':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'suspended':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'valid':
        return 'bg-green-100 text-green-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      case 'suspended':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'en' ? 'en-US' : 'ms-MY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <PageWrapper
      title="Search Results"
      titleBM="Hasil Carian"
      description="Search for Halal certified companies and products"
      descriptionBM="Cari syarikat dan produk yang disijilkan Halal"
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-8">
        {/* Search Section */}
        <div className="card">
          <h2 className="text-xl font-bold mb-4 text-gray-900">
            {language === 'en'
              ? 'Search Halal Certificates'
              : 'Cari Sijil Halal'}
          </h2>
          <SearchWidget
            onSearch={setQuery}
            onResultSelect={(result) => console.log('Selected:', result)}
            placeholder={
              language === 'en'
                ? 'Search by company name, product, or certificate number...'
                : 'Cari mengikut nama syarikat, produk, atau nombor sijil...'
            }
            showRecentSearches={true}
          />
        </div>

        {/* Filters */}
        <div className="card">
          <div className="flex items-center gap-2 mb-4">
            <Filter className="w-5 h-5 text-gray-600" />
            <h3 className="text-lg font-semibold text-gray-900">
              {language === 'en' ? 'Filters' : 'Penapis'}
            </h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {language === 'en' ? 'Status' : 'Status'}
              </label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-transparent bg-white text-gray-800"
              >
                <option value="">
                  {language === 'en' ? 'All Status' : 'Semua Status'}
                </option>
                <option value="valid">
                  {language === 'en' ? 'Valid' : 'Sah'}
                </option>
                <option value="expired">
                  {language === 'en' ? 'Expired' : 'Tamat Tempoh'}
                </option>
                <option value="suspended">
                  {language === 'en' ? 'Suspended' : 'Digantung'}
                </option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {language === 'en' ? 'Category' : 'Kategori'}
              </label>
              <select
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-transparent bg-white text-gray-800"
              >
                <option value="">
                  {language === 'en' ? 'All Categories' : 'Semua Kategori'}
                </option>
                <option value="food">
                  {language === 'en' ? 'Food Products' : 'Produk Makanan'}
                </option>
                <option value="cosmetics">
                  {language === 'en' ? 'Cosmetics' : 'Kosmetik'}
                </option>
                <option value="pharmaceuticals">
                  {language === 'en' ? 'Pharmaceuticals' : 'Farmaseutikal'}
                </option>
                <option value="services">
                  {language === 'en' ? 'Services' : 'Perkhidmatan'}
                </option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {language === 'en' ? 'Country' : 'Negara'}
              </label>
              <select
                value={filters.country}
                onChange={(e) => handleFilterChange('country', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-transparent bg-white text-gray-800"
              >
                <option value="">
                  {language === 'en' ? 'All Countries' : 'Semua Negara'}
                </option>
                <option value="malaysia">
                  {language === 'en' ? 'Malaysia' : 'Malaysia'}
                </option>
                <option value="singapore">
                  {language === 'en' ? 'Singapore' : 'Singapura'}
                </option>
                <option value="thailand">
                  {language === 'en' ? 'Thailand' : 'Thailand'}
                </option>
                <option value="indonesia">
                  {language === 'en' ? 'Indonesia' : 'Indonesia'}
                </option>
              </select>
            </div>
          </div>
        </div>

        {/* Results */}
        <div className="card">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              {language === 'en' ? 'Search Results' : 'Hasil Carian'}
              {results.length > 0 && (
                <span className="ml-2 text-sm font-normal text-gray-600">
                  ({results.length} {language === 'en' ? 'results' : 'hasil'})
                </span>
              )}
            </h3>
            <div className="flex items-center gap-2">
              <label className="text-sm text-gray-600">
                {language === 'en' ? 'Sort by:' : 'Susun mengikut:'}
              </label>
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-');
                  setSortBy(field);
                  setSortOrder(order as 'asc' | 'desc');
                  if (query) {
                    handleSearch();
                  }
                }}
                className="px-3 py-1 border border-gray-300 rounded text-sm bg-white text-gray-800"
              >
                <option value="companyName-asc">
                  {language === 'en' ? 'Company A-Z' : 'Syarikat A-Z'}
                </option>
                <option value="companyName-desc">
                  {language === 'en' ? 'Company Z-A' : 'Syarikat Z-A'}
                </option>
                <option value="issueDate-desc">
                  {language === 'en' ? 'Newest First' : 'Terbaru Dahulu'}
                </option>
                <option value="issueDate-asc">
                  {language === 'en' ? 'Oldest First' : 'Terlama Dahulu'}
                </option>
              </select>
            </div>
          </div>

          {loading && (
            <div className="text-center py-8">
              <div className="inline-flex items-center gap-2 text-gray-600">
                <div className="w-5 h-5 border-2 border-primary-green border-t-transparent rounded-full animate-spin" />
                {language === 'en' ? 'Searching...' : 'Mencari...'}
              </div>
            </div>
          )}

          {error && (
            <div className="text-center py-8">
              <p className="text-red-600">{error}</p>
            </div>
          )}

          {!loading && !error && results.length === 0 && query && (
            <div className="text-center py-8">
              <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                {language === 'en' ? 'No results found' : 'Tiada hasil ditemui'}
              </h4>
              <p className="text-gray-600">
                {language === 'en'
                  ? 'Try adjusting your search terms or filters'
                  : 'Cuba laraskan istilah carian atau penapis anda'}
              </p>
            </div>
          )}

          {!loading && !error && results.length > 0 && (
            <div className="space-y-4">
              {results.map((result) => (
                <div
                  key={result.certificateNumber}
                  className="p-4 border border-gray-200 rounded-lg hover:border-primary-green transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="font-semibold text-gray-900">
                          {result.companyName}
                        </h4>
                        <span
                          className={`inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(result.status)}`}
                        >
                          {getStatusIcon(result.status)}
                          {result.status}
                        </span>
                      </div>
                      <p className="text-gray-600 mb-2">{result.productName}</p>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span className="font-mono">
                          {result.certificateNumber}
                        </span>
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          <span>
                            {formatDate(result.issueDate)} -{' '}
                            {formatDate(result.expiryDate)}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPin className="w-4 h-4" />
                          <span>{result.country}</span>
                        </div>
                      </div>
                    </div>
                    <div className="ml-4">
                      <button className="px-4 py-2 text-primary-green border border-primary-green rounded-lg hover:bg-primary-green hover:text-white transition-colors text-sm">
                        {language === 'en' ? 'View Details' : 'Lihat Butiran'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </PageWrapper>
  );
}

export default function SearchPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SearchContent />
    </Suspense>
  );
}
