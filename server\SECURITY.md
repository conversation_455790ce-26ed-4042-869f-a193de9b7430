# Security Configuration for Halal Chatbot

## 🔐 Security Overview

This document outlines the security measures implemented in the Halal Chatbot system and provides guidelines for maintaining security best practices.

## 🛡️ Current Security Measures

### Authentication & Authorization
- JWT-based authentication with configurable expiration
- Role-based access control (ADMIN, AGENT, SUP<PERSON><PERSON>SOR, EDITOR)
- Secure password hashing using bcrypt with salt rounds
- Token-based API authentication

### Data Protection
- Environment variables stored as Cloudflare Workers secrets
- Sensitive data not stored in version control
- Database credentials secured via environment variables
- Parameterized queries using Drizzle ORM (prevents SQL injection)

### Network Security
- CORS configuration restricting origins
- Rate limiting (1000 requests per 15 minutes per IP)
- Helmet.js security headers
- HTTPS enforcement in production

### File Upload Security
- File type validation (images and audio only)
- File size limits (10MB maximum)
- Unique filename generation to prevent conflicts
- Secure file storage with proper access controls

## 🔐 Secure Environment Variables

This project uses Cloudflare Workers secrets for sensitive data instead of storing them in `wrangler.toml` to maintain security.

### Why Use Secrets?

- **Security**: Sensitive data like API keys are not stored in version control
- **Environment Isolation**: Different secrets for production/development
- **Access Control**: Only authorized users can view/modify secrets

## ⚠️ Critical Security Issues to Address

### 1. Rate Limiting Enhancement
**Current**: 1000 requests per 15 minutes per IP
**Recommendation**: Implement tiered rate limiting:
- Chat endpoints: 60 requests per minute per IP
- Authentication endpoints: 5 attempts per minute per IP
- File upload endpoints: 10 uploads per hour per IP

### 2. Input Validation & Sanitization
**Issues Found**:
- Missing input sanitization for chat messages
- No length limits on chat messages
- Image URL validation needs improvement

**Recommendations**:
- Implement message length limits (e.g., 4000 characters)
- Sanitize all user inputs to prevent XSS
- Validate image URLs and implement allowlist for trusted domains

### 3. Session Security
**Issues Found**:
- Chat sessions stored in memory (not persistent)
- No session timeout implementation
- Missing session invalidation on logout

**Recommendations**:
- Implement database-backed session storage
- Add configurable session timeouts
- Implement proper session cleanup

### 4. Error Handling
**Issues Found**:
- Some error messages may leak sensitive information
- Inconsistent error response formats

**Recommendations**:
- Implement standardized error responses
- Avoid exposing internal system details in errors
- Add proper logging without sensitive data exposure

### Setting Up Secrets

#### Required Secrets for Production:
```bash
# OpenAI API Key
npx wrangler secret put OPENAI_API_KEY --env production

# JWT Secret for authentication (use strong random key)
npx wrangler secret put JWT_SECRET --env production

# Database URL
npx wrangler secret put DATABASE_URL --env production

# Admin default password (change from default)
npx wrangler secret put ADMIN_DEFAULT_PASSWORD --env production

# WhatsApp Business API (optional)
npx wrangler secret put WHATSAPP_ACCESS_TOKEN --env production
npx wrangler secret put WHATSAPP_PHONE_NUMBER_ID --env production
npx wrangler secret put WHATSAPP_WEBHOOK_VERIFY_TOKEN --env production
npx wrangler secret put WHATSAPP_BUSINESS_ACCOUNT_ID --env production

# Twilio WhatsApp API (optional)
npx wrangler secret put TWILIO_ACCOUNT_SID --env production
npx wrangler secret put TWILIO_AUTH_TOKEN --env production
npx wrangler secret put TWILIO_PHONE_NUMBER --env production
```

#### Required Secrets for Development:
```bash
# OpenAI API Key
npx wrangler secret put OPENAI_API_KEY --env development

# JWT Secret for authentication
npx wrangler secret put JWT_SECRET --env development

# Database URL
npx wrangler secret put DATABASE_URL --env development

# Admin default password
npx wrangler secret put ADMIN_DEFAULT_PASSWORD --env development

# Twilio WhatsApp API (optional, for development testing)
npx wrangler secret put TWILIO_ACCOUNT_SID --env development
npx wrangler secret put TWILIO_AUTH_TOKEN --env development
npx wrangler secret put TWILIO_PHONE_NUMBER --env development
```

### Automated Setup

Use the provided script to set up all secrets from your `.env.production` file:

```bash
./scripts/setup-secrets.sh
```

### Viewing Current Secrets

```bash
# List production secrets
npx wrangler secret list --env production

# List development secrets
npx wrangler secret list --env development
```

### Deleting Secrets

```bash
# Delete a secret
npx wrangler secret delete SECRET_NAME --env production
```

## 📁 File Structure

- `wrangler.toml` - Contains only non-sensitive configuration
- `.env.production` - Local file with actual values (NOT committed to git)
- `.env.example` - Template showing required variables
- `scripts/setup-secrets.sh` - Automated secret setup script

## 🚀 Deployment

After setting up secrets, deploy with:

```bash
# Production
npx wrangler deploy --env production

# Development
npx wrangler deploy --env development
```

## ⚠️ Important Notes

1. **Never commit `.env.production`** - Add it to `.gitignore`
2. **Use different secrets** for production and development
3. **Rotate secrets regularly** for security
4. **Use strong, unique passwords** for JWT secrets and admin accounts

## 🔧 Security Improvements Implementation

### Enhanced Rate Limiting
Create tiered rate limiting for different endpoint types:

```typescript
// Enhanced rate limiting configuration
const chatLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 60, // 60 requests per minute for chat
  message: 'Too many chat requests, please slow down.',
});

const authLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 5, // 5 login attempts per minute
  message: 'Too many login attempts, please try again later.',
});

const uploadLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 uploads per hour
  message: 'Upload limit exceeded, please try again later.',
});
```

### Input Validation & Sanitization
Implement comprehensive input validation:

```typescript
import DOMPurify from 'isomorphic-dompurify';
import validator from 'validator';

// Message validation
const validateChatMessage = (message: string): string => {
  // Length validation
  if (message.length > 4000) {
    throw new Error('Message too long (max 4000 characters)');
  }

  // Sanitize HTML/XSS
  const sanitized = DOMPurify.sanitize(message);

  // Additional validation
  if (!validator.isLength(sanitized, { min: 1, max: 4000 })) {
    throw new Error('Invalid message length');
  }

  return sanitized;
};
```

### Session Security Enhancement
Implement secure session management:

```typescript
// Session configuration
const sessionConfig = {
  timeout: 30 * 60 * 1000, // 30 minutes
  maxSessions: 5, // Max concurrent sessions per user
  cleanupInterval: 5 * 60 * 1000, // Cleanup every 5 minutes
};
```

## 🔒 Security Best Practices

### JWT Configuration
- Use strong, randomly generated JWT secrets (minimum 256 bits)
- Set appropriate token expiration times (24h max recommended)
- Implement token refresh mechanisms for long-lived sessions
- Store JWT secrets securely using Cloudflare Workers secrets

### Database Security
- Use parameterized queries (already implemented with Drizzle ORM)
- Implement proper database user permissions
- Regular database backups and security updates
- Connection string stored as secret, not in code

### API Security
- Implement comprehensive input validation and sanitization
- Use HTTPS for all communications (enforced in production)
- Regular security audits and dependency updates
- Implement proper error handling without information leakage

### File Upload Security
- Validate file types and sizes (already implemented)
- Scan uploaded files for malware (recommended addition)
- Store files in secure location with proper access controls
- Implement file cleanup for temporary uploads

## 🚨 Security Monitoring & Incident Response

### Monitoring Recommendations
- Implement security event logging
- Monitor for unusual API usage patterns
- Set up alerts for failed authentication attempts
- Track file upload patterns and anomalies

### Incident Response Plan
1. **Immediate Actions**: Rotate secrets, review logs, isolate affected systems
2. **Investigation**: Identify scope, document findings, assess impact
3. **Recovery**: Update systems, implement fixes, conduct security review

## 📋 Security Implementation Checklist

### Immediate Actions Required:
- [ ] Implement enhanced rate limiting for different endpoint types
- [ ] Add input validation and sanitization for all user inputs
- [ ] Implement session timeout and cleanup mechanisms
- [ ] Add comprehensive error handling without information leakage
- [ ] Implement security headers (CSP, HSTS, etc.)

### Recommended Enhancements:
- [ ] Add request logging and monitoring
- [ ] Implement API key rotation mechanism
- [ ] Add malware scanning for file uploads
- [ ] Implement IP allowlisting for admin endpoints
- [ ] Add two-factor authentication for admin accounts
- [ ] Implement audit logging for sensitive operations

### Regular Maintenance:
- [ ] Regular dependency updates and security patches
- [ ] Periodic security audits and penetration testing
- [ ] Review and rotate API keys and secrets quarterly
- [ ] Monitor security logs and respond to alerts promptly
