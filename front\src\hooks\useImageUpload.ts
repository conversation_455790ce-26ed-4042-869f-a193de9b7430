'use client';

import { useCallback, useState } from 'react';

interface UploadedImage {
  url: string;
  filename: string;
  originalFilename: string;
  size: number;
  file: File;
}

interface UseImageUploadProps {
  onImageUploaded?: (image: UploadedImage) => void;
  maxFileSize?: number; // in bytes
}

interface ImageUploadState {
  isUploading: boolean;
  error: string | null;
  uploadedImage: UploadedImage | null;
}

export function useImageUpload({
  onImageUploaded,
  maxFileSize = 10 * 1024 * 1024, // 10MB default
}: UseImageUploadProps = {}) {
  const [state, setState] = useState<ImageUploadState>({
    isUploading: false,
    error: null,
    uploadedImage: null,
  });

  const validateFile = useCallback(
    (file: File): string | null => {
      // Check file type
      const allowedTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
      ];
      if (!allowedTypes.includes(file.type)) {
        console.log('Invalid file type:', file.type);
        return 'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.';
      }

      // Check file size
      if (file.size > maxFileSize) {
        const maxSizeMB = maxFileSize / (1024 * 1024);
        return `File size exceeds ${maxSizeMB}MB limit.`;
      }

      return null;
    },
    [maxFileSize],
  );

  const uploadImage = useCallback(
    async (file: File): Promise<UploadedImage | null> => {
      try {
        setState((prev) => ({ ...prev, isUploading: true, error: null }));

        // Validate file
        const validationError = validateFile(file);
        if (validationError) {
          setState((prev) => ({
            ...prev,
            error: validationError,
            isUploading: false,
          }));
          return null;
        }

        // Create form data
        const formData = new FormData();
        formData.append('file', file);

        // Upload to server
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/upload`,
          {
            method: 'POST',
            body: formData,
          },
        );

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          console.error('Upload error response:', errorData);
          throw new Error(
            errorData.error ||
              errorData.message ||
              `HTTP error! status: ${response.status}`,
          );
        }

        const data = await response.json();

        if (data.type !== 'image') {
          throw new Error('Uploaded file is not an image');
        }

        const uploadedImage: UploadedImage = {
          url: data.url,
          filename: data.filename,
          originalFilename: data.originalFilename,
          size: data.size,
          file,
        };

        setState((prev) => ({
          ...prev,
          uploadedImage,
          isUploading: false,
        }));

        if (onImageUploaded) {
          onImageUploaded(uploadedImage);
        }

        return uploadedImage;
      } catch (error) {
        console.error('Failed to upload image:', error);
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to upload image';
        setState((prev) => ({
          ...prev,
          error: errorMessage,
          isUploading: false,
        }));
        return null;
      }
    },
    [validateFile, onImageUploaded],
  );

  const uploadMultipleImages = useCallback(
    async (files: File[]): Promise<UploadedImage[]> => {
      const results: UploadedImage[] = [];

      for (const file of files) {
        const result = await uploadImage(file);
        if (result) {
          results.push(result);
        }
      }

      return results;
    },
    [uploadImage],
  );

  const clearImage = useCallback(() => {
    setState((prev) => ({ ...prev, uploadedImage: null, error: null }));
  }, []);

  const clearError = useCallback(() => {
    setState((prev) => ({ ...prev, error: null }));
  }, []);

  const deleteImage = useCallback(
    async (filename: string): Promise<boolean> => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/upload/${filename}`,
          {
            method: 'DELETE',
          },
        );

        if (!response.ok) {
          throw new Error(`Failed to delete image: ${response.status}`);
        }

        // Clear the uploaded image if it matches the deleted filename
        setState((prev) => ({
          ...prev,
          uploadedImage:
            prev.uploadedImage?.filename === filename
              ? null
              : prev.uploadedImage,
        }));

        return true;
      } catch (error) {
        console.error('Failed to delete image:', error);
        setState((prev) => ({
          ...prev,
          error:
            error instanceof Error ? error.message : 'Failed to delete image',
        }));
        return false;
      }
    },
    [],
  );

  return {
    ...state,
    uploadImage,
    uploadMultipleImages,
    clearImage,
    clearError,
    deleteImage,
    validateFile,
  };
}
