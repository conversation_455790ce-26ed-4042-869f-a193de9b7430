'use client';

import { Edit, Plus, Trash2 } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { Button, type Column, Modal, Table } from '@/components/ui';

interface Product {
  id: number;
  productName: string;
  companyName: string;
  certificateNumber?: string;
  certificateType?: string;
  status?: string;
  category?: string;
  subcategory?: string;
  state?: string;
  country?: string;
  createdAt: string;
  updatedAt: string;
}

interface ProductsResponse {
  products: Product[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
    totalPages: number;
  };
}

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    hasMore: false,
    totalPages: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    product: Product | null;
  }>({ isOpen: false, product: null });

  const fetchProducts = async (page = 1) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/products?page=${page}&limit=20`);
      if (response.ok) {
        const data: ProductsResponse = await response.json();
        setProducts(data.products);
        setPagination(data.pagination);
      } else {
        console.error('Failed to fetch products');
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (product: Product) => {
    try {
      const response = await fetch(`/api/products?id=${product.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        await fetchProducts(pagination.page);
        setDeleteModal({ isOpen: false, product: null });
      } else {
        console.error('Failed to delete product');
      }
    } catch (error) {
      console.error('Error deleting product:', error);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  const columns: Column<Product>[] = [
    {
      key: 'productName',
      header: 'Product Name',
      sortable: true,
    },
    {
      key: 'companyName',
      header: 'Company',
      sortable: true,
    },
    {
      key: 'certificateNumber',
      header: 'Certificate Number',
    },
    {
      key: 'certificateType',
      header: 'Certificate Type',
    },
    {
      key: 'status',
      header: 'Status',
      render: (product) => (
        <span
          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            product.status === 'Active'
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          {product.status || 'Unknown'}
        </span>
      ),
    },
    {
      key: 'category',
      header: 'Category',
    },
    {
      key: 'state',
      header: 'State',
    },
    {
      key: 'actions',
      header: 'Actions',
      isActions: true,
      render: (product) => (
        <div className="flex space-x-2">
          <Link href={`/cp1/products/${product.id}/edit`}>
            <Button variant="outline" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
          <Button
            variant="danger"
            size="sm"
            onClick={() => setDeleteModal({ isOpen: true, product })}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Products</h1>
            <p className="text-gray-600">Manage halal certified products</p>
          </div>
          <Link href="/cp1/products/create">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Product
            </Button>
          </Link>
        </div>

        {/* Products Table */}
        <Table
          data={products}
          columns={columns}
          isLoading={isLoading}
          pagination={pagination}
          onPageChange={fetchProducts}
        />

        {/* Delete Confirmation Modal */}
        <Modal
          isOpen={deleteModal.isOpen}
          onClose={() => setDeleteModal({ isOpen: false, product: null })}
          title="Delete Product"
        >
          <div className="space-y-4">
            <p className="text-gray-600">
              Are you sure you want to delete "
              {deleteModal.product?.productName}"? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setDeleteModal({ isOpen: false, product: null })}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={() =>
                  deleteModal.product && handleDelete(deleteModal.product)
                }
              >
                Delete
              </Button>
            </div>
          </div>
        </Modal>
      </div>
    </AdminLayout>
  );
}
