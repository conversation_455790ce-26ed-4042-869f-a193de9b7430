'use client';

import { Calendar, Clock } from 'lucide-react';
import { operationHours } from '@/data/navigation';
import { useLanguage } from '@/lib/language-context';
import { cn } from '@/lib/utils';

interface OperationHoursProps {
  variant?: 'default' | 'compact' | 'card';
  showIcon?: boolean;
  className?: string;
}

export function OperationHours({
  variant = 'default',
  showIcon = true,
  className,
}: OperationHoursProps) {
  const { language } = useLanguage();

  const getCurrentStatus = () => {
    const now = new Date();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const currentTime = now.getHours() * 100 + now.getMinutes(); // HHMM format

    // Weekend (Saturday = 6, Sunday = 0)
    if (currentDay === 0 || currentDay === 6) {
      return {
        isOpen: false,
        status: language === 'en' ? 'Closed' : 'Tutup',
        nextOpen:
          language === 'en' ? 'Opens Monday 8:30 AM' : 'Buka Isnin 8:30 Pagi',
      };
    }

    // Friday (5)
    if (currentDay === 5) {
      if (currentTime >= 830 && currentTime < 1200) {
        return {
          isOpen: true,
          status: language === 'en' ? 'Open' : 'Buka',
          nextClose:
            language === 'en'
              ? 'Closes at 12:00 PM'
              : 'Tutup pada 12:00 Tengahari',
        };
      }
      if (currentTime >= 1445 && currentTime < 1630) {
        return {
          isOpen: true,
          status: language === 'en' ? 'Open' : 'Buka',
          nextClose:
            language === 'en' ? 'Closes at 4:30 PM' : 'Tutup pada 4:30 Petang',
        };
      }
      return {
        isOpen: false,
        status: language === 'en' ? 'Closed' : 'Tutup',
        nextOpen:
          currentTime < 830
            ? language === 'en'
              ? 'Opens at 8:30 AM'
              : 'Buka pada 8:30 Pagi'
            : currentTime < 1445
              ? language === 'en'
                ? 'Opens at 2:45 PM'
                : 'Buka pada 2:45 Petang'
              : language === 'en'
                ? 'Opens Monday 8:30 AM'
                : 'Buka Isnin 8:30 Pagi',
      };
    }

    // Monday to Thursday (1-4)
    if (currentDay >= 1 && currentDay <= 4) {
      if (currentTime >= 830 && currentTime < 1230) {
        return {
          isOpen: true,
          status: language === 'en' ? 'Open' : 'Buka',
          nextClose:
            language === 'en'
              ? 'Closes at 12:30 PM'
              : 'Tutup pada 12:30 Tengahari',
        };
      }
      if (currentTime >= 1430 && currentTime < 1630) {
        return {
          isOpen: true,
          status: language === 'en' ? 'Open' : 'Buka',
          nextClose:
            language === 'en' ? 'Closes at 4:30 PM' : 'Tutup pada 4:30 Petang',
        };
      }
      return {
        isOpen: false,
        status: language === 'en' ? 'Closed' : 'Tutup',
        nextOpen:
          currentTime < 830
            ? language === 'en'
              ? 'Opens at 8:30 AM'
              : 'Buka pada 8:30 Pagi'
            : currentTime < 1430
              ? language === 'en'
                ? 'Opens at 2:30 PM'
                : 'Buka pada 2:30 Petang'
              : language === 'en'
                ? 'Opens tomorrow 8:30 AM'
                : 'Buka esok 8:30 Pagi',
      };
    }

    return {
      isOpen: false,
      status: language === 'en' ? 'Closed' : 'Tutup',
      nextOpen:
        language === 'en' ? 'Opens Monday 8:30 AM' : 'Buka Isnin 8:30 Pagi',
    };
  };

  const currentStatus = getCurrentStatus();

  if (variant === 'compact') {
    return (
      <div
        className={cn(
          'flex items-center gap-3 p-3 bg-white border border-gray-200 rounded-lg',
          className,
        )}
      >
        {showIcon && (
          <div
            className={cn(
              'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center',
              currentStatus.isOpen
                ? 'bg-green-100 text-green-600'
                : 'bg-red-100 text-red-600',
            )}
          >
            <Clock className="w-4 h-4" />
          </div>
        )}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span
              className={cn(
                'font-medium',
                currentStatus.isOpen ? 'text-green-600' : 'text-red-600',
              )}
            >
              {currentStatus.status}
            </span>
            <span className="text-sm text-gray-500">
              {currentStatus.isOpen
                ? currentStatus.nextClose
                : currentStatus.nextOpen}
            </span>
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'card') {
    return (
      <div className={cn('card', className)}>
        <div className="flex items-center gap-3 mb-4">
          {showIcon && <Clock className="w-6 h-6 text-primary-green" />}
          <h3 className="text-lg font-semibold text-gray-900">
            {language === 'en'
              ? 'Counter Operation Hours'
              : 'Waktu Operasi Kaunter'}
          </h3>
        </div>

        <div
          className={cn(
            'flex items-center gap-3 p-3 rounded-lg mb-4',
            currentStatus.isOpen
              ? 'bg-green-50 border border-green-200'
              : 'bg-red-50 border border-red-200',
          )}
        >
          <div
            className={cn(
              'w-3 h-3 rounded-full',
              currentStatus.isOpen ? 'bg-green-500' : 'bg-red-500',
            )}
          />
          <div>
            <span
              className={cn(
                'font-medium',
                currentStatus.isOpen ? 'text-green-700' : 'text-red-700',
              )}
            >
              {currentStatus.status}
            </span>
            <p className="text-sm text-gray-600">
              {currentStatus.isOpen
                ? currentStatus.nextClose
                : currentStatus.nextOpen}
            </p>
          </div>
        </div>

        <div className="space-y-3">
          <div>
            <h4 className="font-medium text-gray-900 mb-1">
              {language === 'en' ? 'Monday - Thursday' : 'Isnin - Khamis'}
            </h4>
            <p className="text-sm text-gray-600">
              {language === 'bm'
                ? operationHours.weekdays.bm.morning
                : operationHours.weekdays.en.morning}
            </p>
            <p className="text-sm text-gray-600">
              {language === 'bm'
                ? operationHours.weekdays.bm.afternoon
                : operationHours.weekdays.en.afternoon}
            </p>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 mb-1">
              {language === 'en' ? 'Friday' : 'Jumaat'}
            </h4>
            <p className="text-sm text-gray-600">
              {language === 'bm'
                ? operationHours.friday.bm.morning
                : operationHours.friday.en.morning}
            </p>
            <p className="text-sm text-gray-600">
              {language === 'bm'
                ? operationHours.friday.bm.afternoon
                : operationHours.friday.en.afternoon}
            </p>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 mb-1">
              {language === 'en' ? 'Saturday - Sunday' : 'Sabtu - Ahad'}
            </h4>
            <p className="text-sm text-gray-600">
              {language === 'bm'
                ? operationHours.weekend.bm
                : operationHours.weekend.en}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="flex items-center gap-3 mb-4">
        {showIcon && <Clock className="w-6 h-6 text-primary-green" />}
        <h3 className="text-xl font-bold text-gray-900">
          {language === 'en'
            ? 'Counter Operation Hours'
            : 'Waktu Operasi Kaunter'}
        </h3>
      </div>

      <div
        className={cn(
          'flex items-center gap-3 p-4 rounded-lg mb-6',
          currentStatus.isOpen
            ? 'bg-green-50 border border-green-200'
            : 'bg-red-50 border border-red-200',
        )}
      >
        <div
          className={cn(
            'w-4 h-4 rounded-full',
            currentStatus.isOpen ? 'bg-green-500' : 'bg-red-500',
          )}
        />
        <div>
          <span
            className={cn(
              'font-semibold text-lg',
              currentStatus.isOpen ? 'text-green-700' : 'text-red-700',
            )}
          >
            {currentStatus.status}
          </span>
          <p className="text-gray-600">
            {currentStatus.isOpen
              ? currentStatus.nextClose
              : currentStatus.nextOpen}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Calendar className="w-4 h-4 text-primary-green" />
            <h4 className="font-semibold text-gray-900">
              {language === 'en' ? 'Monday - Thursday' : 'Isnin - Khamis'}
            </h4>
          </div>
          <p className="text-sm text-gray-600 mb-1">
            {language === 'bm'
              ? operationHours.weekdays.bm.morning
              : operationHours.weekdays.en.morning}
          </p>
          <p className="text-sm text-gray-600">
            {language === 'bm'
              ? operationHours.weekdays.bm.afternoon
              : operationHours.weekdays.en.afternoon}
          </p>
        </div>

        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Calendar className="w-4 h-4 text-primary-green" />
            <h4 className="font-semibold text-gray-900">
              {language === 'en' ? 'Friday' : 'Jumaat'}
            </h4>
          </div>
          <p className="text-sm text-gray-600 mb-1">
            {language === 'bm'
              ? operationHours.friday.bm.morning
              : operationHours.friday.en.morning}
          </p>
          <p className="text-sm text-gray-600">
            {language === 'bm'
              ? operationHours.friday.bm.afternoon
              : operationHours.friday.en.afternoon}
          </p>
        </div>

        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Calendar className="w-4 h-4 text-red-500" />
            <h4 className="font-semibold text-gray-900">
              {language === 'en' ? 'Saturday - Sunday' : 'Sabtu - Ahad'}
            </h4>
          </div>
          <p className="text-sm text-red-600 font-medium">
            {language === 'bm'
              ? operationHours.weekend.bm
              : operationHours.weekend.en}
          </p>
        </div>
      </div>
    </div>
  );
}
