'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Save } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { AdminLayout } from '@/components/admin/AdminLayout';
import {
  Button,
  Input,
  Select,
  type SelectOption,
  Textarea,
} from '@/components/ui';

const statusOptions: SelectOption[] = [
  { value: 'Active', label: 'Active' },
  { value: 'Inactive', label: 'Inactive' },
  { value: 'Pending', label: 'Pending' },
  { value: 'Expired', label: 'Expired' },
];

const certificateTypeOptions: SelectOption[] = [
  { value: 'HALAL', label: 'HALAL' },
  { value: 'HALAL JAKIM', label: 'HALAL JAKIM' },
  { value: 'HALAL SELANGOR', label: 'HALAL SELANGOR' },
];

const categoryOptions: SelectOption[] = [
  { value: 'Food', label: 'Food' },
  { value: 'Beverage', label: 'Beverage' },
  { value: 'Cosmetics', label: 'Cosmetics' },
  { value: 'Pharmaceutical', label: 'Pharmaceutical' },
  { value: 'Others', label: 'Others' },
];

const stateOptions: SelectOption[] = [
  { value: 'Selangor', label: 'Selangor' },
  { value: 'Kuala Lumpur', label: 'Kuala Lumpur' },
  { value: 'Johor', label: 'Johor' },
  { value: 'Penang', label: 'Penang' },
  { value: 'Perak', label: 'Perak' },
  { value: 'Kedah', label: 'Kedah' },
  { value: 'Kelantan', label: 'Kelantan' },
  { value: 'Terengganu', label: 'Terengganu' },
  { value: 'Pahang', label: 'Pahang' },
  { value: 'Negeri Sembilan', label: 'Negeri Sembilan' },
  { value: 'Melaka', label: 'Melaka' },
  { value: 'Perlis', label: 'Perlis' },
  { value: 'Sabah', label: 'Sabah' },
  { value: 'Sarawak', label: 'Sarawak' },
];

export default function CreateProductPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formData, setFormData] = useState({
    productName: '',
    companyName: '',
    certificateNumber: '',
    certificateType: '',
    issuedDate: '',
    expiryDate: '',
    status: '',
    category: '',
    subcategory: '',
    address: '',
    state: '',
    country: 'Malaysia',
    contactInfo: '',
    website: '',
    sourceUrl: '',
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.productName.trim()) {
      newErrors.productName = 'Product name is required';
    }

    if (!formData.companyName.trim()) {
      newErrors.companyName = 'Company name is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        router.push('/cp1/products');
      } else {
        const errorData = await response.json();
        console.error('Failed to create product:', errorData);
      }
    } catch (error) {
      console.error('Error creating product:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/cp1/products">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Products
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Create Product</h1>
            <p className="text-gray-600">Add a new halal certified product</p>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Basic Information
                </h3>

                <Input
                  label="Product Name *"
                  value={formData.productName}
                  onChange={(e) =>
                    handleInputChange('productName', e.target.value)
                  }
                  error={errors.productName}
                  placeholder="Enter product name"
                />

                <Input
                  label="Company Name *"
                  value={formData.companyName}
                  onChange={(e) =>
                    handleInputChange('companyName', e.target.value)
                  }
                  error={errors.companyName}
                  placeholder="Enter company name"
                />

                <Select
                  label="Category"
                  value={formData.category}
                  onChange={(e) =>
                    handleInputChange('category', e.target.value)
                  }
                  options={categoryOptions}
                  placeholder="Select category"
                />

                <Input
                  label="Subcategory"
                  value={formData.subcategory}
                  onChange={(e) =>
                    handleInputChange('subcategory', e.target.value)
                  }
                  placeholder="Enter subcategory"
                />
              </div>

              {/* Certificate Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Certificate Information
                </h3>

                <Input
                  label="Certificate Number"
                  value={formData.certificateNumber}
                  onChange={(e) =>
                    handleInputChange('certificateNumber', e.target.value)
                  }
                  placeholder="Enter certificate number"
                />

                <Select
                  label="Certificate Type"
                  value={formData.certificateType}
                  onChange={(e) =>
                    handleInputChange('certificateType', e.target.value)
                  }
                  options={certificateTypeOptions}
                  placeholder="Select certificate type"
                />

                <Input
                  label="Issued Date"
                  type="date"
                  value={formData.issuedDate}
                  onChange={(e) =>
                    handleInputChange('issuedDate', e.target.value)
                  }
                />

                <Input
                  label="Expiry Date"
                  type="date"
                  value={formData.expiryDate}
                  onChange={(e) =>
                    handleInputChange('expiryDate', e.target.value)
                  }
                />

                <Select
                  label="Status"
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  options={statusOptions}
                  placeholder="Select status"
                />
              </div>
            </div>

            {/* Location Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">
                Location Information
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Textarea
                  label="Address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="Enter address"
                  rows={3}
                />

                <div className="space-y-4">
                  <Select
                    label="State"
                    value={formData.state}
                    onChange={(e) => handleInputChange('state', e.target.value)}
                    options={stateOptions}
                    placeholder="Select state"
                  />

                  <Input
                    label="Country"
                    value={formData.country}
                    onChange={(e) =>
                      handleInputChange('country', e.target.value)
                    }
                    placeholder="Enter country"
                  />
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">
                Contact Information
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Textarea
                  label="Contact Information"
                  value={formData.contactInfo}
                  onChange={(e) =>
                    handleInputChange('contactInfo', e.target.value)
                  }
                  placeholder="Enter contact information"
                  rows={3}
                />

                <div className="space-y-4">
                  <Input
                    label="Website"
                    type="url"
                    value={formData.website}
                    onChange={(e) =>
                      handleInputChange('website', e.target.value)
                    }
                    placeholder="https://example.com"
                  />

                  <Input
                    label="Source URL"
                    type="url"
                    value={formData.sourceUrl}
                    onChange={(e) =>
                      handleInputChange('sourceUrl', e.target.value)
                    }
                    placeholder="https://source.com"
                  />
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <Link href="/cp1/products">
                <Button variant="outline">Cancel</Button>
              </Link>
              <Button type="submit" isLoading={isLoading}>
                <Save className="h-4 w-4 mr-2" />
                Create Product
              </Button>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
