import chalk from 'chalk';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export class Logger {
  private static level: LogLevel = LogLevel.INFO;
  private static startTime: Date = new Date();

  static setLevel(level: LogLevel): void {
    Logger.level = level;
  }

  static debug(message: string, ...args: any[]): void {
    if (Logger.level <= LogLevel.DEBUG) {
      console.log(
        chalk.gray(`[${Logger.getTimestamp()}] [DEBUG] ${message}`),
        ...args,
      );
    }
  }

  static info(message: string, ...args: any[]): void {
    if (Logger.level <= LogLevel.INFO) {
      console.log(
        chalk.blue(`[${Logger.getTimestamp()}] [INFO] ${message}`),
        ...args,
      );
    }
  }

  static success(message: string, ...args: any[]): void {
    if (Logger.level <= LogLevel.INFO) {
      console.log(
        chalk.green(`[${Logger.getTimestamp()}] [SUCCESS] ${message}`),
        ...args,
      );
    }
  }

  static warn(message: string, ...args: any[]): void {
    if (Logger.level <= LogLevel.WARN) {
      console.log(
        chalk.yellow(`[${Logger.getTimestamp()}] [WARN] ${message}`),
        ...args,
      );
    }
  }

  static error(message: string, ...args: any[]): void {
    if (Logger.level <= LogLevel.ERROR) {
      console.log(
        chalk.red(`[${Logger.getTimestamp()}] [ERROR] ${message}`),
        ...args,
      );
    }
  }

  static progress(message: string, current: number, total: number): void {
    const percentage = Math.round((current / total) * 100);
    const progressBar = Logger.createProgressBar(percentage);
    console.log(
      chalk.cyan(
        `[${Logger.getTimestamp()}] ${message} ${progressBar} ${current}/${total} (${percentage}%)`,
      ),
    );
  }

  static stats(title: string, stats: Record<string, any>): void {
    console.log(chalk.magenta(`\n📊 ${title}`));
    console.log(chalk.magenta('─'.repeat(50)));

    Object.entries(stats).forEach(([key, value]) => {
      const formattedKey = key
        .replace(/([A-Z])/g, ' $1')
        .replace(/^./, (str) => str.toUpperCase());
      console.log(
        chalk.white(`${formattedKey.padEnd(20)}: ${chalk.cyan(value)}`),
      );
    });

    console.log(chalk.magenta('─'.repeat(50)));
  }

  static section(title: string): void {
    console.log(chalk.bold.blue(`\n🔹 ${title}`));
    console.log(chalk.blue('─'.repeat(title.length + 3)));
  }

  static header(title: string): void {
    const border = '═'.repeat(title.length + 4);
    console.log(chalk.bold.green(`\n${border}`));
    console.log(chalk.bold.green(`  ${title}  `));
    console.log(chalk.bold.green(`${border}\n`));
  }

  static footer(): void {
    const duration = Date.now() - Logger.startTime.getTime();
    const seconds = Math.round(duration / 1000);
    console.log(chalk.gray(`\n⏱️  Total execution time: ${seconds}s`));
  }

  private static getTimestamp(): string {
    return new Date().toLocaleTimeString();
  }

  private static createProgressBar(percentage: number, width = 20): string {
    const filled = Math.round((percentage / 100) * width);
    const empty = width - filled;
    return `[${'█'.repeat(filled)}${' '.repeat(empty)}]`;
  }
}

export class ProgressTracker {
  private current = 0;
  private total: number;
  private startTime: Date;
  private lastUpdate: Date;
  private title: string;

  constructor(total: number, title = 'Progress') {
    this.total = total;
    this.title = title;
    this.startTime = new Date();
    this.lastUpdate = new Date();
  }

  update(increment = 1): void {
    this.current += increment;
    this.lastUpdate = new Date();

    const percentage = Math.round((this.current / this.total) * 100);
    const elapsed = Date.now() - this.startTime.getTime();
    const rate = this.current / (elapsed / 1000);
    const eta =
      this.current > 0 ? Math.round((this.total - this.current) / rate) : 0;

    Logger.progress(`${this.title} - ETA: ${eta}s`, this.current, this.total);
  }

  complete(): void {
    this.current = this.total;
    const duration = Date.now() - this.startTime.getTime();
    Logger.success(
      `${this.title} completed in ${Math.round(duration / 1000)}s`,
    );
  }

  getCurrentProgress(): { current: number; total: number; percentage: number } {
    return {
      current: this.current,
      total: this.total,
      percentage: Math.round((this.current / this.total) * 100),
    };
  }
}
