#!/usr/bin/env python3
"""
Test script to verify Qdrant functionality locally
"""

import sys
import os
sys.path.append('/app' if os.path.exists('/app') else '.')

from qdrant_manager import push_output_files_to_qdrant

def main():
    print("🧪 Testing Qdrant functionality...")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Files in current directory: {os.listdir('.')}")
    
    # Check if output directory exists
    if os.path.exists('output'):
        print(f"✅ Output directory found")
        print(f"Contents: {os.listdir('output')}")
        
        # Find .md files
        md_files = []
        for root, dirs, files in os.walk('output'):
            for file in files:
                if file.endswith('.md'):
                    md_files.append(os.path.join(root, file))
        
        print(f"📄 Found {len(md_files)} .md files:")
        for f in md_files:
            print(f"  - {f}")
    else:
        print("❌ Output directory not found")
        return
    
    # Test Qdrant connection
    try:
        print("\n🔗 Testing Qdrant connection...")
        stats = push_output_files_to_qdrant(
            collection_name="test_collection",
            output_dir="output",
            qdrant_url="http://host.docker.internal:6333"
        )
        print(f"✅ Success! Stats: {stats}")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
