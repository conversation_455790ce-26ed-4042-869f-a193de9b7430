'use client';

export const runtime = 'edge';
export const dynamic = 'force-dynamic';

import { ArrowLeft, Save, Copy } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import type React from 'react';
import { useEffect, useState } from 'react';
import { Link } from '@/i18n/navigation';
import { api } from '@/lib/api';
import type { AdminUser, AdminUserCreationRequest } from '@/types';
import { UserRole } from '@/types/roles';

export default function CloneUserPage() {
  const params = useParams();
  const router = useRouter();
  const userId = Number(params.id);
  const locale = params.locale as string;

  const [originalUser, setOriginalUser] = useState<AdminUser | null>(null);
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [roles, setRoles] = useState<UserRole[]>([UserRole.AGENT]);
  const [isActive, setIsActive] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentUser, setCurrentUser] = useState<AdminUser | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [userResponse, currentUserResponse] = await Promise.all([
          api.admin.getUserById(userId),
          api.admin.getMe(),
        ]);

        if (userResponse.success && userResponse.data) {
          const user = userResponse.data;
          setOriginalUser(user);
          // Pre-populate form with cloned data
          setUsername(`${user.username}_copy`);
          setEmail(user.email || '');
          setFirstName(user.firstName || '');
          setLastName(user.lastName || '');
          setRoles(user.roles && user.roles.length > 0 ? user.roles : [UserRole.AGENT]);
          setIsActive(user.isActive !== undefined ? user.isActive : true);
        }

        if (currentUserResponse.success && currentUserResponse.data) {
          setCurrentUser(currentUserResponse.data.user);
        }
      } catch (err) {
        console.error('Failed to fetch user data:', err);
        setError('Failed to load user data for cloning.');
      }
    };

    fetchData();
  }, [userId]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!username.trim() || !password.trim()) {
      setError('Username and password are required.');
      return;
    }
    if (roles.length === 0) {
      setError('At least one role is required.');
      return;
    }
    setIsLoading(true);
    setError(null);

    const userData: AdminUserCreationRequest = {
      username,
      email: email.trim() || undefined,
      password,
      firstName: firstName.trim() || undefined,
      lastName: lastName.trim() || undefined,
      roles,
      isActive,
    };

    try {
      await api.admin.createUser(userData);
      router.push('/admin/users');
    } catch (err: any) {
      setError(
        err.response?.data?.error || err.message || 'Failed to create user.',
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (!originalUser) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <Link
          href="/admin/users"
          className="inline-flex items-center text-blue-600 hover:text-blue-800"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Users
        </Link>
      </div>

      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 flex items-center">
          <Copy className="mr-2 h-6 w-6" />
          Clone User: {originalUser.username}
        </h1>
        <p className="text-gray-600 mt-2">
          Creating a new user based on "{originalUser.username}"
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Username *
            </label>
            <input
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              required
              disabled={isLoading}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              disabled={isLoading}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              First Name
            </label>
            <input
              type="text"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              disabled={isLoading}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Last Name
            </label>
            <input
              type="text"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              disabled={isLoading}
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Password *
          </label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            required
            disabled={isLoading}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Roles *
          </label>
          <div className="space-y-2">
            {Object.values(UserRole).map((roleOption) => (
              <label key={roleOption} className="flex items-center">
                <input
                  type="checkbox"
                  checked={roles.includes(roleOption)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setRoles([...roles, roleOption]);
                    } else {
                      setRoles(roles.filter(r => r !== roleOption));
                    }
                  }}
                  className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  disabled={isLoading}
                />
                <span className="text-sm text-gray-700">{roleOption}</span>
              </label>
            ))}
          </div>
          {roles.length === 0 && (
            <p className="text-red-500 text-sm mt-1">At least one role is required</p>
          )}
        </div>

        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={isActive}
              onChange={(e) => setIsActive(e.target.checked)}
              className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              disabled={isLoading}
            />
            <span className="text-sm font-medium text-gray-700">Active User</span>
          </label>
          <p className="text-xs text-gray-500 mt-1">
            Inactive users cannot log in to the system
          </p>
        </div>

        <div className="flex justify-end space-x-4">
          <Link
            href="/admin/users"
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={isLoading}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            <Save className="mr-2 h-4 w-4" />
            {isLoading ? 'Creating...' : 'Create User'}
          </button>
        </div>
      </form>
    </div>
  );
}