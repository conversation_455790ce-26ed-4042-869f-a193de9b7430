import type { Context } from 'hono';
import DatabaseService from '../services/database';
import { createMessageHandlerService } from '../services/messageHandler';
import TwilioService from '../services/twilio';
import type { TwilioMessage, TwilioWebhookPayload } from '../types';
import {
  getTwilioSignature,
  parseTwilioWebhookBody,
  validateTwilioWebhookPayload,
  verifyTwilioWebhook,
} from '../utils/twilioWebhook';
import { createSuccessResponse, createErrorResponse, handleApiError } from '../utils/response';

/**
 * Twilio Controller - Clean route handlers only
 * Utilities moved to ../utils/twilioWebhook.ts for better organization
 */
export class TwilioController {
  // ==========================================
  // ROUTE HANDLER METHODS
  // ==========================================

  /**
   * Handle Twilio webhook endpoint
   * POST /api/twilio/webhook
   */
  async handleWebhook(c: Context, siteId: string): Promise<Response> {
    try {
      // Get environment from process.env (Hono.js style)
      const env = process.env;

      // Initialize database service (controller responsibility)
      const dbService = new DatabaseService(env);

      // Initialize Twilio service with environment
      const twilioServiceInstance = new TwilioService(dbService, siteId, env);
      await twilioServiceInstance.loadConfig();

      if (!twilioServiceInstance.isConfigured()) {
        console.error(
          'TwilioController: Not configured after load. Cannot process webhook.',
        );
        return c.text('Twilio service not configured', 500);
      }

      // Initialize media service for S3 storage (auto-detects available S3 service)
      await twilioServiceInstance.initializeMediaService();

      const twilioAuthToken = twilioServiceInstance.getConfig()?.authToken;
      if (!twilioAuthToken) {
        console.error(
          'TwilioController: Auth token missing from config. Cannot process webhook.',
        );
        return c.text('Twilio auth token not configured', 500);
      }

      // Check if this is a test mode request (for local development)
      const isTestMode = c.req.header('x-test-mode') === 'true';

      if (!isTestMode) {
        // Production mode: verify signature
        const twilioSignature = getTwilioSignature(c.req.raw);
        if (!twilioSignature) {
          console.warn('TwilioController: Missing X-Twilio-Signature header');
          return c.text('Signature verification failed (missing header)', 403);
        }

        // Get raw body and parse it
        const rawBody = await c.req.text();
        const parsedBody = parseTwilioWebhookBody(rawBody);

        // Validate required fields
        if (!validateTwilioWebhookPayload(parsedBody)) {
          console.error('TwilioController: Invalid webhook payload structure');
          return c.text('Invalid webhook payload', 400);
        }

        // Verify Twilio signature
        const requestUrl = c.req.url; // Full URL Twilio requested
        const isValid = verifyTwilioWebhook(
          twilioAuthToken,
          twilioSignature,
          requestUrl,
          parsedBody,
        );

        if (!isValid) {
          console.warn(
            `TwilioController: Invalid signature. URL: ${requestUrl}, ParsedBody: ${JSON.stringify(parsedBody)}, Signature: ${twilioSignature}`,
          );
          return c.text(
            'Signature verification failed (invalid signature)',
            403,
          );
        }
      } else {
        console.log(
          'TwilioController: Test mode - bypassing signature verification',
        );
      }

      // Get raw body and parse it (for both test and production mode)
      const rawBody = await c.req.text();
      const parsedBody = parseTwilioWebhookBody(rawBody);

      // Validate required fields
      if (!validateTwilioWebhookPayload(parsedBody)) {
        console.error('TwilioController: Invalid webhook payload structure');
        return c.text('Invalid webhook payload', 400);
      }

      console.log('TwilioController: Signature verified successfully.');

      // Process the webhook payload
      const messages = await twilioServiceInstance.processWebhookPayload(
        parsedBody as unknown as TwilioWebhookPayload,
      );

      if (messages && messages.length > 0) {
        for (const message of messages) {
          await this.handleIncomingMessage(
            message,
            env,
            siteId,
            twilioServiceInstance,
          );
        }
      }

      // Return XML response as Twilio expects
      return new Response('OK', {
        status: 200,
        headers: { 'Content-Type': 'text/xml' },
      });
    } catch (error: any) {
      console.error(
        'TwilioController: Webhook processing error:',
        error.message,
        error.stack,
      );
      return c.text(`Webhook processing error: ${error.message}`, 500);
    }
  }

  // ==========================================
  // PRIVATE HELPER METHODS
  // ==========================================

  /**
   * Handle incoming Twilio message - maintains exact same logic as before
   */
  private async handleIncomingMessage(
    message: TwilioMessage,
    env: any,
    siteId: string,
    twilioServiceInstance: TwilioService,
  ): Promise<void> {
    try {
      const userPhoneNumber = message.from; // e.g., whatsapp:+1234567890
      const messageText = message.content;

      console.log(
        `TwilioController: Handling incoming message for site ${siteId} from ${userPhoneNumber}: "${messageText}"`,
      );

      // Use consolidated message handler - same as before
      const messageHandlerServiceInst = createMessageHandlerService();
      const response = await messageHandlerServiceInst.handleIncomingMessage(
        {
          message: messageText,
          sessionId: userPhoneNumber, // Use phone number as session ID for Twilio
          platform: 'twilio',
          // Route message types: images → image handler, audio → audio handler, documents → document handler
          // NOTE: Document handler exists but OpenAI currently only supports image formats (PNG, JPEG, GIF, WebP)
          messageType:
            message.type === 'image'
              ? 'image'
              : message.type === 'audio'
                ? 'audio'
                : message.type === 'document'
                  ? 'document'
                  : 'text',
          mediaUrl: message.mediaUrl,
          userId: userPhoneNumber, // Typically the 'From' number
          config: {
            // Default config, can be overridden by site-specific settings
            maxMessageHistory: 10,
            maxToolCallIterations: 3, // Limit tool call iterations for Twilio to prevent hanging
            enableToolCalling: true,
            defaultModel: env.DEFAULT_OPENAI_MODEL || 'gpt-4o-mini',
          },
        },
        env, // Pass env for access to other services like OpenAI, R2R etc.
      );

      if (response.success && response.message) {
        // Send response back via Twilio - same logic as before
        if (!twilioServiceInstance.isConfigured()) {
          await twilioServiceInstance.loadConfig(); // Attempt to load if not configured yet
        }

        if (twilioServiceInstance.isConfigured()) {
          const sendResult = await twilioServiceInstance.sendTextMessage(
            userPhoneNumber, // Send back to the user's number
            response.message,
          );

          if (!sendResult.success) {
            console.error(
              `TwilioController: Failed to send Twilio response to ${userPhoneNumber}: ${sendResult.error}`,
            );
          } else {
            console.log(
              `TwilioController: Sent Twilio response to ${userPhoneNumber}: ${response.message}`,
            );
          }
        } else {
          console.error(
            `TwilioController: TwilioService not configured, cannot send reply to ${userPhoneNumber}.`,
          );
        }
      } else if (!response.success) {
        console.error(
          `TwilioController: Failed to get AI response for Twilio message from ${userPhoneNumber}: ${response.error}`,
        );
        // Optionally, send a generic error message back to the user - same as before
        if (!twilioServiceInstance.isConfigured()) {
          await twilioServiceInstance.loadConfig();
        }
        if (twilioServiceInstance.isConfigured()) {
          await twilioServiceInstance.sendTextMessage(
            userPhoneNumber,
            "I'm sorry, I encountered an error trying to process your message. Please try again later.",
          );
        } else {
          console.error(
            `TwilioController: TwilioService not configured, cannot send error reply to ${userPhoneNumber}.`,
          );
        }
      }
    } catch (error: any) {
      console.error(
        `TwilioController: Error in handleIncomingMessage for ${message.from}: ${error.message}`,
        error.stack,
      );
      // Avoid crashing the worker, log and move on - same as before
    }
  }

  /**
   * Get webhook URL for admin dashboard configuration
   * GET /api/twilio/webhook-url
   */
  async getWebhookUrl(c: Context): Promise<Response> {
    try {
      const env = process.env;
      const baseUrl = env.WEBHOOK_BASE_URL || 'https://your-hono-server.com';
      const webhookUrl = `${baseUrl}/api/twilio/webhook`;

      return createSuccessResponse(c, {
        webhookUrl,
        instructions: [
          '1. Copy the webhook URL above',
          '2. Go to your Twilio Console > WhatsApp Sandbox or Phone Numbers',
          '3. Set the webhook URL to the URL above',
          '4. Configure webhook for incoming messages',
          '5. Test the integration by sending a message',
        ],
      });
    } catch (error: any) {
      console.error(
        'TwilioController: Error getting webhook URL:',
        error.message,
      );
      return handleApiError(c, error, 'Failed to get webhook URL');
    }
  }
}
