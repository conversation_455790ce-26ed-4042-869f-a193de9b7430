import { Suspense } from 'react';
import { Footer } from '@/components/Footer';
import { Header } from '@/components/Header';

export const runtime = 'edge';

async function AboutContent() {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:16010'}/api/pages?slug=about-us`,
      {
        cache: 'no-store',
      },
    );

    if (!response.ok) {
      throw new Error('Failed to fetch about page content');
    }

    const page = await response.json();

    return (
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-green-600 to-green-800 text-white">
          <div className="container mx-auto px-4 py-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">About Us</h1>
            <p className="text-xl text-green-100 max-w-2xl">
              Leading the way in Halal certification and compliance in Selangor
            </p>
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div
                className="prose prose-lg max-w-none"
                dangerouslySetInnerHTML={{ __html: page.content }}
              />
            </div>

            {/* Mission & Vision */}
            <div className="grid md:grid-cols-2 gap-8 mt-12">
              <div className="bg-white rounded-lg shadow-lg p-8">
                <div className="text-green-600 text-4xl mb-4">🎯</div>
                <h3 className="text-2xl font-bold mb-4">Our Mission</h3>
                <p className="text-gray-600">
                  To ensure the highest standards of Halal compliance and
                  certification throughout Selangor, providing trust and
                  confidence to Muslim consumers and businesses alike.
                </p>
              </div>

              <div className="bg-white rounded-lg shadow-lg p-8">
                <div className="text-green-600 text-4xl mb-4">👁️</div>
                <h3 className="text-2xl font-bold mb-4">Our Vision</h3>
                <p className="text-gray-600">
                  To be the leading authority in Halal certification, recognized
                  globally for our excellence, integrity, and commitment to
                  Islamic principles in food and product certification.
                </p>
              </div>
            </div>

            {/* Values */}
            <div className="bg-white rounded-lg shadow-lg p-8 mt-8">
              <h3 className="text-2xl font-bold mb-6 text-center">
                Our Values
              </h3>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-green-600 text-3xl mb-3">🤝</div>
                  <h4 className="font-semibold mb-2">Integrity</h4>
                  <p className="text-gray-600 text-sm">
                    Upholding the highest ethical standards in all our
                    operations
                  </p>
                </div>

                <div className="text-center">
                  <div className="text-green-600 text-3xl mb-3">⚡</div>
                  <h4 className="font-semibold mb-2">Excellence</h4>
                  <p className="text-gray-600 text-sm">
                    Striving for perfection in our certification processes
                  </p>
                </div>

                <div className="text-center">
                  <div className="text-green-600 text-3xl mb-3">🌟</div>
                  <h4 className="font-semibold mb-2">Trust</h4>
                  <p className="text-gray-600 text-sm">
                    Building confidence through transparent and reliable
                    services
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  } catch {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">About Us</h1>
          <p className="text-gray-600">Content loading...</p>
        </div>
      </div>
    );
  }
}

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main>
        <Suspense
          fallback={
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600" />
            </div>
          }
        >
          <AboutContent />
        </Suspense>
      </main>
      <Footer />
    </div>
  );
}
