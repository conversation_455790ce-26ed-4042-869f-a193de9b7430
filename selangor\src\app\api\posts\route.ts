import { eq } from 'drizzle-orm';
import { type NextRequest } from 'next/server';
import { db, posts } from '@/lib/db';
import { createSuccessResponse, createErrorResponse, handleApiError } from '@/lib/api-response';

// Remove edge runtime to use Node.js runtime for database access

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const slug = searchParams.get('slug');

    if (slug) {
      // Get specific post by slug
      const post = await db.select().from(posts).where(eq(posts.slug, slug));

      if (post.length === 0) {
        return createErrorResponse('Post not found', undefined, 404);
      }

      return createSuccessResponse(post[0]);
    }

    // Get all posts
    const allPosts = await db.select().from(posts);
    return createSuccessResponse(allPosts);
  } catch (error) {
    return handleApiError(error, 'Error fetching posts');
  }
}
