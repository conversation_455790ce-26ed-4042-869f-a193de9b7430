# Deployment Guide

This guide covers various deployment options for the JAKIM Halal Portal.

## 🚀 Deployment Options

### 1. Vercel (Recommended)

Vercel is the recommended platform for deploying Next.js applications.

#### Prerequisites
- Vercel account
- GitHub repository

#### Steps
1. **Connect Repository**
   ```bash
   # Install Vercel CLI
   npm i -g vercel
   
   # Login to Vercel
   vercel login
   
   # Deploy from project directory
   vercel
   ```

2. **Environment Variables**
   Set the following environment variables in Vercel dashboard:
   ```
   NEXT_PUBLIC_API_BASE_URL=https://api.halal.gov.my
   NEXT_PUBLIC_SEARCH_API_URL=https://search.halal.gov.my
   NEXT_PUBLIC_EADUAN_URL=https://eaduan.islam.gov.my
   NEXT_PUBLIC_MYEHALAL_DOMESTIC_URL=https://myehalal.halal.gov.my/domestik/v1/
   NEXT_PUBLIC_MYEHALAL_INTERNATIONAL_URL=https://myehalal.halal.gov.my/international/v1/
   ```

3. **Custom Domain**
   - Add custom domain in Vercel dashboard
   - Configure DNS records
   - SSL certificate is automatically provisioned

#### Automatic Deployments
- Push to `main` branch triggers production deployment
- Pull requests create preview deployments
- Branch deployments for staging environments

### 2. Docker Deployment

#### Dockerfile
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the application
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

#### Docker Compose
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=https://api.halal.gov.my
      - NEXT_PUBLIC_SEARCH_API_URL=https://search.halal.gov.my
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
```

#### Build and Run
```bash
# Build Docker image
docker build -t jakim-halal-portal .

# Run container
docker run -p 3000:3000 \
  -e NEXT_PUBLIC_API_BASE_URL=https://api.halal.gov.my \
  -e NEXT_PUBLIC_SEARCH_API_URL=https://search.halal.gov.my \
  jakim-halal-portal

# Using Docker Compose
docker-compose up -d
```

### 3. Static Export

For hosting on static file servers or CDNs.

#### Configuration
Add to `next.config.js`:
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  }
}

module.exports = nextConfig
```

#### Build and Export
```bash
# Build and export
npm run build

# Files will be in the 'out' directory
# Upload contents to your static hosting provider
```

### 4. Traditional Server Deployment

#### Prerequisites
- Node.js 18+ on server
- Process manager (PM2 recommended)
- Reverse proxy (Nginx recommended)

#### Steps
1. **Build Application**
   ```bash
   npm run build
   ```

2. **Install PM2**
   ```bash
   npm install -g pm2
   ```

3. **PM2 Configuration** (`ecosystem.config.js`)
   ```javascript
   module.exports = {
     apps: [{
       name: 'jakim-halal-portal',
       script: 'npm',
       args: 'start',
       cwd: '/path/to/app',
       env: {
         NODE_ENV: 'production',
         PORT: 3000,
         NEXT_PUBLIC_API_BASE_URL: 'https://api.halal.gov.my'
       },
       instances: 'max',
       exec_mode: 'cluster',
       watch: false,
       max_memory_restart: '1G'
     }]
   }
   ```

4. **Start Application**
   ```bash
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

5. **Nginx Configuration**
   ```nginx
   server {
     listen 80;
     server_name halal.gov.my;
     
     location / {
       proxy_pass http://localhost:3000;
       proxy_http_version 1.1;
       proxy_set_header Upgrade $http_upgrade;
       proxy_set_header Connection 'upgrade';
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header X-Forwarded-Proto $scheme;
       proxy_cache_bypass $http_upgrade;
     }
   }
   ```

## 🔧 Environment Configuration

### Production Environment Variables
```env
# Required
NODE_ENV=production
NEXT_PUBLIC_API_BASE_URL=https://api.halal.gov.my
NEXT_PUBLIC_SEARCH_API_URL=https://search.halal.gov.my

# External Services
NEXT_PUBLIC_EADUAN_URL=https://eaduan.islam.gov.my
NEXT_PUBLIC_MYEHALAL_DOMESTIC_URL=https://myehalal.halal.gov.my/domestik/v1/
NEXT_PUBLIC_MYEHALAL_INTERNATIONAL_URL=https://myehalal.halal.gov.my/international/v1/

# Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# Security (if using custom authentication)
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=https://halal.gov.my

# Database (if applicable)
DATABASE_URL=postgresql://user:password@localhost:5432/halal_portal

# Redis (for caching)
REDIS_URL=redis://localhost:6379
```

### Staging Environment
```env
NODE_ENV=staging
NEXT_PUBLIC_API_BASE_URL=https://staging-api.halal.gov.my
NEXT_PUBLIC_SEARCH_API_URL=https://staging-search.halal.gov.my
# ... other staging URLs
```

## 🔒 Security Considerations

### SSL/TLS Configuration
- Use HTTPS in production
- Configure proper SSL certificates
- Enable HSTS headers
- Use secure cookie settings

### Content Security Policy
Add to `next.config.js`:
```javascript
const securityHeaders = [
  {
    key: 'Content-Security-Policy',
    value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;"
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
]

module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
    ]
  },
}
```

## 📊 Monitoring and Analytics

### Performance Monitoring
- Set up Core Web Vitals monitoring
- Configure error tracking (Sentry recommended)
- Monitor bundle size and performance metrics

### Analytics Setup
```javascript
// Google Analytics 4
export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID

export const pageview = (url) => {
  window.gtag('config', GA_TRACKING_ID, {
    page_path: url,
  })
}

export const event = ({ action, category, label, value }) => {
  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value,
  })
}
```

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Node.js version compatibility
   - Verify all environment variables are set
   - Clear `.next` directory and rebuild

2. **Runtime Errors**
   - Check server logs
   - Verify API endpoints are accessible
   - Ensure all required environment variables are set

3. **Performance Issues**
   - Enable compression (gzip/brotli)
   - Configure proper caching headers
   - Optimize images and assets

### Health Checks
Create a health check endpoint:
```javascript
// pages/api/health.js
export default function handler(req, res) {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version
  })
}
```

## 📋 Deployment Checklist

### Pre-deployment
- [ ] All tests pass
- [ ] Code review completed
- [ ] Environment variables configured
- [ ] SSL certificates ready
- [ ] Database migrations (if applicable)
- [ ] CDN configuration
- [ ] Monitoring setup

### Post-deployment
- [ ] Health check passes
- [ ] Core functionality tested
- [ ] Performance metrics verified
- [ ] Error monitoring active
- [ ] Analytics tracking confirmed
- [ ] Backup procedures verified

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:all

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

---

For additional support, contact the development team or refer to the main documentation.
