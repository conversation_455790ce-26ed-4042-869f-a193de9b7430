#!/usr/bin/env bun

import { DatabaseService } from './src/db/service';
import { Logger } from './src/utils/logger';

async function testConnection() {
  const logger = Logger.getInstance();
  const dbService = new DatabaseService();

  logger.info('Testing database connection...');

  try {
    const isConnected = await dbService.testConnection();

    if (isConnected) {
      logger.success('✅ Database connection successful!');

      // Test getting product count
      const count = await dbService.getProductCount();
      logger.info(`📊 Current products in database: ${count}`);
    } else {
      logger.error('❌ Database connection failed!');
      process.exit(1);
    }
  } catch (error) {
    logger.error('❌ Connection test failed:', error);
    process.exit(1);
  }
}

testConnection();
