@import "tailwindcss";

:root {
  /* Halal Selangor Color System */

  /* Primary Colors - Government Green */
  --primary-green: #2d5a27;
  --primary-green-light: #4a7c59;
  --primary-green-dark: #1a3d1a;

  /* Islamic Green */
  --islamic-green: #008000;
  --islamic-green-light: #33a033;
  --islamic-green-dark: #006600;

  /* Base Colors */
  --background: #ffffff;
  --foreground: #000000; /* Pure black for maximum contrast */
  --white: #ffffff;
  --off-white: #fafafa;

  /* Text Colors */
  --text-primary: #000000; /* Pure black for primary text */
  --text-secondary: #1f2937; /* Dark gray for secondary text */
  --text-muted: #4b5563; /* Medium gray for muted text */

  /* Interactive Colors */
  --link-blue: #0066cc;
  --link-blue-hover: #0052a3;

  /* Gray Scale - Darker for better contrast */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #6b7280;
  --gray-500: #4b5563;
  --gray-600: #374151;
  --gray-700: #1f2937;
  --gray-800: #111827;
  --gray-900: #000000;

  /* Status Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Background Variants */
  --bg-light-green: #f0f9f0;
  --bg-light-blue: #f0f8ff;
  --border-light: #e2e8f0;

  /* Legacy mappings for compatibility */
  --primary: var(--primary-green);
  --primary-foreground: var(--white);
  --secondary: var(--gray-100);
  --secondary-foreground: var(--text-primary);
  --accent: var(--islamic-green);
  --accent-foreground: var(--white);
  --muted: var(--gray-50);
  --muted-foreground: var(--text-secondary);
  --border: var(--border-light);
  --input: var(--white);
  --ring: var(--primary-green);
}

@theme inline {
  /* Color System */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary-green);
  --color-primary-foreground: var(--white);
  --color-secondary: var(--gray-100);
  --color-secondary-foreground: var(--text-primary);
  --color-accent: var(--islamic-green);
  --color-accent-foreground: var(--white);
  --color-muted: var(--gray-50);
  --color-muted-foreground: var(--text-secondary);
  --color-border: var(--border-light);

  /* Custom Halal Portal Colors */
  --color-primary-green: var(--primary-green);
  --color-primary-green-light: var(--primary-green-light);
  --color-primary-green-dark: var(--primary-green-dark);
  --color-islamic-green: var(--islamic-green);
  --color-islamic-green-light: var(--islamic-green-light);
  --color-islamic-green-dark: var(--islamic-green-dark);
  --color-link-blue: var(--link-blue);
  --color-link-blue-hover: var(--link-blue-hover);
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-error: var(--error);
  --color-info: var(--info);

  /* Gray Scale */
  --color-gray-50: var(--gray-50);
  --color-gray-100: var(--gray-100);
  --color-gray-200: var(--gray-200);
  --color-gray-300: var(--gray-300);
  --color-gray-400: var(--gray-400);
  --color-gray-500: var(--gray-500);
  --color-gray-600: var(--gray-600);
  --color-gray-700: var(--gray-700);
  --color-gray-800: var(--gray-800);
  --color-gray-900: var(--gray-900);

  /* Typography */
  --font-sans:
    var(--font-geist-sans), -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-mono:
    var(--font-geist-mono), "SF Mono", Monaco, "Cascadia Code", "Roboto Mono",
    Consolas, monospace;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --text-primary: #333333;
    --text-secondary: #a1a1aa;
    --text-muted: #71717a;
  }
}

* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-feature-settings:
    "rlig" 1,
    "calt" 1;
}

/* White text on dark backgrounds */
.bg-green-600,
.bg-green-600 *,
.bg-green-700,
.bg-green-700 *,
.bg-green-800,
.bg-green-800 *,
.bg-gray-800,
.bg-gray-800 *,
.bg-gray-900,
.bg-gray-900 *,
.bg-black,
.bg-black > * {
  color: #ffffff;
}

/* White text on gradient backgrounds */
.bg-gradient-to-r,
.bg-gradient-to-r > *,
.bg-gradient-to-l,
.bg-gradient-to-l > *,
.bg-gradient-to-t,
.bg-gradient-to-t > *,
.bg-gradient-to-b,
.bg-gradient-to-b > * {
  color: #ffffff important;
}

/* Preserve specific color utilities */
.text-white {
  color: #ffffff !important;
}

.text-green-600 {
  color: #059669 !important;
}

.text-green-100 {
  color: #dcfce7 !important;
}

.text-gray-600 {
  color: #4b5563 !important;
}

.text-gray-500 {
  color: #6b7280 !important;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}

/* Ensure proper text colors for common elements */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--text-primary);
}

p,
span,
div {
  color: inherit;
}

/* Fix for light backgrounds that need dark text */
.bg-white,
.bg-gray-50,
.bg-gray-100,
.bg-gray-200 {
  color: var(--text-primary);
}

/* Fix for dark backgrounds that need light text */
.bg-gray-800,
.bg-gray-900,
.bg-green-600,
.bg-green-700,
.bg-green-800 {
  color: white;
}

/* Ensure buttons have proper contrast */
button {
  color: inherit;
}

/* Fix for input elements */
input,
textarea,
select {
  color: var(--text-primary);
  background-color: var(--white);
}

input::placeholder,
textarea::placeholder {
  color: var(--text-muted);
}

/* Focus states for accessibility */
.focus-visible {
  outline: 2px solid var(--primary-green);
  outline-offset: 2px;
}

/* Explicit Tailwind color class overrides */
.text-gray-800 {
  color: var(--gray-800) !important;
}

.text-gray-600 {
  color: var(--gray-600);
}

.text-gray-500 {
  color: var(--gray-500);
}

.text-gray-700 {
  color: var(--gray-700);
}

.text-gray-900 {
  color: var(--gray-900);
}

/* Ensure headings have proper color */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--text-primary);
}

/* Ensure paragraphs have proper color */
p {
  color: var(--text-secondary);
}

/* Override for specific text color classes */
.text-green-600 {
  color: #059669;
}

.text-green-100 {
  color: #dcfce7;
}

/* Ensure container content has proper text color */
.container {
  color: var(--text-primary);
}

/* Ensure all divs inherit proper text color */
div {
  color: inherit;
}

/* Specific fixes for common layout patterns */
.bg-gray-50 {
  color: var(--text-primary);
}

/* Clean button styling */
button {
  color: inherit;
}

/* Allow specific color overrides on white backgrounds */
.bg-white .text-green-600 {
  color: #059669;
}

.bg-white .text-gray-600 {
  color: #4b5563;
}

/* High contrast support */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --primary-green: #004d00;
    --border-light: #666666;
  }
}

.summary p {
  @apply text-gray-700 my-2;
}

.summary h2 {
  @apply my-2;
}

.summary h3 {
  @apply my-1;
}
