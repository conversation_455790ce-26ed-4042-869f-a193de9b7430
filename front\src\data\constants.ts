import type { ContactInfo, NavItem, OperationHours } from '@/types';

export const LANGUAGES = [
  { code: 'en' as const, name: 'English', flag: '🇬🇧' },
  { code: 'bm' as const, name: 'Bahasa Malaysia', flag: '🇲🇾' },
];

export const OPERATION_HOURS: OperationHours = {
  weekdays: {
    en: {
      morning: '8:30 AM until 12:30 PM',
      afternoon: '2:30 PM until 4:30 PM',
    },
    bm: {
      morning: '8:30 Pagi sehingga 12.30 Tengahari',
      afternoon: '2:30 Petang sehingga 4.30 Petang',
    },
  },
  friday: {
    en: {
      morning: '8:30 AM until 12:00 PM',
      afternoon: '2:45 PM until 4:30 PM',
    },
    bm: {
      morning: '8:30 Pagi sehingga 12.00 Tengahari',
      afternoon: '2:45 Petang sehingga 4.30 Petang',
    },
  },
  weekend: {
    en: 'Closed',
    bm: 'Tutup',
  },
};

export const CONTACT_INFO: ContactInfo = {
  address: {
    en: `BAHAGIAN PENGURUSAN HALAL,
JABATAN KEMAJUAN ISLAM MALAYSIA,
<PERSON><PERSON> 6 & 7, <PERSON><PERSON><PERSON>,
<PERSON><PERSON><PERSON> (KIP),
No. 3 <PERSON><PERSON> <PERSON><PERSON>,
<PERSON>sint 3, 62100 Putrajaya, Malaysia.`,
    bm: `BAHAGIAN PENGURUSAN HALAL,
JABATAN KEMAJUAN ISLAM MALAYSIA,
Aras 6 & 7, Blok D,
Kompleks Islam Putrajaya (KIP),
No. 3 Jalan Tun Abdul Razak,
Presint 3, 62100 Putrajaya, Malaysia.`,
  },
  phone: '03-8892 5000',
  fax: '03-8892 5005',
  email: '<EMAIL>',
};

export const NAVIGATION_ITEMS: NavItem[] = [
  {
    id: 'corporate',
    label: 'Corporate',
    labelBM: 'Korporat',
    href: '/corporate',
  },
  {
    id: 'certification',
    label: 'Certification',
    labelBM: 'Pensijilan',
    href: '/certification',
  },
  {
    id: 'procedure',
    label: 'Procedure',
    labelBM: 'Prosedur',
    href: '/procedure',
  },
  {
    id: 'contact',
    label: 'Contact Us',
    labelBM: 'Hubungi Kami',
    href: '/contact',
  },
];

export const SOCIAL_LINKS = [
  {
    name: 'Facebook',
    url: 'https://www.facebook.com/HabHalalJakim/',
    icon: '/images/social/facebook.png',
  },
  {
    name: 'X (Twitter)',
    url: 'https://x.com/halal_malaysia',
    icon: '/images/social/x.png',
  },
];

export const GOVERNMENT_LINKS = [
  {
    name: 'Malaysia Government',
    nameBM: 'Kerajaan Malaysia',
    url: 'http://www.malaysia.gov.my',
    logo: '/images/logos/gov.png',
  },
  {
    name: 'JAKIM',
    nameBM: 'JAKIM',
    url: 'http://www.islam.gov.my',
    logo: '/images/logos/jakim_logo.png',
  },
  {
    name: 'KPDN',
    nameBM: 'KPDN',
    url: 'https://www.kpdn.gov.my',
    logo: '/images/logos/kpdnkk.png',
  },
  {
    name: 'Suruhanjaya Syarikat Malaysia',
    nameBM: 'Suruhanjaya Syarikat Malaysia',
    url: 'http://www.ssm.com.my',
    logo: '/images/logos/ssm.png',
  },
  {
    name: 'Ministry of Health Malaysia',
    nameBM: 'Kementerian Kesihatan Malaysia',
    url: 'http://www.moh.gov.my',
    logo: '/images/logos/kkm.png',
  },
];
