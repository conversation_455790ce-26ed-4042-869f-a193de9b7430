'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useCallback, useEffect, useState } from 'react';
import { CombinedSearchResults } from '@/components/CombinedSearchResults';
import { Footer } from '@/components/Footer';
import { Header } from '@/components/Header';
import {
  EnhancedSearchBox,
  type SearchType,
} from '@/components/SearchTypeSelector';
import { CategoryFilter } from '@/components/CategoryFilter';
import StateFilter from '@/components/StateFilter';
import type { CompanySearchResponse } from '@/types/company';
import type {
  ProductSearchResponse,
  SemanticProductSearchResponse,
} from '@/types/product';
import type { SearchResponse } from '@/types/search';

interface SummaryResponse {
  query: string;
  summary: string;
  chunks: Array<{
    text: string;
    score: number;
  }>;
  totalChunks: number;
  wordsUsed: number;
}

function SearchContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [searchResponse, setSearchResponse] = useState<SearchResponse | null>(
    null,
  );
  const [productResponse, setProductResponse] = useState<
    ProductSearchResponse | SemanticProductSearchResponse | null
  >(null);
  const [companyResponse, setCompanyResponse] =
    useState<CompanySearchResponse | null>(null);
  const [summaryResponse, setSummaryResponse] =
    useState<SummaryResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isProductLoading, setIsProductLoading] = useState(false);
  const [isCompanyLoading, setIsCompanyLoading] = useState(false);
  const [isSummaryLoading, setIsSummaryLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchInput, setSearchInput] = useState('');
  const [searchType, setSearchType] = useState<SearchType>('keyword');
  const [completedSearches, setCompletedSearches] = useState<string[]>([]);

  const query = searchParams.get('q') || '';
  const category = searchParams.get('category') || '';
  const state = searchParams.get('state') || '';
  const webPage = Number.parseInt(searchParams.get('webPage') || '1');
  const productPage = Number.parseInt(searchParams.get('productPage') || '1');
  const companyPage = Number.parseInt(searchParams.get('companyPage') || '1');
  const limit = Number.parseInt(searchParams.get('limit') || '10');

  // Update search input when query parameter changes
  useEffect(() => {
    setSearchInput(query);
  }, [query]);

  const performSearch = useCallback(
    (
      searchQuery: string,
      webSearchPage = 1,
      productSearchPage = 1,
      companySearchPage = 1,
    ) => {
      setError(null);
      setCompletedSearches([]); // Reset completed searches for new search

      // Helper function to handle fetch and state updates for each search type
      const executeSearch = async <T,>(
        url: string,
        setLoading: (loading: boolean) => void,
        setData: (data: T | null) => void,
        typeName: string,
      ) => {
        setLoading(true);
        try {
          const response = await fetch(url);
          if (response.ok) {
            const data: T = await response.json();
            setData(data);
            // Add to completed searches for progressive feedback
            setCompletedSearches((prev) => [
              ...prev.filter((s) => s !== typeName),
              typeName,
            ]);
          } else {
            console.error(`${typeName} search failed:`, response.statusText);
            // Optionally set a specific error state for this type
          }
        } catch (err) {
          console.error(`${typeName} search error:`, err);
          setError(
            err instanceof Error ? err.message : `${typeName} search failed`,
          );
        } finally {
          setLoading(false);
        }
      };

      // Web Content Search
      if (searchQuery.trim()) {
        void executeSearch<SearchResponse>(
          `/api/search?${new URLSearchParams({
            q: searchQuery,
            page: webSearchPage.toString(),
            limit: limit.toString(),
            minScore: '0.2',
            maxWordCount: '3000',
            retrieveDocument: 'true',
          })}`,
          setIsLoading,
          setSearchResponse,
          'Web',
        );
      } else {
        setSearchResponse(null);
        setIsLoading(false);
      }

      // Product Search
      const productSearchParams = new URLSearchParams({
        q: searchQuery,
        page: productSearchPage.toString(),
        limit: limit.toString(),
      });
      if (category) productSearchParams.set('category', category);

      // Add semantic-only parameters
      if (searchQuery.trim() && searchType === 'semantic') {
        productSearchParams.set('minSimilarity', '0.3');
      }

      const productSearchUrl =
        searchQuery.trim() && searchType === 'semantic'
          ? `/api/products/semantic-search?${productSearchParams}`
          : `/api/products/search?${productSearchParams}`;
      void executeSearch<ProductSearchResponse | SemanticProductSearchResponse>(
        productSearchUrl,
        setIsProductLoading,
        setProductResponse,
        'Product',
      );

      // Company Search
      const companySearchParams = new URLSearchParams({
        q: searchQuery,
        page: companySearchPage.toString(),
        limit: limit.toString(),
      });
      if (category) companySearchParams.set('category', category);
      if (state) companySearchParams.set('state', state);

      void executeSearch<CompanySearchResponse>(
        `/api/companies/search?${companySearchParams}`,
        setIsCompanyLoading,
        setCompanyResponse,
        'Company',
      );

      // Summary Generation
      if (searchQuery.trim()) {
        void executeSearch<SummaryResponse>(
          `/api/search/summary?${new URLSearchParams({ q: searchQuery })}`,
          setIsSummaryLoading,
          setSummaryResponse,
          'Summary',
        );
      } else {
        setSummaryResponse(null);
        setIsSummaryLoading(false);
      }
    },
    [limit, searchType, category, state],
  );

  const handleSearch = (newQuery: string, newSearchType?: SearchType) => {
    const params = new URLSearchParams();
    params.set('q', newQuery);
    // Reset all page parameters to 1 for new search
    params.set('webPage', '1');
    params.set('productPage', '1');
    params.set('companyPage', '1');

    // Preserve category and state filters
    if (category) {
      params.set('category', category);
    }
    if (state) {
      params.set('state', state);
    }

    // Update search type if provided
    if (newSearchType) {
      setSearchType(newSearchType);
    }

    router.push(`/search?${params.toString()}`);
  };

  // Handle real-time search input changes
  const handleInputChange = (newValue: string) => {
    setSearchInput(newValue);
    // Trigger search immediately when input is cleared
    if (newValue.trim() === '' && query !== '') {
      handleSearch('');
    }
  };

  const handleCategoryChange = (newCategory: string | undefined) => {
    const params = new URLSearchParams();
    params.set('q', query);
    // Reset all page parameters to 1 for new filter
    params.set('webPage', '1');
    params.set('productPage', '1');
    params.set('companyPage', '1');

    // Add category if selected
    if (newCategory) {
      params.set('category', newCategory);
    }

    // Preserve state filter
    if (state) {
      params.set('state', state);
    }

    router.push(`/search?${params.toString()}`);
  };

  const handleStateChange = (newState: string) => {
    const params = new URLSearchParams();
    params.set('q', query);
    // Reset all page parameters to 1 for new filter
    params.set('webPage', '1');
    params.set('productPage', '1');
    params.set('companyPage', '1');

    // Add state if selected
    if (newState) {
      params.set('state', newState);
    }

    // Preserve category filter
    if (category) {
      params.set('category', category);
    }

    router.push(`/search?${params.toString()}`);
  };

  const handlePageChange = (
    newPage: number,
    type: 'web' | 'products' | 'companies' = 'web',
  ) => {
    const params = new URLSearchParams();
    params.set('q', query);

    // Set the appropriate page parameter based on type
    if (type === 'web') {
      params.set('webPage', newPage.toString());
      if (productPage !== 1) params.set('productPage', productPage.toString());
      if (companyPage !== 1) params.set('companyPage', companyPage.toString());
    } else if (type === 'products') {
      params.set('productPage', newPage.toString());
      if (webPage !== 1) params.set('webPage', webPage.toString());
      if (companyPage !== 1) params.set('companyPage', companyPage.toString());
    } else if (type === 'companies') {
      params.set('companyPage', newPage.toString());
      if (webPage !== 1) params.set('webPage', webPage.toString());
      if (productPage !== 1) params.set('productPage', productPage.toString());
    }

    // Preserve filters
    if (category) {
      params.set('category', category);
    }
    if (state) {
      params.set('state', state);
    }
    if (limit !== 10) {
      params.set('limit', limit.toString());
    }
    router.push(`/search?${params.toString()}`);
  };

  useEffect(() => {
    // Always perform search - if no query, it will list all products and companies
    performSearch(query, webPage, productPage, companyPage);
  }, [query, category, state, webPage, productPage, companyPage, performSearch]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Search Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Search Halal Selangor
            </h1>
            <EnhancedSearchBox
              placeholder="Search for halal information, guidelines, and resources..."
              onSearch={handleSearch}
              onChange={handleInputChange}
              autoFocus={!query}
              size="lg"
              className="w-full mb-4"
              value={searchInput}
              searchType={searchType}
              onSearchTypeChange={setSearchType}
              showSearchTypeSelector={true}
            />

            {/* Filters */}
            <div className="flex items-center gap-4 flex-wrap">
              <CategoryFilter
                selectedCategory={category || undefined}
                onCategoryChange={handleCategoryChange}
                className="w-64"
                placeholder="Filter by category"
                showCounts={true}
              />
              <StateFilter
                selectedState={state}
                onStateChange={handleStateChange}
                className="w-64"
              />
              <div className="flex items-center gap-2 text-sm text-gray-600">
                {category && (
                  <span>
                    Category: <span className="font-medium text-green-700">{category}</span>
                  </span>
                )}
                {state && (
                  <span>
                    State: <span className="font-medium text-green-700">
                      {(() => {
                        const stateMap: { [key: string]: string } = {
                          'MY-01': 'Johor', 'MY-02': 'Kedah', 'MY-03': 'Kelantan',
                          'MY-04': 'Melaka', 'MY-05': 'Negeri Sembilan', 'MY-06': 'Pahang',
                          'MY-07': 'Penang', 'MY-08': 'Perak', 'MY-09': 'Perlis',
                          'MY-10': 'Selangor', 'MY-11': 'Terengganu', 'MY-12': 'Sabah',
                          'MY-13': 'Sarawak', 'MY-14': 'Kuala Lumpur', 'MY-15': 'Labuan',
                          'MY-16': 'Putrajaya'
                        };
                        return stateMap[state] || state;
                      })()}
                    </span>
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search Results */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-red-400"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-label="Search Error Icon"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    Search Error
                  </h3>
                  <div className="mt-2 text-sm text-red-700">{error}</div>
                </div>
              </div>
            </div>
          )}

          {/* Progressive Search Status */}
          {query &&
            (isLoading ||
              isProductLoading ||
              isCompanyLoading ||
              isSummaryLoading) && (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-blue-800 font-medium">
                    Search in progress...
                  </span>
                  <div className="flex space-x-2">
                    {completedSearches.includes('Web') && (
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                        ✓ Documents
                      </span>
                    )}
                    {completedSearches.includes('Product') && (
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                        ✓ Products
                      </span>
                    )}
                    {completedSearches.includes('Company') && (
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                        ✓ Companies
                      </span>
                    )}
                    {completedSearches.includes('Summary') && (
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                        ✓ Summary
                      </span>
                    )}
                  </div>
                </div>
              </div>
            )}

          {!error && (
            <CombinedSearchResults
              searchResponse={searchResponse}
              productResponse={productResponse as ProductSearchResponse | null}
              companyResponse={companyResponse}
              summaryResponse={summaryResponse}
              onPageChange={handlePageChange}
              isLoading={isLoading}
              isProductLoading={isProductLoading}
              isCompanyLoading={isCompanyLoading}
              isSummaryLoading={isSummaryLoading}
            />
          )}
        </div>
      </div>
    </div>
  );
}

export default function SearchPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main>
        <Suspense
          fallback={
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600" />
            </div>
          }
        >
          <SearchContent />
        </Suspense>
      </main>
      <Footer />
    </div>
  );
}
