'use client';

import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { ChatCore } from '../../../../components/chat/ChatCore';
import { PageChatLayout } from '../../../../components/chat/PageChatLayout';
import { fetchApiData } from '../../../../lib/api-client';

interface Bot {
  id: number;
  name: string;
  slug: string;
  provider: string;
  model: string;
  temperature: number;
  isDefault: boolean;
}

export default function BotChatPage() {
  const [bot, setBot] = useState<Bot | null>(null);
  const params = useParams();
  const { botSlug } = params;

  useEffect(() => {
    if (botSlug) {
      const fetchBot = async () => {
        try {
          const API_BASE_URL =
            process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:16001';
          const DEFAULT_SITE_ID =
            process.env.NEXT_PUBLIC_DEFAULT_SITE_ID || '1';

          const botData = await fetchApiData(
            `${API_BASE_URL}/api/sites/${DEFAULT_SITE_ID}/bots/slug/${botSlug}`
          );

          if (botData) {
            setBot(botData);
          } else {
            console.error('Bot not found or API error');
            // Handle bot not found
          }
        } catch (error) {
          console.error('Error fetching bot:', error);
        }
      };

      fetchBot();
    }
  }, [botSlug]);

  // Handle session creation
  const handleSessionCreated = (sessionId: string) => {
    console.log('Session created for bot:', bot?.name, sessionId);
  };

  const handleMessageSent = () => {
    // Optional: Handle message sent events
  };

  const handleMessageReceived = () => {
    // Optional: Handle message received events
  };

  const handleHandoverRequested = () => {
    // Optional: Handle handover requested events
  };

  const handleHandoverCompleted = () => {
    // Optional: Handle handover completed events
  };

  if (!bot) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4" />
          <p>Loading bot...</p>
        </div>
      </div>
    );
  }

  return (
    <ChatCore
      botSlug={botSlug as string}
      onSessionCreated={handleSessionCreated}
      onMessageSent={handleMessageSent}
      onMessageReceived={handleMessageReceived}
      onHandoverRequested={handleHandoverRequested}
      onHandoverCompleted={handleHandoverCompleted}
    >
      {(renderProps) => (
        <PageChatLayout renderProps={renderProps} botName={bot.name} />
      )}
    </ChatCore>
  );
}
