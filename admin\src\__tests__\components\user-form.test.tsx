import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { UserForm } from '@/components/forms/user-form';
import { UserRole } from '@/types';

// Mock the UI components
jest.mock('@/components/ui/button', () => ({
  Button: ({ children, ...props }: any) => (
    <button {...props}>{children}</button>
  ),
}));

jest.mock('@/components/ui/card', () => ({
  Card: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  CardContent: ({ children, ...props }: any) => (
    <div {...props}>{children}</div>
  ),
  CardDescription: ({ children, ...props }: any) => (
    <div {...props}>{children}</div>
  ),
  CardHeader: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  CardTitle: ({ children, ...props }: any) => <h2 {...props}>{children}</h2>,
}));

jest.mock('@/components/ui/input', () => ({
  Input: (props: any) => <input {...props} />,
}));

jest.mock('@/components/ui/label', () => ({
  Label: ({ children, ...props }: any) => <label {...props}>{children}</label>,
}));

describe('UserForm', () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();
  const mockClearError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const defaultProps = {
    onSubmit: mockOnSubmit,
    onCancel: mockOnCancel,
    clearError: mockClearError,
    mode: 'create' as const,
  };

  it('renders firstName and lastName fields', () => {
    render(<UserForm {...defaultProps} />);

    expect(screen.getByLabelText('First Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Last Name')).toBeInTheDocument();
  });

  it('loads initial firstName and lastName values', () => {
    const initialData = {
      username: 'testuser',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      roles: [UserRole.EDITOR],
      isActive: true,
    };

    render(<UserForm {...defaultProps} initialData={initialData} />);

    const firstNameInput = screen.getByDisplayValue('John');
    const lastNameInput = screen.getByDisplayValue('Doe');

    expect(firstNameInput).toBeInTheDocument();
    expect(lastNameInput).toBeInTheDocument();
  });

  it('updates firstName and lastName values when typing', async () => {
    render(<UserForm {...defaultProps} />);

    const firstNameInput = screen.getByLabelText('First Name');
    const lastNameInput = screen.getByLabelText('Last Name');

    fireEvent.change(firstNameInput, { target: { value: 'Jane' } });
    fireEvent.change(lastNameInput, { target: { value: 'Smith' } });

    expect(firstNameInput).toHaveValue('Jane');
    expect(lastNameInput).toHaveValue('Smith');
  });

  it('submits form with firstName and lastName data', async () => {
    mockOnSubmit.mockResolvedValue(undefined);

    render(<UserForm {...defaultProps} />);

    // Fill in required fields
    fireEvent.change(screen.getByLabelText('Username *'), {
      target: { value: 'testuser' },
    });
    fireEvent.change(screen.getByLabelText('Password *'), {
      target: { value: 'password123' },
    });
    fireEvent.change(screen.getByLabelText('First Name'), {
      target: { value: 'John' },
    });
    fireEvent.change(screen.getByLabelText('Last Name'), {
      target: { value: 'Doe' },
    });

    // Submit the form
    fireEvent.click(screen.getByText('Create User'));

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        username: 'testuser',
        email: '',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe',
        roles: [UserRole.EDITOR],
        isActive: true,
      });
    });
  });

  it('submits form with empty firstName and lastName when not provided', async () => {
    mockOnSubmit.mockResolvedValue(undefined);

    render(<UserForm {...defaultProps} />);

    // Fill in only required fields
    fireEvent.change(screen.getByLabelText('Username *'), {
      target: { value: 'testuser' },
    });
    fireEvent.change(screen.getByLabelText('Password *'), {
      target: { value: 'password123' },
    });

    // Submit the form
    fireEvent.click(screen.getByText('Create User'));

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        username: 'testuser',
        email: '',
        password: 'password123',
        firstName: '',
        lastName: '',
        roles: [UserRole.EDITOR],
        isActive: true,
      });
    });
  });

  it('works in edit mode with firstName and lastName', async () => {
    const initialData = {
      username: 'existinguser',
      email: '<EMAIL>',
      firstName: 'Existing',
      lastName: 'User',
      roles: [UserRole.ADMIN],
      isActive: true,
    };

    mockOnSubmit.mockResolvedValue(undefined);

    render(
      <UserForm {...defaultProps} mode="edit" initialData={initialData} />,
    );

    // Update firstName and lastName
    fireEvent.change(screen.getByDisplayValue('Existing'), {
      target: { value: 'Updated' },
    });
    fireEvent.change(screen.getByDisplayValue('User'), {
      target: { value: 'Person' },
    });

    // Submit the form
    fireEvent.click(screen.getByText('Update User'));

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        username: 'existinguser',
        email: '<EMAIL>',
        firstName: 'Updated',
        lastName: 'Person',
        roles: [UserRole.ADMIN],
        isActive: true,
      });
    });
  });
});
