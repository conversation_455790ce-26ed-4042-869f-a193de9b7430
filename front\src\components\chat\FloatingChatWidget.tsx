'use client';

import { ChatCore } from './ChatCore';
import { useChatContext } from './ChatProvider';
import { FloatingChatLayout } from './FloatingChatLayout';

export default function FloatingChatWidget() {
  const { isStaffLoggedIn } = useChatContext();

  // Add welcome message when session is created
  const handleSessionCreated = (sessionId: string) => {
    console.log('Session created:', sessionId);
  };

  const handleMessageSent = () => {
    // Optional: Handle message sent events
  };

  const handleMessageReceived = () => {
    // Optional: Handle message received events
  };

  const handleHandoverRequested = () => {
    // Optional: Handle handover requested events
  };

  const handleHandoverCompleted = () => {
    // Optional: Handle handover completed events
  };

  return (
    <ChatCore
      onSessionCreated={handleSessionCreated}
      onMessageSent={handleMessageSent}
      onMessageReceived={handleMessageReceived}
      onHandoverRequested={handleHandoverRequested}
      onHandoverCompleted={handleHandoverCompleted}
    >
      {(renderProps) => (
        <FloatingChatLayout
          renderProps={renderProps}
          isStaffLoggedIn={isStaffLoggedIn}
        />
      )}
    </ChatCore>
  );
}
