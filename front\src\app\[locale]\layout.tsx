import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { hasLocale, NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { ChatProvider } from '@/components/chat/ChatProvider';
import { Footer } from '@/components/footer';
import { Header } from '@/components/header';
import { NotificationToastContainer } from '@/components/notifications/NotificationToast';
import { AuthProvider } from '@/components/providers/AuthProvider';
import { routing } from '@/i18n/routing';
import { LanguageProvider } from '@/lib/language-context';

export const metadata: Metadata = {
  title: 'Halal Malaysia Portal',
  description: 'Official portal for Halal certification in Malaysia - JAKIM',
  keywords: [
    'halal',
    'malaysia',
    'jakim',
    'certification',
    'islamic',
    'food',
    'halal certification',
    'muslim',
    'islamic food',
  ],
  authors: [{ name: 'JAKIM - Jaba<PERSON> Malaysia' }],
  creator: 'JAKIM',
  publisher: 'JAKIM',
  openGraph: {
    title: 'Halal Malaysia Portal',
    description: 'Official portal for Halal certification in Malaysia - JAKIM',
    url: 'https://myehalal.halal.gov.my',
    siteName: 'Halal Malaysia Portal',
    locale: 'ms_MY',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Halal Malaysia Portal',
    description: 'Official portal for Halal certification in Malaysia - JAKIM',
    creator: '@halal_malaysia',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default async function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  // Ensure that the incoming `locale` is valid
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages}>
      <AuthProvider>
        <LanguageProvider>
          <ChatProvider>
            <Header />
            <main className="flex-1">{children}</main>
            <Footer />
            <NotificationToastContainer />
          </ChatProvider>
        </LanguageProvider>
      </AuthProvider>
    </NextIntlClientProvider>
  );
}

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}
