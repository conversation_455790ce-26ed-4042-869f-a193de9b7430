'use client';

export const runtime = 'edge';

import {
  AlertCircle,
  ArrowLeft,
  CheckCircle,
  Copy,
  ExternalLink,
  Eye,
  EyeOff,
  Save,
  TestTube,
} from 'lucide-react';
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';
import { useRouter } from '@/i18n/navigation';
import { useAuthStore } from '@/stores/auth';

interface FacebookConfigForm {
  pageAccessToken: string;
  pageId: string;
  appSecret: string;
  verifyToken: string;
}

interface WebhookInfo {
  webhookUrl: string;
  instructions: string[];
}

export default function FacebookConfigPage() {
  const [form, setForm] = useState<FacebookConfigForm>({
    pageAccessToken: '',
    pageId: '',
    appSecret: '',
    verifyToken: '',
  });
  const [webhookInfo, setWebhookInfo] = useState<WebhookInfo | null>(null);
  const [showTokens, setShowTokens] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [message, setMessage] = useState<{
    type: 'success' | 'error';
    text: string;
  } | null>(null);
  const [isConfigured, setIsConfigured] = useState(false);
  const router = useRouter();
  const { adminToken, isAdminAuthenticated } = useAuthStore();

  const checkAuth = useCallback(() => {
    if (!isAdminAuthenticated || !adminToken) {
      router.push('/admin');
    }
  }, [isAdminAuthenticated, adminToken, router]);

  const loadConfig = useCallback(async () => {
    if (!adminToken) {
      return;
    }

    try {
      const response = await fetch('/api/admin/facebook/config', {
        headers: { Authorization: `Bearer ${adminToken}` },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.configured && data.config) {
          setIsConfigured(true);
          // Don't load sensitive data for security
        }
      }
    } catch (error) {
      console.error('Failed to load config:', error);
    }
  }, [adminToken]);

  const loadWebhookInfo = useCallback(async () => {
    if (!adminToken) {
      return;
    }

    try {
      const response = await fetch('/api/admin/facebook/webhook-url', {
        headers: { Authorization: `Bearer ${adminToken}` },
      });

      if (response.ok) {
        const data = await response.json();
        setWebhookInfo(data);
      }
    } catch (error) {
      console.error('Failed to load webhook info:', error);
    }
  }, [adminToken]);

  useEffect(() => {
    checkAuth();
    loadConfig();
    loadWebhookInfo();
  }, [checkAuth, loadConfig, loadWebhookInfo]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
    if (message) {
      setMessage(null);
    }
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage(null);

    if (!adminToken) {
      setMessage({
        type: 'error',
        text: 'Authentication required. Please log in again.',
      });
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/admin/facebook/config', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${adminToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(form),
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({
          type: 'success',
          text: 'Configuration saved successfully!',
        });
        setIsConfigured(true);
      } else {
        setMessage({
          type: 'error',
          text: data.message || 'Failed to save configuration',
        });
      }
    } catch (_error) {
      setMessage({ type: 'error', text: 'Network error. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTest = async () => {
    setIsTesting(true);
    setMessage(null);

    if (!adminToken) {
      setMessage({
        type: 'error',
        text: 'Authentication required. Please log in again.',
      });
      setIsTesting(false);
      return;
    }

    try {
      const response = await fetch('/api/admin/facebook/test', {
        method: 'POST',
        headers: { Authorization: `Bearer ${adminToken}` },
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({
          type: 'success',
          text: 'Facebook configuration is valid!',
        });
      } else {
        setMessage({
          type: 'error',
          text: data.error || 'Configuration test failed',
        });
      }
    } catch (_error) {
      setMessage({ type: 'error', text: 'Network error. Please try again.' });
    } finally {
      setIsTesting(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setMessage({ type: 'success', text: 'Copied to clipboard!' });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center py-4">
            <button
              onClick={() => router.push('/admin/dashboard')}
              className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <h1 className="text-2xl font-bold text-gray-900">
              Facebook Messenger Configuration
            </h1>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Status Message */}
        {message && (
          <div
            className={`mb-6 p-4 rounded-md flex items-center space-x-2 ${
              message.type === 'success'
                ? 'bg-green-50 border border-green-200'
                : 'bg-red-50 border border-red-200'
            }`}
          >
            {message.type === 'success' ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-500" />
            )}
            <span
              className={`text-sm ${
                message.type === 'success' ? 'text-green-700' : 'text-red-700'
              }`}
            >
              {message.text}
            </span>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Configuration Form */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                API Configuration
              </h2>
              <p className="text-sm text-gray-600">
                Enter your Facebook Messenger API credentials
              </p>
            </div>

            <form onSubmit={handleSave} className="p-6 space-y-6">
              {/* Page Access Token */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Page Access Token *
                </label>
                <div className="relative">
                  <input
                    type={showTokens ? 'text' : 'password'}
                    name="pageAccessToken"
                    value={form.pageAccessToken}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter your Facebook Page access token"
                  />
                  <button
                    type="button"
                    onClick={() => setShowTokens(!showTokens)}
                    className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
                  >
                    {showTokens ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>

              {/* Page ID */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Page ID *
                </label>
                <input
                  type="text"
                  name="pageId"
                  value={form.pageId}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your Facebook Page ID"
                />
              </div>

              {/* App Secret */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  App Secret *
                </label>
                <input
                  type={showTokens ? 'text' : 'password'}
                  name="appSecret"
                  value={form.appSecret}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your Facebook App secret"
                />
              </div>

              {/* Verify Token */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Webhook Verify Token *
                </label>
                <input
                  type={showTokens ? 'text' : 'password'}
                  name="verifyToken"
                  value={form.verifyToken}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your webhook verify token"
                />
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4">
                <button
                  type="submit"
                  disabled={isLoading}
                  className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {isLoading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Save Configuration
                </button>

                {isConfigured && (
                  <button
                    type="button"
                    onClick={handleTest}
                    disabled={isTesting}
                    className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    {isTesting ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    ) : (
                      <TestTube className="h-4 w-4 mr-2" />
                    )}
                    Test
                  </button>
                )}
              </div>
            </form>
          </div>

          {/* Webhook Information */}
          {webhookInfo && (
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">
                  Webhook Setup
                </h2>
                <p className="text-sm text-gray-600">
                  Configure your Facebook Messenger webhook with these details
                </p>
              </div>

              <div className="p-6 space-y-6">
                {/* Webhook URL */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Webhook URL
                  </label>
                  <div className="flex">
                    <input
                      type="text"
                      value={webhookInfo.webhookUrl}
                      readOnly
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50 text-sm"
                    />
                    <button
                      onClick={() => copyToClipboard(webhookInfo.webhookUrl)}
                      className="px-3 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-200"
                    >
                      <Copy className="h-4 w-4 text-gray-600" />
                    </button>
                  </div>
                </div>

                {/* Instructions */}
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    Setup Instructions
                  </h3>
                  <ol className="text-sm text-gray-600 space-y-2">
                    {webhookInfo.instructions.map((instruction, index) => (
                      <li key={index} className="flex">
                        <span className="font-medium mr-2">{index + 1}.</span>
                        <span>{instruction}</span>
                      </li>
                    ))}
                  </ol>
                </div>

                {/* External Link */}
                <div className="pt-4 border-t border-gray-200">
                  <a
                    href="https://developers.facebook.com/docs/messenger-platform/webhooks"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
                  >
                    <ExternalLink className="h-4 w-4 mr-1" />
                    Facebook Messenger Webhook Documentation
                  </a>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
