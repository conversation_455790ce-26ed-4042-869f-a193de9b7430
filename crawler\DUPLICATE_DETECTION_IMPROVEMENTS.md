# Duplicate File Detection Improvements

## Overview
The web crawler has been enhanced with comprehensive duplicate file detection to prevent downloading the same PDF, DOC, and DOCX files multiple times. This saves bandwidth, storage space, and crawling time.

## Key Improvements

### 1. Enhanced Database Schema
The `downloaded_files` table now includes additional fields for better duplicate detection:

```sql
CREATE TABLE downloaded_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    url TEXT NOT NULL UNIQUE,           -- Original: URL of the file
    file_path TEXT NOT NULL,            -- Original: Local path where file is saved
    filename TEXT NOT NULL,             -- NEW: Original filename
    file_size INTEGER NOT NULL,         -- NEW: File size in bytes
    file_hash TEXT,                     -- NEW: SHA-256 hash of file content
    downloaded_at REAL NOT NULL         -- Original: Timestamp
);
```

### 2. Multiple Duplicate Detection Methods

#### Method 1: URL-based Detection
- **Purpose**: Prevents re-downloading from the same URL
- **Implementation**: Checks if the exact URL has been downloaded before
- **Use case**: Same file linked from multiple pages with identical URLs

#### Method 2: Filename-based Detection
- **Purpose**: Prevents downloading files with the same name
- **Implementation**: Checks if a file with the same filename already exists
- **Use case**: Same file available at different URLs but with same filename

#### Method 3: Content Hash Detection
- **Purpose**: Prevents downloading files with identical content
- **Implementation**: Calculates SHA-256 hash of file content and compares
- **Use case**: Same file content available with different filenames or URLs

### 3. Smart File Handling

#### Content Verification
- Downloads file content to memory first
- Calculates hash before saving to disk
- Verifies file integrity after saving

#### Filename Collision Handling
- Automatically generates unique filenames when disk conflicts occur
- Preserves original filename in database for reference
- Uses pattern: `filename_1.ext`, `filename_2.ext`, etc.

#### Database Linking
- When duplicate content is detected, new URL entries point to existing files
- Maintains complete download history while avoiding file duplication

### 4. Database Migration Support
- Automatically upgrades existing databases to new schema
- Preserves all existing data during migration
- Backfills missing filename data for existing records

## Usage Examples

### Scenario 1: Same URL, Multiple Attempts
```
First attempt:  https://example.com/report.pdf → Downloads and saves
Second attempt: https://example.com/report.pdf → Skips (URL duplicate)
```

### Scenario 2: Different URLs, Same Filename
```
URL 1: https://site1.com/annual-report.pdf → Downloads and saves
URL 2: https://site2.com/annual-report.pdf → Checks content hash
  - If different content: Saves as annual-report_1.pdf
  - If same content: Links to existing file
```

### Scenario 3: Different Filenames, Same Content
```
URL 1: https://example.com/report-2023.pdf → Downloads and saves
URL 2: https://example.com/yearly-summary.pdf → Detects same content hash
Result: Links to existing report-2023.pdf, no new download
```

## Performance Benefits

### Storage Savings
- Eliminates duplicate files on disk
- Reduces storage requirements significantly for sites with many duplicate files

### Bandwidth Savings
- Avoids re-downloading identical content
- Faster crawling of sites with many file duplicates

### Database Efficiency
- Indexed lookups for fast duplicate detection
- Optimized queries for URL, filename, and hash checking

## Logging and Monitoring

The crawler now provides detailed logging for duplicate detection:

```
INFO: Skipping already downloaded file (by URL): https://example.com/file.pdf
INFO: Skipping file with same name already downloaded: report.pdf -> /path/to/existing/report.pdf
INFO: Skipping duplicate file (same content): new-name.pdf -> /path/to/existing/file.pdf
INFO: Downloaded: /path/to/file.pdf (1024 bytes)
```

## Testing

A comprehensive test suite (`test_duplicate_detection.py`) verifies:
- URL-based duplicate detection
- Filename-based duplicate detection  
- Content hash-based duplicate detection
- Database migration functionality
- File integrity verification

## Backward Compatibility

- Existing crawl databases are automatically migrated
- All existing functionality remains unchanged
- Resume functionality works with enhanced duplicate detection
- Force recrawl option still clears all data when needed

## Configuration

No additional configuration required. The duplicate detection is enabled by default and works transparently with existing crawler usage:

```python
# Standard usage - duplicate detection is automatic
result = await crawl_website("https://example.com", max_depth=3, max_pages=100)
```

The enhanced duplicate detection makes the crawler more efficient and reliable for large-scale website crawling operations.
