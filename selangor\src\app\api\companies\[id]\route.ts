import { eq } from 'drizzle-orm';
import { type NextRequest } from 'next/server';
import { db } from '@/lib/db';
import { companies } from '@/lib/db/schema';
import { createSuccessResponse, createErrorResponse, handleApiError } from '@/lib/api-response';

// Remove edge runtime to use Node.js runtime for database access

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> },
) {
  const params = await context.params;
  const companyId = Number.parseInt(params.id);

  if (isNaN(companyId)) {
    return createErrorResponse('Invalid company ID', undefined, 400);
  }

  try {
    const [company] = await db
      .select({
        id: companies.id,
        companyName: companies.companyName,
        registrationNumber: companies.registrationNumber,
        businessType: companies.businessType,
        category: companies.category,
        subcategory: companies.subcategory,
        address: companies.address,
        state: companies.state,
        postcode: companies.postcode,
        city: companies.city,
        country: companies.country,
        phone: companies.phone,
        fax: companies.fax,
        email: companies.email,
        website: companies.website,
        contactPerson: companies.contactPerson,
        certificateNumber: companies.certificateNumber,
        certificateType: companies.certificateType,
        certificateStatus: companies.certificateStatus,
        issuedDate: companies.issuedDate,
        expiryDate: companies.expiryDate,
        sourceUrl: companies.sourceUrl,
        rawData: companies.rawData,
        createdAt: companies.createdAt,
        updatedAt: companies.updatedAt,
      })
      .from(companies)
      .where(eq(companies.id, companyId))
      .limit(1);

    if (!company) {
      return createErrorResponse('Company not found', undefined, 404);
    }

    console.log('Company found:', company.id, company.companyName);

    return createSuccessResponse(company);
  } catch (error) {
    return handleApiError(error, 'Failed to fetch company');
  }
}
