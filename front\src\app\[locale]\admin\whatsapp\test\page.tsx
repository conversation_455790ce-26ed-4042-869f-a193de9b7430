'use client';

export const runtime = 'edge';

import {
  AlertCircle,
  ArrowLeft,
  CheckCircle,
  Clock,
  Image,
  MessageSquare,
  Phone,
  Send,
} from 'lucide-react';
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';
import { useRouter } from '@/i18n/navigation';
import { useAuthStore } from '@/stores/auth';

interface TestMessage {
  to: string;
  message: string;
  type: 'text' | 'image';
  imageUrl?: string;
}

interface TestResult {
  success: boolean;
  messageId?: string;
  error?: string;
  timestamp: Date;
}

export default function WhatsAppTestPage() {
  const [form, setForm] = useState<TestMessage>({
    to: '',
    message: '',
    type: 'text',
    imageUrl: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [message, setMessage] = useState<{
    type: 'success' | 'error';
    text: string;
  } | null>(null);
  const router = useRouter();

  const checkAuth = useCallback(() => {
    const token = localStorage.getItem('admin_token');
    if (!token) {
      router.push('/admin');
    }
  }, [router]);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
    if (message) {
      setMessage(null);
    }
  };

  const handleSendTest = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage(null);

    const token = localStorage.getItem('admin_token');
    if (!token) {
      return;
    }

    try {
      const response = await fetch('/api/admin/whatsapp/test-message', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(form),
      });

      const data = await response.json();
      const result: TestResult = {
        success: response.ok,
        messageId: data.messageId,
        error: data.error,
        timestamp: new Date(),
      };

      setTestResults((prev) => [result, ...prev]);

      if (response.ok) {
        setMessage({
          type: 'success',
          text: `Test message sent successfully! Message ID: ${data.messageId}`,
        });
        // Reset form
        setForm((prev) => ({ ...prev, message: '', imageUrl: '' }));
      } else {
        setMessage({
          type: 'error',
          text: data.error || 'Failed to send test message',
        });
      }
    } catch (_error) {
      const result: TestResult = {
        success: false,
        error: 'Network error',
        timestamp: new Date(),
      };
      setTestResults((prev) => [result, ...prev]);
      setMessage({ type: 'error', text: 'Network error. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const formatPhoneNumber = (phone: string) => {
    // Remove all non-digits and add country code if missing
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10 && !cleaned.startsWith('60')) {
      return `60${cleaned}`;
    }
    return cleaned;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center py-4">
            <button
              onClick={() => router.push('/admin/dashboard')}
              className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <h1 className="text-2xl font-bold text-gray-900">
              Test WhatsApp Integration
            </h1>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Status Message */}
        {message && (
          <div
            className={`mb-6 p-4 rounded-md flex items-center space-x-2 ${
              message.type === 'success'
                ? 'bg-green-50 border border-green-200'
                : 'bg-red-50 border border-red-200'
            }`}
          >
            {message.type === 'success' ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-500" />
            )}
            <span
              className={`text-sm ${
                message.type === 'success' ? 'text-green-700' : 'text-red-700'
              }`}
            >
              {message.text}
            </span>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Test Form */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                Send Test Message
              </h2>
              <p className="text-sm text-gray-600">
                Test your WhatsApp Business API integration
              </p>
            </div>

            <form onSubmit={handleSendTest} className="p-6 space-y-6">
              {/* Phone Number */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Phone className="inline h-4 w-4 mr-1" />
                  Phone Number *
                </label>
                <input
                  type="tel"
                  name="to"
                  value={form.to}
                  onChange={(e) => {
                    const formatted = formatPhoneNumber(e.target.value);
                    setForm((prev) => ({ ...prev, to: formatted }));
                  }}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-800"
                  placeholder="*********** (Malaysia format)"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Enter phone number with country code (e.g., *********** for
                  Malaysia)
                </p>
              </div>

              {/* Message Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Message Type
                </label>
                <select
                  name="type"
                  value={form.type}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-800"
                >
                  <option value="text">Text Message</option>
                  <option value="image">Image Message</option>
                </select>
              </div>

              {/* Message Text */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <MessageSquare className="inline h-4 w-4 mr-1" />
                  Message {form.type === 'image' ? '(Caption)' : '*'}
                </label>
                <textarea
                  name="message"
                  value={form.message}
                  onChange={handleInputChange}
                  required={form.type === 'text'}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-800"
                  placeholder={
                    form.type === 'text'
                      ? 'Enter your test message...'
                      : 'Enter image caption (optional)...'
                  }
                />
              </div>

              {/* Image URL (if image type) */}
              {form.type === 'image' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Image className="inline h-4 w-4 mr-1" />
                    Image URL *
                  </label>
                  <input
                    type="url"
                    name="imageUrl"
                    value={form.imageUrl}
                    onChange={handleInputChange}
                    required={form.type === 'image'}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-800"
                    placeholder="https://example.com/image.jpg"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Image must be publicly accessible via HTTPS
                  </p>
                </div>
              )}

              {/* Send Button */}
              <button
                type="submit"
                disabled={
                  isLoading ||
                  !form.to ||
                  (form.type === 'text' && !form.message) ||
                  (form.type === 'image' && !form.imageUrl)
                }
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {isLoading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                ) : (
                  <Send className="h-4 w-4 mr-2" />
                )}
                Send Test Message
              </button>
            </form>
          </div>

          {/* Test Results */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                Test Results
              </h2>
              <p className="text-sm text-gray-600">
                History of test messages sent
              </p>
            </div>

            <div className="p-6">
              {testResults.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No test messages sent yet</p>
                  <p className="text-sm">
                    Send a test message to see results here
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {testResults.map((result, index) => (
                    <div
                      key={index}
                      className={`p-4 rounded-lg border ${
                        result.success
                          ? 'bg-green-50 border-green-200'
                          : 'bg-red-50 border-red-200'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-2">
                          {result.success ? (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          ) : (
                            <AlertCircle className="h-5 w-5 text-red-500" />
                          )}
                          <span
                            className={`font-medium ${
                              result.success ? 'text-green-800' : 'text-red-800'
                            }`}
                          >
                            {result.success ? 'Success' : 'Failed'}
                          </span>
                        </div>
                        <div className="flex items-center text-xs text-gray-500">
                          <Clock className="h-3 w-3 mr-1" />
                          {result.timestamp.toLocaleTimeString()}
                        </div>
                      </div>

                      {result.success && result.messageId && (
                        <p className="text-sm text-green-700 mt-2">
                          Message ID: {result.messageId}
                        </p>
                      )}

                      {!result.success && result.error && (
                        <p className="text-sm text-red-700 mt-2">
                          Error: {result.error}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-4">
            Testing Instructions
          </h3>
          <ul className="text-sm text-blue-800 space-y-2">
            <li>
              • Make sure your WhatsApp Business API is properly configured
            </li>
            <li>
              • Use a valid phone number with country code (e.g., ***********
              for Malaysia)
            </li>
            <li>
              • For image messages, ensure the image URL is publicly accessible
              via HTTPS
            </li>
            <li>• Test messages will be sent to the specified phone number</li>
            <li>• Check the recipient's WhatsApp to verify message delivery</li>
          </ul>
        </div>
      </main>
    </div>
  );
}
