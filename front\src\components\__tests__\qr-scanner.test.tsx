import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { LanguageProvider } from '@/lib/language-context';
import { QRScanner, QRScannerButton } from '../qr-scanner';

// Test utilities for creating real media streams
class MockMediaStream {
  private tracks: MockMediaStreamTrack[] = [];

  constructor() {
    this.tracks = [new MockMediaStreamTrack()];
  }

  getTracks() {
    return this.tracks;
  }

  getVideoTracks() {
    return this.tracks.filter((track) => track.kind === 'video');
  }
}

class MockMediaStreamTrack {
  kind = 'video';
  enabled = true;
  readyState = 'live';

  stop() {
    this.readyState = 'ended';
  }
}

// Test wrapper component with language context
const TestWrapper = ({
  children,
  language = 'en',
}: {
  children: React.ReactNode;
  language?: string;
}) => (
  <LanguageProvider initialLanguage={language}>{children}</LanguageProvider>
);

describe('QRScanner', () => {
  let mockGetUserMedia: jest.Mock;
  let mockOnScan: jest.Mock;
  let mockOnClose: jest.Mock;
  let originalPrompt: typeof window.prompt;

  beforeEach(() => {
    // Setup fresh mocks for each test
    mockOnScan = jest.fn();
    mockOnClose = jest.fn();

    // Setup media devices
    mockGetUserMedia = jest.fn();
    Object.defineProperty(navigator, 'mediaDevices', {
      writable: true,
      value: {
        getUserMedia: mockGetUserMedia,
      },
    });

    // Setup video element
    Object.defineProperty(HTMLVideoElement.prototype, 'play', {
      writable: true,
      value: jest.fn().mockResolvedValue(undefined),
    });

    Object.defineProperty(HTMLVideoElement.prototype, 'pause', {
      writable: true,
      value: jest.fn(),
    });

    // Setup window.prompt
    originalPrompt = window.prompt;
    window.prompt = jest.fn();
  });

  afterEach(() => {
    window.prompt = originalPrompt;
    jest.clearAllMocks();
  });

  const defaultProps = {
    onScan: mockOnScan,
    onClose: mockOnClose,
    isOpen: true,
  };

  it('should not render when isOpen is false', () => {
    render(
      <TestWrapper>
        <QRScanner {...defaultProps} isOpen={false} />
      </TestWrapper>,
    );

    expect(screen.queryByText(/scan qr code/i)).not.toBeInTheDocument();
  });

  it('should render scanner interface when open', () => {
    render(
      <TestWrapper>
        <QRScanner {...defaultProps} />
      </TestWrapper>,
    );

    expect(screen.getByText(/scan qr code/i)).toBeInTheDocument();
    expect(screen.getByText(/position the qr code/i)).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /start camera/i }),
    ).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /enter manually/i }),
    ).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
  });

  it('should close scanner when close button is clicked', async () => {
    const user = userEvent.setup();
    render(
      <TestWrapper>
        <QRScanner {...defaultProps} />
      </TestWrapper>,
    );

    const closeButton = screen.getByRole('button', { name: '' }); // X button
    await user.click(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should close scanner when cancel button is clicked', async () => {
    const user = userEvent.setup();
    render(
      <TestWrapper>
        <QRScanner {...defaultProps} />
      </TestWrapper>,
    );

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await user.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should start camera when start camera button is clicked', async () => {
    const user = userEvent.setup();
    const mockStream = new MockMediaStream();
    mockGetUserMedia.mockResolvedValueOnce(mockStream);

    render(
      <TestWrapper>
        <QRScanner {...defaultProps} />
      </TestWrapper>,
    );

    const startButton = screen.getByRole('button', { name: /start camera/i });
    await user.click(startButton);

    await waitFor(() => {
      expect(mockGetUserMedia).toHaveBeenCalledWith({
        video: {
          facingMode: 'environment',
          width: { ideal: 1280 },
          height: { ideal: 720 },
        },
      });
    });
  });

  it('should show error when camera access fails', async () => {
    const user = userEvent.setup();
    mockGetUserMedia.mockRejectedValueOnce(new Error('Camera access denied'));

    render(
      <TestWrapper>
        <QRScanner {...defaultProps} />
      </TestWrapper>,
    );

    const startButton = screen.getByRole('button', { name: /start camera/i });
    await user.click(startButton);

    await waitFor(() => {
      expect(screen.getByText(/unable to access camera/i)).toBeInTheDocument();
    });
  });

  it('should show error when getUserMedia is not supported', async () => {
    const user = userEvent.setup();
    // Temporarily remove getUserMedia support
    const originalGetUserMedia = navigator.mediaDevices.getUserMedia;
    delete (navigator.mediaDevices as any).getUserMedia;

    render(<QRScanner {...defaultProps} />);

    const startButton = screen.getByRole('button', { name: /start camera/i });
    await user.click(startButton);

    await waitFor(() => {
      expect(
        screen.getByText(/camera access is not supported/i),
      ).toBeInTheDocument();
    });

    // Restore getUserMedia
    navigator.mediaDevices.getUserMedia = originalGetUserMedia;
  });

  it('should handle manual input when enter manually button is clicked', async () => {
    const user = userEvent.setup();
    mockPrompt.mockReturnValueOnce('TEST-CERTIFICATE-123');

    render(<QRScanner {...defaultProps} />);

    const manualButton = screen.getByRole('button', {
      name: /enter manually/i,
    });
    await user.click(manualButton);

    expect(mockPrompt).toHaveBeenCalledWith(
      expect.stringContaining('Enter certificate number'),
    );
    expect(mockOnScan).toHaveBeenCalledWith('TEST-CERTIFICATE-123');
  });

  it('should not call onScan when manual input is cancelled', async () => {
    const user = userEvent.setup();
    mockPrompt.mockReturnValueOnce(null); // User cancelled

    render(<QRScanner {...defaultProps} />);

    const manualButton = screen.getByRole('button', {
      name: /enter manually/i,
    });
    await user.click(manualButton);

    expect(mockOnScan).not.toHaveBeenCalled();
  });

  it('should not call onScan when manual input is empty', async () => {
    const user = userEvent.setup();
    mockPrompt.mockReturnValueOnce('   '); // Empty/whitespace input

    render(<QRScanner {...defaultProps} />);

    const manualButton = screen.getByRole('button', {
      name: /enter manually/i,
    });
    await user.click(manualButton);

    expect(mockOnScan).not.toHaveBeenCalled();
  });

  it('should show scanning status when camera is active', async () => {
    const user = userEvent.setup();
    mockGetUserMedia.mockResolvedValueOnce(mockStream);

    render(<QRScanner {...defaultProps} />);

    const startButton = screen.getByRole('button', { name: /start camera/i });
    await user.click(startButton);

    await waitFor(() => {
      expect(screen.getByText(/scanning/i)).toBeInTheDocument();
    });
  });

  it('should stop camera stream when component unmounts', () => {
    mockGetUserMedia.mockResolvedValueOnce(mockStream);

    const { unmount } = render(<QRScanner {...defaultProps} />);

    // Simulate camera start
    fireEvent.click(screen.getByRole('button', { name: /start camera/i }));

    unmount();

    // Note: In a real test, you'd need to wait for the camera to start
    // and then verify the stream is stopped on unmount
  });
});

describe('QRScannerButton', () => {
  const mockOnScan = jest.fn();

  beforeEach(() => {
    cleanupMocks();
    mockOnScan.mockClear();
  });

  const defaultProps = {
    onScan: mockOnScan,
  };

  it('should render scanner button', () => {
    render(<QRScannerButton {...defaultProps} />);

    expect(
      screen.getByRole('button', { name: /scan qr code/i }),
    ).toBeInTheDocument();
  });

  it('should render with primary variant by default', () => {
    render(<QRScannerButton {...defaultProps} />);

    const button = screen.getByRole('button', { name: /scan qr code/i });
    expect(button).toHaveClass('btn-primary');
  });

  it('should render with secondary variant when specified', () => {
    render(<QRScannerButton {...defaultProps} variant="secondary" />);

    const button = screen.getByRole('button', { name: /scan qr code/i });
    expect(button).toHaveClass('btn-secondary');
  });

  it('should apply custom className', () => {
    render(<QRScannerButton {...defaultProps} className="custom-class" />);

    const button = screen.getByRole('button', { name: /scan qr code/i });
    expect(button).toHaveClass('custom-class');
  });

  it('should open scanner when button is clicked', async () => {
    const user = userEvent.setup();
    render(<QRScannerButton {...defaultProps} />);

    const button = screen.getByRole('button', { name: /scan qr code/i });
    await user.click(button);

    expect(screen.getByText(/scan qr code/i)).toBeInTheDocument(); // Scanner modal title
  });

  it('should close scanner and call onScan when scan is completed', async () => {
    const user = userEvent.setup();
    mockPrompt.mockReturnValueOnce('TEST-DATA');

    render(<QRScannerButton {...defaultProps} />);

    // Open scanner
    const button = screen.getByRole('button', { name: /scan qr code/i });
    await user.click(button);

    // Use manual input
    const manualButton = screen.getByRole('button', {
      name: /enter manually/i,
    });
    await user.click(manualButton);

    expect(mockOnScan).toHaveBeenCalledWith('TEST-DATA');

    // Scanner should close automatically after scan
    await waitFor(() => {
      expect(
        screen.queryByText(/position the qr code/i),
      ).not.toBeInTheDocument();
    });
  });

  it('should handle scan without onScan callback', async () => {
    const user = userEvent.setup();
    mockPrompt.mockReturnValueOnce('TEST-DATA');

    render(<QRScannerButton />);

    // Open scanner
    const button = screen.getByRole('button', { name: /scan qr code/i });
    await user.click(button);

    // Use manual input - should not throw error
    const manualButton = screen.getByRole('button', {
      name: /enter manually/i,
    });
    await user.click(manualButton);

    // Should still close scanner
    await waitFor(() => {
      expect(
        screen.queryByText(/position the qr code/i),
      ).not.toBeInTheDocument();
    });
  });
});
