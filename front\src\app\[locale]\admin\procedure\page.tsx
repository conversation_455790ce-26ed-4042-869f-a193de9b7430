'use client';

export const runtime = 'edge';

import {
  BookOpen,
  Calendar,
  Download,
  Edit3,
  Eye,
  File,
  FileText,
  PlusCircle,
  Search,
  Trash2,
  Upload,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Link, useRouter } from '@/i18n/navigation';
import { api } from '@/lib/api';

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

interface Procedure {
  id: number;
  title: string;
  description: string;
  category: string;
  version: string;
  status: 'draft' | 'published' | 'archived' | 'under_review';
  documentUrl?: string;
  author: string;
  lastModified: string;
  createdAt: string;
  updatedAt: string;
}

interface ProcedureResponse {
  procedures: Procedure[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
    totalPages: number;
  };
}

export default function ProcedurePage() {
  const router = useRouter();
  const [procedures, setProcedures] = useState<Procedure[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    hasMore: false,
    totalPages: 0,
  });

  useEffect(() => {
    fetchProcedures();
  }, [pagination.page, searchTerm, categoryFilter, statusFilter]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchProcedures = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });

      if (searchTerm) {
        params.append('search', searchTerm);
      }
      if (categoryFilter !== 'all') {
        params.append('category', categoryFilter);
      }
      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }

      const response = await api.get<ProcedureResponse>(
        `/admin/procedures?${params}`,
      );
      setProcedures(response.data.procedures);
      setPagination(response.data.pagination);
    } catch (err) {
      console.error('Error fetching procedures:', err);
      setError('Failed to load procedures');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this procedure?')) {
      return;
    }

    try {
      await api.delete(`/admin/procedures/${id}`);
      await fetchProcedures(); // Refresh the list
    } catch (err) {
      console.error('Error deleting procedure:', err);
      setError('Failed to delete procedure');
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchProcedures();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'draft':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'under_review':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'archived':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (isLoading && procedures.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">
            Procedure Management
          </h1>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading procedures...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <Button variant="ghost" size="sm" onClick={() => setError(null)}>
              ×
            </Button>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Procedure Management
          </h1>
          <p className="text-gray-600 mt-2">
            Manage standard operating procedures, guidelines, and documentation
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push('/admin/procedure/upload')}
          >
            <Upload className="mr-2 h-4 w-4" />
            Upload Document
          </Button>
          <Button onClick={() => router.push('/admin/procedure/new')}>
            <PlusCircle className="mr-2 h-4 w-4" />
            New Procedure
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter Procedures</CardTitle>
          <CardDescription>
            Find procedures by title, category, or author
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search procedures..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Button type="submit">
                <Search className="mr-2 h-4 w-4" />
                Search
              </Button>
            </div>
            <div className="flex gap-4">
              <div className="flex-1">
                <Select
                  value={categoryFilter}
                  onValueChange={setCategoryFilter}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="certification">Certification</SelectItem>
                    <SelectItem value="audit">Audit</SelectItem>
                    <SelectItem value="compliance">Compliance</SelectItem>
                    <SelectItem value="training">Training</SelectItem>
                    <SelectItem value="quality">Quality Control</SelectItem>
                    <SelectItem value="safety">Safety</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex-1">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="under_review">Under Review</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Procedures Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {procedures.map((procedure) => (
          <Card
            key={procedure.id}
            className="hover:shadow-md transition-shadow"
          >
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  <CardTitle className="text-lg truncate">
                    {procedure.title}
                  </CardTitle>
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      router.push(`/admin/procedure/${procedure.id}`)
                    }
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      router.push(`/admin/procedure/${procedure.id}/edit`)
                    }
                  >
                    <Edit3 className="h-4 w-4" />
                  </Button>
                  {procedure.documentUrl && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        window.open(procedure.documentUrl, '_blank')
                      }
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDelete(procedure.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardDescription>
                <div className="flex items-center gap-2">
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(procedure.status)}`}
                  >
                    {procedure.status.replace('_', ' ').toUpperCase()}
                  </span>
                  <span className="text-xs text-gray-500">
                    v{procedure.version}
                  </span>
                </div>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <p className="text-gray-600 line-clamp-2">
                  {procedure.description}
                </p>
                <div>
                  <span className="font-medium">Category:</span>{' '}
                  <span className="text-gray-600">{procedure.category}</span>
                </div>
                <div>
                  <span className="font-medium">Author:</span>{' '}
                  <span className="text-gray-600">{procedure.author}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3 text-gray-400" />
                  <span className="text-gray-600 text-xs">
                    Modified:{' '}
                    {new Date(procedure.lastModified).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {procedures.length === 0 && !isLoading && (
          <Card className="col-span-full">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Procedures Found
              </h3>
              <p className="text-gray-600 text-center mb-4">
                {searchTerm
                  ? `No procedures found matching "${searchTerm}"`
                  : 'Get started by creating your first procedure.'}
              </p>
              <Button onClick={() => router.push('/admin/procedure/new')}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Create Procedure
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Total Procedures
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {pagination.total}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Published</p>
                <p className="text-2xl font-bold text-gray-900">
                  {procedures.filter((p) => p.status === 'published').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <File className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Drafts</p>
                <p className="text-2xl font-bold text-gray-900">
                  {procedures.filter((p) => p.status === 'draft').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Under Review
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {procedures.filter((p) => p.status === 'under_review').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
