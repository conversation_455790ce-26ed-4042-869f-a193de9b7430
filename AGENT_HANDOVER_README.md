# Agent Handover System

A comprehensive agent handover system that allows seamless transfer of chat sessions from AI bots to human agents.

## 🚀 Features

### Core Functionality
- **Seamless Handover**: Users can request human assistance at any time
- **Agent Dashboard**: Dedicated interface for agents to manage chat sessions
- **Real-time Updates**: WebSocket-based real-time communication
- **Multi-platform Support**: Works with web chat, WhatsApp, and Facebook Messenger
- **Rich Media Support**: Agents can send voice messages and file attachments

### Agent Capabilities
- **Session Management**: View and manage active chat sessions
- **Voice Messages**: Record and send voice messages to users
- **File Attachments**: Send documents, images, and other files
- **Session Transfer**: Hand sessions back to the bot when complete
- **Priority Handling**: Manage handover requests by priority level

### Admin Features
- **Agent Management**: Create and manage agent accounts
- **Session Monitoring**: Monitor all chat sessions and handovers
- **Analytics Dashboard**: View statistics and performance metrics
- **Role-based Access**: Support for agents and supervisors

## 🏗️ Architecture

### Database Schema
- `agent_users` - Agent authentication and profiles
- `chat_sessions` - Persistent chat session storage
- `chat_messages` - Individual chat messages with rich media support
- `session_assignments` - Track which agent is handling which session
- `handover_requests` - Queue and manage handover requests
- `agent_messages` - Agent-specific message metadata

### API Endpoints
- `/api/agent/*` - Agent authentication and management
- `/api/sessions/*` - Session listing, assignment, and handover
- `/api/admin/agents/*` - Agent management for admins

### Frontend Components
- **Agent Login** (`/agent`) - Agent authentication portal
- **Agent Dashboard** (`/agent/dashboard`) - Main agent interface
- **Agent Chat** (`/agent/chat/[sessionId]`) - Individual session chat interface
- **Admin Agent Management** (`/admin/agents`) - Agent administration

## 🛠️ Setup Instructions

### 1. Database Setup
Run the database setup script to create the necessary tables:

```bash
cd server
node scripts/setup-agent-db.js
```

This will create:
- All required database tables
- Necessary indexes for performance
- Sample agent users for testing

### 2. Environment Variables
Add the following environment variables to your `.env` file:

```env
# JWT Secret for agent authentication
JWT_SECRET=your-jwt-secret-key

# Database connection (if using external PostgreSQL)
DATABASE_URL=postgresql://user:password@host:port/database

# WebSocket URL for real-time updates
NEXT_PUBLIC_WS_URL=ws://localhost:8080
```

### 3. Start the Services
Start both the backend and frontend services:

```bash
# Start the backend server
cd server
npm run dev

# Start the frontend (in another terminal)
cd front
npm run dev
```

## 👥 User Roles

### Agent
- Access to agent dashboard
- Can take over chat sessions
- Can send messages, voice, and files
- Can complete sessions and return to bot

### Supervisor
- All agent capabilities
- Can view all agent activities
- Can manage agent assignments
- Access to advanced analytics

### Admin
- Full system access
- Can create and manage agent accounts
- Can monitor all sessions and handovers
- System configuration access

## 🔐 Authentication

### Agent Login
Agents log in through the dedicated agent portal at `/agent` using their username and password.

### Sample Credentials
The setup script creates these sample accounts:
- **Agent 1**: `agent1` / `password123`
- **Agent 2**: `agent2` / `password123`
- **Supervisor**: `supervisor1` / `password123`

## 💬 How It Works

### 1. User Requests Handover
- User clicks "Talk to Human" button in chat interface
- System creates a handover request with priority level
- Request appears in agent dashboard queue

### 2. Agent Takes Over
- Agent sees pending handover requests in dashboard
- Agent clicks "Take Over" to assign session to themselves
- User is notified that they're now connected to an agent
- Bot stops responding to that session

### 3. Agent Conversation
- Agent can send text messages, voice recordings, and files
- All messages are stored in database with agent attribution
- Real-time updates keep both sides synchronized
- User sees agent name and status indicators

### 4. Session Completion
- Agent clicks "Complete Session" when done
- Session is returned to bot control
- User is notified they can continue with AI assistant
- Session statistics are updated

## 🔄 Real-time Communication

The system uses WebSocket connections for real-time updates:

### Message Types
- `session_assigned` - User notified of agent assignment
- `agent_message` - Real-time agent messages to user
- `user_message` - Real-time user messages to agent
- `session_completed` - Session returned to bot
- `handover_requested` - New handover request created

### Connection Management
- Users connect as `user` type with session ID
- Agents connect as `agent` type with agent ID
- Automatic reconnection with exponential backoff
- Connection cleanup on disconnect

## 📱 Multi-platform Support

The handover system works across all supported platforms:

### Web Chat
- Integrated handover button in chat interface
- Real-time status indicators
- Rich media support for agents

### WhatsApp Business API
- Handover requests via special keywords
- Agent messages appear as business messages
- File and voice message support

### Facebook Messenger
- Handover through quick reply buttons
- Agent takeover with page messaging
- Rich media and attachment support

## 🎨 User Interface

### Agent Dashboard Features
- **Session Queue**: Pending handover requests with priority
- **Active Sessions**: Currently assigned sessions
- **Statistics**: Performance metrics and activity stats
- **Status Control**: Online/offline status management

### Chat Interface Features
- **Agent Indicators**: Show when connected to human agent
- **Handover Button**: Easy access to request human help
- **Rich Media**: Support for voice, images, and files
- **Status Updates**: Real-time connection status

## 🔧 Customization

### Priority Levels
Handover requests support four priority levels:
- `low` - Non-urgent inquiries
- `normal` - Standard requests (default)
- `high` - Important issues
- `urgent` - Critical problems requiring immediate attention

### Message Types
The system supports various message types:
- `text` - Standard text messages
- `voice` - Audio recordings
- `file` - Document attachments
- `image` - Image uploads

### Session States
Sessions can be in different states:
- `active` - Normal bot conversation
- `pending_handover` - Waiting for agent assignment
- `agent_assigned` - Currently with human agent
- `completed` - Finished and returned to bot

## 📊 Analytics & Monitoring

### Dashboard Statistics
- Total active sessions
- Pending handover requests
- Agent online status
- Completed sessions today

### Performance Metrics
- Average response time
- Session completion rate
- Agent utilization
- User satisfaction scores

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify PostgreSQL is running
   - Check database credentials in environment variables
   - Ensure database exists and is accessible

2. **WebSocket Connection Failures**
   - Check WebSocket URL configuration
   - Verify firewall settings
   - Ensure WebSocket server is running

3. **Agent Login Issues**
   - Verify agent accounts exist in database
   - Check JWT secret configuration
   - Ensure password hashing is working correctly

### Debug Mode
Enable debug logging by setting:
```env
DEBUG=true
LOG_LEVEL=debug
```

## 🔮 Future Enhancements

- **Video Calling**: Integration with video call services
- **Screen Sharing**: Agent screen sharing capabilities
- **AI Assistance**: AI-powered agent suggestions
- **Advanced Analytics**: Detailed performance reporting
- **Mobile App**: Dedicated mobile app for agents
- **Integration APIs**: Third-party system integrations

## 📄 License

This agent handover system is part of the Halal Malaysia chatbot project and follows the same licensing terms.
