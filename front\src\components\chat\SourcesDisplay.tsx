'use client';

import { ExternalLink, File, FileText, Globe, Star } from 'lucide-react';

interface TextResult {
  text: string;
  type: 'vector' | 'graph' | string;
  document_id?: string | null;
  score: number;
  wordCount: number;
  metadata?: {
    title?: string;
    url?: string;
    source?: string;
    chunk_index?: number;
    document_type?: string;
    [key: string]: any;
  };
}

interface SourcesDisplayProps {
  sources: TextResult[];
  className?: string;
}

export function SourcesDisplay({
  sources,
  className = '',
}: SourcesDisplayProps) {
  if (!sources || sources.length === 0) {
    return null;
  }

  // Get file icon based on source or document type
  const getFileIcon = (metadata: TextResult['metadata']) => {
    const source = metadata?.source?.toLowerCase() || '';
    const docType = metadata?.document_type?.toLowerCase() || '';

    if (source.includes('.pdf') || docType.includes('pdf')) {
      return <FileText className="w-4 h-4 text-red-500" />;
    }
    if (
      source.includes('.doc') ||
      source.includes('.docx') ||
      docType.includes('doc')
    ) {
      return <FileText className="w-4 h-4 text-blue-500" />;
    }
    if (source.includes('http') || metadata?.url) {
      return <Globe className="w-4 h-4 text-green-500" />;
    }
    return <File className="w-4 h-4 text-gray-500" />;
  };

  // Extract filename from source path
  const getFileName = (metadata: TextResult['metadata']) => {
    const source = metadata?.source;
    if (!source) return null;

    // Extract filename from path (handle both forward and backward slashes)
    const parts = source.split(/[/\\]/);
    const filename = parts[parts.length - 1];

    // If it looks like a URL, return the domain
    if (source.startsWith('http')) {
      try {
        const url = new URL(source);
        return url.hostname;
      } catch {
        return filename;
      }
    }

    return filename;
  };

  // Format score as percentage
  const scorePercentage = (score: number) => Math.round(score * 100);

  // Get score color based on value
  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600 bg-green-50';
    if (score >= 0.6) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  return (
    <div className={`mt-3 ${className}`}>
      <div className="text-xs font-medium text-gray-600 mb-2">
        📚 Sources ({sources.length})
      </div>
      <div className="space-y-2">
        {sources.map((source, index) => {
          const fileName = getFileName(source.metadata);
          const fileIcon = getFileIcon(source.metadata);

          return (
            <div
              key={`${source.document_id}-${index}`}
              className="bg-gray-50 rounded-lg p-3 border border-gray-200"
            >
              {/* Document Header */}
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1">
                  {/* Document Title */}
                  {source.metadata?.title ? (
                    <h4 className="text-sm font-medium text-gray-900 mb-1">
                      📄 {source.metadata.title}
                    </h4>
                  ) : (
                    <h4 className="text-sm font-medium text-gray-900 mb-1">
                      📄 Document {index + 1}
                    </h4>
                  )}

                  {/* Document ID */}
                  {source.document_id && (
                    <div className="text-xs text-gray-600 mb-1">
                      <span className="font-medium">ID:</span>{' '}
                      <span className="font-mono bg-gray-100 px-1 rounded">
                        {source.document_id}
                      </span>
                    </div>
                  )}
                </div>

                {/* Score badge */}
                <div
                  className={`flex items-center px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(source.score)}`}
                >
                  <Star className="w-3 h-3 mr-1" />
                  {scorePercentage(source.score)}%
                </div>
              </div>

              {/* File source and link */}
              <div className="flex items-center justify-between mb-2">
                {fileName && (
                  <div className="flex items-center">
                    {fileIcon}
                    <span className="ml-1 text-xs text-gray-600">
                      {fileName}
                    </span>
                  </div>
                )}

                {source.metadata?.url && (
                  <a
                    href={source.metadata.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 text-xs font-medium"
                  >
                    <ExternalLink className="w-3 h-3 mr-1" />
                    View Document
                  </a>
                )}
              </div>

              {/* Content preview */}
              <div className="text-xs text-gray-700 bg-white p-2 rounded border">
                {source.text.length > 150
                  ? `${source.text.substring(0, 150)}...`
                  : source.text}
              </div>

              {/* Additional metadata */}
              <div className="flex items-center text-xs text-gray-500 space-x-3 mt-2">
                {source.wordCount && <span>📊 {source.wordCount} words</span>}
                {source.metadata?.chunk_index !== undefined && (
                  <span>🧩 Chunk {source.metadata.chunk_index + 1}</span>
                )}
                {source.type && <span>🔍 {source.type}</span>}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
