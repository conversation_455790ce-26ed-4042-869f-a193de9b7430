'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Save, X } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useUsersStore } from '@/stores/users';
import { type User, UserRole, type UserUpdateRequest } from '@/types';

export default function EditUserPage() {
  const router = useRouter();
  const params = useParams();
  const userId = params.id ? Number.parseInt(params.id as string, 10) : null;

  const { updateUser, fetchUserById, isLoading, error, clearError } =
    useUsersStore();
  const [user, setUser] = useState<User | null>(null);
  const [isFetching, setIsFetching] = useState(true);

  const [formData, setFormData] = useState<UserUpdateRequest>({
    username: '',
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    roles: [],
    isActive: true,
  });

  const [selectedRoles, setSelectedRoles] = useState<UserRole[]>([]);

  useEffect(() => {
    const loadUser = async () => {
      if (!userId) {
        return;
      }

      setIsFetching(true);
      const userData = await fetchUserById(userId);
      if (userData) {
        setUser(userData);
        setFormData({
          username: userData.username,
          email: userData.email || '',
          password: '', // Don't pre-fill password
          firstName: userData.firstName || '',
          lastName: userData.lastName || '',
          roles: userData.roles,
          isActive: userData.isActive,
        });
        setSelectedRoles(userData.roles);
      }
      setIsFetching(false);
    };

    loadUser();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId]); // fetchUserById is a stable Zustand store function

  const handleInputChange =
    (field: keyof UserUpdateRequest) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (field === 'isActive') {
        setFormData((prev) => ({
          ...prev,
          [field]: e.target.checked,
        }));
      } else {
        setFormData((prev) => ({
          ...prev,
          [field]: e.target.value,
        }));
      }
      // Clear error when user starts typing
      if (error) {
        clearError();
      }
    };

  const handleRoleToggle = (role: UserRole) => {
    const newRoles = selectedRoles.includes(role)
      ? selectedRoles.filter((r) => r !== role)
      : [...selectedRoles, role];

    setSelectedRoles(newRoles);
    setFormData((prev) => ({
      ...prev,
      roles: newRoles,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!userId) {
      return;
    }

    // Validate form
    if (!formData.username) {
      return;
    }

    if (formData.roles && formData.roles.length === 0) {
      return;
    }

    // Only include password if it's been changed
    const updateData: UserUpdateRequest = { ...formData };
    if (!updateData.password) {
      delete updateData.password;
    }

    const success = await updateUser(userId, updateData);

    if (success) {
      router.push('/users');
    }
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.SUPERADMIN:
        return 'bg-purple-100 text-purple-800 border-purple-300';
      case UserRole.ADMIN:
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case UserRole.EDITOR:
        return 'bg-green-100 text-green-800 border-green-300';
      case UserRole.AGENT:
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case UserRole.SUPERVISOR:
        return 'bg-orange-100 text-orange-800 border-orange-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  if (isFetching) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <p className="text-gray-500">Loading user...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (!user) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-gray-500">User not found</p>
            <Link href="/users">
              <Button className="mt-4">Back to Users</Button>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link href="/users">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Users
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Edit User</h1>
            <p className="text-gray-600">Update user information and roles</p>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <div className="flex items-center justify-between">
              <span>{error}</span>
              <Button variant="ghost" size="sm" onClick={clearError}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <Card>
            <CardHeader>
              <CardTitle>User Information</CardTitle>
              <CardDescription>
                Update the user details and roles
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="username">Username *</Label>
                  <Input
                    id="username"
                    type="text"
                    value={formData.username}
                    onChange={handleInputChange('username')}
                    placeholder="Enter username"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange('email')}
                    placeholder="Enter email address"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    type="text"
                    value={formData.firstName}
                    onChange={handleInputChange('firstName')}
                    placeholder="Enter first name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    type="text"
                    value={formData.lastName}
                    onChange={handleInputChange('lastName')}
                    placeholder="Enter last name"
                  />
                </div>

                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="password">
                    Password (leave blank to keep current)
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    value={formData.password}
                    onChange={handleInputChange('password')}
                    placeholder="Enter new password"
                  />
                </div>
              </div>

              <div className="space-y-3">
                <Label>User Roles *</Label>
                <div className="flex flex-wrap gap-2">
                  {Object.values(UserRole).map((role) => (
                    <button
                      key={role}
                      type="button"
                      onClick={() => handleRoleToggle(role)}
                      className={`px-3 py-2 rounded-md text-sm font-medium border transition-colors ${
                        selectedRoles.includes(role)
                          ? getRoleColor(role)
                          : 'bg-gray-50 text-gray-700 border-gray-300 hover:bg-gray-100'
                      }`}
                    >
                      {role}
                    </button>
                  ))}
                </div>
                {selectedRoles.length === 0 && (
                  <p className="text-sm text-red-600">
                    At least one role must be selected
                  </p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <input
                  id="isActive"
                  type="checkbox"
                  checked={formData.isActive}
                  onChange={handleInputChange('isActive')}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="isActive">Active User</Label>
              </div>

              <div className="flex items-center justify-end space-x-4 pt-6">
                <Link href="/users">
                  <Button type="button" variant="outline">
                    Cancel
                  </Button>
                </Link>
                <Button
                  type="submit"
                  disabled={
                    isLoading ||
                    !formData.username ||
                    selectedRoles.length === 0
                  }
                >
                  <Save className="mr-2 h-4 w-4" />
                  {isLoading ? 'Updating...' : 'Update User'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </form>
      </div>
    </DashboardLayout>
  );
}
