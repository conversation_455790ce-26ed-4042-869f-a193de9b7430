import { expect, test } from '@playwright/test';

test.describe('User Journey Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the home page before each test
    await page.goto('/');
  });

  test('should complete basic navigation flow', async ({ page }) => {
    // Test home page loads correctly
    await expect(page.locator('h1')).toContainText(/JAKIM.*Halal/i);

    // Test language switching
    const languageToggle = page.locator('[data-testid="language-toggle"]');
    if (await languageToggle.isVisible()) {
      await languageToggle.click();
      await expect(page.locator('h1')).toContainText(/JAKIM.*Halal/i);
    }

    // Test navigation to corporate pages
    await page.click('text=Corporate');
    await expect(page).toHaveURL(/.*corporate/);

    // Test navigation to about page
    await page.click('text=About Us');
    await expect(page).toHaveURL(/.*corporate\/about/);
    await expect(page.locator('h1')).toContainText(/About Us|Tentang Kami/);

    // Test breadcrumb navigation
    await page.click('text=Corporate');
    await expect(page).toHaveURL(/.*corporate/);
  });

  test('should perform search functionality', async ({ page }) => {
    // Test search widget on home page
    const searchInput = page.locator('[data-testid="search-input"]');
    await searchInput.fill('test company');

    // Submit search
    await page.click('button:has-text("Search")');
    await expect(page).toHaveURL(/.*search.*q=test%20company/);

    // Test search results page
    await expect(page.locator('h1')).toContainText(
      /Search Results|Hasil Carian/,
    );

    // Test filters
    await page.selectOption('select[aria-label*="Status"]', 'valid');
    await page.selectOption('select[aria-label*="Category"]', 'food');

    // Test sort options
    await page.selectOption(
      'select:has-option("Company A-Z")',
      'companyName-desc',
    );
  });

  test('should navigate through procedure pages', async ({ page }) => {
    // Navigate to procedure page
    await page.click('text=Procedure');
    await expect(page).toHaveURL(/.*procedure/);
    await expect(page.locator('h1')).toContainText(/Procedure|Prosedur/);

    // Test application process page
    await page.click('text=Application Process');
    await expect(page).toHaveURL(/.*procedure\/application/);
    await expect(page.locator('h1')).toContainText(
      /Application Process|Proses Permohonan/,
    );

    // Test requirements page
    await page.goto('/procedure');
    await page.click('text=Requirements');
    await expect(page).toHaveURL(/.*procedure\/requirements/);
    await expect(page.locator('h1')).toContainText(/Requirements|Keperluan/);

    // Test guidelines page
    await page.goto('/procedure');
    await page.click('text=Guidelines');
    await expect(page).toHaveURL(/.*procedure\/guidelines/);
    await expect(page.locator('h1')).toContainText(/Guidelines|Garis Panduan/);
  });

  test('should access information pages', async ({ page }) => {
    // Test FHCB page
    await page.goto('/fhcb');
    await expect(page.locator('h1')).toContainText(
      /Foreign Halal Certification Body|Badan Pensijilan Halal Asing/,
    );

    // Test Halal Info page
    await page.goto('/info');
    await expect(page.locator('h1')).toContainText(
      /Halal Information|Maklumat Halal/,
    );

    // Test Circular page
    await page.goto('/circular');
    await expect(page.locator('h1')).toContainText(/Circular|Pekeliling/);

    // Test Press page
    await page.goto('/press');
    await expect(page.locator('h1')).toContainText(
      /Press Statement|Kenyataan Akhbar/,
    );

    // Test Journal page
    await page.goto('/journal');
    await expect(page.locator('h1')).toContainText(/Journal|Jurnal/);

    // Test MyHAC page
    await page.goto('/myhac');
    await expect(page.locator('h1')).toContainText(/Info MyHAC/);
  });

  test('should use contact form', async ({ page }) => {
    // Navigate to contact page
    await page.goto('/contact');
    await expect(page.locator('h1')).toContainText(/Contact Us|Hubungi Kami/);

    // Fill out contact form
    await page.fill('input[name="name"]', 'John Doe');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="phone"]', '************');
    await page.fill('input[name="company"]', 'Test Company');
    await page.selectOption('select[name="category"]', 'general');
    await page.fill('input[name="subject"]', 'Test Inquiry');
    await page.fill(
      'textarea[name="message"]',
      'This is a test message for the contact form.',
    );

    // Submit form
    await page.click('button[type="submit"]');

    // Check for success message or loading state
    await expect(page.locator('text=Sending')).toBeVisible();
  });

  test('should handle responsive design', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Check if mobile menu is accessible
    const mobileMenuButton = page.locator('[data-testid="mobile-menu-button"]');
    if (await mobileMenuButton.isVisible()) {
      await mobileMenuButton.click();
      await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
    }

    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('h1')).toBeVisible();

    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('h1')).toBeVisible();
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Test 404 page
    await page.goto('/non-existent-page');
    await expect(page.locator('text=404')).toBeVisible();

    // Test search with no results
    await page.goto('/search?q=nonexistentcompany12345');
    await expect(page.locator('text=No results found')).toBeVisible();
  });

  test('should have proper accessibility features', async ({ page }) => {
    // Check for proper heading hierarchy
    const h1 = await page.locator('h1').count();
    expect(h1).toBeGreaterThan(0);

    // Check for alt text on images
    const images = page.locator('img');
    const imageCount = await images.count();

    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i);
      const alt = await img.getAttribute('alt');
      expect(alt).toBeTruthy();
    }

    // Check for proper form labels
    await page.goto('/contact');
    const inputs = page.locator('input[required]');
    const inputCount = await inputs.count();

    for (let i = 0; i < inputCount; i++) {
      const input = inputs.nth(i);
      const id = await input.getAttribute('id');
      if (id) {
        const label = page.locator(`label[for="${id}"]`);
        await expect(label).toBeVisible();
      }
    }
  });

  test('should handle external links correctly', async ({ page }) => {
    // Test E-Aduan external links
    await page.goto('/');

    // Look for E-Aduan links
    const eAduanLinks = page.locator('a[href*="eaduan"]');
    const linkCount = await eAduanLinks.count();

    if (linkCount > 0) {
      const firstLink = eAduanLinks.first();
      const target = await firstLink.getAttribute('target');
      expect(target).toBe('_blank');

      const rel = await firstLink.getAttribute('rel');
      expect(rel).toContain('noopener');
    }
  });

  test('should load performance critical resources', async ({ page }) => {
    // Monitor network requests
    const responses: Response[] = [];
    page.on('response', (response) => responses.push(response));

    await page.goto('/');

    // Check that critical resources loaded successfully
    const criticalResources = responses.filter(
      (response) =>
        response.url().includes('.css') ||
        response.url().includes('.js') ||
        response.url().includes('.woff'),
    );

    for (const resource of criticalResources) {
      expect(resource.status()).toBeLessThan(400);
    }
  });

  test('should maintain state during navigation', async ({ page }) => {
    // Test language preference persistence
    const languageToggle = page.locator('[data-testid="language-toggle"]');

    if (await languageToggle.isVisible()) {
      await languageToggle.click();

      // Navigate to another page
      await page.click('text=Corporate');

      // Check if language preference is maintained
      // This would depend on how language state is managed
      await expect(page.locator('h1')).toBeVisible();
    }
  });

  test('should handle form validation properly', async ({ page }) => {
    await page.goto('/contact');

    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Check for validation messages
    await expect(page.locator('text=required')).toBeVisible();

    // Fill invalid email
    await page.fill('input[name="email"]', 'invalid-email');
    await page.click('button[type="submit"]');

    // Check for email validation message
    await expect(page.locator('text=Invalid email')).toBeVisible();
  });
});
