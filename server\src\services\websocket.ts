// WebSocket service for real-time communication
import { v4 as uuidv4 } from 'uuid';

interface WebSocketMessage {
  type: string;
  data: any;
  sessionId?: string;
  agentId?: number;
  userId?: string;
}

interface WebSocketConnection {
  id: string;
  type: 'user' | 'agent' | 'admin';
  sessionId?: string;
  agentId?: number;
  userId?: string;
  lastSeen: Date;
  ws?: any; // WebSocket instance
}

class WebSocketService {
  private connections = new Map<string, WebSocketConnection>();
  private sessionConnections = new Map<string, string[]>(); // sessionId -> connectionIds
  private agentConnections = new Map<number, string[]>(); // agentId -> connectionIds
  private wsConnections = new Map<string, any>(); // connectionId -> WebSocket instance

  // Generate a unique connection ID
  generateConnectionId(): string {
    return uuidv4();
  }

  // Add WebSocket connection
  addWebSocketConnection(connectionId: string, ws: any) {
    this.wsConnections.set(connectionId, ws);
  }

  // Find connection ID by WebSocket instance
  findConnectionIdByWebSocket(ws: any): string | null {
    for (const [connectionId, wsInstance] of Array.from(
      this.wsConnections.entries(),
    )) {
      if (wsInstance === ws) {
        return connectionId;
      }
    }
    return null;
  }

  // Register a new connection
  registerConnection(
    connectionId: string,
    type: 'user' | 'agent' | 'admin',
    metadata: {
      sessionId?: string;
      agentId?: number;
      userId?: string;
    } = {},
  ) {
    const connection: WebSocketConnection = {
      id: connectionId,
      type,
      sessionId: metadata.sessionId,
      agentId: metadata.agentId,
      userId: metadata.userId,
      lastSeen: new Date(),
      ws: this.wsConnections.get(connectionId),
    };

    this.connections.set(connectionId, connection);

    // Index by session
    if (metadata.sessionId) {
      const sessionConns =
        this.sessionConnections.get(metadata.sessionId) || [];
      sessionConns.push(connectionId);
      this.sessionConnections.set(metadata.sessionId, sessionConns);
    }

    // Index by agent
    if (metadata.agentId) {
      const agentConns = this.agentConnections.get(metadata.agentId) || [];
      agentConns.push(connectionId);
      this.agentConnections.set(metadata.agentId, agentConns);
    }

    console.log(`WebSocket connection registered: ${connectionId} (${type})`);
  }

  // Update last seen timestamp
  updateLastSeen(connectionId: string) {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.lastSeen = new Date();
    }
  }

  // Remove a connection
  removeConnection(connectionId: string) {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      return;
    }

    // Remove from session index
    if (connection.sessionId) {
      const sessionConns =
        this.sessionConnections.get(connection.sessionId) || [];
      const filtered = sessionConns.filter((id) => id !== connectionId);
      if (filtered.length > 0) {
        this.sessionConnections.set(connection.sessionId, filtered);
      } else {
        this.sessionConnections.delete(connection.sessionId);
      }
    }

    // Remove from agent index
    if (connection.agentId) {
      const agentConns = this.agentConnections.get(connection.agentId) || [];
      const filtered = agentConns.filter((id) => id !== connectionId);
      if (filtered.length > 0) {
        this.agentConnections.set(connection.agentId, filtered);
      } else {
        this.agentConnections.delete(connection.agentId);
      }
    }

    // Remove WebSocket connection
    this.wsConnections.delete(connectionId);
    this.connections.delete(connectionId);
    console.log(`WebSocket connection removed: ${connectionId}`);
  }

  // Send message to specific connection
  sendToConnection(connectionId: string, message: WebSocketMessage): boolean {
    const connection = this.connections.get(connectionId);
    const ws = this.wsConnections.get(connectionId);

    if (!connection || !ws) {
      console.warn(`Connection not found: ${connectionId}`);
      return false;
    }

    try {
      ws.send(JSON.stringify(message));
      console.log(`Message sent to ${connectionId}:`, message.type);
      return true;
    } catch (error) {
      console.error(`Failed to send message to ${connectionId}:`, error);
      return false;
    }
  }

  // Send message to all connections in a session
  sendToSession(
    sessionId: string,
    message: WebSocketMessage,
    excludeConnectionId?: string,
  ) {
    const connectionIds = this.sessionConnections.get(sessionId) || [];
    let sent = 0;

    for (const connectionId of connectionIds) {
      if (excludeConnectionId && connectionId === excludeConnectionId) {
        continue;
      }
      if (this.sendToConnection(connectionId, message)) {
        sent++;
      }
    }

    console.log(`Sent message to ${sent} connections in session ${sessionId}`);
    return sent;
  }

  // Send message to all agent connections
  sendToAgents(message: WebSocketMessage, excludeAgentId?: number) {
    let sent = 0;

    for (const [agentId, connectionIds] of Array.from(
      this.agentConnections.entries(),
    )) {
      if (excludeAgentId && agentId === excludeAgentId) {
        continue;
      }

      for (const connectionId of connectionIds) {
        if (this.sendToConnection(connectionId, message)) {
          sent++;
        }
      }
    }

    console.log(`Sent message to ${sent} agent connections`);
    return sent;
  }

  // Send message to specific agent
  sendToAgent(agentId: number, message: WebSocketMessage) {
    const connectionIds = this.agentConnections.get(agentId) || [];
    let sent = 0;

    for (const connectionId of connectionIds) {
      if (this.sendToConnection(connectionId, message)) {
        sent++;
      }
    }

    console.log(`Sent message to ${sent} connections for agent ${agentId}`);
    return sent;
  }

  // Broadcast to all connections of a specific type
  broadcastToType(type: 'user' | 'agent' | 'admin', message: WebSocketMessage) {
    let sent = 0;

    for (const connection of Array.from(this.connections.values())) {
      if (connection.type === type) {
        if (this.sendToConnection(connection.id, message)) {
          sent++;
        }
      }
    }

    console.log(`Broadcast message to ${sent} ${type} connections`);
    return sent;
  }

  // Get connection statistics
  getStats() {
    const stats = {
      totalConnections: this.connections.size,
      userConnections: 0,
      agentConnections: 0,
      adminConnections: 0,
      activeSessions: this.sessionConnections.size,
      activeAgents: this.agentConnections.size,
    };

    for (const connection of Array.from(this.connections.values())) {
      switch (connection.type) {
        case 'user':
          stats.userConnections++;
          break;
        case 'agent':
          stats.agentConnections++;
          break;
        case 'admin':
          stats.adminConnections++;
          break;
      }
    }

    return stats;
  }

  // Clean up stale connections (call periodically)
  cleanupStaleConnections(maxAgeMinutes = 30) {
    const cutoff = new Date(Date.now() - maxAgeMinutes * 60 * 1000);
    const staleConnections: string[] = [];

    for (const [connectionId, connection] of Array.from(
      this.connections.entries(),
    )) {
      if (connection.lastSeen < cutoff) {
        staleConnections.push(connectionId);
      }
    }

    for (const connectionId of staleConnections) {
      this.removeConnection(connectionId);
    }

    if (staleConnections.length > 0) {
      console.log(`Cleaned up ${staleConnections.length} stale connections`);
    }

    return staleConnections.length;
  }
}

// Create singleton instance
export const websocketService = new WebSocketService();

// Message types for real-time communication
export const MESSAGE_TYPES = {
  // Session events
  SESSION_ASSIGNED: 'session_assigned',
  SESSION_COMPLETED: 'session_completed',
  SESSION_TRANSFERRED: 'session_transferred',

  // Message events
  NEW_MESSAGE: 'new_message',
  AGENT_MESSAGE: 'agent_message',
  USER_MESSAGE: 'user_message',

  // Handover events
  HANDOVER_REQUESTED: 'handover_requested',
  HANDOVER_ASSIGNED: 'handover_assigned',
  HANDOVER_COMPLETED: 'handover_completed',

  // Agent events
  AGENT_ONLINE: 'agent_online',
  AGENT_OFFLINE: 'agent_offline',
  AGENT_TYPING: 'agent_typing',

  // System events
  CONNECTION_ESTABLISHED: 'connection_established',
  PING: 'ping',
  PONG: 'pong',
} as const;

export default websocketService;
