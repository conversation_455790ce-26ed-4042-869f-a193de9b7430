# Malaysian Halal Companies Directory Crawler

A robust Bun.js web scraper for crawling the Malaysian halal companies directory using Puppeteer and saving data to PostgreSQL with Drizzle ORM.

## Features

- 🚀 **High Performance**: Built with Bun.js for fast execution
- 🕷️ **Web Scraping**: Puppeteer-based scraping with pagination support
- 🗄️ **Database Integration**: PostgreSQL with Drizzle ORM
- 🔍 **Duplicate Detection**: Advanced duplicate checking with data hashing
- ✅ **Data Validation**: Comprehensive data validation and quality checks
- 🔄 **Retry Logic**: Robust error handling with exponential backoff
- 📊 **Progress Tracking**: Real-time progress bars and statistics
- 🎯 **CLI Interface**: User-friendly command-line interface
- 📈 **Quality Reports**: Data quality assessment and reporting

## Prerequisites

- [Bun.js](https://bun.sh/) (latest version)
- PostgreSQL database
- Node.js (for compatibility with some packages)

## Installation

1. Clone or navigate to the project directory:
```bash
cd crawl-org
```

2. Install dependencies:
```bash
bun install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
```env
DATABASE_URL=postgresql://root:000000@localhost:5432/halal
MAX_PAGES=10
SITE_ID=1
DEBUG=false
HEADLESS=true
```

4. Generate database migrations:
```bash
bun run db:generate
```

5. Apply migrations:
```bash
bun run db:migrate
```

## Usage

### Basic Crawling

Start crawling with default settings:
```bash
bun run crawl
```

### Advanced Options

Crawl with custom parameters:
```bash
bun run crawl --pages 20 --concurrent 5 --delay 2000 --debug
```

Available options:
- `--pages <number>`: Maximum pages to crawl (default: 10)
- `--concurrent <number>`: Concurrent pages (default: 3)
- `--delay <number>`: Delay between requests in ms (default: 1000)
- `--retries <number>`: Retry attempts (default: 3)
- `--timeout <number>`: Timeout in ms (default: 50000)
- `--headless`: Run browser in headless mode (default: true)
- `--debug`: Enable debug logging
- `--site-id <number>`: Site ID for database (default: 1)

### Database Statistics

View crawling statistics:
```bash
bun run stats
```

### Search Companies

Search for companies by name:
```bash
bun run search "ABC Company"
```

### System Test

Test database connection and configuration:
```bash
bun run test
```

## CLI Commands

The crawler provides several CLI commands:

### `crawl`
Main crawling command with extensive options for customization.

### `stats`
Display database statistics including:
- Total companies
- Companies by state
- Companies by category
- Recently added companies

### `search <term>`
Search companies by name with options for:
- Site ID filtering
- Result limit

### `test`
System health check including:
- Database connection test
- Environment variable validation

## Database Schema

The crawler uses a `companies` table with the following structure:

```sql
CREATE TABLE companies (
  id SERIAL PRIMARY KEY,
  site_id INTEGER NOT NULL,
  company_name VARCHAR(500) NOT NULL,
  registration_number VARCHAR(255),
  business_type VARCHAR(255),
  category VARCHAR(255),
  subcategory VARCHAR(255),
  address TEXT,
  state VARCHAR(255),
  postcode VARCHAR(20),
  city VARCHAR(255),
  country VARCHAR(255) DEFAULT 'Malaysia',
  phone VARCHAR(50),
  fax VARCHAR(50),
  email VARCHAR(255),
  website VARCHAR(500),
  contact_person VARCHAR(255),
  certificate_number VARCHAR(255),
  certificate_type VARCHAR(255),
  certificate_status VARCHAR(100),
  issued_date VARCHAR(255),
  expiry_date VARCHAR(255),
  source_url VARCHAR(1000),
  page_number INTEGER,
  raw_data TEXT,
  data_hash VARCHAR(64) UNIQUE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## Data Quality Features

### Validation
- Company name requirements (minimum length, maximum length)
- Email format validation
- Phone number format validation (Malaysian patterns)
- URL format validation
- Malaysian postcode validation

### Duplicate Detection
- Data hashing for exact duplicate detection
- Similarity matching for near-duplicates
- Phone number and email matching

### Quality Reporting
- Completeness scoring
- Validation error tracking
- Data quality metrics

## Error Handling

The crawler implements comprehensive error handling:

### Retry Logic
- Exponential backoff for network errors
- Different retry strategies for different error types
- Circuit breaker pattern for persistent failures

### Error Categories
- Network errors (connection issues, timeouts)
- Browser errors (navigation, protocol errors)
- Database errors (connection, constraint violations)
- Validation errors (data format issues)

### Recovery Features
- Automatic retry with backoff
- Graceful degradation
- Error statistics tracking
- Abort conditions for excessive errors

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | Required |
| `MAX_PAGES` | Maximum pages to crawl | 10 |
| `CONCURRENT_PAGES` | Concurrent page processing | 3 |
| `DELAY_BETWEEN_REQUESTS` | Delay between requests (ms) | 1000 |
| `RETRY_ATTEMPTS` | Number of retry attempts | 3 |
| `TIMEOUT_MS` | Request timeout (ms) | 
| `SITE_ID` | Site ID for database | 1 |
| `DEBUG` | Enable debug logging | false |
| `HEADLESS` | Run browser in headless mode | true |

## Development

### Project Structure
```
crawl-org/
├── src/
│   ├── cli/           # CLI commands and interface
│   ├── crawler/       # Main crawler logic
│   ├── database/      # Database service and operations
│   ├── db/           # Database schema and connection
│   ├── scraper/      # Web scraping logic
│   ├── types/        # TypeScript type definitions
│   └── utils/        # Utilities (logging, validation, retry)
├── drizzle/          # Database migrations
├── package.json
├── tsconfig.json
└── README.md
```

### Running in Development

```bash
# Watch mode
bun run dev

# Build
bun run build

# Database operations
bun run db:generate  # Generate migrations
bun run db:migrate   # Apply migrations
bun run db:studio    # Open Drizzle Studio
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check DATABASE_URL in .env
   - Ensure PostgreSQL is running
   - Verify credentials and database exists

2. **Browser Launch Failed**
   - Install required system dependencies
   - Check if running in headless mode
   - Verify Puppeteer installation

3. **No Data Extracted**
   - Check target website structure
   - Enable debug mode for detailed logs
   - Verify selectors in extractor.ts

4. **High Error Rate**
   - Reduce concurrent pages
   - Increase delays between requests
   - Check network connectivity

### Debug Mode

Enable debug logging for detailed information:
```bash
bun run crawl --debug
```

This will show:
- Detailed navigation logs
- Data extraction details
- Error stack traces
- Performance metrics

## License

MIT License - see LICENSE file for details.
