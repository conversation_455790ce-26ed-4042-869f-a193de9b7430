'use client';

import { Save, X } from 'lucide-react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  type UserCreateRequest,
  UserRole,
  type UserUpdateRequest,
} from '@/types';

interface UserFormProps {
  initialData?: Partial<UserCreateRequest | UserUpdateRequest>;
  onSubmit: (data: UserCreateRequest | UserUpdateRequest) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  error?: string | null;
  clearError?: () => void;
  mode: 'create' | 'edit';
}

export function UserForm({
  initialData = {},
  onSubmit,
  onCancel,
  isLoading = false,
  error = null,
  clearError,
  mode,
}: UserFormProps) {
  const [formData, setFormData] = useState({
    username: initialData.username || '',
    email: initialData.email || '',
    password: initialData.password || '',
    firstName: initialData.firstName || '',
    lastName: initialData.lastName || '',
    roles: initialData.roles || [UserRole.EDITOR],
    isActive: initialData.isActive ?? true,
  });

  const [selectedRoles, setSelectedRoles] = useState<UserRole[]>(
    initialData.roles || [UserRole.EDITOR],
  );

  const handleInputChange =
    (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
      if (field === 'isActive') {
        setFormData((prev) => ({
          ...prev,
          [field]: e.target.checked,
        }));
      } else {
        setFormData((prev) => ({
          ...prev,
          [field]: e.target.value,
        }));
      }
      // Clear error when user starts typing
      if (error && clearError) {
        clearError();
      }
    };

  const handleRoleToggle = (role: UserRole) => {
    const newRoles = selectedRoles.includes(role)
      ? selectedRoles.filter((r) => r !== role)
      : [...selectedRoles, role];

    setSelectedRoles(newRoles);
    setFormData((prev) => ({
      ...prev,
      roles: newRoles,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.username) {
      return;
    }

    if (mode === 'create' && !formData.password) {
      return;
    }

    if (formData.roles.length === 0) {
      return;
    }

    // For edit mode, only include password if it's been changed
    let submitData = { ...formData };
    if (mode === 'edit' && !submitData.password) {
      const { password: _, ...dataWithoutPassword } = submitData;
      submitData = dataWithoutPassword as typeof submitData;
    }

    await onSubmit(submitData);
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.SUPERADMIN:
        return 'bg-purple-100 text-purple-800 border-purple-300';
      case UserRole.ADMIN:
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case UserRole.EDITOR:
        return 'bg-green-100 text-green-800 border-green-300';
      case UserRole.AGENT:
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case UserRole.SUPERVISOR:
        return 'bg-orange-100 text-orange-800 border-orange-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  return (
    <>
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            {clearError && (
              <Button variant="ghost" size="sm" onClick={clearError}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
            <CardDescription>
              {mode === 'create'
                ? 'Enter the details for the new user'
                : 'Update the user details and roles'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="username">Username *</Label>
                <Input
                  id="username"
                  type="text"
                  value={formData.username}
                  onChange={handleInputChange('username')}
                  placeholder="Enter username"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange('email')}
                  placeholder="Enter email address"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  type="text"
                  value={formData.firstName}
                  onChange={handleInputChange('firstName')}
                  placeholder="Enter first name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  type="text"
                  value={formData.lastName}
                  onChange={handleInputChange('lastName')}
                  placeholder="Enter last name"
                />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="password">
                  Password{' '}
                  {mode === 'create' ? '*' : '(leave blank to keep current)'}
                </Label>
                <Input
                  id="password"
                  type="password"
                  value={formData.password}
                  onChange={handleInputChange('password')}
                  placeholder={
                    mode === 'create' ? 'Enter password' : 'Enter new password'
                  }
                  required={mode === 'create'}
                />
              </div>
            </div>

            <div className="space-y-3">
              <Label>User Roles *</Label>
              <div className="flex flex-wrap gap-2">
                {Object.values(UserRole).map((role) => (
                  <button
                    key={role}
                    type="button"
                    onClick={() => handleRoleToggle(role)}
                    className={`px-3 py-2 rounded-md text-sm font-medium border transition-colors ${
                      selectedRoles.includes(role)
                        ? getRoleColor(role)
                        : 'bg-gray-50 text-gray-700 border-gray-300 hover:bg-gray-100'
                    }`}
                  >
                    {role}
                  </button>
                ))}
              </div>
              {selectedRoles.length === 0 && (
                <p className="text-sm text-red-600">
                  At least one role must be selected
                </p>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <input
                id="isActive"
                type="checkbox"
                checked={formData.isActive}
                onChange={handleInputChange('isActive')}
                className="rounded border-gray-300"
              />
              <Label htmlFor="isActive">Active User</Label>
            </div>

            <div className="flex items-center justify-end space-x-4 pt-6">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={
                  isLoading ||
                  !formData.username ||
                  (mode === 'create' && !formData.password) ||
                  selectedRoles.length === 0
                }
              >
                <Save className="mr-2 h-4 w-4" />
                {isLoading
                  ? mode === 'create'
                    ? 'Creating...'
                    : 'Updating...'
                  : mode === 'create'
                    ? 'Create User'
                    : 'Update User'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </form>
    </>
  );
}
