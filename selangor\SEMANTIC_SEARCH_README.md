# Semantic Search Implementation

This document describes the semantic search functionality implemented for the Halal Selangor product search system.

## Overview

The semantic search feature allows users to search for products using natural language queries that understand meaning and context, rather than just exact keyword matches. The implementation uses vector embeddings to perform similarity searches directly on the products database, finding products based on semantic similarity rather than exact text matches.

## Architecture

### Search Types

1. **Keyword Search** (Traditional)
   - Exact word matching in product names, company names, categories, etc.
   - Uses SQL `ILIKE` operations for pattern matching
   - Fast and precise for known terms

2. **Semantic Search** (AI-Powered)
   - Uses vector embeddings for semantic understanding
   - Converts queries to embeddings and finds similar products
   - Performs cosine similarity search on product embeddings
   - Better for natural language queries and concept-based searches

### Components

#### 1. Database Schema Updates

**New columns added to `products` table:**
- `r2r_document_id` - Links products to R2R documents
- `vectorized_at` - Timestamp of last vectorization
- `vectorization_model` - Model used for vectorization
- `searchable_text` - Preprocessed text for search optimization

#### 2. API Endpoints

**Semantic Search API:** `/api/products/semantic-search`
- Accepts natural language queries
- Uses vector embeddings for semantic understanding
- Returns products with similarity scores
- Supports pagination and filtering
- Enhanced with multilingual translation support

**Enhanced Search Interface:**
- Search type selector (Keyword vs Semantic)
- Real-time search type switching
- Visual indicators for search mode

#### 3. Frontend Components

**SearchTypeSelector Component:**
- Toggle between keyword and semantic search
- Tooltips explaining each search type
- Visual feedback for active search mode

**Enhanced ProductResults Component:**
- Displays similarity scores for semantic search
- Different styling for semantic vs keyword results
- Search type indicators

**Updated Search Page:**
- Integrated search type selection
- Coordinated search across different modes
- Maintains search state across navigation

## Usage

### For Users

1. **Keyword Search** - Use when you know specific product names, company names, or exact terms
   ```
   Example: "Ayam Brand" or "Halal Chicken"
   ```

2. **Semantic Search** - Use for natural language queries or concept-based searches
   ```
   Examples: 
   - "healthy snacks for children"
   - "organic food products"
   - "traditional Malaysian cuisine ingredients"
   ```

### For Developers

#### Running Semantic Search

```bash
# Install dependencies
pnpm install

# Run the development server
pnpm dev

# The semantic search will be available at:
# http://localhost:16010/search
```

#### API Usage

```typescript
// Semantic search API call
const response = await fetch('/api/products/semantic-search', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
  },
  params: new URLSearchParams({
    q: 'healthy organic snacks',
    page: '1',
    limit: '10',
    minScore: '0.3'
  })
});

const data: SemanticProductSearchResponse = await response.json();
```

#### Component Usage

```tsx
import { EnhancedSearchBox } from '@/components/SearchTypeSelector';

function MySearchPage() {
  const [searchType, setSearchType] = useState<SearchType>('keyword');
  
  return (
    <EnhancedSearchBox
      searchType={searchType}
      onSearchTypeChange={setSearchType}
      onSearch={(query, type) => {
        // Handle search with type
        performSearch(query, type);
      }}
      showSearchTypeSelector={true}
    />
  );
}
```

## Configuration

### Environment Variables

```env
# R2R Configuration (required for semantic search)
R2R_URL=http://localhost:7272
R2R_COLLECTION_ID=your-collection-id
R2R_API_KEY=your-api-key

# OpenAI Configuration (for embeddings)
OPENAI_API_KEY=your-openai-api-key
```

### Database Migration

The semantic search features require database schema updates:

```bash
# Generate and run migrations
cd server
pnpm drizzle-kit generate
pnpm drizzle-kit migrate
```

## Performance Considerations

### Semantic Search Performance

- **R2R Integration**: Leverages existing R2R infrastructure for semantic understanding
- **Keyword Extraction**: Converts semantic results to database-friendly queries
- **Caching**: Results can be cached at the R2R level
- **Pagination**: Supports efficient pagination for large result sets

### Optimization Tips

1. **Minimum Score Filtering**: Use appropriate `minScore` values (0.3-0.7) to filter irrelevant results
2. **Limit R2R Results**: Keep R2R result limits reasonable (20-50) to balance quality and performance
3. **Database Indexing**: Ensure proper indexes on searchable text columns
4. **Caching**: Implement caching for frequently searched terms

## Troubleshooting

### Common Issues

1. **R2R Service Unavailable**
   - Check R2R_URL configuration
   - Verify R2R service is running
   - Check network connectivity

2. **No Semantic Results**
   - Verify R2R_COLLECTION_ID is correct
   - Check if documents are indexed in R2R
   - Adjust minScore threshold

3. **Slow Performance**
   - Reduce R2R result limit
   - Optimize database queries
   - Consider caching strategies

### Debug Mode

Enable debug logging by setting:
```env
DEBUG=semantic-search
```

## Future Enhancements

### Planned Features

1. **Direct Vector Storage**: Implement pgvector for local vector operations
2. **Hybrid Search**: Combine keyword and semantic results intelligently
3. **Search Analytics**: Track search patterns and improve relevance
4. **Auto-completion**: Semantic-aware search suggestions
5. **Personalization**: User-specific search result ranking

### Integration Opportunities

1. **Product Recommendations**: Use semantic understanding for related products
2. **Category Discovery**: Automatic product categorization
3. **Content Enhancement**: Improve product descriptions using semantic analysis
4. **Multi-language Support**: Semantic search across different languages

## API Reference

### SemanticProductSearchResponse

```typescript
interface SemanticProductSearchResponse {
  query: string;
  searchType: 'semantic';
  products: ProductWithSimilarity[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
    totalPages: number;
  };
}

interface ProductWithSimilarity extends Product {
  similarityScore?: number; // 0.0 to 1.0
}
```

### Search Parameters

- `q` (required): Search query string
- `page` (optional): Page number (default: 1)
- `limit` (optional): Results per page (default: 10)
- `minScore` (optional): Minimum similarity score (default: 0.3)

## Contributing

When contributing to semantic search functionality:

1. Test both search modes thoroughly
2. Ensure backward compatibility with keyword search
3. Update documentation for new features
4. Consider performance implications
5. Add appropriate error handling

## License

This semantic search implementation is part of the Halal Selangor project and follows the same licensing terms.
