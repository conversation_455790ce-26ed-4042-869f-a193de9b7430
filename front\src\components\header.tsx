'use client';

import { Menu } from 'lucide-react';
import Image from 'next/image';
import { mainNavigation } from '@/data/navigation';
import { Link } from '@/i18n/navigation';
import { useLanguage } from '@/lib/language-context';
import { LanguageSwitcher } from './language-switcher';
import { MobileMenu, useMobileMenu } from './mobile-menu';

export function Header() {
  const { language, t } = useLanguage();
  const mobileMenu = useMobileMenu();

  return (
    <>
      <header className="bg-white shadow-sm border-b sticky top-0 z-30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {/* Top bar with language switcher */}
          <div className="flex justify-end py-2 border-b border-gray-100">
            <LanguageSwitcher className="hidden md:flex" />
          </div>

          {/* Main header */}
          <div className="flex items-center justify-between py-3 md:py-4">
            {/* Logo */}
            <Link
              href="/"
              className="flex items-center gap-2 md:gap-4 flex-shrink-0"
            >
              <Image
                src="/images/logos/jakim_logo.png"
                alt="JAKIM Logo"
                width={60}
                height={60}
                className="w-10 h-10 sm:w-12 sm:h-12 md:w-15 md:h-15"
                priority
              />
              <div className="hidden sm:block">
                <h1 className="text-lg sm:text-xl md:text-xl font-bold text-primary-green leading-tight">
                  <span className="block sm:hidden">
                    {t('site-title-short', 'Halal Portal', 'Portal Halal')}
                  </span>
                  <span className="hidden sm:block">
                    {t(
                      'site-title',
                      'Halal Malaysia Portal',
                      'Portal Halal Malaysia',
                    )}
                  </span>
                </h1>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-6 xl:space-x-8">
              {mainNavigation.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="nav-link text-sm xl:text-base whitespace-nowrap"
                >
                  {language === 'bm' ? item.labelBM : item.label}
                </Link>
              ))}
              <Link
                href="/admin"
                className="nav-link text-sm xl:text-base whitespace-nowrap"
              >
                Admin
              </Link>
            </nav>

            {/* Mobile/Tablet menu button */}
            <button
              type="button"
              onClick={mobileMenu.toggle}
              className="lg:hidden p-2 text-gray-600 hover:text-primary-green transition-colors rounded-md hover:bg-gray-50"
              aria-label={language === 'en' ? 'Open menu' : 'Buka menu'}
              data-testid="mobile-menu-button"
            >
              <Menu className="w-6 h-6" />
            </button>
          </div>
        </div>
      </header>

      {/* Mobile Menu */}
      <MobileMenu isOpen={mobileMenu.isOpen} onClose={mobileMenu.close} />
    </>
  );
}
