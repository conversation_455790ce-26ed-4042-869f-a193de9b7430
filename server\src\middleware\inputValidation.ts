import type { NextFunction, Request, Response } from 'express';
import validator from 'validator';
import { logSecurityEvent } from './security';

// Input sanitization utilities
export const sanitizeString = (input: string, maxLength = 1000): string => {
  if (!input || typeof input !== 'string') {
    throw new Error('Input must be a string');
  }

  // Trim whitespace
  let sanitized = input.trim();

  // Remove null bytes and control characters except newlines and tabs
  sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

  // Check length
  if (sanitized.length > maxLength) {
    throw new Error(`Input too long (max ${maxLength} characters)`);
  }

  return sanitized;
};

export const sanitizeEmail = (email: string): string => {
  const sanitized = sanitizeString(email, 254); // RFC 5321 limit

  if (!validator.isEmail(sanitized)) {
    throw new Error('Invalid email format');
  }

  return sanitized.toLowerCase();
};

export const sanitizeUsername = (username: string): string => {
  const sanitized = sanitizeString(username, 50);

  // Username should only contain alphanumeric characters, underscores, and hyphens
  if (!/^[a-zA-Z0-9_-]+$/.test(sanitized)) {
    throw new Error(
      'Username can only contain letters, numbers, underscores, and hyphens',
    );
  }

  if (sanitized.length < 3) {
    throw new Error('Username must be at least 3 characters long');
  }

  return sanitized;
};

export const sanitizePassword = (password: string): string => {
  if (!password || typeof password !== 'string') {
    throw new Error('Password is required');
  }

  // Don't trim passwords as spaces might be intentional
  if (password.length < 8) {
    throw new Error('Password must be at least 8 characters long');
  }

  if (password.length > 128) {
    throw new Error('Password too long (max 128 characters)');
  }

  return password;
};

export const sanitizePhoneNumber = (phone: string): string => {
  const sanitized = sanitizeString(phone, 20);

  // Remove all non-digit characters except + at the beginning
  const cleaned = sanitized.replace(/[^\d+]/g, '');

  if (!validator.isMobilePhone(cleaned, 'any', { strictMode: false })) {
    throw new Error('Invalid phone number format');
  }

  return cleaned;
};

// Validation middleware factories
export const validateBody = (schema: Record<string, any>) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const errors: string[] = [];

      for (const [field, rules] of Object.entries(schema)) {
        const value = req.body[field];

        // Check required fields
        if (
          rules.required &&
          (!value || (typeof value === 'string' && value.trim() === ''))
        ) {
          errors.push(`${field} is required`);
          continue;
        }

        // Skip validation if field is not required and not provided
        if (
          !rules.required &&
          (!value || (typeof value === 'string' && value.trim() === ''))
        ) {
          continue;
        }

        try {
          // Type validation
          if (rules.type === 'string' && typeof value !== 'string') {
            errors.push(`${field} must be a string`);
            continue;
          }

          if (rules.type === 'number' && typeof value !== 'number') {
            errors.push(`${field} must be a number`);
            continue;
          }

          if (rules.type === 'boolean' && typeof value !== 'boolean') {
            errors.push(`${field} must be a boolean`);
            continue;
          }

          // String validations
          if (rules.type === 'string') {
            if (rules.minLength && value.length < rules.minLength) {
              errors.push(
                `${field} must be at least ${rules.minLength} characters long`,
              );
            }

            if (rules.maxLength && value.length > rules.maxLength) {
              errors.push(
                `${field} must be no more than ${rules.maxLength} characters long`,
              );
            }

            if (rules.pattern && !rules.pattern.test(value)) {
              errors.push(`${field} format is invalid`);
            }

            if (rules.enum && !rules.enum.includes(value)) {
              errors.push(`${field} must be one of: ${rules.enum.join(', ')}`);
            }

            // Apply sanitization
            if (rules.sanitize) {
              switch (rules.sanitize) {
                case 'string':
                  req.body[field] = sanitizeString(
                    value,
                    rules.maxLength || 1000,
                  );
                  break;
                case 'email':
                  req.body[field] = sanitizeEmail(value);
                  break;
                case 'username':
                  req.body[field] = sanitizeUsername(value);
                  break;
                case 'phone':
                  req.body[field] = sanitizePhoneNumber(value);
                  break;
              }
            }
          }

          // Number validations
          if (rules.type === 'number') {
            if (rules.min !== undefined && value < rules.min) {
              errors.push(`${field} must be at least ${rules.min}`);
            }

            if (rules.max !== undefined && value > rules.max) {
              errors.push(`${field} must be no more than ${rules.max}`);
            }
          }
        } catch (sanitizeError) {
          errors.push(
            `${field}: ${sanitizeError instanceof Error ? sanitizeError.message : 'Invalid format'}`,
          );
        }
      }

      if (errors.length > 0) {
        logSecurityEvent({
          event: 'INPUT_VALIDATION_FAILED',
          level: 'warn',
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          userId: req.user?.userId?.toString(),
          endpoint: req.path,
          method: req.method,
          details: {
            validationErrors: errors,
            fieldCount: Object.keys(schema).length,
            providedFields: Object.keys(req.body),
          },
        });

        res.status(400).json({
          error: 'Validation failed',
          message: 'Invalid input data',
          details: errors,
        });
        return;
      }

      next();
    } catch (error) {
      logSecurityEvent({
        event: 'INPUT_VALIDATION_ERROR',
        level: 'error',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        userId: req.user?.userId?.toString(),
        endpoint: req.path,
        method: req.method,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });

      res.status(500).json({
        error: 'Validation error',
        message: 'An error occurred while validating input',
      });
    }
  };
};

// Common validation schemas
export const loginSchema = {
  username: {
    required: true,
    type: 'string',
    minLength: 3,
    maxLength: 50,
    sanitize: 'username',
  },
  password: {
    required: true,
    type: 'string',
    minLength: 8,
    maxLength: 128,
  },
};

export const registerSchema = {
  username: {
    required: true,
    type: 'string',
    minLength: 3,
    maxLength: 50,
    sanitize: 'username',
  },
  email: {
    required: true,
    type: 'string',
    maxLength: 254,
    sanitize: 'email',
  },
  password: {
    required: true,
    type: 'string',
    minLength: 8,
    maxLength: 128,
  },
  firstName: {
    required: false,
    type: 'string',
    maxLength: 50,
    sanitize: 'string',
  },
  lastName: {
    required: false,
    type: 'string',
    maxLength: 50,
    sanitize: 'string',
  },
};

export const chatMessageSchema = {
  sessionId: {
    required: true,
    type: 'string',
    pattern:
      /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  },
  message: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 4000,
    sanitize: 'string',
  },
  model: {
    required: false,
    type: 'string',
    enum: ['gpt-4o-mini', 'gpt-4o', 'gpt-3.5-turbo'],
  },
};

export const imageAnalysisSchema = {
  sessionId: {
    required: true,
    type: 'string',
    pattern:
      /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  },
  imageUrl: {
    required: true,
    type: 'string',
    maxLength: 2048,
  },
  prompt: {
    required: false,
    type: 'string',
    maxLength: 1000,
    sanitize: 'string',
  },
  model: {
    required: false,
    type: 'string',
    enum: ['gpt-4o-mini', 'gpt-4o'],
  },
};
