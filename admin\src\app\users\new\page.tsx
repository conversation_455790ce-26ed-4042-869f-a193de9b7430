'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, X } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useUsersStore } from '@/stores/users';
import { type UserCreateRequest, UserRole } from '@/types';

export default function NewUserPage() {
  const router = useRouter();
  const { createUser, isLoading, error, clearError } = useUsersStore();

  const [formData, setFormData] = useState<UserCreateRequest>({
    username: '',
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    roles: [UserRole.EDITOR],
    isActive: true,
  });

  const [selectedRoles, setSelectedRoles] = useState<UserRole[]>([
    UserRole.EDITOR,
  ]);

  const handleInputChange =
    (field: keyof UserCreateRequest) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (field === 'isActive') {
        setFormData((prev) => ({
          ...prev,
          [field]: e.target.checked,
        }));
      } else {
        setFormData((prev) => ({
          ...prev,
          [field]: e.target.value,
        }));
      }
      // Clear error when user starts typing
      if (error) {
        clearError();
      }
    };

  const handleRoleToggle = (role: UserRole) => {
    const newRoles = selectedRoles.includes(role)
      ? selectedRoles.filter((r) => r !== role)
      : [...selectedRoles, role];

    setSelectedRoles(newRoles);
    setFormData((prev) => ({
      ...prev,
      roles: newRoles,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.username || !formData.password) {
      return;
    }

    if (formData.roles.length === 0) {
      return;
    }

    const success = await createUser(formData);

    if (success) {
      router.push('/users');
    }
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.SUPERADMIN:
        return 'bg-purple-100 text-purple-800 border-purple-300';
      case UserRole.ADMIN:
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case UserRole.EDITOR:
        return 'bg-green-100 text-green-800 border-green-300';
      case UserRole.AGENT:
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case UserRole.SUPERVISOR:
        return 'bg-orange-100 text-orange-800 border-orange-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link href="/users">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Users
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Create New User
            </h1>
            <p className="text-gray-600">Add a new user to the system</p>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <div className="flex items-center justify-between">
              <span>{error}</span>
              <Button variant="ghost" size="sm" onClick={clearError}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <Card>
            <CardHeader>
              <CardTitle>User Information</CardTitle>
              <CardDescription>
                Enter the details for the new user
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="username">Username *</Label>
                  <Input
                    id="username"
                    type="text"
                    value={formData.username}
                    onChange={handleInputChange('username')}
                    placeholder="Enter username"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange('email')}
                    placeholder="Enter email address"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    type="text"
                    value={formData.firstName}
                    onChange={handleInputChange('firstName')}
                    placeholder="Enter first name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    type="text"
                    value={formData.lastName}
                    onChange={handleInputChange('lastName')}
                    placeholder="Enter last name"
                  />
                </div>

                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="password">Password *</Label>
                  <Input
                    id="password"
                    type="password"
                    value={formData.password}
                    onChange={handleInputChange('password')}
                    placeholder="Enter password"
                    required
                  />
                </div>
              </div>

              <div className="space-y-3">
                <Label>User Roles *</Label>
                <div className="flex flex-wrap gap-2">
                  {Object.values(UserRole).map((role) => (
                    <button
                      key={role}
                      type="button"
                      onClick={() => handleRoleToggle(role)}
                      className={`px-3 py-2 rounded-md text-sm font-medium border transition-colors ${
                        selectedRoles.includes(role)
                          ? getRoleColor(role)
                          : 'bg-gray-50 text-gray-700 border-gray-300 hover:bg-gray-100'
                      }`}
                    >
                      {role}
                    </button>
                  ))}
                </div>
                {selectedRoles.length === 0 && (
                  <p className="text-sm text-red-600">
                    At least one role must be selected
                  </p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <input
                  id="isActive"
                  type="checkbox"
                  checked={formData.isActive}
                  onChange={handleInputChange('isActive')}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="isActive">Active User</Label>
              </div>

              <div className="flex items-center justify-end space-x-4 pt-6">
                <Link href="/users">
                  <Button type="button" variant="outline">
                    Cancel
                  </Button>
                </Link>
                <Button
                  type="submit"
                  disabled={
                    isLoading ||
                    !formData.username ||
                    !formData.password ||
                    selectedRoles.length === 0
                  }
                >
                  <Save className="mr-2 h-4 w-4" />
                  {isLoading ? 'Creating...' : 'Create User'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </form>
      </div>
    </DashboardLayout>
  );
}
