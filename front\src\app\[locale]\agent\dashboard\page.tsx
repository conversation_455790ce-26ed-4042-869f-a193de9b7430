'use client';

export const runtime = 'edge';

import axios from 'axios';
import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  LogOut,
  MessageSquare,
  Monitor,
  Phone,
  RefreshCw,
  Shield,
  Smartphone,
  User,
  Users,
  Wifi,
  WifiOff,
} from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { NotificationCenter } from '@/components/notifications/NotificationCenter';
import { useRouter } from '@/i18n/navigation';
import { useNotifications } from '@/lib/notifications';
import { useWebSocket } from '@/lib/websocket';
import { useAgentAuth } from '@/stores/auth';

interface DashboardStats {
  totalSessions: number;
  activeSessions: number;
  pendingHandovers: number;
  completedToday: number;
}

interface HandoverRequest {
  id: number;
  sessionId: string;
  requestedBy: string;
  reason?: string;
  priority: string;
  status: string;
  requestedAt: string;
}

interface Message {
  id: string;
  role: string;
  content: string;
  timestamp: string;
}

interface ActiveSession {
  id: string;
  userId?: string;
  platform: string;
  platformId?: string;
  status: string;
  isHandedOver: boolean;
  createdAt: string;
  lastMessageAt?: string;
  messages: Message[];
}

interface AllActiveSession {
  id: string;
  userId?: string;
  platform: string;
  platformId?: string;
  status: string;
  isHandedOver: boolean;
  createdAt: Date;
  lastMessageAt?: Date;
  messageCount: number;
}

export default function AgentDashboard() {
  const router = useRouter();
  const {
    user,
    token,
    logout,
    verify,
    isAuthenticated,
    isLoading: authIsLoading,
  } = useAgentAuth();
  const notifications = useNotifications();

  const [stats, setStats] = useState<DashboardStats>({
    totalSessions: 0,
    activeSessions: 0,
    pendingHandovers: 0,
    completedToday: 0,
  });
  const [handoverRequests, setHandoverRequests] = useState<HandoverRequest[]>(
    [],
  );
  const [activeSessions, setActiveSessions] = useState<ActiveSession[]>([]);
  const [allActiveSessions, setAllActiveSessions] = useState<
    AllActiveSession[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isOnline, setIsOnline] = useState(false);

  const handleTokenExpiration = useCallback(() => {
    logout();
    router.push('/agent');
  }, [logout, router]);

  const makeAuthenticatedRequest = useCallback(
    async (url: string, options: Record<string, unknown> = {}) => {
      if (!token) {
        handleTokenExpiration();
        throw new Error('No token available');
      }

      try {
        const response = await axios.get(url, {
          ...options,
          headers: {
            ...options.headers,
            Authorization: `Bearer ${token}`,
          },
        });
        return response;
      } catch (error) {
        if (axios.isAxiosError(error) && error.response?.status === 401) {
          handleTokenExpiration();
          throw new Error('Token expired');
        }
        throw error;
      }
    },
    [token, handleTokenExpiration],
  );

  const loadDashboardData = useCallback(async () => {
    try {
      // Load stats, handover requests, active sessions, and all active sessions in parallel
      const [
        statsResponse,
        handoversResponse,
        activeSessionsResponse,
        allActiveSessionsResponse,
      ] = await Promise.all([
        makeAuthenticatedRequest(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/sessions/stats`,
        ),
        makeAuthenticatedRequest(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/sessions/handovers/pending`,
        ),
        makeAuthenticatedRequest(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/sessions/active`,
        ),
        makeAuthenticatedRequest(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/sessions/all-active`,
        ),
      ]);

      setStats(statsResponse.data);
      setHandoverRequests(handoversResponse.data);
      setActiveSessions(activeSessionsResponse.data);
      setAllActiveSessions(allActiveSessionsResponse.data);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // Token expiration is already handled by makeAuthenticatedRequest
    } finally {
      setIsLoading(false);
    }
  }, [makeAuthenticatedRequest]);

  // Memoized WebSocket callbacks to prevent infinite reconnections
  const onConnect = useCallback(() => {
    console.log('Agent dashboard WebSocket connected');
    // Register as agent connection
    if (user) {
      // Use the utility method from websocket service
      // websocketService.registerAgent(user.id);
    }
  }, [user]);

  const onDisconnect = useCallback(() => {
    console.log('Agent dashboard WebSocket disconnected');
  }, []);

  const onAgentStatusUpdate = useCallback((data: any) => {
    console.log('Agent status update:', data);
    // Update agent status in real-time
    if (data.agentId === user?.id) {
      setIsOnline(data.isOnline);
    }
  }, [user?.id]);

  const onSessionUpdate = useCallback((data: any) => {
    console.log('Session update:', data);
    // Refresh dashboard data when sessions change
    loadDashboardData();

    // Show notification for new sessions
    if (data.status === 'active' && !data.isHandedOver) {
      notifications.showInfo(
        'New Session',
        `Session ${data.sessionId.slice(-8)} is now active`,
        `/agent/chat/${data.sessionId}`,
      );
    }
  }, [loadDashboardData, notifications]);

  const onHandoverRequest = useCallback((data: any) => {
    console.log('Handover request:', data);
    // Refresh handover requests
    loadDashboardData();

    // Show urgent notification for new handover requests
    notifications.showUrgent(
      'New Handover Request',
      `Session ${data.sessionId.slice(-8)} needs agent assistance`,
      `/agent/chat/${data.sessionId}`,
    );
  }, [loadDashboardData, notifications]);

  const onNotification = useCallback((data: any) => {
    console.log('Received notification:', data);
    // Add notification to the notification center
    notifications.addNotification({
      type: data.type,
      title: data.title,
      message: data.message,
      urgent: data.urgent,
      actionUrl: data.actionUrl,
    });
  }, [notifications]);

  // WebSocket connection for real-time updates
  const { isConnected: wsConnected } = useWebSocket(
    `${process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:16001'}/ws`,
    {
      onConnect,
      onDisconnect,
      onAgentStatusUpdate,
      onSessionUpdate,
      onHandoverRequest,
      onNotification,
    },
  );

  useEffect(() => {
    const authAndLoad = async () => {
      if (authIsLoading) {
        return;
      }
      // Only attempt to verify if not authenticated and a token exists (from persistence)
      if (!isAuthenticated && token) {
        const isValid = await verify();
        if (!isValid) {
          router.push('/agent');
          return;
        }
      } else if (!isAuthenticated && !token) {
        // If no token and not authenticated, redirect to login
        router.push('/agent');
        return;
      }

      if (user) {
        setIsOnline(user.isOnline ?? false);
      }
      await loadDashboardData();
    };

    authAndLoad();
  }, [
    isAuthenticated,
    user,
    token,
    verify,
    router,
    loadDashboardData,
    authIsLoading,
  ]);

  const handleLogout = async () => {
    await logout();
    router.push('/agent');
  };

  const toggleOnlineStatus = async () => {
    try {
      if (!token) {
        handleTokenExpiration();
        return;
      }

      const newStatus = !isOnline;
      await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/agent/status`,
        { isOnline: newStatus },
        { headers: { Authorization: `Bearer ${token}` } },
      );

      setIsOnline(newStatus);
    } catch (error) {
      console.error('Error updating status:', error);

      // Handle token expiration
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        handleTokenExpiration();
        return;
      }
    }
  };

  const handleTakeOver = async (requestId: number) => {
    try {
      if (!token) {
        handleTokenExpiration();
        return;
      }

      await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/sessions/handovers/${requestId}/assign`,
        {},
        { headers: { Authorization: `Bearer ${token}` } },
      );

      // Refresh handover requests
      loadDashboardData();
    } catch (error) {
      console.error('Error taking over session:', error);

      // Handle token expiration
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        handleTokenExpiration();
        return;
      }
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600 bg-red-100';
      case 'high':
        return 'text-orange-600 bg-orange-100';
      case 'normal':
        return 'text-blue-600 bg-blue-100';
      case 'low':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'whatsapp':
        return <Phone className="h-4 w-4" />;
      case 'facebook':
        return <Smartphone className="h-4 w-4" />;
      default:
        return <Monitor className="h-4 w-4" />;
    }
  };

  const handleTakeoverSession = async (sessionId: string) => {
    try {
      if (!token) {
        handleTokenExpiration();
        return;
      }

      await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/sessions/${sessionId}/takeover`,
        {},
        { headers: { Authorization: `Bearer ${token}` } },
      );

      // Reload dashboard data to reflect changes
      await loadDashboardData();
    } catch (error) {
      console.error('Error taking over session:', error);

      // Handle token expiration
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        handleTokenExpiration();
        return;
      }

      // You might want to show a toast notification here for other errors
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" />
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">
                Agent Dashboard
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              {/* WebSocket Status */}
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-1">
                  {wsConnected ? (
                    <Wifi className="h-4 w-4 text-green-500" />
                  ) : (
                    <WifiOff className="h-4 w-4 text-red-500" />
                  )}
                  <span className="text-xs text-gray-500">
                    {wsConnected ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
              </div>

              {/* Refresh Button */}
              <button
                type="button"
                onClick={loadDashboardData}
                disabled={isLoading}
                className="flex items-center px-2 py-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md disabled:opacity-50"
                title="Refresh data"
              >
                <RefreshCw
                  className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
                />
              </button>

              {/* Notification Center */}
              <NotificationCenter />

              {/* Agent Status */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">Status:</span>
                <button
                  type="button"
                  onClick={toggleOnlineStatus}
                  className={`flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    isOnline
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  <div
                    className={`w-2 h-2 rounded-full mr-2 ${
                      isOnline ? 'bg-green-500' : 'bg-gray-500'
                    }`}
                  />
                  {isOnline ? 'Online' : 'Offline'}
                </button>
              </div>

              {/* User Info */}
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">
                  {user?.firstName} {user?.lastName}
                </span>
                <span className="text-xs text-gray-400">({user?.role})</span>
              </div>

              {/* Logout */}
              <button
                type="button"
                onClick={handleLogout}
                className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
              >
                <LogOut className="h-4 w-4 mr-1" />
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <MessageSquare className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Active Sessions
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stats.activeSessions}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Pending Handovers
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stats.pendingHandovers}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Completed Today
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stats.completedToday}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Total Sessions
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stats.totalSessions}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Active Sessions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* My Active Sessions */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                My Active Sessions
              </h3>

              {activeSessions.length === 0 ? (
                <div className="text-center py-8">
                  <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    No active sessions
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Take over a session from the pending requests to start
                    helping users.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {activeSessions.map((session) => (
                    <div
                      key={session.id}
                      className="border border-gray-200 rounded-lg p-3 hover:bg-gray-50"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-2">
                            {getPlatformIcon(session.platform)}
                            <span className="text-sm font-medium text-gray-900">
                              Session {session.id.slice(-8)}
                            </span>
                          </div>
                          <span className="text-xs text-gray-500">
                            {session.messages.length} messages
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-500">
                            {session.lastMessageAt
                              ? new Date(
                                  session.lastMessageAt,
                                ).toLocaleTimeString()
                              : new Date(
                                  session.createdAt,
                                ).toLocaleTimeString()}
                          </span>
                          <button
                            type="button"
                            onClick={() =>
                              router.push(`/agent/chat/${session.id}`)
                            }
                            className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200"
                          >
                            Open Chat
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* All Active Sessions */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                All Active Sessions
              </h3>

              {allActiveSessions.length === 0 ? (
                <div className="text-center py-8">
                  <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    No active sessions
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    No users are currently chatting.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {allActiveSessions.map((session) => (
                    <div
                      key={session.id}
                      className="border border-gray-200 rounded-lg p-3 hover:bg-gray-50"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-2">
                            {getPlatformIcon(session.platform)}
                            <span className="text-sm font-medium text-gray-900">
                              Session {session.id.slice(-8)}
                            </span>
                            {session.isHandedOver && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                Handed Over
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-500">
                            {session.messageCount} messages
                          </span>
                          <span className="text-xs text-gray-500">
                            {session.lastMessageAt
                              ? new Date(
                                  session.lastMessageAt,
                                ).toLocaleTimeString()
                              : new Date(
                                  session.createdAt,
                                ).toLocaleTimeString()}
                          </span>
                          <button
                            type="button"
                            onClick={() =>
                              router.push(`/agent/chat/${session.id}`)
                            }
                            className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200"
                          >
                            Open Chat
                          </button>
                          {!session.isHandedOver && (
                            <button
                              type="button"
                              onClick={() => handleTakeoverSession(session.id)}
                              className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200"
                            >
                              Take Over
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Handover Requests Queue */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Pending Handover Requests
              </h3>

              {handoverRequests.length === 0 ? (
                <div className="text-center py-8">
                  <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    No pending requests
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    All chat sessions are currently handled by the bot.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {handoverRequests.map((request) => (
                    <div
                      key={request.id}
                      className="border border-gray-200 rounded-lg p-4"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-2">
                            {getPlatformIcon('web')}
                            <span className="text-sm font-medium text-gray-900">
                              Session {request.sessionId.slice(-8)}
                            </span>
                          </div>
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(request.priority)}`}
                          >
                            {request.priority}
                          </span>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="text-right">
                            <p className="text-sm text-gray-500">
                              Requested{' '}
                              {new Date(
                                request.requestedAt,
                              ).toLocaleTimeString()}
                            </p>
                            <p className="text-xs text-gray-400">
                              by {request.requestedBy}
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => handleTakeOver(request.id)}
                            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          >
                            Take Over
                          </button>
                        </div>
                      </div>
                      {request.reason && (
                        <div className="mt-2">
                          <p className="text-sm text-gray-600">
                            <strong>Reason:</strong> {request.reason}
                          </p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
