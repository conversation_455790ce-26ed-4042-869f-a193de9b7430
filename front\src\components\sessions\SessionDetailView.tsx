'use client';

import {
  Calendar,
  Clock,
  Download,
  Edit,
  MessageSquare,
  Plus,
  Save,
  Star,
  Tag,
  User,
  X,
} from 'lucide-react';
import { useState } from 'react';
import ReactMarkdown from 'react-markdown';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'agent';
  content: string;
  timestamp: string;
  metadata?: {
    platform?: string;
    attachments?: string[];
    reactions?: string[];
  };
}

interface AgentNote {
  id: string;
  agentId: number;
  agentName: string;
  content: string;
  timestamp: string;
  type: 'note' | 'escalation' | 'resolution';
}

interface CustomerFeedback {
  id: string;
  rating: number;
  comment?: string;
  timestamp: string;
  categories: string[];
}

interface SessionData {
  id: string;
  userId?: string;
  customerName?: string;
  customerEmail?: string;
  platform: 'web' | 'whatsapp' | 'facebook';
  status: 'active' | 'completed' | 'abandoned' | 'escalated';
  isHandedOver: boolean;
  agentId?: number;
  agentName?: string;
  createdAt: string;
  endedAt?: string;
  duration?: number;
  messageCount: number;
  messages: Message[];
  agentNotes: AgentNote[];
  customerFeedback?: CustomerFeedback;
  tags: string[];
  priority: 'low' | 'normal' | 'high' | 'urgent';
  resolution?: {
    type: 'resolved' | 'escalated' | 'transferred';
    summary: string;
    timestamp: string;
  };
}

interface SessionDetailViewProps {
  session: SessionData;
  onClose: () => void;
  onAddNote?: (note: Omit<AgentNote, 'id' | 'timestamp'>) => void;
  onUpdateTags?: (tags: string[]) => void;
  onExport?: (session: SessionData) => void;
}

export function SessionDetailView({
  session,
  onClose,
  onAddNote,
  onUpdateTags,
  onExport,
}: SessionDetailViewProps) {
  const [activeTab, setActiveTab] = useState<
    'messages' | 'notes' | 'analytics'
  >('messages');
  const [newNote, setNewNote] = useState('');
  const [noteType, setNoteType] = useState<
    'note' | 'escalation' | 'resolution'
  >('note');
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [editingTags, setEditingTags] = useState(false);
  const [tagInput, setTagInput] = useState('');
  const [localTags, setLocalTags] = useState(session.tags);

  const handleAddNote = () => {
    if (newNote.trim() && onAddNote) {
      onAddNote({
        agentId: 1, // Would come from current user context
        agentName: 'Current Agent', // Would come from current user context
        content: newNote.trim(),
        type: noteType,
      });
      setNewNote('');
      setIsAddingNote(false);
    }
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !localTags.includes(tagInput.trim())) {
      const newTags = [...localTags, tagInput.trim()];
      setLocalTags(newTags);
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const newTags = localTags.filter((tag) => tag !== tagToRemove);
    setLocalTags(newTags);
  };

  const handleSaveTags = () => {
    onUpdateTags?.(localTags);
    setEditingTags(false);
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const getMessageBgColor = (role: string) => {
    switch (role) {
      case 'user':
        return 'bg-blue-50 border-blue-200';
      case 'agent':
        return 'bg-green-50 border-green-200';
      case 'assistant':
        return 'bg-gray-50 border-gray-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const getNoteTypeColor = (type: string) => {
    switch (type) {
      case 'escalation':
        return 'bg-red-100 text-red-800';
      case 'resolution':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const calculateResponseTimes = () => {
    const responseTimes: number[] = [];
    let lastUserMessage: Message | null = null;

    session.messages.forEach((message) => {
      if (message.role === 'user') {
        lastUserMessage = message;
      } else if (
        lastUserMessage &&
        (message.role === 'agent' || message.role === 'assistant')
      ) {
        const responseTime =
          new Date(message.timestamp).getTime() -
          new Date(lastUserMessage.timestamp).getTime();
        responseTimes.push(responseTime / 1000); // Convert to seconds
        lastUserMessage = null;
      }
    });

    return responseTimes;
  };

  const responseTimes = calculateResponseTimes();
  const avgResponseTime =
    responseTimes.length > 0
      ? responseTimes.reduce((sum, time) => sum + time, 0) /
        responseTimes.length
      : 0;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-bold text-gray-900">
              Session Details - {session.id.slice(-8)}
            </h2>
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                session.status === 'completed'
                  ? 'bg-green-100 text-green-800'
                  : session.status === 'active'
                    ? 'bg-blue-100 text-blue-800'
                    : session.status === 'escalated'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-gray-100 text-gray-800'
              }`}
            >
              {session.status}
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={() => onExport?.(session)}
              className="flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </button>
            <button
              type="button"
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-md"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Session Info */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Customer</h3>
              <p className="mt-1 text-sm text-gray-900">
                {session.customerName || 'Anonymous'}
              </p>
              {session.customerEmail && (
                <p className="text-sm text-gray-500">{session.customerEmail}</p>
              )}
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Agent</h3>
              <p className="mt-1 text-sm text-gray-900">
                {session.agentName || 'Bot Only'}
              </p>
              <p className="text-sm text-gray-500">
                Platform: {session.platform}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Duration</h3>
              <p className="mt-1 text-sm text-gray-900">
                {session.duration ? `${session.duration} minutes` : 'Ongoing'}
              </p>
              <p className="text-sm text-gray-500">
                {formatTimestamp(session.createdAt)}
              </p>
            </div>
          </div>

          {/* Tags */}
          <div className="mt-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-500">Tags</h3>
              <button
                type="button"
                onClick={() => setEditingTags(!editingTags)}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                <Edit className="h-4 w-4" />
              </button>
            </div>
            <div className="mt-2 flex flex-wrap gap-2">
              {localTags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {tag}
                  {editingTags && (
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 text-blue-600 hover:text-blue-800"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  )}
                </span>
              ))}
              {editingTags && (
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                    placeholder="Add tag..."
                    className="px-2 py-1 text-xs border border-gray-300 rounded"
                  />
                  <button
                    type="button"
                    onClick={handleAddTag}
                    className="text-green-600 hover:text-green-800"
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                  <button
                    type="button"
                    onClick={handleSaveTags}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <Save className="h-4 w-4" />
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {['messages', 'notes', 'analytics'].map((tab) => (
              <button
                key={tab}
                type="button"
                onClick={() => setActiveTab(tab as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm capitalize ${
                  activeTab === tab
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="flex-1 overflow-hidden">
          {activeTab === 'messages' && (
            <div className="h-full overflow-y-auto p-6">
              <div className="space-y-4">
                {session.messages.map((message) => (
                  <div
                    key={message.id}
                    className={`p-4 rounded-lg border ${getMessageBgColor(message.role)}`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium capitalize">
                          {message.role === 'user'
                            ? session.customerName || 'Customer'
                            : message.role === 'agent'
                              ? session.agentName || 'Agent'
                              : 'AI Assistant'}
                        </span>
                        <span className="text-xs text-gray-500">
                          {formatTimestamp(message.timestamp)}
                        </span>
                      </div>
                    </div>
                    <div className="text-sm text-gray-900">
                      <ReactMarkdown>{message.content}</ReactMarkdown>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'notes' && (
            <div className="h-full overflow-y-auto p-6">
              <div className="space-y-4">
                {/* Add Note Form */}
                {isAddingNote ? (
                  <div className="bg-gray-50 p-4 rounded-lg border">
                    <div className="space-y-3">
                      <select
                        value={noteType}
                        onChange={(e) => setNoteType(e.target.value as any)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                      >
                        <option value="note">General Note</option>
                        <option value="escalation">Escalation</option>
                        <option value="resolution">Resolution</option>
                      </select>
                      <textarea
                        value={newNote}
                        onChange={(e) => setNewNote(e.target.value)}
                        placeholder="Add your note..."
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                      />
                      <div className="flex justify-end space-x-2">
                        <button
                          type="button"
                          onClick={() => setIsAddingNote(false)}
                          className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800"
                        >
                          Cancel
                        </button>
                        <button
                          type="button"
                          onClick={handleAddNote}
                          className="px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
                        >
                          Add Note
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <button
                    type="button"
                    onClick={() => setIsAddingNote(true)}
                    className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-500 hover:border-gray-400 hover:text-gray-600"
                  >
                    <Plus className="h-5 w-5 mx-auto mb-2" />
                    Add Note
                  </button>
                )}

                {/* Existing Notes */}
                {session.agentNotes.map((note) => (
                  <div key={note.id} className="bg-white p-4 rounded-lg border">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium">
                          {note.agentName}
                        </span>
                        <span
                          className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getNoteTypeColor(note.type)}`}
                        >
                          {note.type}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500">
                        {formatTimestamp(note.timestamp)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-900">{note.content}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'analytics' && (
            <div className="h-full overflow-y-auto p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white p-4 rounded-lg border">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Session Metrics
                  </h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">
                        Total Messages:
                      </span>
                      <span className="text-sm font-medium">
                        {session.messageCount}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">
                        Avg Response Time:
                      </span>
                      <span className="text-sm font-medium">
                        {avgResponseTime.toFixed(1)}s
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">
                        Session Duration:
                      </span>
                      <span className="text-sm font-medium">
                        {session.duration || 0} minutes
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">
                        Handover Status:
                      </span>
                      <span className="text-sm font-medium">
                        {session.isHandedOver ? 'Yes' : 'No'}
                      </span>
                    </div>
                  </div>
                </div>

                {session.customerFeedback && (
                  <div className="bg-white p-4 rounded-lg border">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                      Customer Feedback
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Star className="h-5 w-5 text-yellow-400" />
                        <span className="text-lg font-medium">
                          {session.customerFeedback.rating}/5
                        </span>
                      </div>
                      {session.customerFeedback.comment && (
                        <p className="text-sm text-gray-600">
                          {session.customerFeedback.comment}
                        </p>
                      )}
                      <div className="flex flex-wrap gap-1">
                        {session.customerFeedback.categories.map(
                          (category, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800"
                            >
                              {category}
                            </span>
                          ),
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
