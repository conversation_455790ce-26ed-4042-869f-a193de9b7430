#!/usr/bin/env node

/**
 * Test script for multi-site implementation
 * This script tests the new site-prefixed API endpoints
 */

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:8787';
const DEFAULT_SITE_ID = '1';

async function testLogin() {
  console.log('🔐 Testing admin login...');

  try {
    const response = await fetch(`${API_BASE_URL}/api/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123',
      }),
    });

    const result = await response.json();

    if (response.ok && result.success) {
      console.log('✅ Login successful');
      return result.token;
    }
    console.log('❌ Login failed:', result.error);
    return null;
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return null;
  }
}

async function testSitePrefixedEndpoint(token) {
  console.log('🌐 Testing site-prefixed endpoint...');

  try {
    const response = await fetch(
      `${API_BASE_URL}/api/sites/${DEFAULT_SITE_ID}/admin/users`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      },
    );

    const result = await response.json();

    if (response.ok) {
      console.log('✅ Site-prefixed endpoint working');
      console.log(`📊 Found ${result.length || 0} users`);
      return true;
    }
    console.log('❌ Site-prefixed endpoint failed:', result.error);
    return false;
  } catch (error) {
    console.log('❌ Site-prefixed endpoint error:', error.message);
    return false;
  }
}

async function testHealthEndpoint() {
  console.log('🏥 Testing health endpoint...');

  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    const result = await response.json();

    if (response.ok && result.status === 'OK') {
      console.log('✅ Health endpoint working');
      return true;
    }
    console.log('❌ Health endpoint failed');
    return false;
  } catch (error) {
    console.log('❌ Health endpoint error:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting multi-site implementation tests...\n');

  // Test 1: Health check
  const healthOk = await testHealthEndpoint();
  console.log('');

  // Test 2: Login
  const token = await testLogin();
  console.log('');

  if (!token) {
    console.log('❌ Cannot continue tests without valid token');
    return;
  }

  // Test 3: Site-prefixed endpoint
  const sitePrefixOk = await testSitePrefixedEndpoint(token);
  console.log('');

  // Summary
  console.log('📋 Test Summary:');
  console.log(`Health endpoint: ${healthOk ? '✅' : '❌'}`);
  console.log(`Login: ${token ? '✅' : '❌'}`);
  console.log(`Site-prefixed API: ${sitePrefixOk ? '✅' : '❌'}`);

  const allPassed = healthOk && token && sitePrefixOk;
  console.log(
    `\n${allPassed ? '🎉 All tests passed!' : '⚠️  Some tests failed'}`,
  );
}

// Run the tests
runTests().catch(console.error);
