import { count, desc, eq, inArray } from 'drizzle-orm';
import { type NextRequest } from 'next/server';
import { getHalalSelangorSiteId } from '@/lib/analytics';
import { db } from '@/lib/db';
import { products, categories, productCategories } from '@/lib/db/schema';
import { createSuccessResponse, createErrorResponse, handleApiError } from '@/lib/api-response';

// Remove edge runtime to use Node.js runtime for database access

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const page = Number.parseInt(searchParams.get('page') || '1');
  const limit = Number.parseInt(searchParams.get('limit') || '20');

  try {
    // Get total count for pagination
    const totalResults = await db.select({ count: count() }).from(products);

    const total = totalResults[0]?.count || 0;

    // Calculate pagination
    const offset = (page - 1) * limit;
    const hasMore = offset + limit < total;

    // Get paginated results
    const productList = await db
      .select({
        id: products.id,
        productName: products.productName,
        companyName: products.companyName,
        certificateNumber: products.certificateNumber,
        certificateType: products.certificateType,
        issuedDate: products.issuedDate,
        expiryDate: products.expiryDate,
        status: products.status,
        category: products.category,
        subcategory: products.subcategory,
        address: products.address,
        state: products.state,
        country: products.country,
        contactInfo: products.contactInfo,
        website: products.website,
        sourceUrl: products.sourceUrl,
        createdAt: products.createdAt,
        updatedAt: products.updatedAt,
      })
      .from(products)
      .limit(limit)
      .offset(offset)
      .orderBy(desc(products.createdAt));

    // Fetch categories from junction table for all products
    const productIds = productList.map(p => p.id);

    if (productIds.length > 0) {
      const productCategoriesData = await db
        .select({
          productId: productCategories.productId,
          categoryName: categories.categoryName,
        })
        .from(productCategories)
        .innerJoin(categories, eq(productCategories.categoryId, categories.id))
        .where(inArray(productCategories.productId, productIds));

      // Group categories by product ID (ensure consistent string keys)
      const categoriesByProduct: { [key: string]: string[] } = {};
      productCategoriesData.forEach(item => {
        const productIdKey = String(item.productId); // Convert to string for consistent lookup
        if (!categoriesByProduct[productIdKey]) {
          categoriesByProduct[productIdKey] = [];
        }
        categoriesByProduct[productIdKey].push(item.categoryName);
      });

      // Replace category field with junction table categories
      productList.forEach(product => {
        const productIdKey = String(product.id); // Convert to string for consistent lookup
        const junctionCategories = categoriesByProduct[productIdKey];
        if (junctionCategories && junctionCategories.length > 0) {
          // Use categories from junction table with spaces around pipe
          product.category = junctionCategories.join(' | ');
        } else {
          // No junction table categories found, set to empty
          product.category = '';
        }
      });
    }

    const response = {
      products: productList,
      pagination: {
        page,
        limit,
        total,
        hasMore,
      },
    };

    console.log('Products list response:', {
      productsCount: productList.length,
      total,
      page,
      hasMore,
    });

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error, 'Failed to fetch products');
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    if (!body.productName || !body.companyName) {
      return createErrorResponse('Product name and company name are required', undefined, 400);
    }

    // Create new product with Selangor site ID
    const newProduct = await db
      .insert(products)
      .values({
        siteId: getHalalSelangorSiteId(),
        productName: body.productName,
        companyName: body.companyName,
        certificateNumber: body.certificateNumber || null,
        certificateType: body.certificateType || null,
        issuedDate: body.issuedDate || null,
        expiryDate: body.expiryDate || null,
        status: body.status || null,
        category: body.category || null,
        subcategory: body.subcategory || null,
        address: body.address || null,
        state: body.state || null,
        country: body.country || 'Malaysia',
        contactInfo: body.contactInfo || null,
        website: body.website || null,
        sourceUrl: body.sourceUrl || null,
        rawData: body.rawData || null,
      })
      .returning({
        id: products.id,
        productName: products.productName,
        companyName: products.companyName,
        certificateNumber: products.certificateNumber,
        certificateType: products.certificateType,
        issuedDate: products.issuedDate,
        expiryDate: products.expiryDate,
        status: products.status,
        category: products.category,
        subcategory: products.subcategory,
        address: products.address,
        state: products.state,
        country: products.country,
        contactInfo: products.contactInfo,
        website: products.website,
        sourceUrl: products.sourceUrl,
        createdAt: products.createdAt,
        updatedAt: products.updatedAt,
      });

    console.log('Product created:', newProduct[0]);

    return createSuccessResponse(newProduct[0], 'Product created successfully');
  } catch (error) {
    return handleApiError(error, 'Failed to create product');
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return createErrorResponse('Product ID is required', undefined, 400);
    }

    // Validate required fields
    if (!updateData.productName || !updateData.companyName) {
      return createErrorResponse('Product name and company name are required', undefined, 400);
    }

    // Update product
    const updatedProduct = await db
      .update(products)
      .set({
        productName: updateData.productName,
        companyName: updateData.companyName,
        certificateNumber: updateData.certificateNumber || null,
        certificateType: updateData.certificateType || null,
        issuedDate: updateData.issuedDate || null,
        expiryDate: updateData.expiryDate || null,
        status: updateData.status || null,
        category: updateData.category || null,
        subcategory: updateData.subcategory || null,
        address: updateData.address || null,
        state: updateData.state || null,
        country: updateData.country || 'Malaysia',
        contactInfo: updateData.contactInfo || null,
        website: updateData.website || null,
        sourceUrl: updateData.sourceUrl || null,
        rawData: updateData.rawData || null,
        updatedAt: new Date(),
      })
      .where(eq(products.id, id))
      .returning({
        id: products.id,
        productName: products.productName,
        companyName: products.companyName,
        certificateNumber: products.certificateNumber,
        certificateType: products.certificateType,
        issuedDate: products.issuedDate,
        expiryDate: products.expiryDate,
        status: products.status,
        category: products.category,
        subcategory: products.subcategory,
        address: products.address,
        state: products.state,
        country: products.country,
        contactInfo: products.contactInfo,
        website: products.website,
        sourceUrl: products.sourceUrl,
        createdAt: products.createdAt,
        updatedAt: products.updatedAt,
      });

    if (updatedProduct.length === 0) {
      return createErrorResponse('Product not found', undefined, 404);
    }

    console.log('Product updated:', updatedProduct[0]);

    return createSuccessResponse(updatedProduct[0], 'Product updated successfully');
  } catch (error) {
    return handleApiError(error, 'Failed to update product');
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return createErrorResponse('Product ID is required', undefined, 400);
    }

    // Delete product
    const deletedProduct = await db
      .delete(products)
      .where(eq(products.id, Number.parseInt(id)))
      .returning({
        id: products.id,
        productName: products.productName,
        companyName: products.companyName,
      });

    if (deletedProduct.length === 0) {
      return createErrorResponse('Product not found', undefined, 404);
    }

    console.log('Product deleted:', deletedProduct[0]);

    return createSuccessResponse(deletedProduct[0], 'Product deleted successfully');
  } catch (error) {
    return handleApiError(error, 'Failed to delete product');
  }
}
