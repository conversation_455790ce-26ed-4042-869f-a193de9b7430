'use client';

import {
  ArrowDownRight,
  ArrowUpRight,
  Download,
  Filter,
  Minus,
  Search,
  TrendingDown,
  TrendingUp,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { generateCategoryBreakdown, generateTopKeywords } from '@/lib/mockData';

export default function SearchQueriesReport() {
  const [keywords, setKeywords] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('volume');
  const [timeRange, setTimeRange] = useState<string>('30d');

  useEffect(() => {
    setKeywords(generateTopKeywords());
    setCategories(generateCategoryBreakdown());
  }, []);

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <ArrowUpRight className="h-4 w-4 text-green-600" />;
      case 'down':
        return <ArrowDownRight className="h-4 w-4 text-red-600" />;
      default:
        return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const filteredKeywords = keywords
    .filter(
      (keyword) =>
        selectedCategory === 'all' || keyword.category === selectedCategory,
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'volume':
          return b.count - a.count;
        case 'trend':
          return b.trendPercentage - a.trendPercentage;
        case 'success':
          return b.successRate - a.successRate;
        default:
          return 0;
      }
    });

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Search Query Analysis
            </h1>
            <p className="text-gray-600">
              Detailed analysis of user search behavior and keyword performance
            </p>
          </div>
          <button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </button>
        </div>

        {/* Filters */}
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                Filters:
              </span>
            </div>

            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>

            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Categories</option>
              <option value="restaurants">Restaurants</option>
              <option value="products">Products</option>
              <option value="services">Services</option>
              <option value="certification">Certification</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            >
              <option value="volume">Sort by Volume</option>
              <option value="trend">Sort by Trend</option>
              <option value="success">Sort by Success Rate</option>
            </select>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Unique Queries
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {keywords.length}
                </p>
              </div>
              <Search className="h-8 w-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Search Volume
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {keywords
                    .reduce((sum, k) => sum + k.count, 0)
                    .toLocaleString()}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Avg Success Rate
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {(
                    (keywords.reduce((sum, k) => sum + k.successRate, 0) /
                      keywords.length) *
                    100
                  ).toFixed(1)}
                  %
                </p>
              </div>
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <div className="w-4 h-4 bg-purple-600 rounded-full" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Trending Up</p>
                <p className="text-2xl font-bold text-gray-900">
                  {keywords.filter((k) => k.trend === 'up').length}
                </p>
              </div>
              <ArrowUpRight className="h-8 w-8 text-green-600" />
            </div>
          </div>
        </div>

        {/* Category Breakdown */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Query Categories Breakdown
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {categories.map((category, index) => (
              <div
                key={category.category}
                className="text-center p-4 border rounded-lg"
              >
                <div
                  className={`w-16 h-16 mx-auto mb-3 rounded-full flex items-center justify-center ${
                    index === 0
                      ? 'bg-blue-100'
                      : index === 1
                        ? 'bg-green-100'
                        : index === 2
                          ? 'bg-yellow-100'
                          : 'bg-purple-100'
                  }`}
                >
                  <div
                    className={`w-8 h-8 rounded-full ${
                      index === 0
                        ? 'bg-blue-500'
                        : index === 1
                          ? 'bg-green-500'
                          : index === 2
                            ? 'bg-yellow-500'
                            : 'bg-purple-500'
                    }`}
                  />
                </div>
                <h4 className="font-medium text-gray-900">
                  {category.category}
                </h4>
                <p className="text-2xl font-bold text-gray-900 mt-1">
                  {category.percentage}%
                </p>
                <p className="text-sm text-gray-500">
                  {category.count} searches
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Top Keywords Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              Top Search Keywords
            </h3>
            <p className="text-sm text-gray-600">
              Ranked list of most frequent search queries with performance
              metrics
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rank
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Search Query
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Search Volume
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trend
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Success Rate
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredKeywords.map((keyword, index) => (
                  <tr key={keyword.query} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      #{index + 1}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {keyword.query}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          keyword.category === 'restaurants'
                            ? 'bg-blue-100 text-blue-800'
                            : keyword.category === 'products'
                              ? 'bg-green-100 text-green-800'
                              : keyword.category === 'services'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-purple-100 text-purple-800'
                        }`}
                      >
                        {keyword.category}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {keyword.count.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getTrendIcon(keyword.trend)}
                        <span
                          className={`ml-1 text-sm font-medium ${getTrendColor(keyword.trend)}`}
                        >
                          {keyword.trendPercentage}%
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex items-center">
                        <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                          <div
                            className="bg-green-600 h-2 rounded-full"
                            style={{ width: `${keyword.successRate * 100}%` }}
                          />
                        </div>
                        {(keyword.successRate * 100).toFixed(1)}%
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
