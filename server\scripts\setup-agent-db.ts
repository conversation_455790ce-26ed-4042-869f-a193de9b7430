#!/usr/bin/env node

/**
 * Database setup script for Agent Handover System
 * This script creates the necessary tables and sample data for the agent handover functionality
 */

import bcrypt from 'bcryptjs';
import { Client, type ClientConfig } from 'pg';

// Database configuration
const dbConfig: ClientConfig = {
  connectionString: process.env.DATABASE_URL,
};

interface AgentUser {
  username: string;
  email: string;
  password_hash: string;
  first_name: string;
  last_name: string;
  role: 'agent' | 'supervisor';
}

async function setupDatabase(): Promise<void> {
  const client = new Client(dbConfig);

  try {
    await client.connect();
    console.log('Connected to PostgreSQL database');

    // Insert sample agent users
    const passwordHash: string = await bcrypt.hash('password123', 10);

    await client.query(
      `
      INSERT INTO agent_users (username, email, password_hash, first_name, last_name, role) 
      VALUES 
        ('agent1', '<EMAIL>', $1, '<PERSON>', '<PERSON><PERSON>', 'agent'),
        ('agent2', '<EMAIL>', $1, '<PERSON>', '<PERSON>', 'agent'),
        ('supervisor1', '<EMAIL>', $1, '<PERSON>', '<PERSON>', 'supervisor')
      ON CONFLICT (username) DO NOTHING;
    `,
      [passwordHash],
    );
    console.log('✓ Created sample agent users');

    console.log('\n🎉 Database setup completed successfully!');
    console.log('\nSample agent credentials:');
    console.log('- Username: agent1, Password: password123');
    console.log('- Username: agent2, Password: password123');
    console.log('- Username: supervisor1, Password: password123');
    console.log('\nYou can now use the agent handover system!');
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run the setup
if (import.meta.url === `file://${process.argv[1]}`) {
  setupDatabase();
}

export { setupDatabase };
