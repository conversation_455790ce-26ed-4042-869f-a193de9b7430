'use client';

export const runtime = 'edge';
export const dynamic = 'force-dynamic';

import { ArrowLeft, Save, Copy } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import type React from 'react';
import { useEffect, useState } from 'react';
import { Link } from '@/i18n/navigation';
import { api } from '@/lib/api';
import type { Collection, CollectionCreateRequest } from '@/types';
import { CollectionStatus } from '@/types';

export default function CloneCollectionPage() {
  const params = useParams();
  const router = useRouter();
  const collectionId = Number(params.id);

  const [originalCollection, setOriginalCollection] = useState<Collection | null>(null);
  const [name, setName] = useState('');
  const [status, setStatus] = useState<CollectionStatus>(CollectionStatus.ACTIVE);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCollection = async () => {
      try {
        const response = await api.admin.getCollectionById(collectionId);
        if (response.success && response.data) {
          const collection = response.data;
          setOriginalCollection(collection);
          setName(`${collection.name}_copy`);
          setStatus(collection.status);
        }
      } catch (err) {
        console.error('Failed to fetch collection:', err);
        setError('Failed to load collection data for cloning.');
      }
    };

    fetchCollection();
  }, [collectionId]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!name.trim()) {
      setError('Collection name is required.');
      return;
    }
    setIsLoading(true);
    setError(null);

    const collectionData: CollectionCreateRequest = { name, status };

    try {
      await api.admin.createCollection(collectionData);
      router.push('/admin/collections');
    } catch (err: any) {
      setError(
        err.response?.data?.error || err.message || 'Failed to create collection.',
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (!originalCollection) {
    return <div>Loading...</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <Link
          href="/admin/collections"
          className="inline-flex items-center text-blue-600 hover:text-blue-800"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Collections
        </Link>
      </div>

      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 flex items-center">
          <Copy className="mr-2 h-6 w-6" />
          Clone Collection: {originalCollection.name}
        </h1>
        <p className="text-gray-600 mt-2">
          Creating a new collection based on "{originalCollection.name}"
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Collection Name *
          </label>
          <input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            Status
          </label>
          <select
            value={status}
            onChange={(e) => setStatus(e.target.value as CollectionStatus)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
          >
            {Object.values(CollectionStatus).map((statusOption) => (
              <option key={statusOption} value={statusOption}>
                {statusOption}
              </option>
            ))}
          </select>
        </div>

        <div className="flex justify-end space-x-4">
          <Link
            href="/admin/collections"
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={isLoading}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            <Save className="mr-2 h-4 w-4" />
            {isLoading ? 'Creating...' : 'Create Collection'}
          </button>
        </div>
      </form>
    </div>
  );
}