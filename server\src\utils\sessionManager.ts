import { v4 as uuidv4 } from 'uuid';
import type { ChatMessage, ChatSession } from '../types';

// Import security logging (avoid circular dependency)
interface SecurityLogData {
  event: string;
  level: 'info' | 'warn' | 'error' | 'critical';
  ip?: string;
  userAgent?: string;
  userId?: string;
  sessionId?: string;
  endpoint?: string;
  method?: string;
  details?: Record<string, any>;
  timestamp: string;
}

const logSecurityEvent = (data: Omit<SecurityLogData, 'timestamp'>) => {
  const logEntry: SecurityLogData = {
    ...data,
    timestamp: new Date().toISOString(),
  };

  const logMessage = `[SESSION_SECURITY] ${data.level.toUpperCase()}: ${data.event}`;

  switch (data.level) {
    case 'critical':
    case 'error':
      console.error(logMessage, logEntry);
      break;
    case 'warn':
      console.warn(logMessage, logEntry);
      break;
    case 'info':
    default:
      console.log(logMessage, logEntry);
      break;
  }
};

interface SessionConfig {
  timeout: number; // Session timeout in milliseconds
  maxSessions: number; // Maximum concurrent sessions per user
  cleanupInterval: number; // Cleanup interval in milliseconds
  maxMessages: number; // Maximum messages per session
}

interface SessionMetadata {
  createdAt: Date;
  lastActivity: Date;
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
}

interface ExtendedChatSession extends ChatSession {
  metadata: SessionMetadata;
}

class SessionManager {
  private sessions = new Map<string, ExtendedChatSession>();
  private userSessions = new Map<string, Set<string>>(); // userId -> sessionIds
  private cleanupTimer: NodeJS.Timeout | null = null;

  private config: SessionConfig = {
    timeout: 30 * 60 * 1000, // 30 minutes
    maxSessions: 5, // Max 5 concurrent sessions per user
    cleanupInterval: 5 * 60 * 1000, // Cleanup every 5 minutes
    maxMessages: 100, // Max 100 messages per session
  };

  constructor(config?: Partial<SessionConfig>) {
    if (config) {
      this.config = { ...this.config, ...config };
    }
    this.startCleanupTimer();
  }

  /**
   * Create a new chat session
   */
  createSession(
    userId?: string,
    ipAddress?: string,
    userAgent?: string,
  ): { sessionId: string; session: ChatSession } {
    const sessionId = uuidv4();
    const now = new Date();

    // Check session limits for authenticated users
    if (userId) {
      this.enforceSessionLimits(userId);
    }

    const session: ExtendedChatSession = {
      id: sessionId,
      messages: [],
      createdAt: now,
      metadata: {
        createdAt: now,
        lastActivity: now,
        userId,
        ipAddress,
        userAgent,
      },
    };

    this.sessions.set(sessionId, session);

    // Track user sessions
    if (userId) {
      if (!this.userSessions.has(userId)) {
        this.userSessions.set(userId, new Set());
      }
      this.userSessions.get(userId)!.add(sessionId);
    }

    // Log session creation
    logSecurityEvent({
      event: 'SESSION_CREATED',
      level: 'info',
      ip: ipAddress,
      userAgent: userAgent?.substring(0, 100),
      userId,
      sessionId,
      details: {
        totalSessions: this.sessions.size,
        userSessionCount: userId ? this.userSessions.get(userId)?.size || 0 : 0,
        isAuthenticated: !!userId,
      },
    });

    // Return session without metadata for external use
    return {
      sessionId,
      session: {
        id: session.id,
        messages: session.messages,
        createdAt: session.createdAt,
      },
    };
  }

  /**
   * Get a session by ID
   */
  getSession(sessionId: string): ChatSession | null {
    const session = this.sessions.get(sessionId);
    if (!session) {
      logSecurityEvent({
        event: 'SESSION_NOT_FOUND',
        level: 'warn',
        sessionId,
        details: {
          totalSessions: this.sessions.size,
          possibleSessionHijacking: true,
        },
      });
      return null;
    }

    // Check if session has expired
    if (this.isSessionExpired(session)) {
      logSecurityEvent({
        event: 'SESSION_EXPIRED_ACCESS_ATTEMPT',
        level: 'warn',
        ip: session.metadata.ipAddress,
        userAgent: session.metadata.userAgent?.substring(0, 100),
        userId: session.metadata.userId,
        sessionId,
        details: {
          sessionAge: Date.now() - session.metadata.createdAt.getTime(),
          lastActivity: session.metadata.lastActivity.toISOString(),
          inactiveTime: Date.now() - session.metadata.lastActivity.getTime(),
        },
      });

      this.deleteSession(sessionId);
      return null;
    }

    // Update last activity
    session.metadata.lastActivity = new Date();

    // Return session without metadata
    return {
      id: session.id,
      messages: session.messages,
      createdAt: session.createdAt,
    };
  }

  /**
   * Update a session with new message
   */
  updateSession(sessionId: string, message: ChatMessage): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }

    // Check if session has expired
    if (this.isSessionExpired(session)) {
      this.deleteSession(sessionId);
      return false;
    }

    // Check message limits
    if (session.messages.length >= this.config.maxMessages) {
      // Remove oldest messages to make room
      const messagesToRemove =
        session.messages.length - this.config.maxMessages + 1;
      session.messages.splice(0, messagesToRemove);
    }

    // Add new message
    session.messages.push(message);
    session.metadata.lastActivity = new Date();

    return true;
  }

  /**
   * Delete a session
   */
  deleteSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return false;
    }

    // Log session deletion
    logSecurityEvent({
      event: 'SESSION_DELETED',
      level: 'info',
      ip: session.metadata.ipAddress,
      userAgent: session.metadata.userAgent?.substring(0, 100),
      userId: session.metadata.userId,
      sessionId,
      details: {
        sessionDuration: Date.now() - session.metadata.createdAt.getTime(),
        messageCount: session.messages.length,
        wasExpired: this.isSessionExpired(session),
        totalSessionsAfterDeletion: this.sessions.size - 1,
      },
    });

    // Remove from user sessions tracking
    if (session.metadata.userId) {
      const userSessionSet = this.userSessions.get(session.metadata.userId);
      if (userSessionSet) {
        userSessionSet.delete(sessionId);
        if (userSessionSet.size === 0) {
          this.userSessions.delete(session.metadata.userId);
        }
      }
    }

    this.sessions.delete(sessionId);
    return true;
  }

  /**
   * Get all sessions for a user
   */
  getUserSessions(userId: string): ChatSession[] {
    const sessionIds = this.userSessions.get(userId);
    if (!sessionIds) {
      return [];
    }

    const sessions: ChatSession[] = [];
    for (const sessionId of sessionIds) {
      const session = this.getSession(sessionId);
      if (session) {
        sessions.push(session);
      }
    }

    return sessions;
  }

  /**
   * Delete all sessions for a user
   */
  deleteUserSessions(userId: string): number {
    const sessionIds = this.userSessions.get(userId);
    if (!sessionIds) {
      return 0;
    }

    let deletedCount = 0;
    for (const sessionId of Array.from(sessionIds)) {
      if (this.deleteSession(sessionId)) {
        deletedCount++;
      }
    }

    return deletedCount;
  }

  /**
   * Get session statistics
   */
  getStats(): {
    totalSessions: number;
    activeSessions: number;
    expiredSessions: number;
    userSessions: number;
    anonymousSessions: number;
  } {
    let activeSessions = 0;
    let expiredSessions = 0;
    let userSessions = 0;
    let anonymousSessions = 0;

    for (const session of this.sessions.values()) {
      if (this.isSessionExpired(session)) {
        expiredSessions++;
      } else {
        activeSessions++;
      }

      if (session.metadata.userId) {
        userSessions++;
      } else {
        anonymousSessions++;
      }
    }

    return {
      totalSessions: this.sessions.size,
      activeSessions,
      expiredSessions,
      userSessions,
      anonymousSessions,
    };
  }

  /**
   * Clean up expired sessions
   */
  cleanup(): number {
    const expiredSessions: string[] = [];

    for (const [sessionId, session] of this.sessions.entries()) {
      if (this.isSessionExpired(session)) {
        expiredSessions.push(sessionId);
      }
    }

    for (const sessionId of expiredSessions) {
      this.deleteSession(sessionId);
    }

    return expiredSessions.length;
  }

  /**
   * Check if a session has expired
   */
  private isSessionExpired(session: ExtendedChatSession): boolean {
    const now = Date.now();
    const lastActivity = session.metadata.lastActivity.getTime();
    return now - lastActivity > this.config.timeout;
  }

  /**
   * Enforce session limits for a user
   */
  private enforceSessionLimits(userId: string): void {
    const userSessionSet = this.userSessions.get(userId);
    if (!userSessionSet || userSessionSet.size < this.config.maxSessions) {
      return;
    }

    // Log session limit enforcement
    logSecurityEvent({
      event: 'SESSION_LIMIT_ENFORCED',
      level: 'warn',
      userId,
      details: {
        currentSessions: userSessionSet.size,
        maxSessions: this.config.maxSessions,
        action: 'removing_oldest_session',
        possibleAbuse: userSessionSet.size > this.config.maxSessions * 2,
      },
    });

    // Find oldest session to remove
    let oldestSessionId: string | null = null;
    let oldestTime = Date.now();

    for (const sessionId of userSessionSet) {
      const session = this.sessions.get(sessionId);
      if (session) {
        const sessionTime = session.metadata.lastActivity.getTime();
        if (sessionTime < oldestTime) {
          oldestTime = sessionTime;
          oldestSessionId = sessionId;
        }
      }
    }

    if (oldestSessionId) {
      logSecurityEvent({
        event: 'OLDEST_SESSION_REMOVED',
        level: 'info',
        userId,
        sessionId: oldestSessionId,
        details: {
          sessionAge: Date.now() - oldestTime,
          remainingSessions: userSessionSet.size - 1,
        },
      });

      this.deleteSession(oldestSessionId);
    }
  }

  /**
   * Start the cleanup timer
   */
  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      const cleanedUp = this.cleanup();
      if (cleanedUp > 0) {
        logSecurityEvent({
          event: 'SESSION_CLEANUP_COMPLETED',
          level: 'info',
          details: {
            expiredSessionsRemoved: cleanedUp,
            remainingSessions: this.sessions.size,
            cleanupInterval: this.config.cleanupInterval,
          },
        });
        console.log(`Cleaned up ${cleanedUp} expired sessions`);
      }
    }, this.config.cleanupInterval);
  }

  /**
   * Force cleanup of all expired sessions
   */
  forceCleanup(): number {
    return this.cleanup();
  }

  /**
   * Get session timeout configuration
   */
  getTimeoutConfig(): { timeout: number; cleanupInterval: number } {
    return {
      timeout: this.config.timeout,
      cleanupInterval: this.config.cleanupInterval,
    };
  }

  /**
   * Update session timeout configuration
   */
  updateTimeoutConfig(timeout?: number, cleanupInterval?: number): void {
    if (timeout !== undefined) {
      this.config.timeout = timeout;
      logSecurityEvent({
        event: 'SESSION_TIMEOUT_UPDATED',
        level: 'info',
        details: {
          newTimeout: timeout,
          oldTimeout: this.config.timeout,
        },
      });
    }

    if (cleanupInterval !== undefined) {
      this.config.cleanupInterval = cleanupInterval;
      // Restart cleanup timer with new interval
      this.startCleanupTimer();
      logSecurityEvent({
        event: 'SESSION_CLEANUP_INTERVAL_UPDATED',
        level: 'info',
        details: {
          newInterval: cleanupInterval,
          oldInterval: this.config.cleanupInterval,
        },
      });
    }
  }

  /**
   * Stop the cleanup timer
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.sessions.clear();
    this.userSessions.clear();
  }
}

// Export singleton instance
export const sessionManager = new SessionManager();
export default SessionManager;
