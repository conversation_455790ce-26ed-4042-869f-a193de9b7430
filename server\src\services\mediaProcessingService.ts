import type DatabaseService from './database';
import S3Service from './s3Service';

/**
 * Standard interface for media messages across all platforms
 */
export interface StandardMediaMessage {
  id: string;
  mediaUrl: string;
  mediaContentType: string;
  platform: string;
  timestamp: Date;
  from: string;
  to: string;
}

/**
 * Authentication configuration for downloading media
 */
export interface AuthConfig {
  headers: Record<string, string>;
  method?: 'GET' | 'POST';
}

/**
 * Universal media processing service for all platforms
 * Handles download, upload to S3, and file organization
 */
export class MediaProcessingService {
  private s3Service: S3Service | null = null;
  private dbService: DatabaseService;

  constructor(dbService: DatabaseService) {
    this.dbService = dbService;
  }

  /**
   * Initialize S3 service with configuration
   * @param s3ConfigId - ID of S3 configuration to use
   * @param serviceName - Optional S3 service name (fallback if ID not provided)
   */
  async initializeS3Service(
    s3ConfigId?: number,
    serviceName?: string,
  ): Promise<boolean> {
    try {
      let s3Config = null;

      if (s3ConfigId) {
        s3Config = await this.dbService.getS3ConfigurationById(s3ConfigId);
      } else if (serviceName) {
        s3Config =
          await this.dbService.getS3ConfigurationByServiceName(serviceName);
      }

      if (!s3Config) {
        console.error('MediaProcessingService: No S3 configuration found');
        return false;
      }

      this.s3Service = new S3Service(s3Config);
      console.log(
        `MediaProcessingService: Initialized with S3 service: ${s3Config.serviceName}`,
      );
      return true;
    } catch (error) {
      console.error(
        'MediaProcessingService: Error initializing S3 service:',
        error,
      );
      return false;
    }
  }

  /**
   * Process media message - download and upload to S3
   * @param message - Standard media message
   * @param authConfig - Authentication configuration for download
   * @returns Updated message with S3 URL
   */
  async processMediaMessage(
    message: StandardMediaMessage,
    authConfig: AuthConfig,
  ): Promise<StandardMediaMessage> {
    if (!message.mediaUrl || !this.s3Service) {
      return message;
    }

    try {
      console.log(
        `MediaProcessingService: Processing media for ${message.platform} message ${message.id}`,
      );

      // Download media from platform
      const mediaBuffer = await this.downloadMedia(
        message.mediaUrl,
        authConfig,
      );

      if (!mediaBuffer) {
        console.error(
          `MediaProcessingService: Failed to download media from ${message.mediaUrl}`,
        );
        return message;
      }

      // Generate organized S3 key
      const s3Key = this.generateS3Key(message);

      // Upload to S3
      const uploadResult = await this.s3Service.uploadFile(
        mediaBuffer,
        s3Key,
        message.mediaContentType || 'application/octet-stream',
      );

      console.log(
        `MediaProcessingService: Uploaded ${message.platform} media to S3: ${uploadResult.Location}`,
      );

      // Return updated message with S3 URL
      return {
        ...message,
        mediaUrl: uploadResult.Location,
      };
    } catch (error) {
      console.error('MediaProcessingService: Error processing media:', error);
      return message; // Return original message if processing fails
    }
  }

  /**
   * Download media file from platform URL
   * @param mediaUrl - Platform media URL
   * @param authConfig - Authentication configuration
   * @returns Buffer containing the media file
   */
  private async downloadMedia(
    mediaUrl: string,
    authConfig: AuthConfig,
  ): Promise<Buffer | null> {
    try {
      const response = await fetch(mediaUrl, {
        method: authConfig.method || 'GET',
        headers: authConfig.headers,
      });

      if (!response.ok) {
        console.error(
          `MediaProcessingService: Failed to download media. Status: ${response.status}`,
        );
        return null;
      }

      const arrayBuffer = await response.arrayBuffer();
      return Buffer.from(arrayBuffer);
    } catch (error) {
      console.error('MediaProcessingService: Error downloading media:', error);
      return null;
    }
  }

  /**
   * Generate organized S3 key for storing media file
   * @param message - Standard media message
   * @returns S3 key string
   */
  private generateS3Key(message: StandardMediaMessage): string {
    const mediaType = this.getMediaType(message.mediaContentType);
    const date = message.timestamp.toISOString().split('T')[0]; // YYYY-MM-DD
    const extension = this.getFileExtension(message.mediaContentType);

    return `${message.platform}/${mediaType}/${date}/${message.id}${extension}`;
  }

  /**
   * Determine media type from content type
   * @param contentType - MIME type
   * @returns Media type folder name
   */
  private getMediaType(contentType: string): string {
    if (!contentType) return 'other';

    // Normalize content type (remove charset, etc.)
    const cleanType = contentType.toLowerCase().split(';')[0].trim();

    // Image formats
    if (cleanType.startsWith('image/')) {
      return 'images';
    }

    // Audio formats
    if (cleanType.startsWith('audio/')) {
      return 'audio';
    }

    // Video formats
    if (cleanType.startsWith('video/')) {
      return 'videos';
    }

    // Document formats (specific mapping)
    const documentTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'text/csv',
      'application/rtf',
    ];

    if (documentTypes.includes(cleanType)) {
      return 'documents';
    }

    // Fallback for unknown types
    return 'other';
  }

  /**
   * Get file extension from content type
   * @param contentType - MIME type
   * @returns File extension with dot
   */
  private getFileExtension(contentType?: string): string {
    if (!contentType) return '';

    const cleanType = contentType.toLowerCase().split(';')[0].trim();

    const extensionMap: Record<string, string> = {
      // Images
      'image/jpeg': '.jpg',
      'image/jpg': '.jpg',
      'image/png': '.png',
      'image/gif': '.gif',
      'image/webp': '.webp',
      'image/bmp': '.bmp',
      'image/tiff': '.tiff',

      // Audio
      'audio/mpeg': '.mp3',
      'audio/mp4': '.m4a',
      'audio/ogg': '.ogg',
      'audio/wav': '.wav',
      'audio/webm': '.webm',
      'audio/aac': '.aac',
      'audio/flac': '.flac',

      // Videos
      'video/mp4': '.mp4',
      'video/quicktime': '.mov',
      'video/webm': '.webm',
      'video/avi': '.avi',
      'video/mpeg': '.mpeg',

      // Documents
      'application/pdf': '.pdf',
      'text/plain': '.txt',
      'text/csv': '.csv',
      'application/rtf': '.rtf',
      'application/msword': '.doc',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        '.docx',
      'application/vnd.ms-excel': '.xls',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        '.xlsx',
      'application/vnd.ms-powerpoint': '.ppt',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        '.pptx',
    };

    return extensionMap[cleanType] || '';
  }

  /**
   * Generate pre-signed URL for accessing media
   * @param s3Key - S3 object key
   * @param expiresSeconds - URL expiration time in seconds
   * @returns Pre-signed URL
   */
  async getMediaPresignedUrl(
    s3Key: string,
    expiresSeconds = 3600,
  ): Promise<string | null> {
    if (!this.s3Service) {
      console.error('MediaProcessingService: S3 service not initialized');
      return null;
    }

    try {
      return await this.s3Service.getPresignedUrl(s3Key, expiresSeconds);
    } catch (error) {
      console.error(
        'MediaProcessingService: Error generating pre-signed URL:',
        error,
      );
      return null;
    }
  }

  /**
   * Check if S3 service is initialized
   */
  isS3ServiceInitialized(): boolean {
    return this.s3Service !== null;
  }
}

export default MediaProcessingService;
