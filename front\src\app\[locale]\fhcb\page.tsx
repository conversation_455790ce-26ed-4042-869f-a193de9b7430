'use client';

export const runtime = 'edge';

import type { BreadcrumbItem } from '@/components/breadcrumb';
import { PageWrapper } from '@/components/page-wrapper';
import { useLanguage } from '@/lib/language-context';

export default function FHCBPage() {
  const { language } = useLanguage();

  const breadcrumbs: BreadcrumbItem[] = [
    {
      label: language === 'en' ? 'Home' : 'Laman <PERSON>',
      href: '/',
    },
    {
      label:
        language === 'en'
          ? 'Foreign Halal Certification Body'
          : '<PERSON><PERSON> As<PERSON>',
      href: '/fhcb',
    },
  ];

  const recognizedBodies = [
    {
      country: 'Singapore',
      countryBM: 'Singapura',
      body: 'Majlis Ugama Islam Singapura (MUIS)',
      bodyBM: 'Majlis Ugama Islam Singapura (MUIS)',
      website: 'https://www.muis.gov.sg',
      status: 'Active',
    },
    {
      country: 'Thailand',
      countryBM: 'Thailand',
      body: 'The Halal Institute of Thailand',
      bodyBM: 'Institut Halal Thailand',
      website: 'https://www.halalinstitute.org',
      status: 'Active',
    },
    {
      country: 'Indonesia',
      countryBM: 'Indonesia',
      body: '<PERSON>an <PERSON>yelenggara Jaminan Produk Halal (BPJPH)',
      bodyBM: 'Badan Penyelenggara Jaminan Produk Halal (BPJPH)',
      website: 'https://www.halal.go.id',
      status: 'Active',
    },
    {
      country: 'Brunei',
      countryBM: 'Brunei',
      body: 'Jabatan Hal Ehwal Syariah',
      bodyBM: 'Jabatan Hal Ehwal Syariah',
      website: 'https://www.jhs.gov.bn',
      status: 'Active',
    },
  ];

  return (
    <PageWrapper
      title="Foreign Halal Certification Body"
      titleBM="Badan Pensijilan Halal Asing"
      description="Information about foreign Halal certification bodies recognized by JAKIM and mutual recognition agreements."
      descriptionBM="Maklumat mengenai badan pensijilan Halal asing yang diiktiraf oleh JAKIM dan perjanjian pengiktirafan bersama."
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-8">
        {/* Introduction */}
        <div className="card">
          <h2 className="text-2xl font-bold mb-4 text-gray-900">
            {language === 'en'
              ? 'Foreign Halal Certification Bodies'
              : 'Badan Pensijilan Halal Asing'}
          </h2>
          <p className="text-gray-600 leading-relaxed mb-4">
            {language === 'en'
              ? 'JAKIM recognizes certain foreign Halal certification bodies through mutual recognition agreements (MRA). These agreements facilitate trade and ensure that Halal products certified by recognized foreign bodies meet Malaysian Halal standards.'
              : 'JAKIM mengiktiraf badan pensijilan Halal asing tertentu melalui perjanjian pengiktirafan bersama (MRA). Perjanjian ini memudahkan perdagangan dan memastikan produk Halal yang disijilkan oleh badan asing yang diiktiraf memenuhi piawaian Halal Malaysia.'}
          </p>
          <p className="text-gray-600 leading-relaxed">
            {language === 'en'
              ? 'Products certified by these recognized bodies can be imported into Malaysia without requiring additional Halal certification from JAKIM, subject to specific conditions and requirements.'
              : 'Produk yang disijilkan oleh badan yang diiktiraf ini boleh diimport ke Malaysia tanpa memerlukan pensijilan Halal tambahan daripada JAKIM, tertakluk kepada syarat dan keperluan khusus.'}
          </p>
        </div>

        {/* Recognition Criteria */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Recognition Criteria'
              : 'Kriteria Pengiktirafan'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en'
                      ? 'Standards Alignment'
                      : 'Penjajaran Piawaian'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Certification standards must align with Malaysian Halal requirements'
                      : 'Piawaian pensijilan mesti sejajar dengan keperluan Halal Malaysia'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en'
                      ? 'Competency Assessment'
                      : 'Penilaian Kecekapan'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Technical competency and capability evaluation'
                      : 'Penilaian kecekapan teknikal dan keupayaan'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en'
                      ? 'Quality Management'
                      : 'Pengurusan Kualiti'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Robust quality management and assurance systems'
                      : 'Sistem pengurusan dan jaminan kualiti yang kukuh'}
                  </p>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en'
                      ? 'Regulatory Compliance'
                      : 'Pematuhan Peraturan'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Compliance with local and international regulations'
                      : 'Pematuhan dengan peraturan tempatan dan antarabangsa'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en'
                      ? 'Monitoring System'
                      : 'Sistem Pemantauan'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Effective monitoring and surveillance mechanisms'
                      : 'Mekanisme pemantauan dan pengawasan yang berkesan'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en'
                      ? 'Information Sharing'
                      : 'Perkongsian Maklumat'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Commitment to information sharing and transparency'
                      : 'Komitmen untuk perkongsian maklumat dan ketelusan'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recognized Bodies */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Recognized Certification Bodies'
              : 'Badan Pensijilan yang Diiktiraf'}
          </h3>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">
                    {language === 'en' ? 'Country' : 'Negara'}
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">
                    {language === 'en'
                      ? 'Certification Body'
                      : 'Badan Pensijilan'}
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">
                    {language === 'en' ? 'Website' : 'Laman Web'}
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">
                    {language === 'en' ? 'Status' : 'Status'}
                  </th>
                </tr>
              </thead>
              <tbody>
                {recognizedBodies.map((body, index) => (
                  <tr
                    key={index}
                    className="border-b border-gray-100 hover:bg-gray-50"
                  >
                    <td className="py-3 px-4 text-gray-900">
                      {language === 'bm' ? body.countryBM : body.country}
                    </td>
                    <td className="py-3 px-4 text-gray-900">
                      {language === 'bm' ? body.bodyBM : body.body}
                    </td>
                    <td className="py-3 px-4">
                      <a
                        href={body.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary-green hover:text-primary-green-dark text-sm"
                      >
                        {language === 'en'
                          ? 'Visit Website'
                          : 'Lawati Laman Web'}
                      </a>
                    </td>
                    <td className="py-3 px-4">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {language === 'en' ? 'Active' : 'Aktif'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Benefits of Recognition */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Benefits of Mutual Recognition'
              : 'Faedah Pengiktirafan Bersama'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-bg-light-green rounded-lg">
              <div className="w-12 h-12 bg-primary-green rounded-full flex items-center justify-center mx-auto mb-3">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en'
                  ? 'Faster Market Access'
                  : 'Akses Pasaran Lebih Pantas'}
              </h4>
              <p className="text-gray-600 text-sm">
                {language === 'en'
                  ? 'Reduced certification time for international trade'
                  : 'Masa pensijilan yang dikurangkan untuk perdagangan antarabangsa'}
              </p>
            </div>
            <div className="text-center p-4 bg-bg-light-green rounded-lg">
              <div className="w-12 h-12 bg-primary-green rounded-full flex items-center justify-center mx-auto mb-3">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                  />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en' ? 'Cost Reduction' : 'Pengurangan Kos'}
              </h4>
              <p className="text-gray-600 text-sm">
                {language === 'en'
                  ? 'Lower certification costs for businesses'
                  : 'Kos pensijilan yang lebih rendah untuk perniagaan'}
              </p>
            </div>
            <div className="text-center p-4 bg-bg-light-green rounded-lg">
              <div className="w-12 h-12 bg-primary-green rounded-full flex items-center justify-center mx-auto mb-3">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en' ? 'Quality Assurance' : 'Jaminan Kualiti'}
              </h4>
              <p className="text-gray-600 text-sm">
                {language === 'en'
                  ? 'Maintained high standards across borders'
                  : 'Piawaian tinggi yang dikekalkan merentas sempadan'}
              </p>
            </div>
          </div>
        </div>

        {/* Application Process */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Recognition Application Process'
              : 'Proses Permohonan Pengiktirafan'}
          </h3>
          <div className="space-y-4">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">1</span>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-1">
                  {language === 'en' ? 'Initial Assessment' : 'Penilaian Awal'}
                </h4>
                <p className="text-gray-600 text-sm">
                  {language === 'en'
                    ? 'Preliminary evaluation of certification standards and procedures'
                    : 'Penilaian awal piawaian dan prosedur pensijilan'}
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">2</span>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-1">
                  {language === 'en'
                    ? 'Documentation Review'
                    : 'Semakan Dokumentasi'}
                </h4>
                <p className="text-gray-600 text-sm">
                  {language === 'en'
                    ? 'Comprehensive review of policies, procedures, and standards'
                    : 'Semakan komprehensif polisi, prosedur, dan piawaian'}
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">3</span>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-1">
                  {language === 'en'
                    ? 'On-site Evaluation'
                    : 'Penilaian Di Tapak'}
                </h4>
                <p className="text-gray-600 text-sm">
                  {language === 'en'
                    ? 'Physical assessment of facilities and operations'
                    : 'Penilaian fizikal kemudahan dan operasi'}
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">4</span>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-1">
                  {language === 'en'
                    ? 'Agreement Negotiation'
                    : 'Rundingan Perjanjian'}
                </h4>
                <p className="text-gray-600 text-sm">
                  {language === 'en'
                    ? 'Negotiation and finalization of mutual recognition agreement'
                    : 'Rundingan dan pemuktamadan perjanjian pengiktirafan bersama'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="card bg-bg-light-green">
          <h3 className="text-xl font-bold mb-4 text-gray-900">
            {language === 'en'
              ? 'For More Information'
              : 'Untuk Maklumat Lanjut'}
          </h3>
          <p className="text-gray-600 mb-4">
            {language === 'en'
              ? 'For inquiries about foreign Halal certification body recognition or mutual recognition agreements, please contact our International Relations unit.'
              : 'Untuk pertanyaan mengenai pengiktirafan badan pensijilan Halal asing atau perjanjian pengiktirafan bersama, sila hubungi unit Hubungan Antarabangsa kami.'}
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="text-sm">
              <span className="font-medium">
                {language === 'en' ? 'Phone:' : 'Telefon:'}
              </span>{' '}
              03-8892 5000
            </div>
            <div className="text-sm">
              <span className="font-medium">
                {language === 'en' ? 'Email:' : 'E-mel:'}
              </span>{' '}
              <EMAIL>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}
