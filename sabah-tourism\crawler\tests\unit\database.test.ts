import { afterEach, beforeEach, describe, expect, it } from 'bun:test';
import { getTestDatabaseConfig } from '../../config/database.config.js';
import { DatabaseManager } from '../../src/crawlers/base/DatabaseManager.js';

describe('DatabaseManager', () => {
  let database: DatabaseManager;

  beforeEach(() => {
    database = new DatabaseManager(getTestDatabaseConfig());
  });

  afterEach(() => {
    database.close();
  });

  describe('Session Management', () => {
    it('should create and retrieve a session', () => {
      const sessionData = {
        platform: 'douyin',
        keywords: JSON.stringify(['test']),
        status: 'running' as const,
        config: JSON.stringify({ test: true }),
      };

      const sessionId = database.createSession(sessionData);
      expect(sessionId).toBeGreaterThan(0);

      const retrievedSession = database.getSession(sessionId);
      expect(retrievedSession).toBeTruthy();
      expect(retrievedSession?.platform).toBe('douyin');
      expect(retrievedSession?.status).toBe('running');
    });

    it('should update session status', () => {
      const sessionId = database.createSession({
        platform: 'douyin',
        keywords: JSON.stringify(['test']),
        status: 'running',
        config: JSON.stringify({}),
      });

      database.updateSession(sessionId, {
        status: 'completed',
        completed_at: new Date(),
      });

      const session = database.getSession(sessionId);
      expect(session?.status).toBe('completed');
      expect(session?.completed_at).toBeTruthy();
    });
  });

  describe('Post Management', () => {
    it('should insert and retrieve a post', () => {
      const postData = {
        platform: 'douyin',
        post_id: 'test123',
        url: 'https://douyin.com/video/test123',
        title: 'Test Post',
        content: 'Test content',
        author_username: 'testuser',
        likes_count: 100,
        comments_count: 10,
        shares_count: 5,
      };

      const postId = database.insertPost(postData);
      expect(postId).toBeGreaterThan(0);

      const retrievedPost = database.getPost('douyin', 'test123');
      expect(retrievedPost).toBeTruthy();
      expect(retrievedPost?.title).toBe('Test Post');
      expect(retrievedPost?.author_username).toBe('testuser');
    });

    it('should handle duplicate posts', () => {
      const postData = {
        platform: 'douyin',
        post_id: 'duplicate123',
        url: 'https://douyin.com/video/duplicate123',
        title: 'Original Title',
      };

      // Insert first time
      const firstId = database.insertPost(postData);

      // Insert again with updated title
      const updatedData = { ...postData, title: 'Updated Title' };
      const secondId = database.insertPost(updatedData);

      // Should update existing record
      const post = database.getPost('douyin', 'duplicate123');
      expect(post?.title).toBe('Updated Title');
    });
  });

  describe('Media Files', () => {
    it('should insert and retrieve media files', () => {
      // First create a post
      const postId = database.insertPost({
        platform: 'douyin',
        post_id: 'media_test',
        url: 'https://douyin.com/video/media_test',
      });

      const mediaData = {
        post_id: postId,
        media_type: 'video' as const,
        file_name: 'test_video.mp4',
        file_path: './output/douyin/test/test_video.mp4',
        original_url: 'https://example.com/video.mp4',
        file_size: 1024000,
        duration: 30,
        width: 1920,
        height: 1080,
      };

      const mediaId = database.insertMediaFile(mediaData);
      expect(mediaId).toBeGreaterThan(0);

      const mediaFiles = database.getMediaFiles(postId);
      expect(mediaFiles).toHaveLength(1);
      expect(mediaFiles[0]?.file_name).toBe('test_video.mp4');
      expect(mediaFiles[0]?.media_type).toBe('video');
    });
  });

  describe('Crawl Attempts', () => {
    it('should manage crawl attempts', () => {
      // Create a session first
      const sessionId = database.createSession({
        platform: 'douyin',
        keywords: JSON.stringify(['test']),
        status: 'running',
        config: JSON.stringify({}),
      });

      const attemptData = {
        session_id: sessionId,
        post_url: 'https://douyin.com/video/attempt_test',
        keyword: 'test',
        status: 'pending' as const,
      };

      const attemptId = database.insertAttempt(attemptData);
      expect(attemptId).toBeGreaterThan(0);

      // Update attempt status
      database.updateAttempt(attemptId, {
        status: 'success',
        completed_at: new Date(),
      });

      const pendingAttempts = database.getPendingAttempts(sessionId);
      expect(pendingAttempts).toHaveLength(0); // Should be empty since we marked it as success
    });
  });

  describe('Statistics', () => {
    it('should return database statistics', () => {
      // Add some test data
      const postId = database.insertPost({
        platform: 'douyin',
        post_id: 'stats_test',
        url: 'https://douyin.com/video/stats_test',
      });

      database.insertMediaFile({
        post_id: postId,
        media_type: 'video',
        file_name: 'stats_video.mp4',
        file_path: './output/stats_video.mp4',
      });

      const sessionId = database.createSession({
        platform: 'douyin',
        keywords: JSON.stringify(['stats']),
        status: 'running',
        config: JSON.stringify({}),
      });

      const stats = database.getStats();

      expect(stats.totalPosts).toBeGreaterThanOrEqual(1);
      expect(stats.totalMediaFiles).toBeGreaterThanOrEqual(1);
      expect(stats.totalSessions).toBeGreaterThanOrEqual(1);
      expect(stats.platformBreakdown).toHaveProperty('douyin');
    });
  });
});
