import { Hono } from 'hono';
import {
  createContact,
  deleteContact,
  getContact,
  getContacts,
  updateContact,
} from '@/controllers/adminContactsController';
import { authenticateAdmin } from '@/middleware/auth';

const contactsRouter = new Hono();

// Apply authentication middleware to all routes
contactsRouter.use('*', authenticateAdmin);

contactsRouter.post('/', createContact);
contactsRouter.get('/', getContacts);
contactsRouter.get('/:id', getContact);
contactsRouter.put('/:id', updateContact);
contactsRouter.patch('/:id', updateContact); // Support both PUT and PATCH
contactsRouter.delete('/:id', deleteContact);

export default contactsRouter;
