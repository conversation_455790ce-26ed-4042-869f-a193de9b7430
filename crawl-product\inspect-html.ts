#!/usr/bin/env bun

import puppeteer from 'puppeteer';

async function inspectHTML() {
  console.log('🔍 Inspecting HTML structure...');

  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });

  const page = await browser.newPage();
  await page.setViewport({ width: 1920, height: 1080 });

  try {
    const url =
      'https://myehalal.halal.gov.my/portal-halal/v1/index.php?data=ZGlyZWN0b3J5L2luZGV4X2RpcmVjdG9yeTs7Ozs=&negeri=&category=&cari=';
    console.log('📍 Navigating to:', url);

    await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 60000 });

    // Wait for content to load
    await new Promise((resolve) => setTimeout(resolve, 5000));

    // Get detailed HTML structure
    const analysis = await page.evaluate(() => {
      const result: any = {
        title: document.title,
        tables: [],
        allText: document.body.textContent?.substring(0, 2000) || '',
        forms: [],
        links: [],
      };

      // Analyze tables
      const tables = document.querySelectorAll('table');
      tables.forEach((table, index) => {
        const rows = table.querySelectorAll('tr');
        const tableInfo: any = {
          index,
          rowCount: rows.length,
          columnCount: 0,
          headers: [],
          sampleRows: [],
          innerHTML: table.innerHTML.substring(0, 1000),
        };

        if (rows.length > 0) {
          const firstRow = rows[0];
          const cells = firstRow.querySelectorAll('td, th');
          tableInfo.columnCount = cells.length;

          // Get headers
          cells.forEach((cell) => {
            tableInfo.headers.push(cell.textContent?.trim() || '');
          });

          // Get sample data rows
          for (let i = 1; i < Math.min(rows.length, 4); i++) {
            const row = rows[i];
            const rowCells = row.querySelectorAll('td, th');
            const rowData: string[] = [];
            rowCells.forEach((cell) => {
              rowData.push(cell.textContent?.trim() || '');
            });
            tableInfo.sampleRows.push(rowData);
          }
        }

        result.tables.push(tableInfo);
      });

      // Analyze forms
      const forms = document.querySelectorAll('form');
      forms.forEach((form, index) => {
        result.forms.push({
          index,
          action: form.getAttribute('action') || '',
          method: form.getAttribute('method') || '',
          inputs: Array.from(form.querySelectorAll('input')).map((input) => ({
            type: input.getAttribute('type') || '',
            name: input.getAttribute('name') || '',
            value: input.getAttribute('value') || '',
          })),
        });
      });

      // Analyze pagination links
      const links = document.querySelectorAll('a');
      links.forEach((link) => {
        const text = link.textContent?.trim() || '';
        const href = link.getAttribute('href') || '';

        if (
          text &&
          href &&
          (text.toLowerCase().includes('next') ||
            text.toLowerCase().includes('previous') ||
            text.toLowerCase().includes('seterusnya') ||
            text.toLowerCase().includes('sebelum') ||
            /^\d+$/.test(text) ||
            text.includes('>') ||
            text.includes('<'))
        ) {
          result.links.push({ text, href });
        }
      });

      return result;
    });

    console.log('\n📊 ANALYSIS RESULTS:');
    console.log('='.repeat(50));
    console.log('📄 Page Title:', analysis.title);
    console.log('📝 Page Text Sample:', analysis.allText.substring(0, 500));

    console.log('\n🗂️ TABLES FOUND:', analysis.tables.length);
    analysis.tables.forEach((table: any, index: number) => {
      console.log(`\n  Table ${index + 1}:`);
      console.log(`    Rows: ${table.rowCount}, Columns: ${table.columnCount}`);
      console.log(`    Headers: [${table.headers.join(', ')}]`);
      console.log('    Sample rows:', table.sampleRows);
      console.log(`    HTML sample: ${table.innerHTML.substring(0, 200)}...`);
    });

    console.log('\n📋 FORMS FOUND:', analysis.forms.length);
    analysis.forms.forEach((form: any, index: number) => {
      console.log(`  Form ${index + 1}: ${form.method} ${form.action}`);
      console.log('    Inputs:', form.inputs);
    });

    console.log('\n🔗 PAGINATION LINKS:', analysis.links.length);
    analysis.links.forEach((link: any) => {
      console.log(`    "${link.text}" -> ${link.href}`);
    });
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await browser.close();
  }
}

inspectHTML();
