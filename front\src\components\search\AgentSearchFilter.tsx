'use client';

import {
  Calendar,
  CheckCircle,
  Filter,
  Search,
  SlidersHorizontal,
  Star,
  Users,
  X,
  XCircle,
} from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

export interface AgentFilterCriteria {
  searchTerm: string;
  status: 'all' | 'online' | 'offline';
  role: 'all' | 'agent' | 'supervisor';
  performanceRating: 'all' | 'excellent' | 'good' | 'average' | 'poor';
  availability: 'all' | 'available' | 'busy' | 'away';
  dateRange: {
    from: string;
    to: string;
  };
  sessionCount: {
    min: number;
    max: number;
  };
  responseTime: {
    min: number; // seconds
    max: number; // seconds
  };
  satisfactionScore: {
    min: number;
    max: number;
  };
}

interface AgentSearchFilterProps {
  onFilterChange: (filters: AgentFilterCriteria) => void;
  totalResults: number;
  isLoading?: boolean;
}

const defaultFilters: AgentFilterCriteria = {
  searchTerm: '',
  status: 'all',
  role: 'all',
  performanceRating: 'all',
  availability: 'all',
  dateRange: {
    from: '',
    to: '',
  },
  sessionCount: {
    min: 0,
    max: 1000,
  },
  responseTime: {
    min: 0,
    max: 300,
  },
  satisfactionScore: {
    min: 1,
    max: 5,
  },
};

export function AgentSearchFilter({
  onFilterChange,
  totalResults,
  isLoading,
}: AgentSearchFilterProps) {
  const [filters, setFilters] = useState<AgentFilterCriteria>(defaultFilters);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [searchDebounce, setSearchDebounce] = useState<NodeJS.Timeout | null>(
    null,
  );

  // Debounced search
  const handleSearchChange = useCallback(
    (value: string) => {
      if (searchDebounce) {
        clearTimeout(searchDebounce);
      }

      const timeout = setTimeout(() => {
        setFilters((prev) => ({ ...prev, searchTerm: value }));
      }, 300);

      setSearchDebounce(timeout);
    },
    [searchDebounce],
  );

  // Update parent when filters change
  useEffect(() => {
    onFilterChange(filters);
  }, [filters, onFilterChange]);

  const updateFilter = <K extends keyof AgentFilterCriteria>(
    key: K,
    value: AgentFilterCriteria[K],
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const resetFilters = () => {
    setFilters(defaultFilters);
  };

  const hasActiveFilters = () => {
    return (
      filters.searchTerm !== '' ||
      filters.status !== 'all' ||
      filters.role !== 'all' ||
      filters.performanceRating !== 'all' ||
      filters.availability !== 'all' ||
      filters.dateRange.from !== '' ||
      filters.dateRange.to !== '' ||
      filters.sessionCount.min !== defaultFilters.sessionCount.min ||
      filters.sessionCount.max !== defaultFilters.sessionCount.max ||
      filters.responseTime.min !== defaultFilters.responseTime.min ||
      filters.responseTime.max !== defaultFilters.responseTime.max ||
      filters.satisfactionScore.min !== defaultFilters.satisfactionScore.min ||
      filters.satisfactionScore.max !== defaultFilters.satisfactionScore.max
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      {/* Search Bar */}
      <div className="flex items-center space-x-4 mb-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search agents by name, email, or ID..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            onChange={(e) => handleSearchChange(e.target.value)}
            defaultValue={filters.searchTerm}
          />
        </div>

        <button
          type="button"
          onClick={() => setShowAdvanced(!showAdvanced)}
          className={`flex items-center px-4 py-2 border rounded-md transition-colors ${
            showAdvanced
              ? 'bg-blue-50 border-blue-300 text-blue-700'
              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
          }`}
        >
          <SlidersHorizontal className="h-4 w-4 mr-2" />
          Advanced Filters
        </button>

        {hasActiveFilters() && (
          <button
            type="button"
            onClick={resetFilters}
            className="flex items-center px-4 py-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors"
          >
            <X className="h-4 w-4 mr-2" />
            Clear All
          </button>
        )}
      </div>

      {/* Quick Filters */}
      <div className="flex flex-wrap gap-3 mb-4">
        <select
          value={filters.status}
          onChange={(e) => updateFilter('status', e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Status</option>
          <option value="online">Online</option>
          <option value="offline">Offline</option>
        </select>

        <select
          value={filters.role}
          onChange={(e) => updateFilter('role', e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Roles</option>
          <option value="agent">Agent</option>
          <option value="supervisor">Supervisor</option>
        </select>

        <select
          value={filters.performanceRating}
          onChange={(e) =>
            updateFilter('performanceRating', e.target.value as any)
          }
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Performance</option>
          <option value="excellent">Excellent (4.5+)</option>
          <option value="good">Good (4.0-4.4)</option>
          <option value="average">Average (3.0-3.9)</option>
          <option value="poor">Poor (&lt;3.0)</option>
        </select>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="border-t border-gray-200 pt-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Date Range
              </label>
              <div className="space-y-2">
                <input
                  type="date"
                  value={filters.dateRange.from}
                  onChange={(e) =>
                    updateFilter('dateRange', {
                      ...filters.dateRange,
                      from: e.target.value,
                    })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="From"
                />
                <input
                  type="date"
                  value={filters.dateRange.to}
                  onChange={(e) =>
                    updateFilter('dateRange', {
                      ...filters.dateRange,
                      to: e.target.value,
                    })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="To"
                />
              </div>
            </div>

            {/* Session Count Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Users className="inline h-4 w-4 mr-1" />
                Session Count ({filters.sessionCount.min} -{' '}
                {filters.sessionCount.max})
              </label>
              <div className="space-y-2">
                <input
                  type="range"
                  min="0"
                  max="1000"
                  value={filters.sessionCount.min}
                  onChange={(e) =>
                    updateFilter('sessionCount', {
                      ...filters.sessionCount,
                      min: Number.parseInt(e.target.value),
                    })
                  }
                  className="w-full"
                />
                <input
                  type="range"
                  min="0"
                  max="1000"
                  value={filters.sessionCount.max}
                  onChange={(e) =>
                    updateFilter('sessionCount', {
                      ...filters.sessionCount,
                      max: Number.parseInt(e.target.value),
                    })
                  }
                  className="w-full"
                />
              </div>
            </div>

            {/* Response Time Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <CheckCircle className="inline h-4 w-4 mr-1" />
                Response Time ({filters.responseTime.min}s -{' '}
                {filters.responseTime.max}s)
              </label>
              <div className="space-y-2">
                <input
                  type="range"
                  min="0"
                  max="300"
                  value={filters.responseTime.min}
                  onChange={(e) =>
                    updateFilter('responseTime', {
                      ...filters.responseTime,
                      min: Number.parseInt(e.target.value),
                    })
                  }
                  className="w-full"
                />
                <input
                  type="range"
                  min="0"
                  max="300"
                  value={filters.responseTime.max}
                  onChange={(e) =>
                    updateFilter('responseTime', {
                      ...filters.responseTime,
                      max: Number.parseInt(e.target.value),
                    })
                  }
                  className="w-full"
                />
              </div>
            </div>

            {/* Satisfaction Score Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Star className="inline h-4 w-4 mr-1" />
                Satisfaction Score ({filters.satisfactionScore.min} -{' '}
                {filters.satisfactionScore.max})
              </label>
              <div className="space-y-2">
                <input
                  type="range"
                  min="1"
                  max="5"
                  step="0.1"
                  value={filters.satisfactionScore.min}
                  onChange={(e) =>
                    updateFilter('satisfactionScore', {
                      ...filters.satisfactionScore,
                      min: Number.parseFloat(e.target.value),
                    })
                  }
                  className="w-full"
                />
                <input
                  type="range"
                  min="1"
                  max="5"
                  step="0.1"
                  value={filters.satisfactionScore.max}
                  onChange={(e) =>
                    updateFilter('satisfactionScore', {
                      ...filters.satisfactionScore,
                      max: Number.parseFloat(e.target.value),
                    })
                  }
                  className="w-full"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Results Summary */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <Filter className="h-4 w-4" />
          <span>
            {isLoading
              ? 'Searching...'
              : `${totalResults} agent${totalResults !== 1 ? 's' : ''} found`}
          </span>
          {hasActiveFilters() && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Filtered
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
