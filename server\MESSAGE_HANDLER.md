# Consolidated Message Handler Documentation

This document provides an overview of the `MessageHandlerService` class defined in the [messageHandler.ts](./src/services/messageHandler.ts) file. The class is responsible for handling incoming messages across various platforms, managing chat sessions, and integrating with AI services for response generation.

## Overview

The `MessageHandlerService` class is designed to process messages from different platforms such as web, WhatsApp, and Facebook Messenger. It maintains chat sessions, handles text and image messages, supports tool calling for extended functionality, and interacts with database and AI services.

## Key Methods and Their Purposes

### `handleIncomingMessage(request: ConsolidatedMessageRequest, env?: any): Promise<ConsolidatedMessageResponse>`

- **Purpose**: This is the primary method for processing incoming messages from all platforms. It manages session handling, message storage, and delegates the processing based on message type (text or image).
- **Functionality**: 
  - Merges default and request-specific configurations.
  - Checks if a web session is handed over to an agent, in which case it bypasses AI processing.
  - Retrieves or creates a chat session for the user.
  - Adds the user's message to the session and database (for web platform).
  - Trims message history to maintain a limit.
  - Delegates to specific handlers for text or image messages.
  - Stores and returns the AI-generated response.

### `handleTextMessage(session: ChatSession, config: MessageHandlerConfig, env?: any): Promise<ConsolidatedMessageResponse>`

- **Purpose**: Handles text messages with support for tool calling if enabled in the configuration.
- **Functionality**: 
  - If tool calling is disabled, falls back to a regular text message handler.
  - Iteratively processes tool calls up to a maximum iteration limit, executing tools and appending results to the message thread.
  - Returns the final AI response once no further tool calls are needed or if the iteration limit is reached.

### `handleRegularTextMessage(session: ChatSession, config: MessageHandlerConfig, env?: any): Promise<ConsolidatedMessageResponse>`

- **Purpose**: Processes text messages without tool calling, providing a straightforward interaction with the OpenAI service.
- **Functionality**: 
  - Prepares messages for OpenAI by excluding timestamps.
  - Sends the messages to OpenAI and retrieves the response.
  - Adds the assistant's response to the session and returns it.

### `handleImageMessage(session: ChatSession, imageUrl: string, prompt: string, config: MessageHandlerConfig, env?: any): Promise<ConsolidatedMessageResponse>`

- **Purpose**: Handles messages that include images, using OpenAI to analyze the image content.
- **Functionality**: 
  - Sends the image URL and a prompt to OpenAI for analysis.
  - Adds the analysis result as an assistant message to the session.
  - Returns the response with the analysis.

### `executeToolCall(toolCall: ToolCall, sessionId: string, env?: any): Promise<ToolCallResult>`

- **Purpose**: Executes a specific tool call as requested by the AI response, extending the functionality beyond simple text responses.
- **Functionality**: 
  - Identifies the tool to be called based on the function name.
  - Currently supports `search_halal_knowledge` for querying halal-related information.
  - Returns the result of the tool execution or an error if the tool is unknown or fails.

### `executeHalalKnowledgeSearch(toolCall: ToolCall, sessionId: string, env?: any): Promise<ToolCallResult>`

- **Purpose**: Specifically handles the `search_halal_knowledge` tool call to search for halal-related information.
- **Functionality**: 
  - Parses the arguments for the search query, maximum results, and minimum score.
  - Uses the `halalKnowledgeService` to perform the search and generate an answer.
  - Returns the search results including the answer and sources.

### `getOrCreateSession(sessionId: string, userId: string, platform: string, env?: any): Promise<ChatSession>`

- **Purpose**: Retrieves an existing chat session or creates a new one if it doesn't exist.
- **Functionality**: 
  - Checks in-memory sessions first, then the database for web platforms.
  - Creates a new session with a system prompt tailored to the platform if none exists.
  - Stores new sessions in the database for web platforms.

### `getSystemPrompt(platform: string): string`

- **Purpose**: Generates a system prompt for the AI based on the platform to tailor the interaction style.
- **Functionality**: 
  - Provides a base prompt for halal inquiries with language flexibility.
  - Adjusts the prompt for conciseness on WhatsApp and Facebook Messenger, or allows detailed responses for web chat.

### `trimMessageHistory(session: ChatSession, maxMessages: number): void`

- **Purpose**: Maintains the session's message history within a specified limit to manage token usage and performance.
- **Functionality**: 
  - Preserves system messages and keeps only the most recent user and assistant messages up to the limit.

### `getSession(sessionId: string): ChatSession | undefined`

- **Purpose**: Retrieves a session by its ID from in-memory storage.
- **Functionality**: 
  - Returns the session if it exists in the map, otherwise undefined.

### `clearSession(sessionId: string): void`

- **Purpose**: Removes a session from in-memory storage.
- **Functionality**: 
  - Deletes the session associated with the provided ID from the sessions map.

## Additional Notes

- The service uses a singleton-like pattern with `createMessageHandlerService()` to ensure a new instance is created for each request, avoiding I/O context issues in environments like Cloudflare Workers.
- The class integrates with other services like `DatabaseService`, `halalKnowledgeService`, and `openaiService` to manage data persistence, halal knowledge queries, and AI interactions respectively.

## Link to Source Code

For the complete implementation details, refer to the source file: [messageHandler.ts](./src/services/messageHandler.ts)
