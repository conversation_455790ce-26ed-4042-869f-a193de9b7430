'use client';

import {
  Cloud,
  Database,
  Facebook,
  Globe,
  LayoutDashboard,
  LogOut,
  MessageSquare,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/stores/auth';

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard' as const,
    icon: LayoutDashboard,
  },
  {
    name: 'Sites',
    href: '/sites' as const,
    icon: Globe,
  },
  {
    name: 'Users',
    href: '/users' as const,
    icon: Users,
  },
  {
    name: 'Collections',
    href: '/collections' as const,
    icon: Database,
  },
  {
    name: 'WhatsApp Config',
    href: '/configs/whatsapp' as const,
    icon: MessageSquare,
  },
  {
    name: 'Messenger Config',
    href: '/configs/messenger' as const,
    icon: Facebook,
  },
  {
    name: 'S3 Config',
    href: '/configs/s3' as const,
    icon: Cloud,
  },
];

export function Sidebar() {
  const pathname = usePathname();
  const { logout, user } = useAuthStore();

  const handleLogout = () => {
    logout();
    window.location.href = '/login';
  };

  return (
    <div className="flex flex-col h-full bg-gray-900 text-white">
      <div className="flex items-center h-16 px-4 border-b border-gray-700">
        <h1 className="text-xl font-bold">Halal Admin</h1>
      </div>

      <nav className="flex-1 px-4 py-4 space-y-2">
        {navigation.map((item) => {
          const isActive =
            pathname === item.href || pathname.startsWith(item.href + '/');
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                isActive
                  ? 'bg-gray-800 text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white',
              )}
            >
              <item.icon className="mr-3 h-5 w-5" />
              {item.name}
            </Link>
          );
        })}
      </nav>

      <div className="p-4 border-t border-gray-700">
        <div className="mb-3 text-sm">
          <p className="text-gray-400">Signed in as</p>
          <p className="font-medium">{user?.username}</p>
          <p className="text-xs text-gray-500">{user?.roles.join(', ')}</p>
        </div>
        <Button
          onClick={handleLogout}
          variant="ghost"
          size="sm"
          className="w-full justify-start text-gray-300 hover:text-white hover:bg-gray-700"
        >
          <LogOut className="mr-2 h-4 w-4" />
          Sign Out
        </Button>
      </div>
    </div>
  );
}
