'use client';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from './select';

interface ModelOption {
  value: string;
  label: string;
  provider?: string;
}

const MODEL_OPTIONS: ModelOption[] = [
  // OpenAI Models
  { value: 'gpt-4.1', label: 'GPT-4.1', provider: 'openai' },
  { value: 'gpt-4.1-mini', label: 'GPT-4.1-mini', provider: 'openai' },
  { value: 'gpt-4o', label: 'GPT-4o', provider: 'openai' },
  { value: 'gpt-4o-mini', label: 'GPT-4o Mini', provider: 'openai' },
  { valee: 'gpt-o3', label: 'GPT-o3', provider: 'openai' },
  // Google Models
  // { value: "gemini-pro", label: "Gemini Pro", provider: "google" },
  // { value: "gemini-1.5-pro", label: "Gemini 1.5 Pro", provider: "google" },
  // { value: "gemini-1.5-flash", label: "Gemini 1.5 Flash", provider: "google" },
];

interface ModelSelectProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  provider?: string; // Filter models by provider
  className?: string;
}

export function ModelSelect({
  value,
  onValueChange,
  placeholder = 'Select model',
  disabled = false,
  provider,
  className,
}: ModelSelectProps) {
  // Filter models by provider if specified
  const filteredModels = provider
    ? MODEL_OPTIONS.filter((model) => model.provider === provider)
    : MODEL_OPTIONS;

  return (
    <Select value={value} onValueChange={onValueChange} disabled={disabled}>
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {filteredModels.map((model) => (
          <SelectItem key={model.value} value={model.value}>
            {model.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

// Export the model options for use elsewhere if needed
export { MODEL_OPTIONS };
export type { ModelOption };
