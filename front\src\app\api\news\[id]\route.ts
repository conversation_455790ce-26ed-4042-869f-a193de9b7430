import { type NextRequest, NextResponse } from 'next/server';
import { news } from '@/data/content';
export const runtime = 'edge';
export const dynamic = 'force-dynamic';

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    const newsItem = news.find((item) => item.id === id);

    if (!newsItem) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not found',
          message: 'News item not found',
        },
        { status: 404 },
      );
    }

    return NextResponse.json({
      success: true,
      data: newsItem,
    });
  } catch (error) {
    console.error('News detail API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch news item',
      },
      { status: 500 },
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;
    const body = await request.json();

    const newsIndex = news.findIndex((item) => item.id === id);

    if (newsIndex === -1) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not found',
          message: 'News item not found',
        },
        { status: 404 },
      );
    }

    // Update news item (in real app, update database)
    const updatedNewsItem = {
      ...news[newsIndex],
      ...body,
      id, // Ensure ID doesn't change
    };

    return NextResponse.json({
      success: true,
      data: updatedNewsItem,
      message: 'News item updated successfully',
    });
  } catch (error) {
    console.error('Update news API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to update news item',
      },
      { status: 500 },
    );
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    const newsIndex = news.findIndex((item) => item.id === id);

    if (newsIndex === -1) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not found',
          message: 'News item not found',
        },
        { status: 404 },
      );
    }

    // Delete news item (in real app, delete from database)
    // news.splice(newsIndex, 1)

    return NextResponse.json({
      success: true,
      message: 'News item deleted successfully',
    });
  } catch (error) {
    console.error('Delete news API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to delete news item',
      },
      { status: 500 },
    );
  }
}
