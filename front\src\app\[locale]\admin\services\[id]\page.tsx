'use client';

export const runtime = 'edge';

import { ArrowLeft, Save } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Link, useRouter } from '@/i18n/navigation';
import { api } from '@/lib/api';
import type {
  ExternalAPIConfiguration,
  R2RConfiguration,
  ServiceConfiguration,
  ServiceUpdateRequest,
  SMTPConfiguration,
} from '@/types';

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

interface EditServicePageProps {
  params: {
    id: string;
  };
}

export default function EditServicePage({ params }: EditServicePageProps) {
  const router = useRouter();
  const serviceId = Number.parseInt(params.id, 10);

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [service, setService] = useState<ServiceConfiguration | null>(null);

  // Form state
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [isActive, setIsActive] = useState(true);
  const [configuration, setConfiguration] = useState<any>({});

  useEffect(() => {
    const fetchService = async () => {
      try {
        setLoading(true);
        const response = await api.admin.getServiceById(serviceId);
        const serviceData = response.data || response;

        if (serviceData) {
          setService(serviceData);
          setName(serviceData.name);
          setDescription(serviceData.description || '');
          setIsActive(serviceData.isActive);

          // Parse configuration JSON
          try {
            const config = JSON.parse(serviceData.configuration);
            setConfiguration(config);
          } catch (e) {
            console.error('Failed to parse service configuration:', e);
            setConfiguration({});
          }
        } else {
          setError('Service not found');
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch service');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    if (serviceId) {
      fetchService();
    }
  }, [serviceId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    try {
      const updateData: ServiceUpdateRequest = {
        name,
        description: description || undefined,
        isActive,
        configuration,
      };

      await api.admin.updateService(serviceId, updateData);
      router.push('/admin/services');
    } catch (err: any) {
      setError(err.message || 'Failed to update service');
      console.error(err);
    } finally {
      setSaving(false);
    }
  };

  const updateConfiguration = (key: string, value: any) => {
    setConfiguration((prev: any) => ({
      ...prev,
      [key]: value,
    }));
  };

  const renderConfigurationForm = () => {
    if (!service) return null;

    switch (service.type) {
      case 'R2R_RAG': {
        const r2rConfig = configuration as R2RConfiguration;
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">
              R2R RAG Configuration
            </h3>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                URL *
              </label>
              <input
                type="url"
                required
                value={r2rConfig.url || ''}
                onChange={(e) => updateConfiguration('url', e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Username
                </label>
                <input
                  type="text"
                  value={r2rConfig.username || ''}
                  onChange={(e) =>
                    updateConfiguration('username', e.target.value)
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <input
                  type="password"
                  value={r2rConfig.password || ''}
                  onChange={(e) =>
                    updateConfiguration('password', e.target.value)
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Collection ID
              </label>
              <input
                type="text"
                value={r2rConfig.collectionId || ''}
                onChange={(e) =>
                  updateConfiguration('collectionId', e.target.value)
                }
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Search Limit
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={r2rConfig.searchLimit || 10}
                  onChange={(e) =>
                    updateConfiguration(
                      'searchLimit',
                      Number.parseInt(e.target.value),
                    )
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Min Score
                </label>
                <input
                  type="number"
                  min="0"
                  max="1"
                  step="0.1"
                  value={r2rConfig.minScore || 0.2}
                  onChange={(e) =>
                    updateConfiguration(
                      'minScore',
                      Number.parseFloat(e.target.value),
                    )
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        );
      }

      case 'SMTP_PROVIDER': {
        const smtpConfig = configuration as SMTPConfiguration;
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">
              SMTP Provider Configuration
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Host *
                </label>
                <input
                  type="text"
                  required
                  value={smtpConfig.host || ''}
                  onChange={(e) => updateConfiguration('host', e.target.value)}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Port *
                </label>
                <input
                  type="number"
                  required
                  min="1"
                  max="65535"
                  value={smtpConfig.port || 587}
                  onChange={(e) =>
                    updateConfiguration('port', Number.parseInt(e.target.value))
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={smtpConfig.secure || false}
                  onChange={(e) =>
                    updateConfiguration('secure', e.target.checked)
                  }
                  className="mr-2"
                />
                <span className="text-sm font-medium text-gray-700">
                  Use SSL/TLS (port 465)
                </span>
              </label>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Username *
                </label>
                <input
                  type="text"
                  required
                  value={smtpConfig.username || ''}
                  onChange={(e) =>
                    updateConfiguration('username', e.target.value)
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Password *
                </label>
                <input
                  type="password"
                  required
                  value={smtpConfig.password || ''}
                  onChange={(e) =>
                    updateConfiguration('password', e.target.value)
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  From Email *
                </label>
                <input
                  type="email"
                  required
                  value={smtpConfig.fromEmail || ''}
                  onChange={(e) =>
                    updateConfiguration('fromEmail', e.target.value)
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  From Name
                </label>
                <input
                  type="text"
                  value={smtpConfig.fromName || ''}
                  onChange={(e) =>
                    updateConfiguration('fromName', e.target.value)
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        );
      }

      case 'EXTERNAL_API': {
        const apiConfig = configuration as ExternalAPIConfiguration;
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">
              External API Configuration
            </h3>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Base URL *
              </label>
              <input
                type="url"
                required
                value={apiConfig.baseUrl || ''}
                onChange={(e) => updateConfiguration('baseUrl', e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                API Key
              </label>
              <input
                type="password"
                value={apiConfig.apiKey || ''}
                onChange={(e) => updateConfiguration('apiKey', e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Timeout (ms)
                </label>
                <input
                  type="number"
                  min="1000"
                  max="300000"
                  value={apiConfig.timeout || 30000}
                  onChange={(e) =>
                    updateConfiguration(
                      'timeout',
                      Number.parseInt(e.target.value),
                    )
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Retry Attempts
                </label>
                <input
                  type="number"
                  min="0"
                  max="10"
                  value={apiConfig.retryAttempts || 3}
                  onChange={(e) =>
                    updateConfiguration(
                      'retryAttempts',
                      Number.parseInt(e.target.value),
                    )
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        );
      }

      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500" />
      </div>
    );
  }

  if (error || !service) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">
            {error || 'Service not found'}
          </span>
        </div>
        <div className="mt-4">
          <Link
            href="/admin/services"
            className="text-blue-500 hover:text-blue-700"
          >
            &larr; Back to Services
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="mb-6">
        <h1 className="text-2xl md:text-3xl font-semibold text-gray-800">
          Edit Service: {service.name}
        </h1>
      </div>

      {error && (
        <div className="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow-md rounded-lg p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">
              Basic Information
            </h3>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Service Name *
              </label>
              <input
                type="text"
                required
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Service Type
              </label>
              <input
                type="text"
                value={service.type}
                disabled
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-100 text-gray-500"
              />
              <p className="mt-1 text-sm text-gray-500">
                Service type cannot be changed after creation
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={isActive}
                  onChange={(e) => setIsActive(e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm font-medium text-gray-700">
                  Active
                </span>
              </label>
            </div>
          </div>

          {/* Configuration */}
          {renderConfigurationForm()}

          {/* Actions */}
          <div className="flex justify-between pt-6">
            <Link
              href="/admin/services"
              className="text-gray-500 hover:text-gray-700 inline-flex items-center"
            >
              <ArrowLeft size={18} className="mr-1" /> Back to Services
            </Link>
            <button
              type="submit"
              disabled={saving}
              className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white font-medium py-2 px-4 rounded-lg inline-flex items-center transition duration-150 ease-in-out"
            >
              <Save size={18} className="mr-2" />
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
