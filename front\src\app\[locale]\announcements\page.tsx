'use client';

export const runtime = 'edge';

import { Filter, Search, X } from 'lucide-react';
import { useState } from 'react';
import { AnnouncementList } from '@/components/announcement-card';
import { PageWrapper } from '@/components/page-wrapper';
import { useAnnouncements, useFilters, usePagination } from '@/hooks/use-api';
import { useLanguage } from '@/lib/language-context';
import { cn } from '@/lib/utils';

export default function AnnouncementsPage() {
  const { language } = useLanguage();
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const { page, limit, nextPage, prevPage, goToPage } = usePagination(1, 10);
  const { filters, updateFilter, clearAllFilters, hasActiveFilters } =
    useFilters({
      category: '',
      featured: undefined as boolean | undefined,
    });

  const {
    data: announcements,
    loading,
    error,
    refetch,
  } = useAnnouncements({
    ...filters,
    page,
    limit,
  });

  const breadcrumbs = [
    {
      label: 'Announcements',
      labelBM: 'Pengumuman',
    },
  ];

  const categories = [
    {
      value: '',
      label: language === 'en' ? 'All Categories' : 'Semua Kategori',
    },
    {
      value: 'announcement',
      label: language === 'en' ? 'Announcement' : 'Pengumuman',
    },
    {
      value: 'media-statement',
      label: language === 'en' ? 'Media Statement' : 'Kenyataan Media',
    },
    {
      value: 'withdrawal',
      label: language === 'en' ? 'Withdrawal' : 'Penarikan Balik',
    },
    { value: 'general', label: language === 'en' ? 'General' : 'Umum' },
  ];

  const handleLoadMore = () => {
    nextPage();
  };

  const handleFilterChange = (key: string, value: any) => {
    updateFilter(key, value);
    goToPage(1); // Reset to first page when filters change
  };

  const handleClearFilters = () => {
    clearAllFilters();
    setSearchQuery('');
    goToPage(1);
  };

  return (
    <PageWrapper
      title="Announcements"
      titleBM="Pengumuman"
      description="Latest announcements and updates from JAKIM's Halal Management Division."
      descriptionBM="Pengumuman dan kemas kini terkini daripada Bahagian Pengurusan Halal JAKIM."
      breadcrumbs={breadcrumbs}
    >
      {/* Filters and Search */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={cn(
                'flex items-center gap-2 px-4 py-2 border rounded-lg transition-colors',
                showFilters
                  ? 'bg-primary-green text-white border-primary-green'
                  : 'bg-white text-gray-700 border-gray-300 hover:border-primary-green',
              )}
            >
              <Filter className="w-4 h-4" />
              {language === 'en' ? 'Filters' : 'Penapis'}
            </button>

            {hasActiveFilters() && (
              <button
                onClick={handleClearFilters}
                className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-red-600 transition-colors"
              >
                <X className="w-4 h-4" />
                {language === 'en' ? 'Clear Filters' : 'Kosongkan Penapis'}
              </button>
            )}
          </div>

          <div className="relative w-full md:w-80">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={
                language === 'en'
                  ? 'Search announcements...'
                  : 'Cari pengumuman...'
              }
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-transparent"
            />
          </div>
        </div>

        {/* Filter Panel */}
        {showFilters && (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === 'en' ? 'Category' : 'Kategori'}
                </label>
                <select
                  value={filters.category}
                  onChange={(e) =>
                    handleFilterChange('category', e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-transparent"
                >
                  {categories.map((category) => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === 'en' ? 'Type' : 'Jenis'}
                </label>
                <select
                  value={
                    filters.featured === undefined
                      ? ''
                      : filters.featured.toString()
                  }
                  onChange={(e) => {
                    const value = e.target.value;
                    handleFilterChange(
                      'featured',
                      value === '' ? undefined : value === 'true',
                    );
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-transparent"
                >
                  <option value="">
                    {language === 'en' ? 'All Types' : 'Semua Jenis'}
                  </option>
                  <option value="true">
                    {language === 'en' ? 'Featured' : 'Ditampilkan'}
                  </option>
                  <option value="false">
                    {language === 'en' ? 'Regular' : 'Biasa'}
                  </option>
                </select>
              </div>

              <div className="flex items-end">
                <button onClick={refetch} className="w-full btn-secondary">
                  {language === 'en' ? 'Apply Filters' : 'Gunakan Penapis'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      {loading && (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-green" />
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <p className="text-red-700">
            {language === 'en'
              ? 'Error loading announcements:'
              : 'Ralat memuatkan pengumuman:'}{' '}
            {error}
          </p>
          <button
            onClick={refetch}
            className="mt-2 text-red-600 hover:text-red-800 font-medium"
          >
            {language === 'en' ? 'Try Again' : 'Cuba Lagi'}
          </button>
        </div>
      )}

      {announcements && announcements.length > 0 && (
        <AnnouncementList
          announcements={announcements}
          variant="default"
          showLoadMore={announcements.length === limit}
          onLoadMore={handleLoadMore}
        />
      )}

      {announcements && announcements.length === 0 && !loading && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">
            {language === 'en'
              ? 'No announcements found'
              : 'Tiada pengumuman ditemui'}
          </p>
          {hasActiveFilters() && (
            <button
              onClick={handleClearFilters}
              className="mt-4 text-primary-green hover:text-primary-green-dark font-medium"
            >
              {language === 'en'
                ? 'Clear filters to see all announcements'
                : 'Kosongkan penapis untuk melihat semua pengumuman'}
            </button>
          )}
        </div>
      )}

      {/* Pagination Info */}
      {announcements && announcements.length > 0 && (
        <div className="mt-8 text-center text-sm text-gray-600">
          {language === 'en'
            ? `Showing page ${page} of announcements`
            : `Menunjukkan halaman ${page} pengumuman`}
        </div>
      )}
    </PageWrapper>
  );
}
