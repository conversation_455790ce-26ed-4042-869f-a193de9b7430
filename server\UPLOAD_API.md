# Upload API Documentation

## Overview

The `/api/upload` endpoint provides file upload functionality that automatically uses the default S3 configuration for the site. Files are uploaded to S3 with automatic organization by site ID.

## Endpoints

### Upload File

**POST** `/api/upload`
**POST** `/api/sites/{siteId}/upload`

Uploads a file to S3 using the site's default S3 configuration.

#### Request

- **Content-Type**: `multipart/form-data`
- **Body**: Form data with a `file` field

#### Supported File Types

**Images:**
- `image/jpeg`
- `image/png`
- `image/gif`
- `image/webp`

**Audio:**
- `audio/mpeg`
- `audio/wav`
- `audio/webm`
- `audio/ogg`

#### File Size Limit

- Maximum file size: 10MB

#### Response

**Success (200):**
```json
{
  "type": "image",
  "url": "https://bucket-name.s3.amazonaws.com/uploads/1/uuid.png",
  "s3Key": "uploads/1/uuid.png",
  "originalFilename": "example.png",
  "size": 12345,
  "mimetype": "image/png"
}
```

**Error Responses:**
- `400`: No file uploaded, file too large, or invalid file type
- `500`: No S3 configuration found or upload failed

### Delete File

**DELETE** `/api/upload/{s3Key}`
**DELETE** `/api/sites/{siteId}/upload/{s3Key}`

Deletes a file from S3.

#### Parameters

- `s3Key`: The S3 key of the file to delete (e.g., `uploads/1/uuid.png`)

#### Response

**Success (200):**
```json
{
  "message": "File deleted successfully"
}
```

**Error Responses:**
- `400`: S3 key is required or invalid site ID
- `500`: No S3 configuration found or deletion failed

## Features

1. **Automatic S3 Configuration**: Uses the default S3 configuration for the site
2. **Site-based Organization**: Files are organized by site ID in S3 (`uploads/{siteId}/filename`)
3. **Unique Filenames**: Generates UUID-based filenames to prevent conflicts
4. **File Validation**: Validates file types and size limits
5. **Multi-site Support**: Supports both default site and site-specific uploads

## Usage Examples

### JavaScript/Fetch

```javascript
// Upload file
const formData = new FormData();
formData.append('file', fileInput.files[0]);

const response = await fetch('/api/upload', {
  method: 'POST',
  body: formData
});

const result = await response.json();
console.log('Uploaded:', result.url);

// Delete file
await fetch(`/api/upload/${result.s3Key}`, {
  method: 'DELETE'
});
```

### cURL

```bash
# Upload file
curl -X POST \
  -F "file=@example.png" \
  http://localhost:16001/api/upload

# Delete file
curl -X DELETE \
  http://localhost:16001/api/upload/uploads/1/uuid.png
```

## Configuration Requirements

The upload functionality requires:

1. **S3 Configuration**: At least one S3 configuration must exist for the site in the database
2. **Database Access**: The database service must be properly configured
3. **S3 Credentials**: Valid S3 credentials with upload/delete permissions

## Error Handling

The API provides detailed error messages for common issues:
- Missing files
- Invalid file types
- File size exceeded
- Missing S3 configuration
- S3 upload/delete failures
