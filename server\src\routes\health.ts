import { Hono } from 'hono';
import { testConnection } from '../db/connection';
import ConnectionManager from '../utils/connectionManager';
import { createSuccessResponse, createErrorResponse } from '../utils/response';

const health = new Hono();

// Basic health check
health.get('/', (c) => {
  return createSuccessResponse(c, {
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
  });
});

// Database health check
health.get('/db', async (c) => {
  try {
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      return createErrorResponse(c, 'DATABASE_URL not configured', 'Database connection failed', 500);
    }

    // Test database connection
    const isConnected = await testConnection(databaseUrl);

    return createSuccessResponse(c, {
      status: isConnected ? 'OK' : 'ERROR',
      database: {
        connected: isConnected,
        activeConnections: ConnectionManager.getConnectionCount(),
        url: databaseUrl.replace(/:[^:]*@/, ':***@'), // Hide password
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return createErrorResponse(
      c,
      error instanceof Error ? error.message : 'Unknown error',
      'Database health check failed',
      500
    );
  }
});

// Connection pool status
health.get('/connections', (c) => {
  return createSuccessResponse(c, {
    status: 'OK',
    connectionPool: {
      activeConnections: ConnectionManager.getConnectionCount(),
      maxConnections: 5, // Based on our pool configuration
    },
    timestamp: new Date().toISOString(),
  });
});

export default health;
