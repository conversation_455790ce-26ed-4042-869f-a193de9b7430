#!/usr/bin/env python3
"""
Test script to verify duplicate file detection in the crawler
"""

import asyncio
import tempfile
import shutil
from pathlib import Path
from crawl_website import CrawlerDatabase
import hashlib

def create_test_file(content: str, filename: str) -> tuple[str, int, str]:
    """Create a test file and return its path, size, and hash"""
    content_bytes = content.encode('utf-8')
    file_size = len(content_bytes)
    file_hash = hashlib.sha256(content_bytes).hexdigest()
    return content, file_size, file_hash

def test_duplicate_detection():
    """Test the duplicate detection functionality"""
    print("🧪 Testing duplicate file detection...")
    
    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        db_file = temp_path / "test_crawl.db"
        
        # Initialize database
        db = CrawlerDatabase(db_file)
        
        # Test data
        test_files = [
            {
                'url': 'https://example.com/file1.pdf',
                'filename': 'document1.pdf',
                'content': 'This is the content of document 1'
            },
            {
                'url': 'https://example.com/file2.pdf',
                'filename': 'document2.pdf',
                'content': 'This is the content of document 2'
            },
            {
                'url': 'https://another-site.com/same-file.pdf',
                'filename': 'document1.pdf',  # Same filename as first
                'content': 'This is the content of document 1'  # Same content as first
            },
            {
                'url': 'https://example.com/renamed.pdf',
                'filename': 'renamed_document.pdf',  # Different filename
                'content': 'This is the content of document 1'  # Same content as first
            }
        ]
        
        print("\n📝 Test Case 1: Adding first file")
        file1 = test_files[0]
        content1, size1, hash1 = create_test_file(file1['content'], file1['filename'])
        file_path1 = temp_path / file1['filename']
        
        # Check initial state
        assert not db.is_file_downloaded(file1['url']), "File should not be downloaded initially"
        assert db.is_file_downloaded_by_name(file1['filename']) is None, "Filename should not exist initially"
        assert db.is_file_downloaded_by_hash(hash1) is None, "Hash should not exist initially"
        
        # Add first file
        db.add_downloaded_file(file1['url'], str(file_path1), file1['filename'], size1, hash1)
        
        # Verify first file is recorded
        assert db.is_file_downloaded(file1['url']), "File should be marked as downloaded"
        assert db.is_file_downloaded_by_name(file1['filename']) == str(file_path1), "Filename should be found"
        assert db.is_file_downloaded_by_hash(hash1) == str(file_path1), "Hash should be found"
        print("✅ First file added successfully")
        
        print("\n📝 Test Case 2: Adding second file (different content)")
        file2 = test_files[1]
        content2, size2, hash2 = create_test_file(file2['content'], file2['filename'])
        file_path2 = temp_path / file2['filename']
        
        # Check that second file is not detected as duplicate
        assert not db.is_file_downloaded(file2['url']), "Second file URL should not be downloaded"
        assert db.is_file_downloaded_by_name(file2['filename']) is None, "Second filename should not exist"
        assert db.is_file_downloaded_by_hash(hash2) is None, "Second hash should not exist"
        
        # Add second file
        db.add_downloaded_file(file2['url'], str(file_path2), file2['filename'], size2, hash2)
        print("✅ Second file added successfully")
        
        print("\n📝 Test Case 3: Detecting duplicate by URL")
        # Try to add the same URL again
        assert db.is_file_downloaded(file1['url']), "First file URL should be detected as duplicate"
        print("✅ Duplicate URL detection working")
        
        print("\n📝 Test Case 4: Detecting duplicate by filename")
        file3 = test_files[2]
        existing_path = db.is_file_downloaded_by_name(file3['filename'])
        assert existing_path == str(file_path1), f"Duplicate filename should point to existing file: {existing_path}"
        print("✅ Duplicate filename detection working")
        
        print("\n📝 Test Case 5: Detecting duplicate by content hash")
        file4 = test_files[3]
        content4, size4, hash4 = create_test_file(file4['content'], file4['filename'])
        existing_path = db.is_file_downloaded_by_hash(hash4)
        assert existing_path == str(file_path1), f"Duplicate hash should point to existing file: {existing_path}"
        print("✅ Duplicate content hash detection working")
        
        print("\n📊 Database Statistics:")
        print(f"   Total downloaded files: {db.get_downloaded_count()}")
        
        # Test database migration
        print("\n📝 Test Case 6: Database migration")
        # This would be tested by creating an old database format and migrating it
        # For now, we'll just verify the migration method exists
        assert hasattr(db, '_migrate_database'), "Migration method should exist"
        print("✅ Database migration method available")
        
        print("\n🎉 All duplicate detection tests passed!")
        return True

if __name__ == "__main__":
    try:
        test_duplicate_detection()
        print("\n✅ Test completed successfully!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise
