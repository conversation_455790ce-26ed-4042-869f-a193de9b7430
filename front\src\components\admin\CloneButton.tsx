import { Copy } from 'lucide-react';
import { useParams } from 'next/navigation';

interface CloneButtonProps {
  entityType: string;
  entityId: number;
  className?: string;
  variant?: 'button' | 'dropdown-item';
}

export function CloneButton({ 
  entityType, 
  entityId, 
  className = '', 
  variant = 'button' 
}: CloneButtonProps) {
  const params = useParams();
  const locale = params.locale as string;

  const handleClick = () => {
    window.location.href = `/${locale}/admin/${entityType}/${entityId}/clone`;
  };

  if (variant === 'dropdown-item') {
    return (
      <div
        onClick={handleClick}
        className={`flex items-center px-2 py-1 text-sm cursor-pointer hover:bg-gray-100 ${className}`}
      >
        <Copy className="mr-2 h-4 w-4" />
        Clone {entityType.slice(0, -1)}
      </div>
    );
  }

  return (
    <button
      onClick={handleClick}
      className={`inline-flex items-center px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md ${className}`}
    >
      <Copy className="mr-1 h-4 w-4" />
      Clone
    </button>
  );
}