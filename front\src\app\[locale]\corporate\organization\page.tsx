'use client';

export const runtime = 'edge';

import type { BreadcrumbItem } from '@/components/breadcrumb';
import { PageWrapper } from '@/components/page-wrapper';
import { useLanguage } from '@/lib/language-context';

export default function OrganizationPage() {
  const { language } = useLanguage();

  const breadcrumbs: BreadcrumbItem[] = [
    {
      label: language === 'en' ? 'Home' : 'Laman <PERSON>',
      href: '/',
    },
    {
      label: language === 'en' ? 'Corporate' : 'Korporat',
      href: '/corporate',
    },
    {
      label: language === 'en' ? 'Organization Chart' : 'Carta Organisasi',
      href: '/corporate/organization',
    },
  ];

  return (
    <PageWrapper
      title="Organization Chart"
      titleBM="Carta Organisasi"
      description="View our organizational structure and key personnel in the Halal Management Division."
      descriptionBM="Lihat struktur organisasi kami dan kakitangan utama dalam Bahagian Pengurusan Halal."
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-8">
        {/* Introduction */}
        <div className="card">
          <h2 className="text-2xl font-bold mb-4 text-gray-900">
            {language === 'en'
              ? 'Organizational Structure'
              : 'Struktur Organisasi'}
          </h2>
          <p className="text-gray-600 leading-relaxed">
            {language === 'en'
              ? 'The Halal Management Division is structured to ensure efficient operations and effective service delivery. Our organizational structure reflects our commitment to maintaining the highest standards of Halal certification and compliance.'
              : 'Bahagian Pengurusan Halal distrukturkan untuk memastikan operasi yang cekap dan penyampaian perkhidmatan yang berkesan. Struktur organisasi kami mencerminkan komitmen kami untuk mengekalkan piawaian pensijilan dan pematuhan Halal tertinggi.'}
          </p>
        </div>

        {/* Organization Chart */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900 text-center">
            {language === 'en'
              ? 'Halal Management Division Structure'
              : 'Struktur Bahagian Pengurusan Halal'}
          </h3>

          {/* Director Level */}
          <div className="flex justify-center mb-8">
            <div className="bg-primary-green text-white p-4 rounded-lg text-center min-w-[200px]">
              <div className="font-bold text-lg">
                {language === 'en' ? 'Director' : 'Pengarah'}
              </div>
              <div className="text-sm mt-1">
                {language === 'en'
                  ? 'Halal Management Division'
                  : 'Bahagian Pengurusan Halal'}
              </div>
            </div>
          </div>

          {/* Deputy Director Level */}
          <div className="flex justify-center mb-8">
            <div className="bg-green-600 text-white p-4 rounded-lg text-center min-w-[200px]">
              <div className="font-bold">
                {language === 'en' ? 'Deputy Director' : 'Timbalan Pengarah'}
              </div>
              <div className="text-sm mt-1">
                {language === 'en'
                  ? 'Operations & Standards'
                  : 'Operasi & Piawaian'}
              </div>
            </div>
          </div>

          {/* Department Level */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <div className="bg-green-500 text-white p-4 rounded-lg text-center">
              <div className="font-semibold text-sm">
                {language === 'en'
                  ? 'Certification Department'
                  : 'Jabatan Pensijilan'}
              </div>
              <div className="text-xs mt-2">
                {language === 'en'
                  ? 'Application Processing'
                  : 'Pemprosesan Permohonan'}
              </div>
            </div>
            <div className="bg-green-500 text-white p-4 rounded-lg text-center">
              <div className="font-semibold text-sm">
                {language === 'en' ? 'Audit Department' : 'Jabatan Audit'}
              </div>
              <div className="text-xs mt-2">
                {language === 'en' ? 'Site Inspections' : 'Pemeriksaan Tapak'}
              </div>
            </div>
            <div className="bg-green-500 text-white p-4 rounded-lg text-center">
              <div className="font-semibold text-sm">
                {language === 'en'
                  ? 'Monitoring Department'
                  : 'Jabatan Pemantauan'}
              </div>
              <div className="text-xs mt-2">
                {language === 'en'
                  ? 'Compliance Monitoring'
                  : 'Pemantauan Pematuhan'}
              </div>
            </div>
            <div className="bg-green-500 text-white p-4 rounded-lg text-center">
              <div className="font-semibold text-sm">
                {language === 'en'
                  ? 'Standards Department'
                  : 'Jabatan Piawaian'}
              </div>
              <div className="text-xs mt-2">
                {language === 'en'
                  ? 'Standards Development'
                  : 'Pembangunan Piawaian'}
              </div>
            </div>
          </div>

          {/* Unit Level */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            <div className="bg-green-400 text-white p-3 rounded text-center text-sm">
              {language === 'en' ? 'Food Products Unit' : 'Unit Produk Makanan'}
            </div>
            <div className="bg-green-400 text-white p-3 rounded text-center text-sm">
              {language === 'en'
                ? 'Cosmetics & Pharmaceuticals Unit'
                : 'Unit Kosmetik & Farmaseutikal'}
            </div>
            <div className="bg-green-400 text-white p-3 rounded text-center text-sm">
              {language === 'en' ? 'Services Unit' : 'Unit Perkhidmatan'}
            </div>
            <div className="bg-green-400 text-white p-3 rounded text-center text-sm">
              {language === 'en'
                ? 'International Relations Unit'
                : 'Unit Hubungan Antarabangsa'}
            </div>
            <div className="bg-green-400 text-white p-3 rounded text-center text-sm">
              {language === 'en'
                ? 'Training & Development Unit'
                : 'Unit Latihan & Pembangunan'}
            </div>
            <div className="bg-green-400 text-white p-3 rounded text-center text-sm">
              {language === 'en' ? 'IT & Systems Unit' : 'Unit IT & Sistem'}
            </div>
          </div>
        </div>

        {/* Key Personnel */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Key Personnel' : 'Kakitangan Utama'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="w-16 h-16 bg-primary-green rounded-full flex items-center justify-center mx-auto mb-3">
                <svg
                  className="w-8 h-8 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900">
                {language === 'en' ? 'Director' : 'Pengarah'}
              </h4>
              <p className="text-sm text-gray-600 mt-1">
                {language === 'en'
                  ? 'Overall leadership and strategic direction'
                  : 'Kepimpinan keseluruhan dan hala tuju strategik'}
              </p>
            </div>
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg
                  className="w-8 h-8 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900">
                {language === 'en' ? 'Deputy Director' : 'Timbalan Pengarah'}
              </h4>
              <p className="text-sm text-gray-600 mt-1">
                {language === 'en'
                  ? 'Operations management and coordination'
                  : 'Pengurusan operasi dan penyelarasan'}
              </p>
            </div>
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg
                  className="w-8 h-8 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900">
                {language === 'en' ? 'Department Heads' : 'Ketua Jabatan'}
              </h4>
              <p className="text-sm text-gray-600 mt-1">
                {language === 'en'
                  ? 'Specialized department leadership'
                  : 'Kepimpinan jabatan khusus'}
              </p>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="card">
          <h3 className="text-xl font-bold mb-4 text-gray-900">
            {language === 'en'
              ? 'Contact Our Division'
              : 'Hubungi Bahagian Kami'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en' ? 'General Inquiries' : 'Pertanyaan Am'}
              </h4>
              <div className="space-y-1 text-sm text-gray-600">
                <p>
                  <span className="font-medium">
                    {language === 'en' ? 'Phone:' : 'Telefon:'}
                  </span>{' '}
                  03-8892 5000
                </p>
                <p>
                  <span className="font-medium">
                    {language === 'en' ? 'Email:' : 'E-mel:'}
                  </span>{' '}
                  <EMAIL>
                </p>
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en' ? 'Office Hours' : 'Waktu Pejabat'}
              </h4>
              <div className="space-y-1 text-sm text-gray-600">
                <p>
                  <span className="font-medium">
                    {language === 'en'
                      ? 'Monday - Thursday:'
                      : 'Isnin - Khamis:'}
                  </span>{' '}
                  8:30 AM - 4:30 PM
                </p>
                <p>
                  <span className="font-medium">
                    {language === 'en' ? 'Friday:' : 'Jumaat:'}
                  </span>{' '}
                  8:30 AM - 4:30 PM
                </p>
                <p>
                  <span className="font-medium">
                    {language === 'en' ? 'Weekend:' : 'Hujung Minggu:'}
                  </span>{' '}
                  {language === 'en' ? 'Closed' : 'Tutup'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}
