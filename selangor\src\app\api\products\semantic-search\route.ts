import { NextRequest, NextResponse } from 'next/server';
import {
  extractAnalyticsFromRequest,
  getHalalSelangorSiteId,
  trackSearchAnalytics,
} from '@/lib/analytics';
import { db } from '@/lib/db';
import { EmbeddingService } from '@/lib/embedding';
import {
  detectLanguageAndTranslate,
  type TranslationResult,
} from '@/lib/translation';
import type {
  ProductWithSimilarity,
  SemanticProductSearchResponse,
} from '@/types/product';
import { count, ilike, or, and, eq, inArray, sql, desc } from 'drizzle-orm';
import { products, categories, productCategories } from '@/lib/db/schema';

// Remove edge runtime to use Node.js runtime for database access

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('q');
  const page = Number.parseInt(searchParams.get('page') || '1');
  const limit = Number.parseInt(searchParams.get('limit') || '10');
  const minScore = Number.parseFloat(searchParams.get('minScore') || '0.3');
  const category = searchParams.get('category');


  console.log('Semantic product search query:', query);

  if (!query) {
    return NextResponse.json(
      { error: 'Query parameter is required for semantic search' },
      { status: 400 },
    );
  }

  try {
    // Detect language and generate translated terms using OpenAI
    const translationResult = await detectLanguageAndTranslate(query);
    const allQueries = [query, ...translationResult.translatedTerms];

    console.log('Semantic search queries:', {
      original: query,
      detectedLanguage: translationResult.detectedLanguage,
      confidence: translationResult.confidence,
      translatedTerms: translationResult.translatedTerms,
      allQueries,
    });

    // Generate embeddings for all queries
    const embeddingService = new EmbeddingService();
    const queryEmbeddings = await Promise.all(
      allQueries.map(async (searchQuery) => {
        const embeddingResult =
          await embeddingService.generateEmbedding(searchQuery);
        return { query: searchQuery, embedding: embeddingResult.embedding };
      }),
    );

    console.log('Generated embeddings for queries:', queryEmbeddings.length);

    // Use vector similarity search directly on products table
    const vectorSimilarityResults = await Promise.all(
      queryEmbeddings.map(async ({ query: searchQuery, embedding }) => {
        // Build WHERE conditions
        let whereConditions = `embedding IS NOT NULL AND site_id = ${getHalalSelangorSiteId()}`;

        // Add category filter if provided
        if (category) {
          // First check if category exists in categories table to get ID
          const categoryRecord = await db
            .select({ id: categories.id })
            .from(categories)
            .where(eq(categories.categoryName, category))
            .limit(1);

          if (categoryRecord.length > 0) {
            const categoryId = categoryRecord[0].id;
            // Get product IDs that belong to this category
            const productCategoryLinks = await db
              .select({ productId: productCategories.productId })
              .from(productCategories)
              .where(eq(productCategories.categoryId, categoryId));

            const productIdsFromCategory = productCategoryLinks.map((link: any) => link.productId);

            if (productIdsFromCategory.length > 0) {
              whereConditions += ` AND id = ANY(ARRAY[${productIdsFromCategory.join(',')}])`;
            } else {
              // Fallback to legacy category search
              whereConditions += ` AND (category ILIKE '%${category}%' OR subcategory ILIKE '%${category}%')`;
            }
          } else {
            // Category not found, use legacy search
            whereConditions += ` AND (category ILIKE '%${category}%' OR subcategory ILIKE '%${category}%')`;
          }
        }

        const similarityQuery = sql`
          SELECT
            id,
            product_name,
            company_name,
            certificate_number,
            certificate_type,
            issued_date,
            expiry_date,
            status,
            category,
            subcategory,
            address,
            state,
            country,
            contact_info,
            website,
            source_url,
            created_at,
            updated_at,
            1 - (embedding <=> ${`[${embedding.join(',')}]`}::vector) as similarity_score
          FROM products
          WHERE ${sql.raw(whereConditions)}
          ORDER BY embedding <=> ${`[${embedding.join(',')}]`}::vector
          LIMIT ${limit * 2}
        `;

        console.log('Executing similarity query for:', searchQuery);
        const results = await db.execute(similarityQuery);
        console.log('Raw DB results:', results);
        const rows = results.rows || results || [];
        return rows.map((row: any) => ({
          ...row,
          searchQuery,
          similarityScore: Number(row.similarity_score),
        }));
      }),
    );

    // Combine and deduplicate results from all queries
    const combinedResults = new Map<number, any>();

    vectorSimilarityResults.flat().forEach((result) => {
      const existingResult = combinedResults.get(result.id);
      if (
        !existingResult ||
        result.similarityScore > existingResult.similarityScore
      ) {
        combinedResults.set(result.id, {
          ...result,
          foundBySearchTerm: result.searchQuery, // Track which search term found this product
        });
      }
    });

    // Filter by minimum similarity score and sort
    const relevantProducts = Array.from(combinedResults.values())
      .filter((product) => product.similarityScore >= minScore)
      .sort((a, b) => b.similarityScore - a.similarityScore);

    console.log(
      `Found ${relevantProducts.length} products with similarity >= ${minScore}`,
    );

    if (relevantProducts.length === 0) {
      return NextResponse.json({
        query,
        searchType: 'semantic' as const,
        products: [],
        pagination: {
          page,
          limit,
          total: 0,
          hasMore: false,
          totalPages: 0,
        },
        ...(translationResult.translatedTerms.length > 0 && {
          translatedTerms: translationResult.translatedTerms,
          detectedLanguage: translationResult.detectedLanguage,
          confidence: translationResult.confidence,
          searchType: 'multilingual-semantic' as const,
        }),
      });
    }

    // Paginate results
    const total = relevantProducts.length;
    const totalPages = Math.ceil(total / limit);
    const offset = (page - 1) * limit;
    const hasMore = offset + limit < total;
    const paginatedProducts = relevantProducts.slice(offset, offset + limit);

    console.log('Pagination info:', {
      total,
      totalPages,
      page,
      limit,
      offset,
      paginatedCount: paginatedProducts.length,
      hasMore,
    });

    // Transform results to match expected format
    const transformedProducts: ProductWithSimilarity[] = paginatedProducts.map(
      (product: any) => ({
        id: product.id,
        productName: product.product_name,
        companyName: product.company_name,
        certificateNumber: product.certificate_number,
        certificateType: product.certificate_type,
        issuedDate: product.issued_date,
        expiryDate: product.expiry_date,
        status: product.status,
        category: product.category,
        subcategory: product.subcategory,
        address: product.address,
        state: product.state,
        country: product.country,
        contactInfo: product.contact_info,
        website: product.website,
        sourceUrl: product.source_url,
        createdAt: product.created_at,
        updatedAt: product.updated_at,
        similarityScore: product.similarityScore,
        foundBySearchTerm: product.foundBySearchTerm,
      }),
    );

    const response: SemanticProductSearchResponse = {
      query,
      searchType:
        translationResult.translatedTerms.length > 0
          ? 'multilingual-semantic'
          : 'semantic',
      products: transformedProducts,
      pagination: {
        page,
        limit,
        total,
        hasMore,
        totalPages,
      },
      ...(translationResult.translatedTerms.length > 0 && {
        translatedTerms: translationResult.translatedTerms,
        detectedLanguage: translationResult.detectedLanguage,
        confidence: translationResult.confidence,
      }),
    };

    console.log('Semantic product search response:', {
      query,
      detectedLanguage: translationResult.detectedLanguage,
      translatedTerms: translationResult.translatedTerms,
      productsCount: transformedProducts.length,
      total,
      page,
      hasMore,
      avgSimilarity:
        transformedProducts.length > 0
          ? transformedProducts.reduce(
              (sum, p) => sum + (p.similarityScore || 0),
              0,
            ) / transformedProducts.length
          : 0,
      queriesUsed: allQueries.length,
    });

    // Track search analytics
    const analyticsData = extractAnalyticsFromRequest(request);
    await trackSearchAnalytics({
      siteId: getHalalSelangorSiteId(),
      searchQuery: query,
      searchType: 'semantic',
      resultsCount: transformedProducts.length,
      hasResults: transformedProducts.length > 0,
      searchFilters: JSON.stringify({
        minScore,
        page,
        limit,
        category,
        detectedLanguage: translationResult.detectedLanguage,
        translatedTerms: translationResult.translatedTerms,
      }),
      ...analyticsData,
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error('Semantic product search error:', error);
    return NextResponse.json(
      {
        error: 'Failed to perform semantic search',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query, page = 1, limit = 10, minSimilarity = 0.7 } = body;

    if (!query) {
      return NextResponse.json(
        { error: 'Query is required for semantic search' },
        { status: 400 },
      );
    }

    // Use the same logic as GET but with POST body parameters
    const searchParams = new URLSearchParams({
      q: query,
      page: page.toString(),
      limit: limit.toString(),
      minSimilarity: minSimilarity.toString(),
    });

    // Create a new request with the search params
    const getRequest = new NextRequest(
      `${request.url}?${searchParams.toString()}`,
      { method: 'GET' },
    );

    return GET(getRequest);
  } catch (error) {
    console.error('Semantic product search POST error:', error);
    return NextResponse.json(
      {
        error: 'Failed to process semantic search request',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    );
  }
}
