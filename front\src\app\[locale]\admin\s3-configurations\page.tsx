'use client';

export const runtime = 'edge';

import {
  ArrowLeft,
  Database,
  Edit3,
  Globe,
  Key,
  PlusCircle,
  Server,
  Trash2,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Link, useRouter } from '@/i18n/navigation';
import { api } from '@/lib/api';
import type {
  AdminUser,
  S3ConfigurationBase,
  S3ConfigurationFull,
  S3ConfigurationResponse,
} from '@/types';
import { UserRole } from '@/types/roles';

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

export default function S3ConfigurationsPage() {
  const [configurations, setConfigurations] = useState<
    S3ConfigurationResponse[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const _router = useRouter();
  const [currentUser, setCurrentUser] = useState<AdminUser | null>(null);

  useEffect(() => {
    const fetchInitialData = async () => {
      setLoading(true);
      try {
        const meResponse = await api.admin.getMe();
        // Type assertion for user object within ApiResponse
        const userFromApi = meResponse.user as AdminUser | undefined;

        if (
          !userFromApi ||
          (userFromApi.role !== UserRole.ADMIN &&
            userFromApi.role !== UserRole.EDITOR)
        ) {
          setError(
            'Access Denied: You do not have permission to view this page.',
          );
          setLoading(false);
          // router.push('/admin/dashboard'); // Optional redirect
          return;
        }
        setCurrentUser(userFromApi);

        const s3Data = await api.admin.listS3Configurations();

        if (Array.isArray(s3Data)) {
          setConfigurations(s3Data);
        } else {
          console.warn(
            'Unexpected response structure for listS3Configurations:',
            s3Data,
          );
          setConfigurations([]);
        }
      } catch (err: unknown) {
        setError(
          (err as Error).message || 'Failed to fetch S3 configurations.',
        );
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    fetchInitialData();
  }, []);

  const handleDelete = async (configId: number, configName: string) => {
    if (
      window.confirm(
        `Are you sure you want to delete S3 configuration "${configName}"? This may affect document storage if it's in use.`,
      )
    ) {
      try {
        await api.admin.deleteS3Configuration(configId);
        setConfigurations((prevConfigs) =>
          prevConfigs.filter((config) => config.id !== configId),
        );
        // Add success notification
      } catch (err: unknown) {
        setError(
          (err as any).response?.data?.error ||
            (err as Error).message ||
            'Failed to delete S3 configuration.',
        );
        console.error(err);
        // Add error notification
      }
    }
  };

  const canPerformWriteActions = currentUser?.role === UserRole.ADMIN;

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
          role="alert"
        >
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="mt-4">
          <Link
            href="/admin/dashboard"
            className="text-blue-500 hover:text-blue-700"
          >
            &larr; Back to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  if (
    !currentUser ||
    (currentUser.role !== UserRole.ADMIN &&
      currentUser.role !== UserRole.EDITOR)
  ) {
    // This should be caught by the error state from useEffect, but as a safeguard:
    return (
      <div className="container mx-auto p-4">
        <div
          className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative"
          role="alert"
        >
          Access Denied. You do not have permission to view this page.
        </div>
        <div className="mt-4">
          <Link
            href="/admin/dashboard"
            className="text-blue-500 hover:text-blue-700"
          >
            &larr; Back to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl md:text-3xl font-semibold text-gray-800">
          S3 Configurations
        </h1>
        {canPerformWriteActions && (
          <Link
            href="/admin/s3-configurations/new"
            className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg inline-flex items-center transition duration-150 ease-in-out"
          >
            <PlusCircle size={20} className="mr-2" />
            Add New Configuration
          </Link>
        )}
      </div>

      {configurations.length === 0 ? (
        <div className="text-center py-10">
          <Database size={48} className="mx-auto text-gray-400 mb-4" />
          <p className="text-gray-500 text-lg">No S3 configurations found.</p>
          {canPerformWriteActions && (
            <p className="text-gray-400 mt-1">
              Add one to start managing S3 storage.
            </p>
          )}
        </div>
      ) : (
        <div className="bg-white shadow-md rounded-lg overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  ID
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Service Name
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Bucket Name
                </th>
                {currentUser?.role === UserRole.ADMIN && (
                  <>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Access Key ID
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Region
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Endpoint URL
                    </th>
                  </>
                )}
                {canPerformWriteActions && (
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Actions
                  </th>
                )}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {configurations.map((config) => {
                const isAdminView = currentUser?.role === UserRole.ADMIN;
                const fullConfig = config as S3ConfigurationFull; // Cast for admin view
                const baseConfig = config as S3ConfigurationBase; // For editor view or if not full

                return (
                  <tr
                    key={config.id}
                    className="hover:bg-gray-50 transition-colors"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                      {config.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {config.serviceName}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                      {baseConfig.bucketName}
                    </td>
                    {isAdminView &&
                      fullConfig.accessKeyId && ( // Check if fullConfig has accessKeyId, as EDITORs won't
                        <>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 flex items-center">
                            <Key size={14} className="mr-1 text-gray-400" />{' '}
                            {fullConfig.accessKeyId
                              ? `${fullConfig.accessKeyId.substring(0, 4)}...`
                              : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <Globe
                              size={14}
                              className="mr-1 text-gray-400 inline-block"
                            />{' '}
                            {fullConfig.region || 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <Server
                              size={14}
                              className="mr-1 text-gray-400 inline-block"
                            />{' '}
                            {fullConfig.endpointUrl || 'N/A'}
                          </td>
                        </>
                      )}

                    {canPerformWriteActions && (
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <Link
                          href={`/admin/s3-configurations/${config.id}`}
                          className="text-indigo-600 hover:text-indigo-900 mr-3 transition duration-150"
                        >
                          <Edit3 size={18} className="inline-block" />
                        </Link>
                        <button
                          type="button"
                          onClick={() =>
                            handleDelete(config.id, config.serviceName)
                          }
                          className="text-red-600 hover:text-red-900 transition duration-150"
                          title="Delete Configuration"
                        >
                          <Trash2 size={18} className="inline-block" />
                        </button>
                      </td>
                    )}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      )}
      <div className="mt-6">
        <Link
          href="/admin/dashboard"
          className="text-blue-500 hover:text-blue-700 inline-flex items-center"
        >
          <ArrowLeft size={18} className="mr-1" /> Back to Dashboard
        </Link>
      </div>
    </div>
  );
}
