'use client';

import axios from 'axios';
import {
  Headphones,
  Maximize2,
  MessageCircle,
  Mic,
  Mic<PERSON>ff,
  Minimize2,
  Send,
  User,
  X,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import ReactMarkdown from 'react-markdown';
import { TypingIndicator } from './LoadingSpinner';
import { SourcesDisplay } from './SourcesDisplay';

interface TextResult {
  text: string;
  type: 'vector' | 'graph' | string;
  document_id?: string | null;
  score: number;
  wordCount: number;
  metadata?: {
    title?: string;
    url?: string;
    source?: string;
    chunk_index?: number;
    document_type?: string;
    [key: string]: any;
  };
}

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'agent';
  content: string;
  timestamp: Date;
  imageUrl?: string;
  agentName?: string;
  sources?: TextResult[];
}

export default function TestChatWidget() {
  const t = useTranslations('chat');
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [uploadedImage, setUploadedImage] = useState<{
    url: string;
    file: File;
  } | null>(null);
  const [hasNewMessage, setHasNewMessage] = useState(false);
  const [isHandedOver, _setIsHandedOver] = useState(false);
  const [agentName, _setAgentName] = useState<string | null>(null);
  const [showHandoverButton, setShowHandoverButton] = useState(true);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  const initializeSession = useCallback(async () => {
    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/chat/session`,
      );
      setSessionId(response.data.sessionId);

      // Add welcome message
      const welcomeMessage: Message = {
        id: 'welcome',
        role: 'assistant',
        content: t('welcome'),
        timestamp: new Date(),
      };
      setMessages([welcomeMessage]);
    } catch (error: unknown) {
      console.error('Failed to initialize session:', error);
    }
  }, [t]);

  // Initialize chat session when widget opens
  useEffect(() => {
    if (isOpen && !sessionId) {
      initializeSession();
    }
  }, [isOpen, sessionId, initializeSession]);

  // Auto-scroll to show latest message when bot replies
  useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      // Only auto-scroll when bot replies (assistant role)
      if (lastMessage.role === 'assistant') {
        // Scroll to show the latest message, not necessarily to the bottom
        setTimeout(() => {
          if (messagesContainerRef.current) {
            const container = messagesContainerRef.current;
            const lastMessageElement =
              container.lastElementChild?.previousElementSibling; // Skip the messagesEndRef div
            if (lastMessageElement) {
              lastMessageElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start', // Show the start of the message
              });
            }
          }
        }, 100); // Small delay to ensure DOM is updated
      }
    }
  }, [messages]);

  // Show notification when chat is closed and new message arrives
  useEffect(() => {
    if (!isOpen && messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.role === 'assistant') {
        setHasNewMessage(true);
      }
    }
  }, [messages, isOpen]);

  // Clear notification and focus input when chat opens
  useEffect(() => {
    if (isOpen) {
      setHasNewMessage(false);
      inputRef.current?.focus();
    } else {
      // Reset maximize state when closing
      setIsMaximized(false);
    }
  }, [isOpen]);

  // Reset maximize state when minimizing
  useEffect(() => {
    if (isMinimized) {
      setIsMaximized(false);
    }
  }, [isMinimized]);

  const sendMessage = async (text?: string, imageUrl?: string) => {
    if (!sessionId || (!text && !imageUrl)) {
      return;
    }

    const messageText = text || inputText;
    if (!messageText && !imageUrl) {
      return;
    }

    // Add user message to UI
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: messageText,
      timestamp: new Date(),
      imageUrl,
    };
    setMessages((prev) => [...prev, userMessage]);
    setInputText('');
    setUploadedImage(null);
    setIsLoading(true);

    try {
      // Simulate bot response for testing
      setTimeout(() => {
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: `This is a test response to: "${messageText}". The maximize button and auto-scroll features are now working!`,
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, assistantMessage]);
        setIsLoading(false);

        // Refocus the input after the message is sent and loading is complete
        setTimeout(() => {
          inputRef.current?.focus();
        }, 100);
      }, 1000);
    } catch (error: unknown) {
      console.error('Failed to send message:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
      setIsLoading(false);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: () => {},
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp'],
      'audio/*': ['.mp3', '.wav', '.webm', '.ogg'],
    },
    maxFiles: 1,
    noClick: true,
    noKeyboard: true,
  });

  if (!isOpen) {
    return (
      <div className="fixed bottom-4 right-4 z-50 text-gray-600">
        <button
          type="button"
          onClick={() => setIsOpen(true)}
          className="relative p-4 rounded-full shadow-lg transition-all duration-200 bg-blue-600 hover:bg-blue-700 text-white hover:scale-105"
          aria-label="Open chat"
        >
          <MessageCircle className="w-6 h-6" />
          {hasNewMessage && (
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse" />
          )}
        </button>
      </div>
    );
  }

  return (
    <div
      className={`fixed z-50 transition-all duration-200 ${
        isMaximized ? 'inset-4' : 'bottom-4 right-4'
      }`}
    >
      <div
        className={`bg-white rounded-lg shadow-2xl border transition-all duration-200 flex flex-col ${
          isMaximized
            ? 'w-full h-full'
            : isMinimized
              ? 'max-w-[calc(100vw-4rem)] w-140 h-16'
              : 'max-w-[calc(100vw-4rem)] w-140 h-[32rem]'
        }`}
      >
        {/* Header */}
        <div className="bg-blue-600 text-white p-3 rounded-t-lg flex-shrink-0">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <MessageCircle className="w-5 h-5" />
              <span className="font-medium text-sm">Test Assistant</span>
            </div>
            <div className="flex items-center gap-1">
              <button
                type="button"
                onClick={() => setIsMinimized(!isMinimized)}
                className="p-1 hover:bg-blue-700 rounded"
                title={isMinimized ? 'Restore' : 'Minimize'}
              >
                {isMinimized ? (
                  <Maximize2 className="w-4 h-4" />
                ) : (
                  <Minimize2 className="w-4 h-4" />
                )}
              </button>
              <button
                type="button"
                onClick={() => setIsMaximized(!isMaximized)}
                className="p-1 hover:bg-blue-700 rounded"
                title={isMaximized ? 'Restore' : 'Maximize'}
              >
                {isMaximized ? (
                  <Minimize2 className="w-4 h-4" />
                ) : (
                  <Maximize2 className="w-4 h-4" />
                )}
              </button>
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="p-1 hover:bg-blue-700 rounded"
                title="Close"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Agent Status */}
          {!isMinimized && (
            <div className="flex justify-between items-center">
              <div className="text-xs text-blue-100">Test AI Assistant</div>
            </div>
          )}
        </div>

        {!isMinimized && (
          <>
            {/* Messages */}
            <div
              ref={messagesContainerRef}
              className="flex-1 overflow-y-auto p-4 space-y-4 min-h-0"
              {...getRootProps()}
            >
              <input {...getInputProps()} />
              {isDragActive && (
                <div className="absolute inset-0 bg-blue-50 border-2 border-dashed border-blue-300 rounded-lg flex items-center justify-center z-10">
                  <p className="text-blue-600 font-medium">
                    Drop files here...
                  </p>
                </div>
              )}

              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-sm px-4 py-3 rounded-lg ${
                      message.role === 'user'
                        ? 'bg-blue-600 text-white'
                        : message.role === 'agent'
                          ? 'bg-green-100 text-green-800 border border-green-200'
                          : 'bg-gray-200 text-gray-800'
                    }`}
                  >
                    <div className="prose prose-sm max-w-none">
                      <ReactMarkdown>{message.content}</ReactMarkdown>
                    </div>
                  </div>
                </div>
              ))}

              {isLoading && (
                <div className="flex justify-start">
                  <TypingIndicator />
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <div className="p-4 border-t flex-shrink-0">
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  if (inputText.length > 0) {
                    sendMessage(inputText);
                  }
                }}
                className="flex space-x-3"
              >
                <input
                  type="text"
                  ref={inputRef}
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  placeholder="Type your message..."
                  className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-800"
                  disabled={isLoading}
                  autoComplete="off"
                />

                <button
                  type="submit"
                  disabled={isLoading || inputText.length === 0}
                  className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white p-2 rounded-lg"
                >
                  <Send className="w-5 h-5" />
                </button>
              </form>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
