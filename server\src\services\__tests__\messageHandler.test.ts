import MessageHandlerService from '../messageHandler';

describe('MessageHandler Service', () => {
  let messageHandlerService: MessageHandlerService;
  const mockEnv = {
    OPENAI_API_KEY: process.env.OPENAI_API_KEY || 'test-key',
    R2R_BASE_URL: process.env.R2R_BASE_URL || 'http://localhost:8000',
    R2R_COLLECTION_ID: process.env.R2R_COLLECTION_ID || 'test-collection',
  };

  beforeEach(() => {
    messageHandlerService = new MessageHandlerService();
    jest.clearAllMocks();
  });

  describe('handleIncomingMessage', () => {
    it('should handle basic text message successfully', async () => {
      const messageData = {
        message: 'Hello, how are you?',
        sessionId: 'test-session-1',
        platform: 'web' as const,
        messageType: 'text' as const,
        userId: 'test-user-1',
        config: {
          maxMessageHistory: 10,
          enableToolCalling: false,
          defaultModel: 'gpt-4o-mini',
        },
      };

      const response = await messageHandlerService.handleIncomingMessage(
        messageData,
        mockEnv,
      );

      expect(response).toBeDefined();
      expect(response.success).toBe(true);
      expect(response.message).toBeDefined();
      expect(typeof response.message).toBe('string');
      expect(response.message.length).toBeGreaterThan(0);
    }, 15000); // Longer timeout for API calls

    it('should handle halal knowledge queries with tool calling', async () => {
      const messageData = {
        message: 'Is chicken halal in Islam?',
        sessionId: 'test-session-halal',
        platform: 'web' as const,
        messageType: 'text' as const,
        userId: 'test-user-halal',
        config: {
          maxMessageHistory: 10,
          enableToolCalling: true,
          defaultModel: 'gpt-4o-mini',
        },
      };

      const response = await messageHandlerService.handleIncomingMessage(
        messageData,
        mockEnv,
      );

      expect(response).toBeDefined();
      expect(response.success).toBe(true);
      expect(response.message).toBeDefined();
      expect(typeof response.message).toBe('string');
      // Should contain relevant halal information
      expect(response.message.toLowerCase()).toMatch(/halal|islam|chicken/);
    }, 20000);

    it('should respect message history limit', async () => {
      const sessionId = 'test-session-history';
      const maxHistory = 3;

      // Send multiple messages to build history
      for (let i = 1; i <= 5; i++) {
        const messageData = {
          message: `Message ${i}`,
          sessionId,
          platform: 'web' as const,
          messageType: 'text' as const,
          userId: 'test-user-history',
          config: {
            maxMessageHistory: maxHistory,
            enableToolCalling: false,
            defaultModel: 'gpt-4o-mini',
          },
        };

        const response = await messageHandlerService.handleIncomingMessage(
          messageData,
          mockEnv,
        );

        expect(response.success).toBe(true);
      }

      // The service should handle history internally
      // This test verifies it doesn't crash with multiple messages
      expect(true).toBe(true);
    }, 30000);

    it('should handle different platforms correctly', async () => {
      const platforms = ['whatsapp', 'facebook', 'web'] as const;
      const results = [];

      for (const platform of platforms) {
        const messageData = {
          message: `Hello from ${platform}`,
          sessionId: `test-session-${platform}`,
          platform,
          messageType: 'text' as const,
          userId: `test-user-${platform}`,
          config: {
            maxMessageHistory: 10,
            enableToolCalling: false,
            defaultModel: 'gpt-4o-mini',
          },
        };

        const response = await messageHandlerService.handleIncomingMessage(
          messageData,
          mockEnv,
        );

        results.push({
          platform,
          success: response.success,
          hasMessage: !!response.message,
        });

        expect(response.success).toBe(true);
        expect(response.message).toBeDefined();
      }

      // All platforms should work
      expect(results.every((r) => r.success && r.hasMessage)).toBe(true);
    }, 25000);

    it('should handle invalid input gracefully', async () => {
      const invalidMessageData = {
        message: '', // Empty message
        sessionId: '',
        platform: 'web' as const,
        messageType: 'text' as const,
        userId: '',
        config: {
          maxMessageHistory: 10,
          enableToolCalling: false,
          defaultModel: 'gpt-4o-mini',
        },
      };

      const response = await messageHandlerService.handleIncomingMessage(
        invalidMessageData,
        mockEnv,
      );

      // Should handle gracefully, either with error or default response
      expect(response).toBeDefined();
      expect(typeof response.success).toBe('boolean');
    });

    it('should handle missing environment variables', async () => {
      const emptyEnv = {};
      const messageData = global.testUtils.createTestMessage();

      const response = await messageHandlerService.handleIncomingMessage(
        messageData,
        emptyEnv,
      );

      // Should handle missing env vars gracefully
      expect(response).toBeDefined();
      expect(typeof response.success).toBe('boolean');
      if (!response.success) {
        expect(response.error).toBeDefined();
      }
    });
  });
});
