import { type NextRequest, NextResponse } from 'next/server';

export const runtime = 'edge';

export async function GET(
  request: NextRequest,
  { params }: { params: { documentId: string } },
) {
  try {
    const { documentId } = params;

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 },
      );
    }

    // For now, return a message indicating that direct download is not available
    return NextResponse.json(
      {
        error: 'Direct PDF download not available',
        message: 'PDF download functionality is being implemented.',
        documentId: documentId,
        suggestion: 'You can view the document content in the search results.',
      },
      { status: 404 },
    );
  } catch (error) {
    console.error('Document download error:', error);
    return NextResponse.json(
      { error: 'Failed to process download request' },
      { status: 500 },
    );
  }
}
