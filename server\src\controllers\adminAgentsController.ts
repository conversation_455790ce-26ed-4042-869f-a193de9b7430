import { and, eq, or, sql } from 'drizzle-orm';
import type { Context } from 'hono';
import { users } from '@/db/schema';
import DatabaseService from '@/services/database';

export const getAgents = async (c: Context) => {
  try {
    const db = c.get('db');
    const agents = await db
      .select()
      .from(users)
      .where(
        or(
          sql`'AGENT' = ANY(${users.roles})`,
          sql`'SUPERVISOR' = ANY(${users.roles})`,
        ),
      )
      .orderBy(sql`${users.createdAt} DESC`);

    return c.json({ success: true, data: agents });
  } catch (error: any) {
    console.error('Error fetching agents:', error);
    return c.json(
      { 
        success: false, 
        data: null, 
        error: 'Failed to fetch agents', 
        message: error.message 
      },
      500
    );
  }
};

export const getAgent = async (c: Context) => {
  try {
    const id = c.req.param('id');
    const db = c.get('db');
    
    const agentId = Number.parseInt(id);
    if (Number.isNaN(agentId)) {
      return c.json({ 
        success: false, 
        data: null, 
        error: 'Invalid agent ID' 
      }, 400);
    }

    const [agent] = await db
      .select()
      .from(users)
      .where(
        and(
          eq(users.id, agentId),
          or(
            sql`'AGENT' = ANY(${users.roles})`,
            sql`'SUPERVISOR' = ANY(${users.roles})`,
          ),
        ),
      );

    if (!agent) {
      return c.json({ 
        success: false, 
        data: null, 
        error: 'Agent not found' 
      }, 404);
    }

    return c.json({ success: true, data: agent });
  } catch (error: any) {
    console.error(`Error fetching agent ${c.req.param('id')}:`, error);
    return c.json(
      { 
        success: false, 
        data: null, 
        error: 'Failed to fetch agent', 
        message: error.message 
      },
      500
    );
  }
};

export const createAgent = async (c: Context) => {
  try {
    const { username, email, password, firstName, lastName, role } = await c.req.json();
    const db = c.get('db');

    if (!username || !password || !role) {
      return c.json(
        { 
          success: false, 
          data: null, 
          error: 'Username, password, and role are required' 
        },
        400
      );
    }

    if (!['AGENT', 'SUPERVISOR'].includes(role)) {
      return c.json(
        { 
          success: false, 
          data: null, 
          error: 'Role must be either AGENT or SUPERVISOR' 
        },
        400
      );
    }

    // Use DatabaseService for user creation to handle password hashing
    const env = c.get('env');
    const dbService = new DatabaseService(env);
    
    const userData = {
      username,
      email: email || null,
      password,
      firstName: firstName || null,
      lastName: lastName || null,
      roles: [role],
      isActive: true,
    };

    const newAgent = await dbService.createAdminUser(userData);
    return c.json({ success: true, data: newAgent }, 201);
  } catch (error: any) {
    console.error('Error creating agent:', error);
    if (error.message?.includes('already exists')) {
      return c.json(
        { 
          success: false, 
          data: null, 
          error: error.message 
        },
        409
      );
    }
    return c.json(
      { 
        success: false, 
        data: null, 
        error: 'Failed to create agent', 
        message: error.message 
      },
      500
    );
  }
};

export const updateAgent = async (c: Context) => {
  try {
    const id = c.req.param('id');
    const { username, email, password, firstName, lastName, role, isActive } = await c.req.json();
    const db = c.get('db');
    
    const agentId = Number.parseInt(id);
    if (Number.isNaN(agentId)) {
      return c.json({ 
        success: false, 
        data: null, 
        error: 'Invalid agent ID' 
      }, 400);
    }

    if (role && !['AGENT', 'SUPERVISOR'].includes(role)) {
      return c.json(
        { 
          success: false, 
          data: null, 
          error: 'Role must be either AGENT or SUPERVISOR' 
        },
        400
      );
    }

    // Check if agent exists
    const [existingAgent] = await db
      .select()
      .from(users)
      .where(
        and(
          eq(users.id, agentId),
          or(
            sql`'AGENT' = ANY(${users.roles})`,
            sql`'SUPERVISOR' = ANY(${users.roles})`,
          ),
        ),
      );

    if (!existingAgent) {
      return c.json({ 
        success: false, 
        data: null, 
        error: 'Agent not found' 
      }, 404);
    }

    // Use DatabaseService for user update to handle password hashing
    const env = c.get('env');
    const dbService = new DatabaseService(env);
    
    const updateData: any = {};
    if (username !== undefined) updateData.username = username;
    if (email !== undefined) updateData.email = email;
    if (password !== undefined) updateData.password = password;
    if (firstName !== undefined) updateData.firstName = firstName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (role !== undefined) updateData.roles = [role];
    if (isActive !== undefined) updateData.isActive = isActive;

    const updatedAgent = await dbService.updateAdminUser(agentId, updateData);
    return c.json({ success: true, data: updatedAgent });
  } catch (error: any) {
    console.error(`Error updating agent ${c.req.param('id')}:`, error);
    return c.json(
      { 
        success: false, 
        data: null, 
        error: 'Failed to update agent', 
        message: error.message 
      },
      500
    );
  }
};

export const deleteAgent = async (c: Context) => {
  try {
    const id = c.req.param('id');
    const db = c.get('db');
    
    const agentId = Number.parseInt(id);
    if (Number.isNaN(agentId)) {
      return c.json({ 
        success: false, 
        data: null, 
        error: 'Invalid agent ID' 
      }, 400);
    }

    // Check if agent exists
    const [existingAgent] = await db
      .select()
      .from(users)
      .where(
        and(
          eq(users.id, agentId),
          or(
            sql`'AGENT' = ANY(${users.roles})`,
            sql`'SUPERVISOR' = ANY(${users.roles})`,
          ),
        ),
      );

    if (!existingAgent) {
      return c.json({ 
        success: false, 
        data: null, 
        error: 'Agent not found' 
      }, 404);
    }

    // Use DatabaseService for user deletion
    const env = c.get('env');
    const dbService = new DatabaseService(env);
    
    await dbService.deleteAdminUser(agentId);
    return c.json({ success: true, data: null });
  } catch (error: any) {
    console.error(`Error deleting agent ${c.req.param('id')}:`, error);
    return c.json(
      { 
        success: false, 
        data: null, 
        error: 'Failed to delete agent', 
        message: error.message 
      },
      500
    );
  }
};

export const getAgentStats = async (c: Context) => {
  try {
    const db = c.get('db');
    const agents = await db
      .select()
      .from(users)
      .where(
        or(
          sql`'AGENT' = ANY(${users.roles})`,
          sql`'SUPERVISOR' = ANY(${users.roles})`,
        ),
      );

    const onlineAgents = agents.filter((agent) => agent.isOnline);

    // TODO: Get real session and handover data when implemented
    const stats = {
      totalAgents: agents.length,
      onlineAgents: onlineAgents.length,
      activeSessions: 0, // Placeholder
      pendingHandovers: 0, // Placeholder
    };

    return c.json({ success: true, data: stats });
  } catch (error: any) {
    console.error('Error fetching agent stats:', error);
    return c.json(
      { 
        success: false, 
        data: null, 
        error: 'Failed to fetch agent statistics', 
        message: error.message 
      },
      500
    );
  }
};
