import { act, render } from '@testing-library/react';
import type React from 'react';
import HomePage from '@/app/page';
import { ContactForm } from '@/components/contact-form';
import { QRScanner } from '@/components/qr-scanner';
import { SearchWidget } from '@/components/search-widget';

// Performance testing utilities
const measureRenderTime = (renderFn: () => void): number => {
  const start = performance.now();
  renderFn();
  const end = performance.now();
  return end - start;
};

const _measureAsyncRenderTime = async (
  renderFn: () => Promise<void>,
): Promise<number> => {
  const start = performance.now();
  await renderFn();
  const end = performance.now();
  return end - start;
};

const setViewport = (width: number, height: number) => {
  Object.defineProperty(window, 'innerWidth', { value: width, writable: true });
  Object.defineProperty(window, 'innerHeight', {
    value: height,
    writable: true,
  });
  window.dispatchEvent(new Event('resize'));
};

const VIEWPORTS = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  desktop: { width: 1920, height: 1080 },
};

const generateMockSearchResults = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    id: `${i + 1}`,
    companyName: `Company ${i + 1}`,
    productName: `Product ${i + 1}`,
    certificateNumber: `CERT-${String(i + 1).padStart(3, '0')}`,
    status: 'valid',
    expiryDate: '2024-12-31',
  }));
};

// Test language context provider
const TestLanguageProvider = ({ children }: { children: React.ReactNode }) => {
  return <div data-testid="language-provider">{children}</div>;
};

// Mock environment variables for performance testing
process.env.MOCK_FETCH = 'true';
process.env.MOCK_ROUTER = 'true';

// Mock heavy dependencies
jest.mock('@/components/page-wrapper', () => ({
  PageWrapper: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="page-wrapper">{children}</div>
  ),
}));

jest.mock('@/hooks/use-api', () => ({
  useSearch: () => ({
    search: jest.fn(),
    results: [],
    loading: false,
    error: null,
  }),
  useAnnouncements: () => ({
    announcements: [],
    loading: false,
    error: null,
  }),
  useNews: () => ({
    news: [],
    loading: false,
    error: null,
  }),
}));

describe('Performance Tests', () => {
  beforeEach(() => {
    // Reset viewport to desktop for consistent testing
    setViewport(VIEWPORTS.desktop.width, VIEWPORTS.desktop.height);

    // Mock fetch for performance tests
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true, data: [] }),
    });

    jest.clearAllMocks();
  });

  afterEach(() => {
    // Clean up any performance observers or timers
    jest.clearAllTimers();
    jest.useRealTimers();
  });

  describe('Component Render Performance', () => {
    it('should render SearchWidget within acceptable time', () => {
      const renderTime = measureRenderTime(() => {
        render(
          <TestLanguageProvider>
            <SearchWidget onSearch={jest.fn()} onResultSelect={jest.fn()} />
          </TestLanguageProvider>,
        );
      });

      // Should render within 100ms (more realistic for complex components)
      expect(renderTime).toBeLessThan(100);
    });

    it('should render ContactForm within acceptable time', () => {
      const renderTime = measureRenderTime(() => {
        render(
          <TestLanguageProvider>
            <ContactForm onSubmit={jest.fn()} />
          </TestLanguageProvider>,
        );
      });

      // Should render within 150ms (form with validation)
      expect(renderTime).toBeLessThan(150);
    });

    it('should render QRScanner within acceptable time', () => {
      const renderTime = measureRenderTime(() => {
        render(
          <QRScanner isOpen={true} onScan={jest.fn()} onClose={jest.fn()} />,
        );
      });

      // Should render within 75ms
      expect(renderTime).toBeLessThan(75);
    });

    it('should render HomePage within acceptable time', () => {
      const renderTime = measureRenderTime(() => {
        render(<HomePage />);
      });

      // Should render within 200ms (complex page)
      expect(renderTime).toBeLessThan(200);
    });
  });

  describe('Large Dataset Performance', () => {
    it('should handle large search results efficiently', () => {
      const largeResults = generateMockSearchResults(100);

      const renderTime = measureRenderTime(() => {
        render(
          <div>
            {largeResults.map((result) => (
              <div key={result.certificateNumber} className="search-result">
                <h3>{result.companyName}</h3>
                <p>{result.productName}</p>
                <span>{result.status}</span>
              </div>
            ))}
          </div>,
        );
      });

      // Should handle 100 results within 300ms
      expect(renderTime).toBeLessThan(300);
    });

    it('should handle very large datasets with virtualization consideration', () => {
      const veryLargeResults = generateMockSearchResults(1000);

      // In a real app, you'd use virtualization for this many items
      // This test ensures we're aware of the performance implications
      const renderTime = measureRenderTime(() => {
        render(
          <div>
            {veryLargeResults.slice(0, 50).map((result) => (
              <div key={result.certificateNumber} className="search-result">
                <h3>{result.companyName}</h3>
                <p>{result.productName}</p>
                <span>{result.status}</span>
              </div>
            ))}
          </div>,
        );
      });

      // Should handle 50 items (pagination) within 150ms
      expect(renderTime).toBeLessThan(150);
    });
  });

  describe('Memory Usage', () => {
    it('should not create memory leaks with repeated renders', () => {
      const initialMemory =
        (performance as Performance & { memory?: { usedJSHeapSize: number } })
          .memory?.usedJSHeapSize || 0;

      // Render and unmount component multiple times
      for (let i = 0; i < 10; i++) {
        const { unmount } = render(
          <SearchWidget onSearch={jest.fn()} onResultSelect={jest.fn()} />,
        );
        unmount();
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory =
        (performance as Performance & { memory?: { usedJSHeapSize: number } })
          .memory?.usedJSHeapSize || 0;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be minimal (less than 1MB)
      expect(memoryIncrease).toBeLessThan(1024 * 1024);
    });
  });

  describe('Responsive Performance', () => {
    it('should render efficiently on mobile viewport', () => {
      setViewport(VIEWPORTS.mobile.width, VIEWPORTS.mobile.height);

      const renderTime = measureRenderTime(() => {
        render(<HomePage />);
      });

      // Mobile should render within 250ms
      expect(renderTime).toBeLessThan(250);
    });

    it('should render efficiently on tablet viewport', () => {
      setViewport(VIEWPORTS.tablet.width, VIEWPORTS.tablet.height);

      const renderTime = measureRenderTime(() => {
        render(<HomePage />);
      });

      // Tablet should render within 200ms
      expect(renderTime).toBeLessThan(200);
    });

    it('should render efficiently on desktop viewport', () => {
      setViewport(VIEWPORTS.desktop.width, VIEWPORTS.desktop.height);

      const renderTime = measureRenderTime(() => {
        render(<HomePage />);
      });

      // Desktop should render within 150ms
      expect(renderTime).toBeLessThan(150);
    });
  });

  describe('Animation Performance', () => {
    it('should handle CSS animations without blocking render', () => {
      const { container } = render(
        <div className="animate-pulse">
          <SearchWidget onSearch={jest.fn()} onResultSelect={jest.fn()} />
        </div>,
      );

      // Check that animated elements are present
      expect(container.querySelector('.animate-pulse')).toBeInTheDocument();

      // Render time should not be significantly impacted by animations
      const renderTime = measureRenderTime(() => {
        render(
          <div className="animate-pulse">
            <ContactForm onSubmit={jest.fn()} />
          </div>,
        );
      });

      expect(renderTime).toBeLessThan(120);
    });
  });

  describe('Bundle Size Considerations', () => {
    it('should not import unnecessary dependencies', () => {
      // This is more of a build-time check, but we can verify
      // that components don't import heavy libraries unnecessarily

      const { container } = render(
        <SearchWidget onSearch={jest.fn()} onResultSelect={jest.fn()} />,
      );

      // Component should render successfully without heavy deps
      expect(container.firstChild).toBeInTheDocument();
    });
  });

  describe('Event Handler Performance', () => {
    it('should handle rapid user input efficiently', async () => {
      const mockOnSearch = jest.fn();
      const { container } = render(
        <SearchWidget onSearch={mockOnSearch} onResultSelect={jest.fn()} />,
      );

      const input = container.querySelector('input') as HTMLInputElement;

      // Simulate rapid typing
      const startTime = performance.now();

      for (let i = 0; i < 10; i++) {
        input.value = `search query ${i}`;
        input.dispatchEvent(new Event('input', { bubbles: true }));
      }

      const endTime = performance.now();
      const totalTime = endTime - startTime;

      // Should handle 10 rapid inputs within 50ms
      expect(totalTime).toBeLessThan(50);
    });

    it('should debounce search efficiently', async () => {
      jest.useFakeTimers();

      const mockOnSearch = jest.fn();
      const { container } = render(
        <SearchWidget onSearch={mockOnSearch} onResultSelect={jest.fn()} />,
      );

      const input = container.querySelector('input') as HTMLInputElement;

      // Simulate rapid typing that should be debounced
      input.value = 'a';
      input.dispatchEvent(new Event('input', { bubbles: true }));

      input.value = 'ab';
      input.dispatchEvent(new Event('input', { bubbles: true }));

      input.value = 'abc';
      input.dispatchEvent(new Event('input', { bubbles: true }));

      // Fast forward time
      jest.advanceTimersByTime(300);

      // Should only call search once due to debouncing
      expect(mockOnSearch).toHaveBeenCalledTimes(1);

      jest.useRealTimers();
    });
  });

  describe('Accessibility Performance', () => {
    it('should maintain performance with screen reader support', () => {
      const renderTime = measureRenderTime(() => {
        render(
          <main aria-label="Main content">
            <SearchWidget onSearch={jest.fn()} onResultSelect={jest.fn()} />
            <ContactForm onSubmit={jest.fn()} />
          </main>,
        );
      });

      // Accessibility attributes should not significantly impact performance
      expect(renderTime).toBeLessThan(150);
    });
  });

  describe('Error Boundary Performance', () => {
    it('should handle errors without performance degradation', () => {
      const ErrorComponent = () => {
        throw new Error('Test error');
      };

      const ErrorBoundary = ({ children }: { children: React.ReactNode }) => {
        try {
          return <>{children}</>;
        } catch (_error) {
          return <div>Error occurred</div>;
        }
      };

      const renderTime = measureRenderTime(() => {
        render(
          <ErrorBoundary>
            <ErrorComponent />
          </ErrorBoundary>,
        );
      });

      // Error handling should be fast
      expect(renderTime).toBeLessThan(50);
    });
  });
});

// Performance benchmarks for CI/CD
describe('Performance Benchmarks', () => {
  const PERFORMANCE_BUDGETS = {
    COMPONENT_RENDER: 100, // ms
    PAGE_RENDER: 300, // ms
    LARGE_LIST: 500, // ms
    USER_INTERACTION: 16, // ms (60fps)
  };

  it('should meet component render budget', () => {
    const components = [
      () =>
        render(
          <SearchWidget onSearch={jest.fn()} onResultSelect={jest.fn()} />,
        ),
      () => render(<ContactForm onSubmit={jest.fn()} />),
    ];

    components.forEach((renderComponent) => {
      const renderTime = measureRenderTime(renderComponent);
      expect(renderTime).toBeLessThan(PERFORMANCE_BUDGETS.COMPONENT_RENDER);
    });
  });

  it('should meet page render budget', () => {
    const renderTime = measureRenderTime(() => {
      render(<HomePage />);
    });

    expect(renderTime).toBeLessThan(PERFORMANCE_BUDGETS.PAGE_RENDER);
  });

  it('should meet large list render budget', () => {
    const largeList = generateMockSearchResults(200);

    const renderTime = measureRenderTime(() => {
      render(
        <div>
          {largeList.map((item) => (
            <div key={item.certificateNumber}>
              {item.companyName} - {item.productName}
            </div>
          ))}
        </div>,
      );
    });

    expect(renderTime).toBeLessThan(PERFORMANCE_BUDGETS.LARGE_LIST);
  });
});
