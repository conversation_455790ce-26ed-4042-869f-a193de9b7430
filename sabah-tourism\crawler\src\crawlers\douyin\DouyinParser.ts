import { z } from 'zod';
import type { MediaItem, SocialMediaPost } from '../../types/social-media.js';
import { crawlerLogger } from '../../utils/logger.js';
import { douyinSelectors } from './DouyinConfig.js';

// Zod schema for Stagehand extraction
export const DouyinPostSchema = z.object({
  posts: z.array(
    z.object({
      id: z.string(),
      url: z.string().url(),
      title: z.string().optional(),
      description: z.string().optional(),
      author: z.object({
        username: z.string(),
        displayName: z.string().optional(),
        avatarUrl: z.string().url().optional(),
      }),
      engagement: z.object({
        likes: z.number().default(0),
        comments: z.number().default(0),
        shares: z.number().default(0),
        views: z.number().optional(),
      }),
      mediaUrl: z.string().url().optional(),
      thumbnailUrl: z.string().url().optional(),
      hashtags: z.array(z.string()).default([]),
      publishedAt: z.string().optional(),
    }),
  ),
});

export const DouyinVideoDetailSchema = z.object({
  id: z.string(),
  title: z.string().optional(),
  description: z.string().optional(),
  author: z.object({
    username: z.string(),
    displayName: z.string().optional(),
    avatarUrl: z.string().url().optional(),
    verified: z.boolean().optional(),
    followerCount: z.number().optional(),
  }),
  engagement: z.object({
    likes: z.number().default(0),
    comments: z.number().default(0),
    shares: z.number().default(0),
    views: z.number().optional(),
  }),
  media: z.object({
    videoUrl: z.string().url().optional(),
    thumbnailUrl: z.string().url().optional(),
    duration: z.number().optional(),
    width: z.number().optional(),
    height: z.number().optional(),
  }),
  hashtags: z.array(z.string()).default([]),
  mentions: z.array(z.string()).default([]),
  publishedAt: z.string().optional(),
  location: z
    .object({
      name: z.string(),
      coordinates: z
        .object({
          latitude: z.number(),
          longitude: z.number(),
        })
        .optional(),
    })
    .optional(),
});

export class DouyinParser {
  /**
   * Parse search results from Douyin search page
   */
  public static parseSearchResults(extractedData: any): SocialMediaPost[] {
    try {
      const validatedData = DouyinPostSchema.parse(extractedData);

      return validatedData.posts.map((post) =>
        DouyinParser.convertToSocialMediaPost(post),
      );
    } catch (error) {
      crawlerLogger.error('Failed to parse Douyin search results', {
        error,
        extractedData,
      });
      return [];
    }
  }

  /**
   * Parse detailed video information from video page
   */
  public static parseVideoDetail(extractedData: any): SocialMediaPost | null {
    try {
      const validatedData = DouyinVideoDetailSchema.parse(extractedData);

      return DouyinParser.convertDetailToSocialMediaPost(validatedData);
    } catch (error) {
      crawlerLogger.error('Failed to parse Douyin video detail', {
        error,
        extractedData,
      });
      return null;
    }
  }

  /**
   * Convert basic post data to SocialMediaPost format
   */
  private static convertToSocialMediaPost(post: any): SocialMediaPost {
    const media: MediaItem[] = [];

    if (post.mediaUrl) {
      media.push({
        type: 'video',
        url: post.mediaUrl,
        thumbnailUrl: post.thumbnailUrl,
        format: 'mp4',
      });
    }

    return {
      id: post.id,
      platform: 'douyin',
      url: post.url,
      title: post.title,
      content: post.description,
      author: {
        username: post.author.username,
        displayName: post.author.displayName,
        avatarUrl: post.author.avatarUrl,
      },
      publishedAt: post.publishedAt ? new Date(post.publishedAt) : undefined,
      engagement: {
        likes: post.engagement.likes,
        comments: post.engagement.comments,
        shares: post.engagement.shares,
        views: post.engagement.views,
      },
      hashtags: post.hashtags,
      mentions: [],
      media,
      metadata: {
        platform: 'douyin',
        extractedAt: new Date().toISOString(),
        source: 'search',
      },
    };
  }

  /**
   * Convert detailed video data to SocialMediaPost format
   */
  private static convertDetailToSocialMediaPost(detail: any): SocialMediaPost {
    const media: MediaItem[] = [];

    if (detail.media.videoUrl) {
      media.push({
        type: 'video',
        url: detail.media.videoUrl,
        thumbnailUrl: detail.media.thumbnailUrl,
        duration: detail.media.duration,
        dimensions:
          detail.media.width && detail.media.height
            ? {
                width: detail.media.width,
                height: detail.media.height,
              }
            : undefined,
        format: 'mp4',
      });
    }

    return {
      id: detail.id,
      platform: 'douyin',
      url: `https://www.douyin.com/video/${detail.id}`,
      title: detail.title,
      content: detail.description,
      author: {
        username: detail.author.username,
        displayName: detail.author.displayName,
        avatarUrl: detail.author.avatarUrl,
        verified: detail.author.verified,
        followerCount: detail.author.followerCount,
      },
      publishedAt: detail.publishedAt
        ? new Date(detail.publishedAt)
        : undefined,
      engagement: {
        likes: detail.engagement.likes,
        comments: detail.engagement.comments,
        shares: detail.engagement.shares,
        views: detail.engagement.views,
      },
      hashtags: detail.hashtags,
      mentions: detail.mentions,
      media,
      location: detail.location,
      metadata: {
        platform: 'douyin',
        extractedAt: new Date().toISOString(),
        source: 'detail',
      },
    };
  }

  /**
   * Extract video ID from Douyin URL
   */
  public static extractVideoId(url: string): string | null {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');

      // Handle different URL formats
      if (pathParts.includes('video')) {
        const videoIndex = pathParts.indexOf('video');
        return pathParts[videoIndex + 1] || null;
      }

      // Handle short URLs
      const match = url.match(/\/video\/(\w+)/);
      return match ? match[1] : null;
    } catch (error) {
      crawlerLogger.error('Failed to extract video ID from URL', {
        url,
        error,
      });
      return null;
    }
  }

  /**
   * Extract hashtags from text content
   */
  public static extractHashtags(text: string): string[] {
    if (!text) return [];

    const hashtagRegex = /#[\u4e00-\u9fa5\w]+/g;
    const matches = text.match(hashtagRegex);

    return matches ? matches.map((tag) => tag.substring(1)) : [];
  }

  /**
   * Extract mentions from text content
   */
  public static extractMentions(text: string): string[] {
    if (!text) return [];

    const mentionRegex = /@[\u4e00-\u9fa5\w]+/g;
    const matches = text.match(mentionRegex);

    return matches ? matches.map((mention) => mention.substring(1)) : [];
  }

  /**
   * Clean and normalize text content
   */
  public static cleanText(text: string): string {
    if (!text) return '';

    return text
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[\r\n]+/g, ' ')
      .substring(0, 1000); // Limit length
  }

  /**
   * Parse engagement numbers (handles Chinese number formats)
   */
  public static parseEngagementNumber(text: string): number {
    if (!text) return 0;

    // Remove non-numeric characters except for Chinese number indicators
    const cleaned = text.replace(/[^\d万千百十.]/g, '');

    if (cleaned.includes('万')) {
      const num = Number.parseFloat(cleaned.replace('万', ''));
      return Math.round(num * 10000);
    }

    if (cleaned.includes('千')) {
      const num = Number.parseFloat(cleaned.replace('千', ''));
      return Math.round(num * 1000);
    }

    if (cleaned.includes('百')) {
      const num = Number.parseFloat(cleaned.replace('百', ''));
      return Math.round(num * 100);
    }

    return Number.parseInt(cleaned) || 0;
  }

  /**
   * Validate if URL is a valid Douyin video URL
   */
  public static isValidDouyinUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return (
        urlObj.hostname.includes('douyin.com') &&
        (urlObj.pathname.includes('/video/') ||
          urlObj.pathname.includes('/share/'))
      );
    } catch {
      return false;
    }
  }
}
