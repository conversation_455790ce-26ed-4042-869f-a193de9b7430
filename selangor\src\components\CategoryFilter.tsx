'use client';

import { useState, useEffect } from 'react';
import { ChevronDown, Filter, X } from 'lucide-react';

export interface CategoryOption {
  value: string;
  label: string;
  count: number;
  type: 'category' | 'subcategory';
}

interface CategoryFilterProps {
  selectedCategory?: string;
  onCategoryChange: (category: string | undefined) => void;
  className?: string;
  placeholder?: string;
  showCounts?: boolean;
}

export function CategoryFilter({
  selectedCategory,
  onCategoryChange,
  className = '',
  placeholder = 'All Categories',
  showCounts = true,
}: CategoryFilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [categories, setCategories] = useState<CategoryOption[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/categories');
      if (!response.ok) {
        throw new Error(`Failed to fetch categories: ${response.status}`);
      }
      const data = await response.json();
      setCategories(data.filterOptions || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load categories');
      console.error('Error fetching categories:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCategorySelect = (category: string | undefined) => {
    onCategoryChange(category);
    setIsOpen(false);
  };

  const selectedOption = categories.find(cat => cat.value === selectedCategory);

  if (error) {
    return (
      <div className={`text-sm text-red-600 ${className}`}>
        Failed to load categories
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <div
        className={`
          flex items-center justify-between w-full px-4 py-2 text-left
          bg-white border border-gray-300 rounded-lg shadow-sm
          hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500
          ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          ${isOpen ? 'border-green-500 ring-2 ring-green-500' : ''}
        `}
      >
        <button
          type="button"
          onClick={() => !isLoading && setIsOpen(!isOpen)}
          disabled={isLoading}
          className="flex items-center min-w-0 flex-1 focus:outline-none"
        >
          <Filter className="w-4 h-4 text-gray-400 mr-2 flex-shrink-0" />
          <span className="truncate">
            {isLoading ? (
              'Loading...'
            ) : selectedOption ? (
              <>
                {selectedOption.label}
                {showCounts && (
                  <span className="ml-1 text-gray-500 text-sm">
                    ({selectedOption.count})
                  </span>
                )}
              </>
            ) : (
              placeholder
            )}
          </span>
        </button>

        <div className="flex items-center ml-2">
          {selectedCategory && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                handleCategorySelect(undefined);
              }}
              className="p-1 hover:bg-gray-100 rounded mr-1 focus:outline-none"
            >
              <X className="w-3 h-3 text-gray-400" />
            </button>
          )}
          <button
            type="button"
            onClick={() => !isLoading && setIsOpen(!isOpen)}
            disabled={isLoading}
            className="focus:outline-none"
          >
            <ChevronDown
              className={`w-4 h-4 text-gray-400 transition-transform ${
                isOpen ? 'transform rotate-180' : ''
              }`}
            />
          </button>
        </div>
      </div>

      {isOpen && !isLoading && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-auto">
          {/* All Categories Option */}
          <button
            type="button"
            onClick={() => handleCategorySelect(undefined)}
            className={`
              w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-between
              ${!selectedCategory ? 'bg-green-50 text-green-700' : 'text-gray-900'}
            `}
          >
            <span>All Categories</span>
            {!selectedCategory && (
              <span className="text-green-600">✓</span>
            )}
          </button>

          {/* Category Options */}
          {categories.length > 0 ? (
            <>
              {/* Categories Section */}
              {categories.filter(cat => cat.type === 'category').length > 0 && (
                <>
                  <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50 border-t">
                    Categories
                  </div>
                  {categories
                    .filter(cat => cat.type === 'category')
                    .map((category) => (
                      <button
                        key={`category-${category.value}`}
                        type="button"
                        onClick={() => handleCategorySelect(category.value)}
                        className={`
                          w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-between
                          ${selectedCategory === category.value ? 'bg-green-50 text-green-700' : 'text-gray-900'}
                        `}
                      >
                        <span className="truncate">{category.label}</span>
                        <div className="flex items-center ml-2">
                          {showCounts && (
                            <span className="text-gray-500 text-sm mr-2">
                              {category.count}
                            </span>
                          )}
                          {selectedCategory === category.value && (
                            <span className="text-green-600">✓</span>
                          )}
                        </div>
                      </button>
                    ))}
                </>
              )}

              {/* Subcategories Section */}
              {categories.filter(cat => cat.type === 'subcategory').length > 0 && (
                <>
                  <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50 border-t">
                    Subcategories
                  </div>
                  {categories
                    .filter(cat => cat.type === 'subcategory')
                    .map((subcategory) => (
                      <button
                        key={`subcategory-${subcategory.value}`}
                        type="button"
                        onClick={() => handleCategorySelect(subcategory.value)}
                        className={`
                          w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-between
                          ${selectedCategory === subcategory.value ? 'bg-green-50 text-green-700' : 'text-gray-900'}
                        `}
                      >
                        <span className="truncate">{subcategory.label}</span>
                        <div className="flex items-center ml-2">
                          {showCounts && (
                            <span className="text-gray-500 text-sm mr-2">
                              {subcategory.count}
                            </span>
                          )}
                          {selectedCategory === subcategory.value && (
                            <span className="text-green-600">✓</span>
                          )}
                        </div>
                      </button>
                    ))}
                </>
              )}
            </>
          ) : (
            <div className="px-4 py-2 text-gray-500 text-sm">
              No categories available
            </div>
          )}
        </div>
      )}

      {/* Backdrop */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
