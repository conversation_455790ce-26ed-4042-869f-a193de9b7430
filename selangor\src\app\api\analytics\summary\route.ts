import { and, count, desc, eq, gte, lte, sql } from 'drizzle-orm';
import { type NextRequest, NextResponse } from 'next/server';
import { getHalalSelangorSiteId } from '@/lib/analytics';
import { db } from '@/lib/db';
import { searchAnalytics } from '@/lib/db/schema';

// Remove edge runtime to use Node.js runtime for database access

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const startDate = searchParams.get('startDate'); // ISO date string
  const endDate = searchParams.get('endDate'); // ISO date string
  const period = searchParams.get('period') || '7d'; // '1d', '7d', '30d', 'all'

  try {
    const siteId = getHalalSelangorSiteId();

    // Calculate date range based on period
    let dateFilter: Date | null = null;
    if (period !== 'all') {
      const now = new Date();
      switch (period) {
        case '1d':
          dateFilter = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          dateFilter = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          dateFilter = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
      }
    }

    // Override with custom date range if provided
    const actualStartDate = startDate ? new Date(startDate) : dateFilter;
    const actualEndDate = endDate ? new Date(endDate) : null;

    // Build where conditions
    const whereConditions = [eq(searchAnalytics.siteId, siteId)];

    if (actualStartDate) {
      whereConditions.push(gte(searchAnalytics.createdAt, actualStartDate));
    }

    if (actualEndDate) {
      whereConditions.push(lte(searchAnalytics.createdAt, actualEndDate));
    }

    // Get total searches
    const totalSearches = await db
      .select({ count: count() })
      .from(searchAnalytics)
      .where(and(...whereConditions));

    // Get searches with results
    const searchesWithResults = await db
      .select({ count: count() })
      .from(searchAnalytics)
      .where(and(...whereConditions, eq(searchAnalytics.hasResults, true)));

    // Get searches by type
    const searchesByType = await db
      .select({
        searchType: searchAnalytics.searchType,
        count: count(),
      })
      .from(searchAnalytics)
      .where(and(...whereConditions))
      .groupBy(searchAnalytics.searchType)
      .orderBy(desc(count()));

    // Get top search queries
    const topQueries = await db
      .select({
        searchQuery: searchAnalytics.searchQuery,
        count: count(),
        avgResultsCount: sql<number>`AVG(${searchAnalytics.resultsCount})`,
        successRate: sql<number>`AVG(CASE WHEN ${searchAnalytics.hasResults} THEN 1.0 ELSE 0.0 END)`,
      })
      .from(searchAnalytics)
      .where(and(...whereConditions))
      .groupBy(searchAnalytics.searchQuery)
      .orderBy(desc(count()))
      .limit(20);

    // Get average response time
    const avgResponseTime = await db
      .select({
        avgTime: sql<number>`AVG(${searchAnalytics.responseTime})`,
      })
      .from(searchAnalytics)
      .where(
        and(
          ...whereConditions,
          sql`${searchAnalytics.responseTime} IS NOT NULL`,
        ),
      );

    // Get searches by hour (for the last 24 hours if period is 1d)
    let searchesByHour = null;
    if (
      period === '1d' ||
      (actualStartDate &&
        actualEndDate &&
        actualEndDate.getTime() - actualStartDate.getTime() <=
          24 * 60 * 60 * 1000)
    ) {
      searchesByHour = await db
        .select({
          hour: sql<number>`EXTRACT(HOUR FROM ${searchAnalytics.createdAt})`,
          count: count(),
        })
        .from(searchAnalytics)
        .where(and(...whereConditions))
        .groupBy(sql`EXTRACT(HOUR FROM ${searchAnalytics.createdAt})`)
        .orderBy(sql`EXTRACT(HOUR FROM ${searchAnalytics.createdAt})`);
    }

    const summary = {
      totalSearches: totalSearches[0]?.count || 0,
      searchesWithResults: searchesWithResults[0]?.count || 0,
      successRate:
        totalSearches[0]?.count > 0
          ? (searchesWithResults[0]?.count || 0) / totalSearches[0].count
          : 0,
      avgResponseTime: avgResponseTime[0]?.avgTime || null,
      searchesByType: searchesByType.map(
        (item: { searchType: string; count: number }) => ({
          type: item.searchType,
          count: item.count,
        }),
      ),
      topQueries: topQueries.map(
        (item: {
          searchQuery: string;
          count: number;
          avgResultsCount: number;
          successRate: number;
        }) => ({
          query: item.searchQuery,
          count: item.count,
          avgResultsCount: Math.round(item.avgResultsCount * 100) / 100,
          successRate: Math.round(item.successRate * 100) / 100,
        }),
      ),
      ...(searchesByHour && {
        searchesByHour: searchesByHour.map(
          (item: { hour: number; count: number }) => ({
            hour: item.hour,
            count: item.count,
          }),
        ),
      }),
      period: {
        type: period,
        startDate: actualStartDate?.toISOString(),
        endDate: actualEndDate?.toISOString(),
      },
    };

    return NextResponse.json(summary);
  } catch (error) {
    console.error('Analytics summary API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics summary' },
      { status: 500 },
    );
  }
}
