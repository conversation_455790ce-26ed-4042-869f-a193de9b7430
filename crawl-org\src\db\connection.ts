import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

/**
 * Initialize database connection for PostgreSQL with Drizzle ORM
 * @returns Configured Drizzle database instance
 */
export function initializeDatabase(): ReturnType<typeof drizzle> {
  const databaseUrl = process.env.DATABASE_URL;

  if (!databaseUrl) {
    throw new Error(
      'DATABASE_URL is required. Make sure it is set in your environment variables.',
    );
  }

  // Create PostgreSQL connection using postgres-js with crawling-optimized settings
  const sql = postgres(databaseUrl, {
    prepare: false, // Disable prepared statements for better compatibility
    max: 2, // Lower pool size for crawling to avoid overwhelming DB
    idle_timeout: 30, // Longer idle timeout for batch operations
    max_lifetime: 60 * 45, // 45 minutes max lifetime for long crawls
    connect_timeout: 15, // Longer timeout for crawling operations
    debug: process.env.CRAWL_DEBUG === 'true', // Enable debug if needed
    // Crawling-specific options
    connection: {
      application_name: 'halal-org-crawler',
    },
    transform: {
      undefined: null,
    },
    // Handle connection errors gracefully
    onnotice: process.env.NODE_ENV === 'development' ? console.log : undefined,
  });

  // Initialize Drizzle with the postgres connection and schema
  const db = drizzle(sql, { schema });

  return db;
}

/**
 * Test database connection
 * @param databaseUrl - Database URL to test
 */
export async function testConnection(databaseUrl?: string): Promise<boolean> {
  try {
    const url = databaseUrl || process.env.DATABASE_URL;
    if (!url) {
      throw new Error('Database URL not provided');
    }

    const sql = postgres(url, { prepare: false });

    // Simple query to test connection
    await sql`SELECT 1`;
    await sql.end();
    return true;
  } catch (error) {
    console.error('Database connection test failed:', error);
    return false;
  }
}

// Export the schema for use in other files
export { schema };
export * from './schema';
