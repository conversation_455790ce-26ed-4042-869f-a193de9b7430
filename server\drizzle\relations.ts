import { relations } from 'drizzle-orm/relations';
import { sites, twilioConfigs, twilioMessages } from './schema';

export const twilioConfigsRelations = relations(twilioConfigs, ({ one }) => ({
  site: one(sites, {
    fields: [twilioConfigs.siteId],
    references: [sites.id],
  }),
}));

export const sitesRelations = relations(sites, ({ many }) => ({
  twilioConfigs: many(twilioConfigs),
  twilioMessages: many(twilioMessages),
}));

export const twilioMessagesRelations = relations(twilioMessages, ({ one }) => ({
  site: one(sites, {
    fields: [twilioMessages.siteId],
    references: [sites.id],
  }),
}));
