import bcrypt from 'bcryptjs';
import type { NextFunction, Request, Response } from 'express';
import jwt, { type SignOptions } from 'jsonwebtoken';
import databaseService from '../services/database';
import {
  type AdminLoginRequest,
  type AdminLoginResponse,
  type AdminUser,
  type JWTPayload,
  UserRole,
} from '../types'; // Added AdminUser for role

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: JWTPayload;
    }
  }
}

class AuthService {
  private readonly jwtSecret: string;
  private readonly jwtExpiresIn: string;

  constructor() {
    this.jwtSecret =
      process.env.JWT_SECRET || 'your-secret-key-change-in-production';
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '365d';

    if (
      process.env.NODE_ENV === 'production' &&
      this.jwtSecret === 'your-secret-key-change-in-production'
    ) {
      console.warn(
        'WARNING: Using default JWT secret in production. Please set JWT_SECRET environment variable.',
      );
    }
  }

  // Generate JWT token
  generateToken(
    userId: number,
    username: string,
    role: UserRole,
    env?: { JWT_SECRET?: string },
  ): string {
    // Added role
    const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
      // Explicitly type payload
      userId,
      username,
      role,
    };

    const options: SignOptions = {
      // biome-ignore lint/suspicious/noExplicitAny: Necessary to match jsonwebtoken expected type
      expiresIn: this.jwtExpiresIn as any,
    };

    const secret = env?.JWT_SECRET || this.jwtSecret;
    return jwt.sign(payload, secret, options);
  }

  // Verify JWT token
  verifyToken(token: string): JWTPayload | null {
    try {
      return jwt.verify(token, this.jwtSecret) as JWTPayload;
    } catch (_error) {
      return null;
    }
  }

  // Hash password
  hashPassword(password: string): string {
    return bcrypt.hashSync(password, 10);
  }

  // Compare password
  comparePassword(password: string, hash: string): boolean {
    return bcrypt.compareSync(password, hash);
  }

  // Login admin user
  async login(
    credentials: AdminLoginRequest,
    dbService?: unknown,
    env?: { JWT_SECRET?: string },
    siteId?: string,
  ): Promise<AdminLoginResponse> {
    try {
      const { username, password } = credentials;
      console.log('Login attempt for username:', username);

      const db = dbService || databaseService;
      console.log(
        'Using database service:',
        db === databaseService ? 'default' : 'custom',
      );

      // Get admin user from database with siteId validation
      const admin = await (
        db as unknown as {
          getAdminByUsername: (
            username: string,
            siteId?: string,
          ) => Promise<AdminUser | null>;
        }
      ).getAdminByUsername(username, siteId);
      console.log('Admin user found:', admin ? 'yes' : 'no');

      if (!admin) {
        console.log('Login failed: Admin user not found');
        return {
          success: false,
          error: 'Invalid username or password',
        };
      }

      // Verify password
      const passwordValid = this.comparePassword(password, admin.passwordHash);
      console.log('Password validation:', passwordValid ? 'passed' : 'failed');

      if (!passwordValid) {
        console.log('Login failed: Invalid password');
        return {
          success: false,
          error: 'Invalid username or password',
        };
      }

      // Update last login time
      try {
        if (dbService) {
          await (
            db as unknown as {
              updateAdminLastLogin: (id: number) => Promise<void>;
            }
          ).updateAdminLastLogin(admin.id);
        } else {
          await (
            databaseService as unknown as {
              updateAdminLastLogin: (id: number) => Promise<void>;
            }
          ).updateAdminLastLogin(admin.id);
        }
        console.log('Last login time updated successfully');
      } catch (updateError) {
        console.error('Failed to update last login time:', updateError);
        // Continue login process despite update failure
      }

      // Ensure admin has roles and determine primary role
      console.log('Admin roles:', admin.roles);
      if (!admin.roles || admin.roles.length === 0) {
        console.error(
          'Admin user roles not found during login for user:',
          admin.username,
        );
        return {
          success: false,
          error: 'User role configuration error.',
        };
      }

      // Use primary admin role (SUPERADMIN takes precedence over ADMIN, which takes precedence over EDITOR)
      const primaryRole = admin.roles.includes(UserRole.SUPERADMIN)
        ? UserRole.SUPERADMIN
        : admin.roles.includes(UserRole.ADMIN)
          ? UserRole.ADMIN
          : admin.roles[0];
      console.log('Selected primary role:', primaryRole);

      // Generate token
      const token = this.generateToken(
        admin.id,
        admin.username,
        primaryRole,
        env,
      );
      console.log('JWT token generated successfully');

      return {
        success: true,
        token,
        user: {
          id: admin.id,
          username: admin.username,
          email: admin.email,
          firstName: admin.firstName,
          lastName: admin.lastName,
          roles: admin.roles,
          isActive: admin.isActive,
          isOnline: admin.isOnline,
          lastSeenAt: admin.lastLoginAt,
        },
      };
    } catch (error) {
      console.error('Login error:', error);
      // Re-throw the error so the server can handle fallback logic
      throw error;
    }
  }

  // Middleware to authenticate admin requests
  authenticateAdmin: (req: Request, res: Response, next: NextFunction) => void =
    (req, res, next) => {
      try {
        const authHeader = req.headers.authorization;

        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          res.status(401).json({
            error: 'Access denied',
            message: 'No token provided',
          });
          return;
        }

        const token = authHeader.substring(7); // Remove 'Bearer ' prefix
        const decoded = this.verifyToken(token);

        if (!decoded) {
          res.status(401).json({
            error: 'Access denied',
            message: 'Invalid token',
          });
          return;
        }

        req.user = decoded;
        next();
      } catch (error) {
        console.error('Authentication error:', error);
        res.status(500).json({
          error: 'Internal server error',
          message: 'Authentication failed',
        });
      }
    };

  // Middleware to authenticate admin requests (optional - for routes that work with or without auth)
  optionalAuth: (req: Request, res: Response, next: NextFunction) => void = (
    req,
    _res,
    next,
  ) => {
    try {
      const authHeader = req.headers.authorization;

      if (authHeader?.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const decoded = this.verifyToken(token);

        if (decoded) {
          req.user = decoded;
        }
      }

      next();
    } catch (_error) {
      // Continue without authentication for optional auth
      next();
    }
  };

  // Check if user is authenticated
  isAuthenticated(req: Request): boolean {
    return !!req.user;
  }

  // Get current user info
  getCurrentUser(req: Request): JWTPayload | null {
    return req.user || null;
  }

  // Middleware to authorize based on roles
  authorizeRoles: (
    allowedRoles: string[],
  ) => (req: Request, res: Response, next: NextFunction) => Promise<void> = (
    allowedRoles,
  ) => {
    return async (req, res, next) => {
      if (!req.user || !req.user.role) {
        res.status(403).json({
          error: 'Forbidden',
          message: 'User role not available for authorization.',
        });
        return;
      }

      try {
        // Get user's full role information from database to check multiple roles
        const dbService = new (await import('../services/database')).default();
        const user = await dbService.getUserById(req.user.userId);

        if (!user || !user.roles || user.roles.length === 0) {
          res.status(403).json({
            error: 'Forbidden',
            message: 'User roles not found.',
          });
          return;
        }

        // Check if user has any of the allowed roles
        const hasRole = user.roles.some((role) => allowedRoles.includes(role));

        if (!hasRole) {
          res.status(403).json({
            error: 'Forbidden',
            message: `Access denied. User roles [${user.roles.join(', ')}] are not authorized for this resource.`,
          });
          return;
        }

        next();
      } catch (error) {
        console.error('Authorization error:', error);
        res.status(500).json({
          error: 'Internal server error',
          message: 'Authorization failed',
        });
      }
    };
  };
}

const authService = new AuthService();

// Hono-compatible authenticateAdmin middleware
export const authenticateAdmin = async (c: any, next: any) => {
  const authHeader = c.req.header('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return c.json(
      {
        error: 'Access denied',
        message: 'No token provided',
      },
      401,
    );
  }

  const token = authHeader.substring(7); // Remove 'Bearer ' prefix
  const decoded = authService.verifyToken(token);

  if (!decoded) {
    return c.json(
      {
        error: 'Access denied',
        message: 'Invalid token',
      },
      401,
    );
  }

  // Attach user to context
  c.set('user', decoded);
  await next();
};

export default authService;
