# Drizzle ORM Setup Commands

Run these commands in the `server` directory to complete the Drizzle ORM setup:

```bash
# 1. Install dependencies (if not already done)
pnpm install

# 2. Generate database migrations
pnpm db:migrate:generate

# 3. Apply migrations to database
pnpm db:migrate

# 4. Seed the database (optional)
pnpm db:seed

# 5. Start the development server
pnpm dev
```

## Verification

After running the commands above, you should see:
- ✅ Drizzle migrations generated successfully
- ✅ Database schema applied correctly
- ✅ All integration tests pass
- ✅ Server starts without errors
- ✅ Database operations work with type safety

## Available Scripts

- `pnpm db:migrate:generate` - Generate Drizzle migrations
- `pnpm db:migrate` - Apply migrations to database
- `pnpm db:push` - Push schema changes directly (development only)
- `pnpm db:studio` - Open Drizzle Studio (database browser)
- `pnpm db:seed` - Seed database with test data
- `pnpm dev` - Start development server with Drizzle ORM
