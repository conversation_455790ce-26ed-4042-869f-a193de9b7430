#!/usr/bin/env bun

import 'dotenv/config';
import chalk from 'chalk';
import { createCrawlerConfig } from '../config/crawler.config.js';
import { createDatabaseConfig } from '../config/database.config.js';
import { DatabaseManager } from '../src/crawlers/base/DatabaseManager.js';
import { DouyinCrawler } from '../src/crawlers/douyin/DouyinCrawler.js';
import logger from '../src/utils/logger.js';

interface CrawlOptions {
  keywords: string[];
  maxPosts?: number;
  downloadMedia?: boolean;
  headless?: boolean;
  resumeSession?: number;
  dryRun?: boolean;
}

async function crawlDouyin(options: CrawlOptions) {
  console.log(chalk.blue('🚀 Starting Douyin Crawler for Sabah Tourism'));

  let database: DatabaseManager | null = null;
  let crawler: DouyinCrawler | null = null;

  try {
    // Initialize database
    console.log(chalk.blue('📊 Initializing database...'));
    const dbConfig = createDatabaseConfig();
    database = new DatabaseManager(dbConfig);
    console.log(chalk.green('✅ Database initialized'));

    // Create crawler configuration
    const crawlerConfig = createCrawlerConfig('douyin', options.keywords, {
      maxPosts: options.maxPosts || 50,
      downloadMedia: options.downloadMedia !== false && !options.dryRun,
      mediaTypes: ['image', 'video', 'audio'],
      outputDir: './output',
      browserConfig: {
        headless: options.headless !== false,
        timeout: 30000,
      },
      rateLimiting: {
        requestsPerMinute: 20,
        downloadConcurrency: 2,
      },
    });

    console.log(chalk.green('✅ Configuration loaded'));
    console.log(chalk.cyan(`Keywords: ${options.keywords.join(', ')}`));
    console.log(chalk.cyan(`Max posts: ${crawlerConfig.maxPosts}`));
    console.log(
      chalk.cyan(
        `Download media: ${crawlerConfig.downloadMedia ? 'Yes' : 'No'}`,
      ),
    );
    console.log(
      chalk.cyan(
        `Headless mode: ${crawlerConfig.browserConfig.headless ? 'Yes' : 'No'}`,
      ),
    );

    // Create crawler
    crawler = new DouyinCrawler(crawlerConfig, database);

    // Set up event listeners
    crawler.on('progress', (progress) => {
      const { processedPosts, totalPosts, successfulPosts, failedPosts } =
        progress.progress;
      console.log(
        chalk.blue(
          `📊 Progress: ${processedPosts}/${totalPosts} posts processed`,
        ),
      );
      console.log(chalk.green(`   ✅ Successful: ${successfulPosts}`));
      console.log(chalk.red(`   ❌ Failed: ${failedPosts}`));

      if (progress.currentActivity) {
        console.log(chalk.gray(`   🔄 ${progress.currentActivity}`));
      }
    });

    crawler.on('media-downloaded', (downloadProgress) => {
      if (downloadProgress.status === 'completed') {
        console.log(chalk.green(`📥 Downloaded: ${downloadProgress.fileName}`));
      } else if (downloadProgress.status === 'failed') {
        console.log(
          chalk.red(`❌ Download failed: ${downloadProgress.fileName}`),
        );
        if (downloadProgress.error) {
          console.log(chalk.red(`   Error: ${downloadProgress.error}`));
        }
      } else if (downloadProgress.status === 'downloading') {
        const percent = Math.round(downloadProgress.percentage);
        console.log(
          chalk.blue(
            `📥 Downloading: ${downloadProgress.fileName} (${percent}%)`,
          ),
        );
      }
    });

    crawler.on('error', (error) => {
      console.error(chalk.red(`❌ Crawler error: ${error.message}`));
      logger.error('Crawler error', { error });
    });

    crawler.on('completed', (summary) => {
      console.log(chalk.green('\n🎉 Crawl completed successfully!'));
      console.log(chalk.cyan('📊 Final Summary:'));
      console.log(chalk.cyan(`   Platform: ${summary.platform}`));
      console.log(chalk.cyan(`   Keywords: ${summary.keywords.join(', ')}`));
      console.log(
        chalk.cyan(`   Duration: ${Math.round(summary.duration / 1000)}s`),
      );
      console.log(chalk.cyan(`   Total posts found: ${summary.totalPosts}`));
      console.log(
        chalk.cyan(`   Successfully processed: ${summary.successfulPosts}`),
      );
      console.log(chalk.cyan(`   Failed: ${summary.failedPosts}`));
      console.log(
        chalk.cyan(
          `   Media files downloaded: ${summary.downloadedMedia}/${summary.totalMedia}`,
        ),
      );
      console.log(chalk.cyan(`   Errors encountered: ${summary.errors}`));

      if (summary.totalSize > 0) {
        const sizeMB = Math.round(summary.totalSize / (1024 * 1024));
        console.log(chalk.cyan(`   Total download size: ${sizeMB}MB`));
      }
    });

    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      console.log(
        chalk.yellow(
          '\n⏸️  Received interrupt signal, gracefully shutting down...',
        ),
      );
      if (crawler) {
        await crawler.stop();
        await crawler.cleanup();
      }
      if (database) {
        database.close();
      }
      console.log(chalk.green('✅ Shutdown complete'));
      process.exit(0);
    });

    // Start crawling
    console.log(chalk.blue('\n🔍 Starting crawl process...'));

    const crawlOptions = {
      resumeSession: options.resumeSession,
      dryRun: options.dryRun,
      verbose: true,
    };

    const progress = await crawler.crawl(crawlOptions);

    // Show final statistics
    const stats = database.getStats();
    console.log(chalk.blue('\n📈 Database Statistics:'));
    console.log(chalk.cyan(`   Total posts in database: ${stats.totalPosts}`));
    console.log(chalk.cyan(`   Total media files: ${stats.totalMediaFiles}`));
    console.log(chalk.cyan(`   Total sessions: ${stats.totalSessions}`));
  } catch (error) {
    console.error(chalk.red(`❌ Crawl failed: ${(error as Error).message}`));
    logger.error('Crawl script error', { error, options });
    process.exit(1);
  } finally {
    // Cleanup
    if (crawler) {
      await crawler.cleanup();
    }
    if (database) {
      database.close();
    }
  }
}

// Default crawl configuration for Sabah tourism
const defaultOptions: CrawlOptions = {
  keywords: [
    'sabah',
    'tourism',
    'travel',
    'kota kinabalu',
    'mount kinabalu',
    'sipadan',
  ],
  maxPosts: 100,
  downloadMedia: true,
  headless: false,
  dryRun: false,
};

// Parse command line arguments or use defaults
const args = process.argv.slice(2);
const options = { ...defaultOptions };

// Simple argument parsing
for (let i = 0; i < args.length; i++) {
  switch (args[i]) {
    case '--keywords':
      if (args[i + 1]) {
        options.keywords = args[i + 1].split(',').map((k) => k.trim());
        i++;
      }
      break;
    case '--max-posts':
      if (args[i + 1]) {
        options.maxPosts = Number.parseInt(args[i + 1]);
        i++;
      }
      break;
    case '--no-media':
      options.downloadMedia = false;
      break;
    case '--no-headless':
      options.headless = false;
      break;
    case '--resume':
      if (args[i + 1]) {
        options.resumeSession = Number.parseInt(args[i + 1]);
        i++;
      }
      break;
    case '--dry-run':
      options.dryRun = true;
      break;
    case '--help':
      console.log(chalk.blue('Douyin Crawler for Sabah Tourism'));
      console.log(
        chalk.cyan('\nUsage: bun run scripts/crawl-douyin.ts [options]'),
      );
      console.log(chalk.cyan('\nOptions:'));
      console.log(
        chalk.cyan(
          '  --keywords <keywords>    Comma-separated keywords (default: sabah,tourism,...)',
        ),
      );
      console.log(
        chalk.cyan(
          '  --max-posts <number>     Maximum posts to crawl (default: 100)',
        ),
      );
      console.log(chalk.cyan('  --no-media              Skip media downloads'));
      console.log(
        chalk.cyan('  --no-headless           Run browser in visible mode'),
      );
      console.log(
        chalk.cyan('  --resume <sessionId>     Resume a previous session'),
      );
      console.log(
        chalk.cyan('  --dry-run               Run without downloading media'),
      );
      console.log(
        chalk.cyan('  --help                  Show this help message'),
      );
      process.exit(0);
  }
}

// Validate options
if (!options.keywords || options.keywords.length === 0) {
  console.error(chalk.red('❌ No keywords provided'));
  process.exit(1);
}

if (options.maxPosts && options.maxPosts <= 0) {
  console.error(chalk.red('❌ Max posts must be greater than 0'));
  process.exit(1);
}

// Start crawling
crawlDouyin(options).catch((error) => {
  console.error(chalk.red(`❌ Unexpected error: ${error.message}`));
  logger.error('Unexpected crawl error', { error });
  process.exit(1);
});
