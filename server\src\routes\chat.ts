import { Hono } from 'hono';
import { BotService } from '../services/botService';
import DatabaseService from '../services/database';
import MessageHandlerService from '../services/messageHandler';
import openaiService from '../services/openai';
import whatsappService from '../services/whatsapp';
import type {
  ChatMessage,
  ConsolidatedMessageRequest,
} from '../types';
import { sessionManager } from '../utils/sessionManager';

const router = new Hono();
const messageHandler = new MessageHandlerService();

// Create a new chat session
router.post('/session', async (c) => {
  try {
    const user = c.get('user');
    const { userId } = user || {};
    const { sessionId } = sessionManager.createSession(
      userId?.toString(),
      c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown',
      c.req.header('user-agent'),
    );

    // Immediately create database session for web platform
    const dbService = new DatabaseService();
    await dbService.createChatSession(
      sessionId,
      'web',
      undefined,
      userId?.toString(),
    );

    return c.json({ sessionId });
  } catch (error) {
    console.error('Error creating chat session:', error);
    return c.json({ error: 'Failed to create session' }, 500);
  }
});

// Get chat session
router.get('/session/:sessionId', async (c) => {
  try {
    const sessionId = c.req.param('sessionId');

    // Validate session ID format
    if (
      !sessionId ||
      !/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(
        sessionId,
      )
    ) {
      return c.json({ error: 'Invalid session ID' }, 400);
    }

    const session = sessionManager.getSession(sessionId);

    if (!session) {
      return c.json({ error: 'Chat session not found or has expired' }, 404);
    }

    return c.json(session);
  } catch (error) {
    console.error('Error getting chat session:', error);
    return c.json({ error: 'Failed to get session' }, 500);
  }
});

// Send a text message
router.post('/message', async (c) => {
  try {
    const body = await c.req.json();
    const { sessionId, message, model, botSlug } = body;

    // Get session using session manager
    const session = sessionManager.getSession(sessionId);
    if (!session) {
      return c.json({ error: 'Chat session not found or has expired' }, 404);
    }

    // Get bot configuration
    const dbService = new DatabaseService();
    const botService = new BotService(dbService.db);
    const siteId = Number.parseInt(process.env.DEFAULT_SITE_ID || '1');

    const botConfig = await botService.getBotConfigForChat(siteId, botSlug);

    // Add user message to session
    const userMessage: ChatMessage = {
      role: 'user',
      content: message,
      timestamp: new Date(),
    };

    // Update session with new message
    const updated = sessionManager.updateSession(sessionId, userMessage);
    if (!updated) {
      return c.json({ error: 'Failed to update chat session' }, 500);
    }

    // Use messageHandler to process the message with halal knowledge tools
    const request: ConsolidatedMessageRequest = {
      message,
      sessionId,
      platform: 'web',
      messageType: 'text',
      userId: sessionId, // Use sessionId as userId for web platform
      systemPrompt: botConfig?.systemPrompt,
      config: {
        maxMessageHistory: 10,
        maxToolCallIterations: 5,
        defaultModel: botConfig?.model || model || 'gpt-4o-mini',
        temperature: botConfig?.temperature || 0.7,
        enableToolCalling: true,
        platform: 'web',
      },
    };

    const response = await messageHandler.handleIncomingMessage(request);

    if (!response.success) {
      return c.json({ error: response.error || 'Failed to get AI response' }, 503);
    }

    return c.json({
      message: response.message || '',
      answer: response.answer, // Include answer field for halal knowledge responses
      sources: response.sources, // Include sources for halal knowledge responses
      sessionId,
      usage: response.usage,
    });
  } catch (error) {
    console.error('Error processing message:', error);
    return c.json({ error: 'Failed to process message' }, 500);
  }
});

// Analyze image with optional text prompt
router.post('/image', async (c) => {
  try {
    const body = await c.req.json();
    const { sessionId, imageUrl, prompt, model } = body;

    // Get session using session manager
    const session = sessionManager.getSession(sessionId);
    if (!session) {
      return c.json({ error: 'Session not found' }, 404);
    }

    // Add user message with image to session
    const userMessage: ChatMessage = {
      role: 'user',
      content: `[Image uploaded] ${prompt || "What's in this image?"}`,
      imageUrl,
      timestamp: new Date(),
    };

    // Update session with new message
    const updated = sessionManager.updateSession(sessionId, userMessage);
    if (!updated) {
      return c.json({ error: 'Session not found' }, 404);
    }

    // Get response from OpenAI
    const response = await openaiService.analyzeImage(imageUrl, prompt, model);

    if (!response.success) {
      return c.json({ error: response.error || 'Image analysis failed' }, 500);
    }

    // Add assistant message to session
    const assistantMessage: ChatMessage = {
      role: 'assistant',
      content: response.message || '',
      timestamp: new Date(),
    };

    // Update session with assistant message
    sessionManager.updateSession(sessionId, assistantMessage);

    return c.json({
      message: response.message || '',
      sessionId,
      usage: response.usage,
    });
  } catch (error) {
    console.error('Error analyzing image:', error);
    return c.json({ error: 'Failed to analyze image' }, 500);
  }
});

// Clear chat session
router.delete('/session/:sessionId', async (c) => {
  try {
    const sessionId = c.req.param('sessionId');

    // Validate session ID format
    if (
      !sessionId ||
      !/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(
        sessionId,
      )
    ) {
      return c.json({ error: 'Invalid session ID' }, 400);
    }

    const deleted = sessionManager.deleteSession(sessionId);

    if (!deleted) {
      return c.json({ error: 'Chat session not found or already deleted' }, 404);
    }

    return c.json({
      message: 'Session cleared successfully',
      sessionId,
    });
  } catch (error) {
    console.error('Error deleting session:', error);
    return c.json({ error: 'Failed to delete session' }, 500);
  }
});

// Get WhatsApp integration status
router.get('/whatsapp-status', async (c) => {
  try {
    const isConfigured = whatsappService.isConfigured();
    const config = whatsappService.getConfig();

    return c.json({
      whatsappEnabled: isConfigured,
      phoneNumber: config?.phoneNumberId ? `+${config.phoneNumberId}` : null,
      message: isConfigured
        ? 'WhatsApp integration is active. You can also message us on WhatsApp!'
        : 'WhatsApp integration is not configured.',
    });
  } catch (error) {
    console.error('Error getting WhatsApp status:', error);
    return c.json({ error: 'Failed to get WhatsApp status' }, 500);
  }
});

// Add session statistics endpoint (for monitoring)
router.get('/stats', async (c) => {
  try {
    // Only allow authenticated users to view stats
    const user = c.get('user');
    if (!user) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    const stats = sessionManager.getStats();
    return c.json(stats);
  } catch (error) {
    console.error('Error getting stats:', error);
    return c.json({ error: 'Failed to get stats' }, 500);
  }
});

export default router;
