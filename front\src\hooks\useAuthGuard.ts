import { useEffect, useState } from 'react';
import { useRouter } from '@/i18n/navigation';
import { useAdminAuth, useAgentAuth } from '@/stores/auth';
import type { UserRole } from '@/types/roles';

/**
 * Hook for protecting admin routes
 * Automatically redirects to login if not authenticated
 * Supports role-based access control
 */
export const useAdminAuthGuard = (allowedRoles?: UserRole[]) => {
  const router = useRouter();
  const { isAuthenticated, verify, isLoading, user } = useAdminAuth();
  const [roleCheckLoading, setRoleCheckLoading] = useState(false);

  useEffect(() => {
    let isMounted = true;

    const checkAuth = async () => {
      if (!isAuthenticated) {
        // Try to verify existing token
        const isValid = await verify();
        if (!isValid && isMounted) {
          router.push('/admin');
          return;
        }
      }

      // Check role-based access if roles are specified
      if (allowedRoles && user) {
        const hasRequiredRole = allowedRoles.some((role) => user.role === role);

        if (!hasRequiredRole && isMounted) {
          router.push('/admin/unauthorized');
          return;
        }
      }
    };

    checkAuth();

    return () => {
      isMounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, router, verify, user, allowedRoles]); // verify intentionally excluded

  return {
    isAuthenticated,
    isLoading: isLoading || (!user && isAuthenticated),
    user,
    loading: isLoading || (!user && isAuthenticated),
  };
};

/**
 * Hook for protecting agent routes
 * Automatically redirects to login if not authenticated
 */
export const useAgentAuthGuard = () => {
  const router = useRouter();
  const { isAuthenticated, verify, isLoading } = useAgentAuth();

  useEffect(() => {
    const checkAuth = async () => {
      if (!isAuthenticated) {
        // Try to verify existing token
        const isValid = await verify();
        if (!isValid) {
          router.push('/agent');
        }
      }
    };

    checkAuth();
  }, [isAuthenticated, verify, router]);

  return { isAuthenticated, isLoading };
};

/**
 * Hook for login pages - redirects to dashboard if already authenticated
 */
export const useLoginRedirect = (userType: 'admin' | 'agent') => {
  const router = useRouter();
  const adminAuth = useAdminAuth();
  const agentAuth = useAgentAuth();

  useEffect(() => {
    // console.log('adminAuth.isAuthenticated', adminAuth.isAuthenticated)
    const checkAndRedirect = async () => {
      if (userType === 'admin') {
        if (adminAuth.isAuthenticated) {
          router.push('/admin/dashboard');
        } else {
          // Try to verify existing token
          const isValid = await adminAuth.verify();
          if (isValid) {
            router.push('/admin/dashboard');
          }
        }
      } else if (userType === 'agent') {
        const isValid = await agentAuth.verify();
        console.log('agent.isAuthenticated', {
          auth: agentAuth.isAuthenticated,
          isValid,
        });
        if (agentAuth.isAuthenticated && isValid) {
          router.push('/agent/dashboard');
        } else {
          console.log('isValid agent', isValid);
          if (isValid) {
            router.push('/agent/dashboard');
          }
        }
      }
    };

    checkAndRedirect();
  }, [
    userType,
    adminAuth.isAuthenticated,
    agentAuth.isAuthenticated,
    adminAuth.verify,
    agentAuth.verify,
    router,
  ]);
  return {
    isLoading: userType === 'admin' ? adminAuth.isLoading : agentAuth.isLoading,
  };
};

/**
 * Hook for automatic token refresh
 * Periodically verifies tokens to ensure they're still valid
 */
export const useTokenRefresh = () => {
  const adminAuth = useAdminAuth();
  const agentAuth = useAgentAuth();

  useEffect(() => {
    const refreshInterval = setInterval(
      async () => {
        // Verify admin token if authenticated
        if (adminAuth.isAuthenticated && adminAuth.token) {
          await adminAuth.verify();
        }

        // Verify agent token if authenticated
        if (agentAuth.isAuthenticated && agentAuth.token) {
          await agentAuth.verify();
        }
      },
      5 * 60 * 1000,
    ); // Check every 5 minutes

    return () => clearInterval(refreshInterval);
  }, [adminAuth, agentAuth]);
};
