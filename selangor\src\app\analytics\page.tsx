'use client';

import { useEffect, useState } from 'react';

interface AnalyticsSummary {
  totalSearches: number;
  searchesWithResults: number;
  successRate: number;
  avgResponseTime: number | null;
  searchesByType: Array<{ type: string; count: number }>;
  topQueries: Array<{
    query: string;
    count: number;
    avgResultsCount: number;
    successRate: number;
  }>;
  searchesByHour?: Array<{ hour: number; count: number }>;
  period: {
    type: string;
    startDate?: string;
    endDate?: string;
  };
}

interface SearchAnalytic {
  id: number;
  searchQuery: string;
  searchType: string;
  resultsCount: number;
  hasResults: boolean;
  responseTime: number | null;
  userAgent: string | null;
  ipAddress: string | null;
  sessionId: string | null;
  userId: number | null;
  searchFilters: string | null;
  createdAt: string;
}

export default function AnalyticsPage() {
  const [summary, setSummary] = useState<AnalyticsSummary | null>(null);
  const [recentSearches, setRecentSearches] = useState<SearchAnalytic[]>([]);
  const [period, setPeriod] = useState('7d');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch summary
      const summaryResponse = await fetch(
        `/api/analytics/summary?period=${period}`,
      );
      if (!summaryResponse.ok) {
        throw new Error('Failed to fetch analytics summary');
      }
      const summaryData = await summaryResponse.json();
      setSummary(summaryData);

      // Fetch recent searches
      const searchesResponse = await fetch('/api/analytics/search?limit=20');
      if (!searchesResponse.ok) {
        throw new Error('Failed to fetch recent searches');
      }
      const searchesData = await searchesResponse.json();
      setRecentSearches(searchesData.analytics);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [period]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading analytics...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center text-red-600">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Search Analytics
        </h1>

        {/* Period Selector */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Time Period
          </label>
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 bg-white"
          >
            <option value="1d">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="all">All Time</option>
          </select>
        </div>

        {/* Summary Cards */}
        {summary && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Total Searches
              </h3>
              <p className="text-3xl font-bold text-blue-600">
                {summary.totalSearches}
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Success Rate
              </h3>
              <p className="text-3xl font-bold text-green-600">
                {(summary.successRate * 100).toFixed(1)}%
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Searches with Results
              </h3>
              <p className="text-3xl font-bold text-purple-600">
                {summary.searchesWithResults}
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Avg Response Time
              </h3>
              <p className="text-3xl font-bold text-orange-600">
                {summary.avgResponseTime
                  ? `${summary.avgResponseTime.toFixed(0)}ms`
                  : 'N/A'}
              </p>
            </div>
          </div>
        )}

        {/* Search Types */}
        {summary && summary.searchesByType.length > 0 && (
          <div className="bg-white p-6 rounded-lg shadow mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Searches by Type
            </h3>
            <div className="space-y-2">
              {summary.searchesByType.map((item) => (
                <div
                  key={item.type}
                  className="flex justify-between items-center"
                >
                  <span className="capitalize">{item.type}</span>
                  <span className="font-semibold">{item.count}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Top Queries */}
        {summary && summary.topQueries.length > 0 && (
          <div className="bg-white p-6 rounded-lg shadow mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Top Search Queries
            </h3>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Query</th>
                    <th className="text-right py-2">Count</th>
                    <th className="text-right py-2">Avg Results</th>
                    <th className="text-right py-2">Success Rate</th>
                  </tr>
                </thead>
                <tbody>
                  {summary.topQueries.slice(0, 10).map((item, index) => (
                    <tr key={index} className="border-b">
                      <td className="py-2 pr-4">
                        {item.query || (
                          <em className="text-gray-500">(empty query)</em>
                        )}
                      </td>
                      <td className="text-right py-2">{item.count}</td>
                      <td className="text-right py-2">
                        {item.avgResultsCount}
                      </td>
                      <td className="text-right py-2">
                        {(item.successRate * 100).toFixed(1)}%
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Recent Searches */}
        {recentSearches.length > 0 && (
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Recent Searches
            </h3>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Query</th>
                    <th className="text-left py-2">Type</th>
                    <th className="text-right py-2">Results</th>
                    <th className="text-right py-2">Response Time</th>
                    <th className="text-left py-2">Time</th>
                  </tr>
                </thead>
                <tbody>
                  {recentSearches.slice(0, 10).map((search) => (
                    <tr key={search.id} className="border-b">
                      <td className="py-2 pr-4">
                        {search.searchQuery || (
                          <em className="text-gray-500">(empty query)</em>
                        )}
                      </td>
                      <td className="py-2 capitalize">{search.searchType}</td>
                      <td className="text-right py-2">
                        <span
                          className={
                            search.hasResults
                              ? 'text-green-600'
                              : 'text-red-600'
                          }
                        >
                          {search.resultsCount}
                        </span>
                      </td>
                      <td className="text-right py-2">
                        {search.responseTime
                          ? `${search.responseTime}ms`
                          : 'N/A'}
                      </td>
                      <td className="py-2">
                        {new Date(search.createdAt).toLocaleString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
