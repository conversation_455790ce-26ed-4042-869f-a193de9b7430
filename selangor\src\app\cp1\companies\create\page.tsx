'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Save } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { AdminLayout } from '@/components/admin/AdminLayout';
import {
  Button,
  Input,
  Select,
  type SelectOption,
  Textarea,
} from '@/components/ui';

const businessTypeOptions: SelectOption[] = [
  { value: 'Sdn Bhd', label: 'Sdn Bhd' },
  { value: 'Bhd', label: 'Bhd' },
  { value: 'Enterprise', label: 'Enterprise' },
  { value: 'Partnership', label: 'Partnership' },
  { value: 'Sole Proprietorship', label: 'Sole Proprietorship' },
];

const certificateStatusOptions: SelectOption[] = [
  { value: 'Active', label: 'Active' },
  { value: 'Inactive', label: 'Inactive' },
  { value: 'Pending', label: 'Pending' },
  { value: 'Expired', label: 'Expired' },
  { value: 'Suspended', label: 'Suspended' },
];

const certificateTypeOptions: SelectOption[] = [
  { value: 'HALAL', label: 'HALAL' },
  { value: 'HALAL JAKIM', label: 'HALAL JAKIM' },
  { value: 'HALAL SELANGOR', label: 'HALAL SELANGOR' },
];

const categoryOptions: SelectOption[] = [
  { value: 'Food Manufacturing', label: 'Food Manufacturing' },
  { value: 'Food Processing', label: 'Food Processing' },
  { value: 'Restaurant', label: 'Restaurant' },
  { value: 'Catering', label: 'Catering' },
  { value: 'Retail', label: 'Retail' },
  { value: 'Wholesale', label: 'Wholesale' },
  { value: 'Import/Export', label: 'Import/Export' },
  { value: 'Cosmetics', label: 'Cosmetics' },
  { value: 'Pharmaceutical', label: 'Pharmaceutical' },
  { value: 'Others', label: 'Others' },
];

const stateOptions: SelectOption[] = [
  { value: 'Selangor', label: 'Selangor' },
  { value: 'Kuala Lumpur', label: 'Kuala Lumpur' },
  { value: 'Johor', label: 'Johor' },
  { value: 'Penang', label: 'Penang' },
  { value: 'Perak', label: 'Perak' },
  { value: 'Kedah', label: 'Kedah' },
  { value: 'Kelantan', label: 'Kelantan' },
  { value: 'Terengganu', label: 'Terengganu' },
  { value: 'Pahang', label: 'Pahang' },
  { value: 'Negeri Sembilan', label: 'Negeri Sembilan' },
  { value: 'Melaka', label: 'Melaka' },
  { value: 'Perlis', label: 'Perlis' },
  { value: 'Sabah', label: 'Sabah' },
  { value: 'Sarawak', label: 'Sarawak' },
];

export default function CreateCompanyPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [formData, setFormData] = useState({
    companyName: '',
    registrationNumber: '',
    businessType: '',
    category: '',
    subcategory: '',
    address: '',
    state: '',
    postcode: '',
    city: '',
    country: 'Malaysia',
    phone: '',
    fax: '',
    email: '',
    website: '',
    contactPerson: '',
    certificateNumber: '',
    certificateType: '',
    certificateStatus: '',
    issuedDate: '',
    expiryDate: '',
    sourceUrl: '',
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.companyName.trim()) {
      newErrors.companyName = 'Company name is required';
    }

    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (formData.website && !formData.website.startsWith('http')) {
      newErrors.website = 'Website URL must start with http:// or https://';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/companies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        router.push('/cp1/companies');
      } else {
        const errorData = await response.json();
        console.error('Failed to create company:', errorData);
      }
    } catch (error) {
      console.error('Error creating company:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/cp1/companies">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Companies
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Create Company</h1>
            <p className="text-gray-600">Add a new halal certified company</p>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Basic Information
                </h3>

                <Input
                  label="Company Name *"
                  value={formData.companyName}
                  onChange={(e) =>
                    handleInputChange('companyName', e.target.value)
                  }
                  error={errors.companyName}
                  placeholder="Enter company name"
                />

                <Input
                  label="Registration Number"
                  value={formData.registrationNumber}
                  onChange={(e) =>
                    handleInputChange('registrationNumber', e.target.value)
                  }
                  placeholder="Enter registration number"
                />

                <Select
                  label="Business Type"
                  value={formData.businessType}
                  onChange={(e) =>
                    handleInputChange('businessType', e.target.value)
                  }
                  options={businessTypeOptions}
                  placeholder="Select business type"
                />

                <Select
                  label="Category"
                  value={formData.category}
                  onChange={(e) =>
                    handleInputChange('category', e.target.value)
                  }
                  options={categoryOptions}
                  placeholder="Select category"
                />

                <Input
                  label="Subcategory"
                  value={formData.subcategory}
                  onChange={(e) =>
                    handleInputChange('subcategory', e.target.value)
                  }
                  placeholder="Enter subcategory"
                />
              </div>

              {/* Certificate Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Certificate Information
                </h3>

                <Input
                  label="Certificate Number"
                  value={formData.certificateNumber}
                  onChange={(e) =>
                    handleInputChange('certificateNumber', e.target.value)
                  }
                  placeholder="Enter certificate number"
                />

                <Select
                  label="Certificate Type"
                  value={formData.certificateType}
                  onChange={(e) =>
                    handleInputChange('certificateType', e.target.value)
                  }
                  options={certificateTypeOptions}
                  placeholder="Select certificate type"
                />

                <Select
                  label="Certificate Status"
                  value={formData.certificateStatus}
                  onChange={(e) =>
                    handleInputChange('certificateStatus', e.target.value)
                  }
                  options={certificateStatusOptions}
                  placeholder="Select certificate status"
                />

                <Input
                  label="Issued Date"
                  type="date"
                  value={formData.issuedDate}
                  onChange={(e) =>
                    handleInputChange('issuedDate', e.target.value)
                  }
                />

                <Input
                  label="Expiry Date"
                  type="date"
                  value={formData.expiryDate}
                  onChange={(e) =>
                    handleInputChange('expiryDate', e.target.value)
                  }
                />
              </div>
            </div>

            {/* Location Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">
                Location Information
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Textarea
                  label="Address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="Enter address"
                  rows={3}
                />

                <div className="space-y-4">
                  <Select
                    label="State"
                    value={formData.state}
                    onChange={(e) => handleInputChange('state', e.target.value)}
                    options={stateOptions}
                    placeholder="Select state"
                  />

                  <Input
                    label="City"
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    placeholder="Enter city"
                  />

                  <Input
                    label="Postcode"
                    value={formData.postcode}
                    onChange={(e) =>
                      handleInputChange('postcode', e.target.value)
                    }
                    placeholder="Enter postcode"
                  />

                  <Input
                    label="Country"
                    value={formData.country}
                    onChange={(e) =>
                      handleInputChange('country', e.target.value)
                    }
                    placeholder="Enter country"
                  />
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">
                Contact Information
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <Input
                    label="Contact Person"
                    value={formData.contactPerson}
                    onChange={(e) =>
                      handleInputChange('contactPerson', e.target.value)
                    }
                    placeholder="Enter contact person name"
                  />

                  <Input
                    label="Phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="Enter phone number"
                  />

                  <Input
                    label="Fax"
                    value={formData.fax}
                    onChange={(e) => handleInputChange('fax', e.target.value)}
                    placeholder="Enter fax number"
                  />
                </div>

                <div className="space-y-4">
                  <Input
                    label="Email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    error={errors.email}
                    placeholder="Enter email address"
                  />

                  <Input
                    label="Website"
                    type="url"
                    value={formData.website}
                    onChange={(e) =>
                      handleInputChange('website', e.target.value)
                    }
                    error={errors.website}
                    placeholder="https://example.com"
                  />

                  <Input
                    label="Source URL"
                    type="url"
                    value={formData.sourceUrl}
                    onChange={(e) =>
                      handleInputChange('sourceUrl', e.target.value)
                    }
                    placeholder="https://source.com"
                  />
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <Link href="/cp1/companies">
                <Button variant="outline">Cancel</Button>
              </Link>
              <Button type="submit" isLoading={isLoading}>
                <Save className="h-4 w-4 mr-2" />
                Create Company
              </Button>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
