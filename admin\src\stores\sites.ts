import { create } from 'zustand';
import type {
  PaginatedResponse,
  SearchParams,
  Site,
  SiteCreateRequest,
  SiteUpdateRequest,
} from '@/types';
import { useAuthStore } from './auth';

interface SitesState {
  // State
  sites: Site[];
  currentSite: Site | null;
  isLoading: boolean;
  error: string | null;
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

interface SitesActions {
  // Actions
  fetchSites: (params?: SearchParams) => Promise<void>;
  fetchSiteById: (id: number) => Promise<Site | null>;
  createSite: (data: SiteCreateRequest) => Promise<boolean>;
  updateSite: (id: number, data: SiteUpdateRequest) => Promise<boolean>;
  deleteSite: (id: number) => Promise<boolean>;
  setCurrentSite: (site: Site | null) => void;
  clearError: () => void;
}

type SitesStore = SitesState & SitesActions;

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8787';

export const useSitesStore = create<SitesStore>((set, get) => ({
  // Initial state
  sites: [],
  currentSite: null,
  isLoading: false,
  error: null,
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  },

  // Actions
  fetchSites: async (params?: SearchParams) => {
    set({ isLoading: true, error: null });

    try {
      const { token } = useAuthStore.getState();
      if (!token) {
        set({ error: 'No authentication token', isLoading: false });
        return;
      }

      const searchParams = new URLSearchParams();
      if (params?.query) searchParams.append('query', params.query);
      if (params?.page) searchParams.append('page', params.page.toString());
      if (params?.limit) searchParams.append('limit', params.limit.toString());
      if (params?.sortBy) searchParams.append('sortBy', params.sortBy);
      if (params?.sortOrder) searchParams.append('sortOrder', params.sortOrder);

      const response = await fetch(
        `${API_BASE_URL}/api/admin/sites?${searchParams}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.ok) {
        const data: PaginatedResponse<Site> = await response.json();
        set({
          sites: data.data,
          pagination: {
            total: data.total,
            page: data.page,
            limit: data.limit,
            totalPages: data.totalPages,
          },
          isLoading: false,
        });
      } else {
        const errorData = await response.json();
        set({
          error: errorData.error || 'Failed to fetch sites',
          isLoading: false,
        });
      }
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Network error',
        isLoading: false,
      });
    }
  },

  fetchSiteById: async (id: number): Promise<Site | null> => {
    try {
      const { token } = useAuthStore.getState();
      if (!token) return null;

      const response = await fetch(`${API_BASE_URL}/api/admin/sites/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const site: Site = await response.json();
        set({ currentSite: site });
        return site;
      }
      return null;
    } catch (error) {
      console.error('Failed to fetch site:', error);
      return null;
    }
  },

  createSite: async (data: SiteCreateRequest): Promise<boolean> => {
    set({ isLoading: true, error: null });

    try {
      const { token } = useAuthStore.getState();
      if (!token) {
        set({ error: 'No authentication token', isLoading: false });
        return false;
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/sites`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        // Refresh sites list
        await get().fetchSites();
        set({ isLoading: false });
        return true;
      }
      const errorData = await response.json();
      set({
        error: errorData.error || 'Failed to create site',
        isLoading: false,
      });
      return false;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Network error',
        isLoading: false,
      });
      return false;
    }
  },

  updateSite: async (id: number, data: SiteUpdateRequest): Promise<boolean> => {
    set({ isLoading: true, error: null });

    try {
      const { token } = useAuthStore.getState();
      if (!token) {
        set({ error: 'No authentication token', isLoading: false });
        return false;
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/sites/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        // Refresh sites list
        await get().fetchSites();
        set({ isLoading: false });
        return true;
      }
      const errorData = await response.json();
      set({
        error: errorData.error || 'Failed to update site',
        isLoading: false,
      });
      return false;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Network error',
        isLoading: false,
      });
      return false;
    }
  },

  deleteSite: async (id: number): Promise<boolean> => {
    set({ isLoading: true, error: null });

    try {
      const { token } = useAuthStore.getState();
      if (!token) {
        set({ error: 'No authentication token', isLoading: false });
        return false;
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/sites/${id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        // Refresh sites list
        await get().fetchSites();
        set({ isLoading: false });
        return true;
      }
      const errorData = await response.json();
      set({
        error: errorData.error || 'Failed to delete site',
        isLoading: false,
      });
      return false;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Network error',
        isLoading: false,
      });
      return false;
    }
  },

  setCurrentSite: (site: Site | null) => {
    set({ currentSite: site });
  },

  clearError: () => {
    set({ error: null });
  },
}));
