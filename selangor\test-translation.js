/**
 * Quick test script to verify OpenAI-based translation functionality
 * Run with: node test-translation.js
 */

const { detectLanguageAndTranslate } = require('./src/lib/translation.ts');

async function testTranslation() {
  console.log('Testing OpenAI-based translation...\n');

  const testQueries = [
    'chicken halal', // English - should not translate
    'ayam halal', // Malay - should translate
    'makanan sijil halal', // Malay - should translate
    'شهادة حلال', // Arabic - should translate
    'halal certification', // English - should not translate
  ];

  for (const query of testQueries) {
    try {
      console.log(`Testing: "${query}"`);
      const result = await detectLanguageAndTranslate(query);

      console.log(`  Language: ${result.detectedLanguage}`);
      console.log(`  Confidence: ${result.confidence}`);
      console.log(`  Translations: ${JSON.stringify(result.translatedTerms)}`);
      console.log('');
    } catch (error) {
      console.error(`  Error: ${error.message}`);
      console.log('');
    }
  }
}

// Only run if OpenAI API key is configured
if (process.env.OPENAI_API_KEY) {
  testTranslation().catch(console.error);
} else {
  console.log('OPENAI_API_KEY not configured. Skipping translation test.');
  console.log(
    'Set OPENAI_API_KEY environment variable to test translation functionality.',
  );
}
