import { type RenderOptions, render } from '@testing-library/react';
import type { ReactElement } from 'react';
import type { SearchResult } from '@/types';

// Mock data generators for testing
export const generateMockSearchResult = (
  overrides: Partial<SearchResult> = {},
): SearchResult => ({
  certificateNumber: 'CERT-001',
  companyName: 'Test Company',
  productName: 'Test Product',
  status: 'valid',
  issueDate: '2024-01-01',
  expiryDate: '2026-01-01',
  country: 'Malaysia',
  category: 'food',
  ...overrides,
});

export const generateMockSearchResults = (count = 3): SearchResult[] => {
  return Array.from({ length: count }, (_, index) =>
    generateMockSearchResult({
      certificateNumber: `CERT-${String(index + 1).padStart(3, '0')}`,
      companyName: `Test Company ${index + 1}`,
      productName: `Test Product ${index + 1}`,
      status: index % 2 === 0 ? 'valid' : 'expired',
    }),
  );
};

// Mock API responses
export const mockApiResponses = {
  search: {
    success: {
      results: generateMockSearchResults(5),
      total: 5,
      page: 1,
      limit: 20,
    },
    empty: {
      results: [],
      total: 0,
      page: 1,
      limit: 20,
    },
    error: {
      error: 'Search service unavailable',
    },
  },
  announcements: {
    success: [
      {
        id: '1',
        title: 'Test Announcement',
        titleBM: 'Pengumuman Ujian',
        content: 'This is a test announcement',
        contentBM: 'Ini adalah pengumuman ujian',
        date: '2024-01-01',
        priority: 'high' as const,
      },
    ],
  },
  news: {
    success: [
      {
        id: '1',
        title: 'Test News',
        titleBM: 'Berita Ujian',
        excerpt: 'This is test news',
        excerptBM: 'Ini adalah berita ujian',
        date: '2024-01-01',
        category: 'general',
      },
    ],
  },
};

// Test utilities for form testing
export const fillForm = async (user: any, formData: Record<string, string>) => {
  for (const [field, value] of Object.entries(formData)) {
    const input = document.querySelector(`[name="${field}"]`) as
      | HTMLInputElement
      | HTMLTextAreaElement
      | HTMLSelectElement;
    if (input) {
      if (input.tagName === 'SELECT') {
        await user.selectOptions(input, value);
      } else {
        await user.clear(input);
        await user.type(input, value);
      }
    }
  }
};

// Accessibility testing helpers
export const checkAccessibility = (container: HTMLElement) => {
  // Check for proper heading hierarchy
  const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
  const headingLevels = Array.from(headings).map((h) =>
    Number.parseInt(h.tagName.charAt(1)),
  );

  // Should start with h1
  if (headingLevels.length > 0) {
    expect(headingLevels[0]).toBe(1);
  }

  // Check for alt text on images
  const images = container.querySelectorAll('img');
  images.forEach((img) => {
    expect(img.getAttribute('alt')).toBeTruthy();
  });

  // Check for form labels
  const inputs = container.querySelectorAll(
    'input[required], textarea[required], select[required]',
  );
  inputs.forEach((input) => {
    const id = input.getAttribute('id');
    if (id) {
      const label = container.querySelector(`label[for="${id}"]`);
      expect(label).toBeTruthy();
    }
  });
};

// Performance testing helpers
export const measureRenderTime = (renderFn: () => void): number => {
  const start = performance.now();
  renderFn();
  const end = performance.now();
  return end - start;
};

// Mock intersection observer for components that use it
export const mockIntersectionObserver = () => {
  const mockIntersectionObserver = jest.fn();
  mockIntersectionObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  });
  window.IntersectionObserver = mockIntersectionObserver;
};

// Mock resize observer for components that use it
export const mockResizeObserver = () => {
  const mockResizeObserver = jest.fn();
  mockResizeObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  });
  window.ResizeObserver = mockResizeObserver;
};

// Mock local storage
export const mockLocalStorage = () => {
  const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
  });
  return localStorageMock;
};

// Mock session storage
export const mockSessionStorage = () => {
  const sessionStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock,
  });
  return sessionStorageMock;
};

// Mock fetch for API testing
export const mockFetch = (responses: Record<string, any>) => {
  const originalFetch = global.fetch;

  global.fetch = jest.fn((url: string) => {
    const response = responses[url] || responses.default;
    return Promise.resolve({
      ok: true,
      status: 200,
      json: () => Promise.resolve(response),
      text: () => Promise.resolve(JSON.stringify(response)),
    } as Response);
  });

  return () => {
    global.fetch = originalFetch;
  };
};

// Wait for async operations to complete
export const waitForAsyncOperations = () => {
  return new Promise((resolve) => setTimeout(resolve, 0));
};

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  // Add any provider props here if needed
}

export const customRender = (
  ui: ReactElement,
  options?: CustomRenderOptions,
) => {
  // If you have providers (like language context, theme, etc.), wrap them here
  return render(ui, options);
};

// Re-export everything from testing library
export * from '@testing-library/react';
export { customRender as render };

// Test data constants
export const TEST_CONSTANTS = {
  VALID_EMAIL: '<EMAIL>',
  INVALID_EMAIL: 'invalid-email',
  VALID_PHONE: '+60123456789',
  INVALID_PHONE: '123',
  SAMPLE_COMPANY: 'Test Company Sdn Bhd',
  SAMPLE_CERTIFICATE: 'JAKIM-001-2024',
  SAMPLE_MESSAGE:
    'This is a test message with sufficient length for validation.',
  SHORT_MESSAGE: 'Short',
  LONG_MESSAGE: 'A'.repeat(1000),
} as const;

// Error boundary for testing error states
export class TestErrorBoundary extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'TestErrorBoundary';
  }
}

// Mock console methods for testing
export const mockConsole = () => {
  const originalConsole = { ...console };

  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
  console.info = jest.fn();

  return () => {
    Object.assign(console, originalConsole);
  };
};

// Viewport testing helpers
export const setViewport = (width: number, height: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  });
  window.dispatchEvent(new Event('resize'));
};

// Common viewport sizes
export const VIEWPORTS = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  desktop: { width: 1920, height: 1080 },
  ultrawide: { width: 2560, height: 1440 },
} as const;
