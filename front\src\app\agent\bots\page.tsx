'use client';

import { useEffect, useState } from 'react';

interface Bot {
  id: number;
  name: string;
  slug: string;
  provider: string;
  model: string;
  temperature: number;
  isDefault: boolean;
}

export default function BotsPage() {
  const [bots, setBots] = useState<Bot[]>([]);

  useEffect(() => {
    // Fetch bots from the API
    const fetchBots = async () => {
      const response = await fetch('/api/bots');
      const data = await response.json();
      setBots(data);
    };

    fetchBots();
  }, []);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Bots</h1>
      <div className="mb-4">
        <button
          type="button"
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          Create Bot
        </button>
      </div>
      <div className="bg-white shadow-md rounded my-6">
        <table className="min-w-max w-full table-auto">
          <thead>
            <tr className="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
              <th className="py-3 px-6 text-left">Name</th>
              <th className="py-3 px-6 text-left">Slug</th>
              <th className="py-3 px-6 text-center">Provider</th>
              <th className="py-3 px-6 text-center">Model</th>
              <th className="py-3 px-6 text-center">Default</th>
              <th className="py-3 px-6 text-center">Actions</th>
            </tr>
          </thead>
          <tbody className="text-gray-600 text-sm font-light">
            {bots.map((bot) => (
              <tr
                key={bot.id}
                className="border-b border-gray-200 hover:bg-gray-100"
              >
                <td className="py-3 px-6 text-left whitespace-nowrap">
                  {bot.name}
                </td>
                <td className="py-3 px-6 text-left">{bot.slug}</td>
                <td className="py-3 px-6 text-center">{bot.provider}</td>
                <td className="py-3 px-6 text-center">{bot.model}</td>
                <td className="py-3 px-6 text-center">
                  {bot.isDefault ? 'Yes' : 'No'}
                </td>
                <td className="py-3 px-6 text-center">
                  <div className="flex item-center justify-center">
                    <button
                      type="button"
                      className="w-4 mr-2 transform hover:text-purple-500 hover:scale-110"
                    >
                      {/* Edit Icon */}
                    </button>
                    <button
                      type="button"
                      className="w-4 mr-2 transform hover:text-red-500 hover:scale-110"
                    >
                      {/* Delete Icon */}
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
