'use client';

export const runtime = 'edge';

import type { BreadcrumbItem } from '@/components/breadcrumb';
import { PageWrapper } from '@/components/page-wrapper';
import { useLanguage } from '@/lib/language-context';

export default function ApplicationProcessPage() {
  const { language } = useLanguage();

  const breadcrumbs: BreadcrumbItem[] = [
    {
      label: language === 'en' ? 'Home' : 'Laman <PERSON>',
      href: '/',
    },
    {
      label: language === 'en' ? 'Procedure' : 'Prosedur',
      href: '/procedure',
    },
    {
      label: language === 'en' ? 'Application Process' : 'Proses Permohonan',
      href: '/procedure/application',
    },
  ];

  return (
    <PageWrapper
      title="Application Process"
      titleBM="Proses Permohonan"
      description="Step-by-step guide to the Halal certification application process."
      descriptionBM="Panduan langkah demi langkah untuk proses permohonan pensijilan Halal."
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-8">
        {/* Introduction */}
        <div className="card">
          <h2 className="text-2xl font-bold mb-4 text-gray-900">
            {language === 'en'
              ? 'Halal Certification Application Process'
              : 'Proses Permohonan Pensijilan Halal'}
          </h2>
          <p className="text-gray-600 leading-relaxed">
            {language === 'en'
              ? 'The Halal certification process is designed to ensure that products and services meet the highest Islamic standards. Follow these steps to apply for Halal certification from JAKIM.'
              : 'Proses pensijilan Halal direka untuk memastikan produk dan perkhidmatan memenuhi piawaian Islam tertinggi. Ikuti langkah-langkah ini untuk memohon pensijilan Halal daripada JAKIM.'}
          </p>
        </div>

        {/* Application Steps */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Application Steps'
              : 'Langkah-langkah Permohonan'}
          </h3>
          <div className="space-y-6">
            {/* Step 1 */}
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-10 h-10 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold">1</span>
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900 mb-2">
                  {language === 'en'
                    ? 'Online Registration'
                    : 'Pendaftaran Dalam Talian'}
                </h4>
                <p className="text-gray-600 text-sm mb-3">
                  {language === 'en'
                    ? 'Register your company account on the MYeHALAL system and complete the online application form.'
                    : 'Daftarkan akaun syarikat anda di sistem MYeHALAL dan lengkapkan borang permohonan dalam talian.'}
                </p>
                <div className="flex space-x-4">
                  <a
                    href="https://myehalal.halal.gov.my/domestik/v1/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-4 py-2 bg-primary-green text-white text-sm rounded-lg hover:bg-primary-green-dark transition-colors"
                  >
                    {language === 'en'
                      ? 'MYeHALAL Domestic'
                      : 'MYeHALAL Domestik'}
                    <svg
                      className="w-4 h-4 ml-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                      />
                    </svg>
                  </a>
                  <a
                    href="https://myehalal.halal.gov.my/international/v1/pemohon/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors"
                  >
                    {language === 'en'
                      ? 'MYeHALAL International'
                      : 'MYeHALAL Antarabangsa'}
                    <svg
                      className="w-4 h-4 ml-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                      />
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            {/* Step 2 */}
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-10 h-10 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold">2</span>
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900 mb-2">
                  {language === 'en'
                    ? 'Document Submission'
                    : 'Penyerahan Dokumen'}
                </h4>
                <p className="text-gray-600 text-sm mb-3">
                  {language === 'en'
                    ? 'Submit all required documents including company registration, product specifications, and manufacturing processes.'
                    : 'Serahkan semua dokumen yang diperlukan termasuk pendaftaran syarikat, spesifikasi produk, dan proses pembuatan.'}
                </p>
                <div className="bg-bg-light-green p-3 rounded-lg">
                  <h5 className="font-medium text-gray-900 mb-2">
                    {language === 'en'
                      ? 'Required Documents:'
                      : 'Dokumen Diperlukan:'}
                  </h5>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>
                      •{' '}
                      {language === 'en'
                        ? 'Company registration certificate'
                        : 'Sijil pendaftaran syarikat'}
                    </li>
                    <li>
                      •{' '}
                      {language === 'en'
                        ? 'Product ingredient list'
                        : 'Senarai ramuan produk'}
                    </li>
                    <li>
                      •{' '}
                      {language === 'en'
                        ? 'Manufacturing process flow'
                        : 'Aliran proses pembuatan'}
                    </li>
                    <li>
                      •{' '}
                      {language === 'en'
                        ? 'Supplier certificates'
                        : 'Sijil pembekal'}
                    </li>
                    <li>
                      •{' '}
                      {language === 'en'
                        ? 'Factory layout plan'
                        : 'Pelan susun atur kilang'}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Step 3 */}
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-10 h-10 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold">3</span>
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900 mb-2">
                  {language === 'en' ? 'Document Review' : 'Semakan Dokumen'}
                </h4>
                <p className="text-gray-600 text-sm">
                  {language === 'en'
                    ? 'JAKIM officers will review your submitted documents for completeness and compliance with Halal standards.'
                    : 'Pegawai JAKIM akan menyemak dokumen yang diserahkan untuk kelengkapan dan pematuhan dengan piawaian Halal.'}
                </p>
              </div>
            </div>

            {/* Step 4 */}
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-10 h-10 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold">4</span>
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900 mb-2">
                  {language === 'en' ? 'Site Inspection' : 'Pemeriksaan Tapak'}
                </h4>
                <p className="text-gray-600 text-sm">
                  {language === 'en'
                    ? 'Qualified auditors will conduct an on-site inspection of your facilities to verify compliance with Halal requirements.'
                    : 'Juruaudit berkelayakan akan menjalankan pemeriksaan di tapak kemudahan anda untuk mengesahkan pematuhan dengan keperluan Halal.'}
                </p>
              </div>
            </div>

            {/* Step 5 */}
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-10 h-10 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold">5</span>
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900 mb-2">
                  {language === 'en'
                    ? 'Evaluation & Decision'
                    : 'Penilaian & Keputusan'}
                </h4>
                <p className="text-gray-600 text-sm">
                  {language === 'en'
                    ? 'The Halal Panel will evaluate the inspection report and make a decision on your certification application.'
                    : 'Panel Halal akan menilai laporan pemeriksaan dan membuat keputusan mengenai permohonan pensijilan anda.'}
                </p>
              </div>
            </div>

            {/* Step 6 */}
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-10 h-10 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold">6</span>
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900 mb-2">
                  {language === 'en'
                    ? 'Certificate Issuance'
                    : 'Pengeluaran Sijil'}
                </h4>
                <p className="text-gray-600 text-sm">
                  {language === 'en'
                    ? 'Upon approval, your Halal certificate will be issued and you can begin using the Halal logo on your products.'
                    : 'Setelah diluluskan, sijil Halal anda akan dikeluarkan dan anda boleh mula menggunakan logo Halal pada produk anda.'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Processing Time */}
        <div className="card">
          <h3 className="text-xl font-bold mb-4 text-gray-900">
            {language === 'en' ? 'Processing Time' : 'Masa Pemprosesan'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-bg-light-green rounded-lg">
              <div className="text-2xl font-bold text-primary-green mb-2">
                30-45
              </div>
              <div className="text-sm text-gray-600">
                {language === 'en' ? 'Working Days' : 'Hari Bekerja'}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {language === 'en'
                  ? 'Standard Processing'
                  : 'Pemprosesan Standard'}
              </div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600 mb-2">
                60-90
              </div>
              <div className="text-sm text-gray-600">
                {language === 'en' ? 'Working Days' : 'Hari Bekerja'}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {language === 'en'
                  ? 'Complex Applications'
                  : 'Permohonan Kompleks'}
              </div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600 mb-2">90+</div>
              <div className="text-sm text-gray-600">
                {language === 'en' ? 'Working Days' : 'Hari Bekerja'}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {language === 'en' ? 'Special Cases' : 'Kes Khas'}
              </div>
            </div>
          </div>
        </div>

        {/* Important Notes */}
        <div className="card">
          <h3 className="text-xl font-bold mb-4 text-gray-900">
            {language === 'en' ? 'Important Notes' : 'Nota Penting'}
          </h3>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
              <p className="text-gray-600 text-sm">
                {language === 'en'
                  ? 'All applications must be submitted through the official MYeHALAL system.'
                  : 'Semua permohonan mesti diserahkan melalui sistem MYeHALAL rasmi.'}
              </p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
              <p className="text-gray-600 text-sm">
                {language === 'en'
                  ? 'Incomplete applications will be returned for additional information.'
                  : 'Permohonan yang tidak lengkap akan dikembalikan untuk maklumat tambahan.'}
              </p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
              <p className="text-gray-600 text-sm">
                {language === 'en'
                  ? 'Application fees are non-refundable regardless of the outcome.'
                  : 'Yuran permohonan tidak boleh dikembalikan tanpa mengira keputusan.'}
              </p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
              <p className="text-gray-600 text-sm">
                {language === 'en'
                  ? 'Certificates are valid for 2 years and must be renewed before expiry.'
                  : 'Sijil sah untuk 2 tahun dan mesti diperbaharui sebelum tamat tempoh.'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}
