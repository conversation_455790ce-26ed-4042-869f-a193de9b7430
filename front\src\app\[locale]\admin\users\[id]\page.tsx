'use client';

export const runtime = 'edge';

import { ArrowLeft, Save } from 'lucide-react';
import { useParams } from 'next/navigation';
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';
import { Link, useRouter } from '@/i18n/navigation';
import { api } from '@/lib/api';
import type { AdminUserResponse, AdminUserUpdateRequest } from '@/types';
import { UserRole } from '@/types/roles';

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

export default function EditAdminUserPage() {
  const router = useRouter();
  const params = useParams();
  const userId = params.id ? Number.parseInt(params.id as string, 10) : null;

  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState(''); // Optional: only if changing
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [roles, setRoles] = useState<UserRole[]>([UserRole.EDITOR]);
  const [isActive, setIsActive] = useState(true);
  const [originalUser, setOriginalUser] = useState<AdminUserResponse | null>(
    null,
  );

  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [isAdmin, setIsAdmin] = useState(false);
  const [checkingAuth, setCheckingAuth] = useState(true);

  const fetchUser = useCallback(async (id: number) => {
    setIsFetching(true);
    try {
      const response = await api.admin.getUserById(id);
      // Assuming response.data contains the user object
      const userData = response.data || response;
      if (userData) {
        setOriginalUser(userData);
        setUsername(userData.username);
        setEmail(userData.email || '');
        setFirstName(userData.firstName || '');
        setLastName(userData.lastName || '');
        setRoles(userData.roles && userData.roles.length > 0 ? userData.roles : [UserRole.EDITOR]);
        setIsActive(userData.isActive !== undefined ? userData.isActive : true);
      } else {
        setError('User not found.');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch user details.');
      console.error(err);
    } finally {
      setIsFetching(false);
    }
  }, []);

  useEffect(() => {
    const checkAdminRoleAndFetchUser = async () => {
      setCheckingAuth(true);
      try {
        const meResponse = await api.admin.getMe();
        if (meResponse.user?.role === UserRole.ADMIN) {
          setIsAdmin(true);
          if (userId) {
            fetchUser(userId);
          } else {
            setError('User ID not found.');
            setIsFetching(false);
          }
        } else {
          setError('Access Denied: You do not have permission to edit users.');
          setIsFetching(false); // No need to fetch user if not admin
        }
      } catch (err) {
        setError('Failed to verify user role or fetch user.');
        console.error(err);
        setIsFetching(false);
      } finally {
        setCheckingAuth(false);
      }
    };
    checkAdminRoleAndFetchUser();
  }, [userId, fetchUser]); // Add userId as dependency

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!userId) {
      setError('User ID is missing.');
      return;
    }
    if (!username.trim()) {
      setError('Username is required.');
      return;
    }
    if (roles.length === 0) {
      setError('At least one role is required.');
      return;
    }
    setIsLoading(true);
    setError(null);

    const updateData: AdminUserUpdateRequest = {
      username,
      email: email.trim() || undefined,
      firstName: firstName.trim() || undefined,
      lastName: lastName.trim() || undefined,
      roles,
      isActive
    };
    if (password) {
      // Only include password if it's being changed
      updateData.password = password;
    }

    try {
      await api.admin.updateUser(userId, updateData);
      // Add success message/toast
      router.push('/admin/users');
    } catch (err: any) {
      setError(
        err.response?.data?.error || err.message || 'Failed to update user.',
      );
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  if (checkingAuth || (isAdmin && isFetching && userId)) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500" />
      </div>
    );
  }

  if (!isAdmin) {
    // If error was set during auth check, or if simply not admin
    const accessError =
      error || 'Access Denied: You do not have permission to edit users.';
    return (
      <div className="container mx-auto p-4">
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
          role="alert"
        >
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{accessError}</span>
        </div>
        <Link
          href="/admin/users"
          className="text-blue-500 hover:text-blue-700 inline-flex items-center"
        >
          <ArrowLeft size={18} className="mr-1" /> Back to User List
        </Link>
      </div>
    );
  }

  if (error && isAdmin) {
    // Error during data fetching, but user is admin
    return (
      <div className="container mx-auto p-4">
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
          role="alert"
        >
          <strong className="font-bold">Error fetching user data: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
        <Link
          href="/admin/users"
          className="text-blue-500 hover:text-blue-700 inline-flex items-center"
        >
          <ArrowLeft size={18} className="mr-1" /> Back to User List
        </Link>
      </div>
    );
  }

  if (!originalUser && isAdmin && !isFetching) {
    // Should be caught by error state, but as a fallback
    return (
      <div className="container mx-auto p-4">
        User not found or error occurred.
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="mb-6">
        <Link
          href="/admin/users"
          className="text-blue-500 hover:text-blue-700 inline-flex items-center"
        >
          <ArrowLeft size={18} className="mr-1" />
          Back to User List
        </Link>
      </div>
      <h1 className="text-2xl md:text-3xl font-semibold text-gray-800 mb-6">
        Edit Admin User: {originalUser?.username}
      </h1>

      <form
        onSubmit={handleSubmit}
        className="bg-white shadow-md rounded-lg p-6 md:p-8"
      >
        {error &&
          isAdmin && ( // For form submission errors when user is admin
            <div
              className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6"
              role="alert"
            >
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{error}</span>
            </div>
          )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label
              htmlFor="username"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Username *
            </label>
            <input
              type="text"
              id="username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
              disabled={isLoading}
            />
          </div>

          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Email
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              disabled={isLoading}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label
              htmlFor="firstName"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              First Name
            </label>
            <input
              type="text"
              id="firstName"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              disabled={isLoading}
            />
          </div>

          <div>
            <label
              htmlFor="lastName"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Last Name
            </label>
            <input
              type="text"
              id="lastName"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              disabled={isLoading}
            />
          </div>
        </div>

        <div className="mb-4">
          <label
            htmlFor="password"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            New Password (optional)
          </label>
          <input
            type="password"
            id="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Leave blank to keep current password"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            disabled={isLoading}
          />
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Roles *
          </label>
          <div className="space-y-2">
            {Object.values(UserRole).map((roleOption) => (
              <label key={roleOption} className="flex items-center">
                <input
                  type="checkbox"
                  checked={roles.includes(roleOption)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setRoles([...roles, roleOption]);
                    } else {
                      setRoles(roles.filter(r => r !== roleOption));
                    }
                  }}
                  className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  disabled={isLoading}
                />
                <span className="text-sm text-gray-700">{roleOption}</span>
              </label>
            ))}
          </div>
          {roles.length === 0 && (
            <p className="text-red-500 text-sm mt-1">At least one role is required</p>
          )}
        </div>

        <div className="mb-6">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={isActive}
              onChange={(e) => setIsActive(e.target.checked)}
              className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              disabled={isLoading}
            />
            <span className="text-sm font-medium text-gray-700">Active User</span>
          </label>
          <p className="text-xs text-gray-500 mt-1">
            Inactive users cannot log in to the system
          </p>
        </div>

        <div className="flex items-center justify-end">
          <button
            type="submit"
            className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg inline-flex items-center transition duration-150 ease-in-out disabled:opacity-50"
            disabled={isLoading || !isAdmin}
          >
            <Save size={18} className="mr-2" />
            {isLoading ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
}
