import type { PlatformConfig } from '../../types/social-media.js';

export const douyinConfig: PlatformConfig = {
  name: 'Douyin',
  baseUrl: process.env.DOUYIN_BASE_URL || 'https://www.douyin.com',
  searchEndpoint: '/search',
  rateLimits: {
    requestsPerMinute: 20,
    downloadConcurrency: 2,
  },
  selectors: {
    searchBox: 'input[placeholder*="搜索"]',
    searchButton: 'button[type="submit"]',
    postContainer: '[data-e2e="search-result"]',
    postLink: 'a[href*="/video/"]',
    authorName: '[data-e2e="search-card-user-link"]',
    postContent: '[data-e2e="search-card-desc"]',
    mediaElements: 'video, img[src*="video"]',
    engagementStats:
      '[data-e2e="search-card-like"], [data-e2e="search-card-comment"]',
  },
  waitTimes: {
    pageLoad: Number.parseInt(process.env.PAGE_LOAD_TIMEOUT || '10000'),
    searchDelay: Number.parseInt(process.env.DOUYIN_SEARCH_DELAY || '2000'),
    scrollDelay: Number.parseInt(process.env.DOUYIN_SCROLL_DELAY || '1000'),
    elementTimeout: Number.parseInt(process.env.ELEMENT_TIMEOUT || '5000'),
  },
};

export const douyinSelectors = {
  // Search page selectors
  searchInput: 'input[data-e2e="searchbar-input"]',
  searchButton: 'button[data-e2e="searchbar-button"]',
  searchResults: '[data-e2e="search-result"]',

  // Video card selectors
  videoCard: '[data-e2e="search-card-video"]',
  videoLink: 'a[href*="/video/"]',
  videoTitle: '[data-e2e="search-card-desc"]',
  videoAuthor: '[data-e2e="search-card-user-link"]',
  videoStats: '[data-e2e="search-card-like"], [data-e2e="search-card-comment"]',

  // Video page selectors
  videoPlayer: 'video',
  videoDescription: '[data-e2e="browse-video-desc"]',
  authorInfo: '[data-e2e="browse-user-avatar"]',
  authorName: '[data-e2e="browse-username"]',
  likeCount: '[data-e2e="browse-like-count"]',
  commentCount: '[data-e2e="browse-comment-count"]',
  shareCount: '[data-e2e="browse-share-count"]',

  // Media selectors
  videoSource: 'video source',
  videoThumbnail: 'img[src*="video"]',

  // Navigation selectors
  loadMoreButton: '[data-e2e="search-load-more"]',
  nextPageButton: '[data-e2e="search-pagination-next"]',
};

export const douyinUrls = {
  home: 'https://www.douyin.com',
  search: 'https://www.douyin.com/search',
  video: (videoId: string) => `https://www.douyin.com/video/${videoId}`,
  user: (userId: string) => `https://www.douyin.com/user/${userId}`,
};

export const douyinConstants = {
  maxScrollAttempts: Number.parseInt(
    process.env.DOUYIN_MAX_SCROLL_ATTEMPTS || '10',
  ),
  scrollDelay: Number.parseInt(process.env.DOUYIN_SCROLL_DELAY || '1000'),
  searchDelay: Number.parseInt(process.env.DOUYIN_SEARCH_DELAY || '3000'), // Increased for popup handling
  videoLoadDelay: 5000, // Increased for popup handling
  maxRetries: 3,
  requestTimeout: 60000, // Increased to 60 seconds for better reliability
  popupHandlingTimeout: 10000, // New timeout specifically for popup handling
};

export const douyinHeaders = {
  'User-Agent':
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  Accept:
    'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
  'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
  'Accept-Encoding': 'gzip, deflate, br',
  DNT: '1',
  Connection: 'keep-alive',
  'Upgrade-Insecure-Requests': '1',
};
