'use client';

import { Calendar, ChevronRight, Tag } from 'lucide-react';
import Image from 'next/image';
import { Link } from '@/i18n/navigation';
import { useLanguage } from '@/lib/language-context';
import { cn } from '@/lib/utils';
import type { NewsItem } from '@/types';

interface NewsCardProps {
  news: NewsItem;
  variant?: 'default' | 'compact' | 'featured' | 'horizontal';
  showCategory?: boolean;
  showImage?: boolean;
  className?: string;
}

export function NewsCard({
  news,
  variant = 'default',
  showCategory = true,
  showImage = true,
  className,
}: NewsCardProps) {
  const { language } = useLanguage();

  const title = language === 'bm' ? news.titleBM : news.title;
  const excerpt = language === 'bm' ? news.excerptBM : news.excerpt;
  const category = language === 'bm' ? news.categoryBM : news.category;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'bm' ? 'ms-MY' : 'en-MY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (variant === 'compact') {
    return (
      <Link
        href={`/news/${news.id}`}
        className={cn(
          'block p-4 border border-gray-200 rounded-lg hover:border-primary-green hover:bg-bg-light-green transition-all duration-200',
          className,
        )}
      >
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              {showCategory && (
                <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-primary-green bg-primary-green bg-opacity-10 rounded-full">
                  <Tag className="w-3 h-3" />
                  {category}
                </span>
              )}
              <span className="text-xs text-gray-500 flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                {formatDate(news.date)}
              </span>
            </div>
            <h3 className="font-semibold text-gray-900 line-clamp-2 mb-1">
              {title}
            </h3>
            <p className="text-sm text-gray-600 line-clamp-2">{excerpt}</p>
          </div>
          <ChevronRight className="w-5 h-5 text-gray-400 flex-shrink-0 mt-1" />
        </div>
      </Link>
    );
  }

  if (variant === 'horizontal') {
    return (
      <Link
        href={`/news/${news.id}`}
        className={cn(
          'block card hover:shadow-lg transition-all duration-200',
          className,
        )}
      >
        <div className="flex gap-4">
          {showImage && news.image && (
            <div className="flex-shrink-0 w-24 h-24 md:w-32 md:h-32">
              <Image
                src={news.image}
                alt={title}
                width={128}
                height={128}
                className="w-full h-full object-cover rounded-lg"
              />
            </div>
          )}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              {showCategory && (
                <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-primary-green bg-primary-green bg-opacity-10 rounded-full">
                  <Tag className="w-3 h-3" />
                  {category}
                </span>
              )}
              <span className="text-sm text-gray-500 flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                {formatDate(news.date)}
              </span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 line-clamp-2 mb-2">
              {title}
            </h3>
            <p className="text-gray-600 line-clamp-2">{excerpt}</p>
          </div>
        </div>
      </Link>
    );
  }

  if (variant === 'featured') {
    return (
      <Link
        href={`/news/${news.id}`}
        className={cn(
          'block relative overflow-hidden rounded-xl bg-white shadow-lg hover:shadow-xl transition-all duration-300',
          className,
        )}
      >
        {showImage && news.image && (
          <div className="relative h-48 md:h-64">
            <Image
              src={news.image}
              alt={title}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent" />
            <div className="absolute bottom-4 left-4 right-4">
              <div className="flex items-center gap-2 mb-2">
                {showCategory && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-white bg-white bg-opacity-20 rounded-full">
                    <Tag className="w-3 h-3" />
                    {category}
                  </span>
                )}
                <span className="text-white text-opacity-90 text-sm flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {formatDate(news.date)}
                </span>
              </div>
              <h3 className="text-xl font-bold text-white line-clamp-2">
                {title}
              </h3>
            </div>
          </div>
        )}
        <div className="p-6">
          {(!showImage || !news.image) && (
            <>
              <div className="flex items-center gap-2 mb-3">
                {showCategory && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-primary-green bg-primary-green bg-opacity-10 rounded-full">
                    <Tag className="w-3 h-3" />
                    {category}
                  </span>
                )}
                <span className="text-sm text-gray-500 flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {formatDate(news.date)}
                </span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                {title}
              </h3>
            </>
          )}
          <p className="text-gray-600 line-clamp-3">{excerpt}</p>
          <div className="mt-4 pt-4 border-t border-gray-100">
            <span className="inline-flex items-center text-primary-green hover:text-primary-green-dark font-medium transition-colors">
              {language === 'en' ? 'Read More' : 'Baca Lagi'}
              <ChevronRight className="w-4 h-4 ml-1" />
            </span>
          </div>
        </div>
      </Link>
    );
  }

  return (
    <Link
      href={`/news/${news.id}`}
      className={cn(
        'block card hover:shadow-lg transition-all duration-200',
        className,
      )}
    >
      {showImage && news.image && (
        <div className="relative h-48 mb-4 -mx-6 -mt-6">
          <Image
            src={news.image}
            alt={title}
            fill
            className="object-cover rounded-t-lg"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>
      )}

      <div className="flex items-center gap-2 mb-3">
        {showCategory && (
          <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-primary-green bg-primary-green bg-opacity-10 rounded-full">
            <Tag className="w-3 h-3" />
            {category}
          </span>
        )}
        <span className="text-sm text-gray-500 flex items-center gap-1">
          <Calendar className="w-4 h-4" />
          {formatDate(news.date)}
        </span>
      </div>

      <h3 className="text-lg font-semibold text-gray-900 mb-3 line-clamp-2">
        {title}
      </h3>

      <p className="text-gray-600 mb-4 line-clamp-3">{excerpt}</p>

      <div className="pt-4 border-t border-gray-100">
        <span className="inline-flex items-center text-primary-green hover:text-primary-green-dark font-medium transition-colors">
          {language === 'en' ? 'Read More' : 'Baca Lagi'}
          <ChevronRight className="w-4 h-4 ml-1" />
        </span>
      </div>
    </Link>
  );
}

// List component for multiple news items
export function NewsList({
  news,
  variant = 'default',
  showLoadMore = false,
  onLoadMore,
  className,
}: {
  news: NewsItem[];
  variant?: 'default' | 'compact' | 'featured' | 'horizontal';
  showLoadMore?: boolean;
  onLoadMore?: () => void;
  className?: string;
}) {
  const { language } = useLanguage();

  if (news.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        {language === 'en' ? 'No news available' : 'Tiada berita tersedia'}
      </div>
    );
  }

  return (
    <div className={className}>
      <div
        className={cn(
          'grid gap-6',
          variant === 'compact' && 'space-y-3',
          variant === 'horizontal' && 'space-y-4',
          variant === 'featured' && 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
          variant === 'default' && 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
        )}
      >
        {news.map((item) => (
          <NewsCard key={item.id} news={item} variant={variant} />
        ))}
      </div>

      {showLoadMore && onLoadMore && (
        <div className="text-center mt-8">
          <button onClick={onLoadMore} className="btn-secondary">
            {language === 'en' ? 'Load More' : 'Muat Lebih Banyak'}
          </button>
        </div>
      )}
    </div>
  );
}
