'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import { SearchBox } from './SearchBox';

export function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();

  return (
    <header className="bg-white shadow-sm">
      {/* Top Bar */}
      <div className="bg-green-600 text-white py-2">
        <div className="container mx-auto px-4 text-sm">
          <div className="flex justify-between items-center">
            <div>Welcome to Halal Selangor</div>
            <div className="flex space-x-4">
              <span>📧 <EMAIL></span>
              <span>📞 +603-xxxx-xxxx</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-xl">HS</span>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-800">
                Halal Selangor
              </h1>
              <p className="text-sm text-gray-600">Certified Halal Authority</p>
            </div>
          </div>

          {/* Search Box - Desktop */}
          {pathname !== '/search' && (
            <div className="hidden md:block flex-1 max-w-md mx-8">
              <SearchBox
                placeholder="Search Halal Selangor..."
                size="md"
                showButton={false}
              />
            </div>
          )}

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <Link
              href="/"
              className="text-gray-700 hover:text-green-600 font-medium"
            >
              Home
            </Link>
            <Link
              href="/about"
              className="text-gray-700 hover:text-green-600 font-medium"
            >
              About Us
            </Link>
            <Link
              href="/people"
              className="text-gray-700 hover:text-green-600 font-medium"
            >
              Our People
            </Link>
            <Link
              href="/news"
              className="text-gray-700 hover:text-green-600 font-medium"
            >
              News
            </Link>
            <Link
              href="/contact"
              className="text-gray-700 hover:text-green-600 font-medium"
            >
              Contact Us
            </Link>
          </nav>

          {/* Mobile Menu Button */}
          <button
            type="button"
            className="md:hidden p-2"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle mobile menu"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d={
                  isMobileMenuOpen
                    ? 'M6 18L18 6M6 6l12 12'
                    : 'M4 6h16M4 12h16M4 18h16'
                }
              />
            </svg>
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <nav className="md:hidden mt-4 pb-4 border-t pt-4">
            {/* Mobile Search */}
            {pathname !== '/search' && (
              <div className="mb-4">
                <SearchBox
                  placeholder="Search Halal Selangor..."
                  size="md"
                  showButton={true}
                />
              </div>
            )}

            <div className="flex flex-col space-y-3">
              <Link
                href="/"
                className="text-gray-700 hover:text-green-600 font-medium"
              >
                Home
              </Link>
              <Link
                href="/about"
                className="text-gray-700 hover:text-green-600 font-medium"
              >
                About Us
              </Link>
              <Link
                href="/people"
                className="text-gray-700 hover:text-green-600 font-medium"
              >
                Our People
              </Link>
              <Link
                href="/news"
                className="text-gray-700 hover:text-green-600 font-medium"
              >
                News
              </Link>
              <Link
                href="/contact"
                className="text-gray-700 hover:text-green-600 font-medium"
              >
                Contact Us
              </Link>
            </div>
          </nav>
        )}
      </div>
    </header>
  );
}
