// Mock the schema import
jest.mock('@/lib/db/schema', () => ({
  products: 'mocked-products-table',
  sites: 'mocked-sites-table',
}));

// Mock the database
const mockDb = {
  select: jest.fn().mockReturnValue({
    from: jest.fn().mockReturnValue({
      limit: jest.fn().mockReturnValue({
        offset: jest.fn().mockReturnValue({
          orderBy: jest.fn().mockResolvedValue([
            {
              id: 1,
              productName: 'Test Product',
              companyName: 'Test Company',
              certificateNumber: 'TEST-001',
              certificateType: 'HALAL',
              status: 'Active',
              category: 'Food',
              subcategory: 'Frozen',
              state: 'Selangor',
              country: 'Malaysia',
              website: 'https://test.com',
              contactInfo: '<EMAIL>',
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          ]),
        }),
      }),
    }),
  }),
};

jest.mock('@/lib/db', () => ({
  db: mockDb,
}));

describe('/api/products', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock count query
    mockDb.select.mockImplementation((fields) => {
      if (fields && fields.count) {
        return {
          from: jest.fn().mockResolvedValue([{ count: 1 }]),
        };
      }
      return {
        from: jest.fn().mockReturnValue({
          limit: jest.fn().mockReturnValue({
            offset: jest.fn().mockReturnValue({
              orderBy: jest.fn().mockResolvedValue([
                {
                  id: 1,
                  productName: 'Test Product',
                  companyName: 'Test Company',
                  certificateNumber: 'TEST-001',
                  certificateType: 'HALAL',
                  status: 'Active',
                  category: 'Food',
                  subcategory: 'Frozen',
                  state: 'Selangor',
                  country: 'Malaysia',
                  website: 'https://test.com',
                  contactInfo: '<EMAIL>',
                  createdAt: new Date(),
                  updatedAt: new Date(),
                },
              ]),
            }),
          }),
        }),
      };
    });
  });

  it('should return products list with default pagination', async () => {
    const { GET } = await import('../route');
    const request = new Request('http://localhost:16010/api/products');
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toHaveProperty('products');
    expect(data).toHaveProperty('pagination');
    expect(Array.isArray(data.products)).toBe(true);
    expect(data.pagination.page).toBe(1);
    expect(data.pagination.limit).toBe(20);
  });

  it('should handle custom pagination parameters', async () => {
    const { GET } = await import('../route');
    const request = new Request(
      'http://localhost:16010/api/products?page=2&limit=5',
    );
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.pagination.page).toBe(2);
    expect(data.pagination.limit).toBe(5);
  });
});
