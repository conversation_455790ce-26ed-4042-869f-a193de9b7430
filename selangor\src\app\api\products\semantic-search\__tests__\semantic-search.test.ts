import { NextRequest } from 'next/server';
import { GET } from '../route';

// Mock the dependencies
jest.mock('@/lib/db', () => ({
  db: {
    execute: jest.fn().mockResolvedValue({
      rows: [
        {
          id: 1,
          product_name: 'Halal Chicken',
          company_name: 'Test Company',
          certificate_number: 'CERT-001',
          certificate_type: 'Halal',
          issued_date: '2023-01-01',
          expiry_date: '2024-01-01',
          status: 'Active',
          category: 'Meat',
          subcategory: 'Poultry',
          address: 'Test Address',
          state: 'Selangor',
          country: 'Malaysia',
          contact_info: '<EMAIL>',
          website: 'https://test.com',
          source_url: 'https://source.com',
          created_at: new Date(),
          updated_at: new Date(),
          similarity_score: 0.8,
        },
      ],
    }),
  },
}));

jest.mock('@/lib/embedding', () => ({
  EmbeddingService: jest.fn().mockImplementation(() => ({
    generateEmbedding: jest.fn().mockResolvedValue({
      embedding: new Array(1536).fill(0.1), // Mock embedding vector
      model: 'text-embedding-ada-002',
    }),
  })),
}));

jest.mock('@/lib/translation', () => ({
  detectLanguageAndTranslate: jest.fn().mockResolvedValue({
    detectedLanguage: 'en',
    confidence: 0.9,
    translatedTerms: [], // No translated terms for basic test
  }),
}));

jest.mock('@/lib/analytics', () => ({
  extractAnalyticsFromRequest: jest.fn().mockReturnValue({}),
  getHalalSelangorSiteId: jest.fn().mockReturnValue(2),
  trackSearchAnalytics: jest.fn().mockResolvedValue(undefined),
}));

describe('Semantic Product Search API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return error when query parameter is missing', async () => {
    const url = new URL('http://localhost:16010/api/products/semantic-search');
    const request = new NextRequest(url);

    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toBe('Query parameter is required for semantic search');
  });

  it('should perform semantic search with vector embeddings', async () => {
    const url = new URL(
      'http://localhost:16010/api/products/semantic-search?q=chicken&page=1&limit=10&minScore=0.3',
    );
    const request = new NextRequest(url);

    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toHaveProperty('query', 'chicken');
    expect(data).toHaveProperty('searchType', 'semantic');
    expect(data).toHaveProperty('products');
    expect(data).toHaveProperty('pagination');
    expect(Array.isArray(data.products)).toBe(true);
    expect(data.pagination).toHaveProperty('page', 1);
    expect(data.pagination).toHaveProperty('limit', 10);
    expect(data.pagination).toHaveProperty('total');
    expect(data.pagination).toHaveProperty('hasMore');
    expect(data.pagination).toHaveProperty('totalPages');
  });

  it('should handle multilingual semantic search', async () => {
    const { detectLanguageAndTranslate } = require('@/lib/translation');
    detectLanguageAndTranslate.mockResolvedValueOnce({
      detectedLanguage: 'ms',
      confidence: 0.95,
      translatedTerms: ['chicken', 'poultry', 'meat'],
    });

    const url = new URL(
      'http://localhost:16010/api/products/semantic-search?q=ayam&page=1&limit=10',
    );
    const request = new NextRequest(url);

    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toHaveProperty('query', 'ayam');
    expect(data).toHaveProperty('searchType', 'multilingual-semantic');
    expect(data).toHaveProperty('detectedLanguage', 'ms');
    expect(data).toHaveProperty('translatedTerms');
    expect(Array.isArray(data.translatedTerms)).toBe(true);
  });

  it('should return products with semantic relevance scores', async () => {
    const url = new URL(
      'http://localhost:16010/api/products/semantic-search?q=halal%20food&page=1&limit=10',
    );
    const request = new NextRequest(url);

    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.products).toHaveLength(1);
    expect(data.products[0]).toHaveProperty('similarityScore');
    expect(typeof data.products[0].similarityScore).toBe('number');
    expect(data.products[0].similarityScore).toBeGreaterThanOrEqual(0);
    expect(data.products[0].similarityScore).toBeLessThanOrEqual(1);
  });

  it('should handle vector search correctly', async () => {
    const url = new URL(
      'http://localhost:16010/api/products/semantic-search?q=test&page=1&limit=10',
    );
    const request = new NextRequest(url);

    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toHaveProperty('products');
    expect(data).toHaveProperty('searchType', 'semantic');
    // Should use vector similarity search
  });

  it('should filter results by minimum score', async () => {
    const url = new URL(
      'http://localhost:16010/api/products/semantic-search?q=chicken&minScore=0.8',
    );
    const request = new NextRequest(url);

    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    if (data.products.length > 0) {
      data.products.forEach((product: any) => {
        expect(product.similarityScore).toBeGreaterThanOrEqual(0.8);
      });
    }
  });

  it('should handle pagination correctly', async () => {
    const url = new URL(
      'http://localhost:16010/api/products/semantic-search?q=chicken&page=2&limit=5',
    );
    const request = new NextRequest(url);

    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.pagination.page).toBe(2);
    expect(data.pagination.limit).toBe(5);
  });

  it('should return empty results when no products match semantic criteria', async () => {
    const { db } = require('@/lib/db');
    // Mock the database to return empty results for all queries
    db.execute.mockResolvedValueOnce({ rows: [] });

    const url = new URL(
      'http://localhost:16010/api/products/semantic-search?q=nonexistent&minScore=0.9',
    );
    const request = new NextRequest(url);

    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.products).toHaveLength(0);
    expect(data.pagination.total).toBe(0);
    expect(data.pagination.hasMore).toBe(false);

    // Reset the mock for other tests
    db.execute.mockResolvedValue({
      rows: [
        {
          id: 1,
          product_name: 'Halal Chicken',
          company_name: 'Test Company',
          certificate_number: 'CERT-001',
          certificate_type: 'Halal',
          issued_date: '2023-01-01',
          expiry_date: '2024-01-01',
          status: 'Active',
          category: 'Meat',
          subcategory: 'Poultry',
          address: 'Test Address',
          state: 'Selangor',
          country: 'Malaysia',
          contact_info: '<EMAIL>',
          website: 'https://test.com',
          source_url: 'https://source.com',
          created_at: new Date(),
          updated_at: new Date(),
          similarity_score: 0.8,
        },
      ],
    });
  });
});
