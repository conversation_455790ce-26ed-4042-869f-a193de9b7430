'use client';

import axios from 'axios';
import { Headphones, <PERSON>ader2, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Send, User, X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { useWebSocket } from '../../lib/websocket';
import { axiosApiData } from '@/lib/api-client';
import { SourcesDisplay } from './SourcesDisplay';

interface TextResult {
  text: string;
  type: 'vector' | 'graph' | string;
  document_id?: string | null;
  score: number;
  wordCount: number;
  metadata?: {
    title?: string;
    url?: string;
    source?: string;
    chunk_index?: number;
    document_type?: string;
    [key: string]: any;
  };
}

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'agent';
  content: string;
  timestamp: Date;
  imageUrl?: string;
  agentName?: string;
  sources?: TextResult[];
}

interface IntegrationStatus {
  whatsappEnabled: boolean;
  phoneNumber: string | null;
  facebookEnabled: boolean;
  pageId: string | null;
}

export default function ChatInterface() {
  const t = useTranslations('chat');
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [uploadedImage, setUploadedImage] = useState<{
    url: string;
    file: File;
  } | null>(null);
  const [integrationStatus, setIntegrationStatus] = useState<IntegrationStatus>(
    {
      whatsappEnabled: false,
      phoneNumber: null,
      facebookEnabled: false,
      pageId: null,
    },
  );
  const [isHandedOver, setIsHandedOver] = useState(false);
  const [agentName, setAgentName] = useState<string | null>(null);
  const [showHandoverButton, setShowHandoverButton] = useState(true);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  const initializeSession = useCallback(async () => {
    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/chat/session`,
      );
      setSessionId(response.data.sessionId);
    } catch (error) {
      console.error('Failed to initialize session:', error);
    }
  }, []);

  const loadIntegrationStatus = useCallback(async () => {
    try {
      // Load WhatsApp status
      const whatsappData = await axiosApiData(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/chat/whatsapp-status`,
        { method: 'GET' },
        { whatsappEnabled: false }
      );

      // Load Facebook status
      const facebookData = await axiosApiData(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/chat/facebook-status`,
        { method: 'GET' },
        { facebookEnabled: false }
      );

      setIntegrationStatus({
        whatsappEnabled: whatsappData?.whatsappEnabled || false,
        phoneNumber: whatsappData?.phoneNumber || null,
        facebookEnabled: facebookData?.facebookEnabled || false,
        pageId: facebookData?.pageId || null,
      });
    } catch (error) {
      console.error('Failed to load integration status:', error);
    }
  }, []);

  // Memoized WebSocket callbacks to prevent infinite reconnections
  const onMessage = useCallback((message: any) => {
    console.log('Received WebSocket message:', message);
    handleWebSocketMessage(message);
  }, [handleWebSocketMessage]);

  const onDisconnect = useCallback(() => {
    console.log('WebSocket disconnected');
  }, []);

  // WebSocket connection for real-time updates
  const { isConnected: wsConnected, sendMessage: sendWsMessage } = useWebSocket(
    `${process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:16001'}/ws`,
    {
      onMessage,
      onConnect: () => {
        console.log('WebSocket connected');
      },
      onDisconnect,
    },
  );

  // Register user connection when WebSocket connects and we have sessionId
  useEffect(() => {
    if (wsConnected && sessionId && sendWsMessage) {
      sendWsMessage({
        type: 'register',
        data: {
          connectionType: 'user',
          sessionId,
        },
      });
    }
  }, [wsConnected, sessionId, sendWsMessage]);

  // Initialize chat session and load integration status
  useEffect(() => {
    initializeSession();
    loadIntegrationStatus();
  }, [initializeSession, loadIntegrationStatus]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const sendMessage = async (text?: string, imageUrl?: string) => {
    if (!sessionId || (!text && !imageUrl)) {
      return;
    }

    const messageText = text || inputText.trim();
    if (!messageText && !imageUrl) {
      return;
    }

    // Add user message to UI
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: messageText,
      timestamp: new Date(),
      imageUrl,
    };
    setMessages((prev) => [...prev, userMessage]);
    setInputText('');
    setUploadedImage(null);
    setIsLoading(true);

    try {
      let responseData;

      if (imageUrl) {
        // Send image analysis request
        responseData = await axiosApiData(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/chat/image`,
          {
            method: 'POST',
            data: {
              sessionId,
              imageUrl,
              prompt: messageText || t('imagePrompt'),
            }
          }
        );
      } else {
        // Send text message
        responseData = await axiosApiData(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/chat/message`,
          {
            method: 'POST',
            data: {
              sessionId,
              message: messageText,
            }
          }
        );
      }

      // Add assistant response to UI
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: responseData?.message || responseData?.answer || 'No response received',
        timestamp: new Date(),
        sources: responseData?.sources || undefined,
      };
      setMessages((prev) => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Failed to send message:', error);
      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: 'audio/webm',
        });
        await transcribeAudio(audioBlob);
        stream.getTracks().forEach((track) => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Failed to start recording:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const transcribeAudio = async (audioBlob: Blob) => {
    try {
      const formData = new FormData();
      formData.append('file', audioBlob, 'recording.webm');

      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/upload`,
        formData,
        {
          headers: { 'Content-Type': 'multipart/form-data' },
        },
      );

      if (response.data.transcription) {
        setInputText(response.data.transcription);
      }
    } catch (error) {
      console.error('Failed to transcribe audio:', error);
    }
  };

  const onDrop = async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) {
      return;
    }

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/upload`,
        formData,
        {
          headers: { 'Content-Type': 'multipart/form-data' },
        },
      );

      if (response.data.type === 'image') {
        setUploadedImage({ url: response.data.url, file });
      } else if (
        response.data.type === 'audio' &&
        response.data.transcription
      ) {
        setInputText(response.data.transcription);
      }
    } catch (error) {
      console.error('Failed to upload file:', error);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp'],
      'audio/*': ['.mp3', '.wav', '.webm', '.ogg'],
    },
    maxFiles: 1,
  });

  const handleWebSocketMessage = (message: any) => {
    switch (message.type) {
      case 'session_assigned': {
        setIsHandedOver(true);
        setAgentName(message.data.agentName);
        setShowHandoverButton(false);
        // Add system message about agent assignment
        const assignmentMessage: Message = {
          id: Date.now().toString(),
          role: 'assistant',
          content: `You are now connected to ${message.data.agentName}. They will assist you with your inquiry.`,
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, assignmentMessage]);
        break;
      }

      case 'agent_message': {
        // Add agent message to chat
        const agentMessage: Message = {
          id: message.data.messageId || Date.now().toString(),
          role: 'agent',
          content: message.data.content,
          timestamp: new Date(message.data.timestamp || Date.now()),
          agentName: message.data.agentName,
          audioUrl: message.data.audioUrl,
          fileUrl: message.data.fileUrl,
          fileName: message.data.fileName,
        };
        setMessages((prev) => [...prev, agentMessage]);
        break;
      }

      case 'session_completed': {
        setIsHandedOver(false);
        setAgentName(null);
        setShowHandoverButton(true);
        // Add system message about session completion
        const completionMessage: Message = {
          id: Date.now().toString(),
          role: 'assistant',
          content:
            'The agent has ended the session. You can continue chatting with me or request another agent if needed.',
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, completionMessage]);
        break;
      }

      default:
        console.log('Unknown WebSocket message type:', message.type);
    }
  };

  const requestHandover = async () => {
    if (!sessionId) {
      return;
    }

    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/sessions/handover`,
        {
          sessionId,
          reason: 'User requested human assistance',
          priority: 'normal',
        },
      );

      if (response.data.success) {
        setShowHandoverButton(false);
        // Add system message about handover request
        const systemMessage: Message = {
          id: Date.now().toString(),
          role: 'assistant',
          content:
            "I've requested a human agent to assist you. Please wait while we connect you to an available agent.",
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, systemMessage]);

        // Notify via WebSocket
        if (wsConnected) {
          sendWsMessage({
            type: 'handover_requested',
            data: {
              sessionId,
              reason: 'User requested human assistance',
            },
          });
        }
      }
    } catch (error) {
      console.error('Failed to request handover:', error);
    }
  };

  return (
    <div className="flex flex-col h-screen max-w-4xl mx-auto bg-white">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-xl font-semibold">AI Chat Assistant</h1>
            <p className="text-blue-100 text-sm">
              Send text, voice messages, or upload images
            </p>
          </div>

          {/* Agent Status & Handover Button */}
          <div className="flex flex-col items-end space-y-2">
            {isHandedOver && agentName ? (
              <div className="flex items-center bg-green-500 bg-opacity-20 border border-green-400 rounded-full px-3 py-1">
                <User className="w-4 h-4 mr-2 text-green-300" />
                <span className="text-xs text-green-100">
                  Connected to {agentName}
                </span>
              </div>
            ) : (
              showHandoverButton && (
                <button
                  type="button"
                  onClick={requestHandover}
                  className="flex items-center bg-orange-500 bg-opacity-20 border border-orange-400 rounded-full px-3 py-1 hover:bg-opacity-30 transition-colors"
                >
                  <Headphones className="w-4 h-4 mr-2 text-orange-300" />
                  <span className="text-xs text-orange-100">Talk to Human</span>
                </button>
              )
            )}
          </div>
        </div>

        {/* Integration Status */}
        {(integrationStatus.whatsappEnabled ||
          integrationStatus.facebookEnabled) && (
          <div className="mt-3 pt-3 border-t border-blue-500">
            <p className="text-blue-100 text-xs mb-2">Also available on:</p>
            <div className="flex flex-wrap gap-2">
              {integrationStatus.whatsappEnabled && (
                <div className="bg-green-500 bg-opacity-20 border border-green-400 rounded-full px-3 py-1 flex items-center">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2" />
                  <span className="text-xs text-green-100">
                    WhatsApp{' '}
                    {integrationStatus.phoneNumber &&
                      `(${integrationStatus.phoneNumber})`}
                  </span>
                </div>
              )}
              {integrationStatus.facebookEnabled && (
                <div className="bg-blue-500 bg-opacity-20 border border-blue-400 rounded-full px-3 py-1 flex items-center">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mr-2" />
                  <span className="text-xs text-blue-100">
                    Facebook Messenger
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.role === 'user'
                  ? 'bg-blue-600 text-white'
                  : message.role === 'agent'
                    ? 'bg-green-100 text-green-800 border border-green-200'
                    : 'bg-gray-200 text-gray-800'
              }`}
            >
              {message.role === 'agent' && message.agentName && (
                <div className="flex items-center mb-1">
                  <User className="w-3 h-3 mr-1 text-green-600" />
                  <span className="text-xs font-medium text-green-600">
                    {message.agentName}
                  </span>
                </div>
              )}
              {message.imageUrl && (
                <img
                  src={message.imageUrl}
                  alt="Uploaded"
                  className="w-full h-32 object-cover rounded mb-2"
                />
              )}
              <p className="text-sm">{message.content}</p>

              {/* Display sources if available */}
              {message.sources && message.sources.length > 0 && (
                <SourcesDisplay sources={message.sources} />
              )}

              <p className="text-xs opacity-70 mt-1">
                {message.timestamp.toLocaleTimeString()}
              </p>
            </div>
          </div>
        ))}
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg">
              <Loader2 className="w-4 h-4 animate-spin" />
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Image Preview */}
      {uploadedImage && (
        <div className="p-4 bg-gray-50 border-t">
          <div className="flex items-center space-x-2">
            <img
              src={uploadedImage.url}
              alt="Preview"
              className="w-16 h-16 object-cover rounded"
            />
            <span className="text-sm text-gray-600">Image ready to send</span>
            <button
              type="button"
              onClick={() => setUploadedImage(null)}
              className="text-red-500 hover:text-red-700"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Input Area */}
      <div className="border-t p-4">
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-2 mb-4 transition-colors ${
            isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
          }`}
        >
          <input {...getInputProps()} />
          <p className="text-center text-sm text-gray-500">
            {isDragActive
              ? 'Drop files here...'
              : 'Drag & drop images or audio files, or click to select'}
          </p>
        </div>

        <div className="flex space-x-2">
          <input
            type="text"
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
              }
            }}
            placeholder={t('placeholder')}
            className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-800"
            disabled={isLoading}
          />

          <button
            type="button"
            onClick={isRecording ? stopRecording : startRecording}
            className={`p-2 rounded-lg ${
              isRecording
                ? 'bg-red-500 hover:bg-red-600 text-white'
                : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
            }`}
            disabled={isLoading}
          >
            {isRecording ? (
              <MicOff className="w-5 h-5" />
            ) : (
              <Mic className="w-5 h-5" />
            )}
          </button>

          <button
            type="button"
            onClick={() => sendMessage(inputText, uploadedImage?.url)}
            disabled={isLoading || (!inputText.trim() && !uploadedImage)}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white p-2 rounded-lg"
          >
            <Send className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
}
