import express, { type Request, type Response } from 'express';
import multer from 'multer';
import authService from '../middleware/auth';
import databaseServiceInstance from '../services/database'; // Renamed for clarity
import facebookService from '../services/facebook';

import whatsappService from '../services/whatsapp';
import {
  type AdminLoginRequest,
  type AdminUserCreationRequest, // New
  type AdminUserUpdateRequest, // New
  type CollectionCreationRequest,
  CollectionStatus,
  type CollectionUpdateRequest,
  type DocumentCreationRequest,
  type Document as DocumentType,
  type FacebookConfig,
  UserRole, // New
  type WhatsAppConfig,
} from '../types';

const router = express.Router();
const dbService = new databaseServiceInstance(); // Instantiate, or use as singleton if designed that way

// Initialize Facebook service with database service
facebookService.initialize(dbService, 1); // Using default site ID 1

// Multer setup for file uploads (memory storage, can be configured for disk storage)
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // Example: 10MB file size limit
});

// S3 Presets - consider moving to a config file or a service if they get complex

// Admin login
router.post('/login', async (req: Request, res: Response): Promise<void> => {
  try {
    const credentials: AdminLoginRequest = req.body;

    if (!credentials.username || !credentials.password) {
      res.status(400).json({
        error: 'Validation error',
        message: 'Username and password are required',
      });
      return;
    }

    const result = await authService.login(credentials);

    if (result.success) {
      res.json(result);
      return;
    }
    res.status(401).json(result);
    return;
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Login failed',
    });
    return;
  }
});

// Get current admin user info
router.get(
  '/me',
  authService.authenticateAdmin,
  (req: Request, res: Response): void => {
    const user = authService.getCurrentUser(req);
    res.json({
      user: {
        id: user?.userId,
        username: user?.username,
        role: user?.role, // Added role to the response
      },
    });
  },
);

// Verify admin token (for frontend to check if token is still valid)
router.get(
  '/verify',
  authService.authenticateAdmin,
  (req: Request, res: Response): void => {
    const user = authService.getCurrentUser(req);
    res.json({
      success: true,
      user: {
        id: user?.userId,
        username: user?.username,
        role: user?.role,
      },
    });
  },
);

// Get WhatsApp configuration
router.get(
  '/whatsapp/config',
  authService.authenticateAdmin,
  async (_req: Request, res: Response): Promise<void> => {
    try {
      // const config = await databaseService.getWhatsAppConfig();

      res.json({
        configured: false,
        config: null,
      });
      return;
    } catch (error) {
      console.error('Error getting WhatsApp config:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to get WhatsApp configuration',
      });
      return;
    }
  },
);

// Save WhatsApp configuration
router.post(
  '/whatsapp/config',
  authService.authenticateAdmin,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        accessToken,
        phoneNumberId,
        webhookVerifyToken,
        businessAccountId,
      } = req.body;

      if (!accessToken || !phoneNumberId || !webhookVerifyToken) {
        res.status(400).json({
          error: 'Validation error',
          message:
            'Access token, phone number ID, and webhook verify token are required',
        });
        return;
      }

      const config: Omit<WhatsAppConfig, 'id' | 'createdAt' | 'updatedAt'> = {
        accessToken,
        phoneNumberId,
        webhookVerifyToken,
        businessAccountId: businessAccountId || undefined,
        isActive: true,
      };

      await whatsappService.updateConfig(config);

      res.json({
        success: true,
        message: 'WhatsApp configuration saved successfully',
      });
      return;
    } catch (error) {
      console.error('Error saving WhatsApp config:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to save WhatsApp configuration',
      });
      return;
    }
  },
);

// Test WhatsApp configuration
router.post(
  '/whatsapp/test',
  authService.authenticateAdmin,
  async (_req: Request, res: Response): Promise<void> => {
    try {
      const result = await whatsappService.testConfiguration();

      if (result.success) {
        res.json({
          success: true,
          message: 'WhatsApp configuration is valid',
        });
        return;
      }
      res.status(400).json({
        success: false,
        error: result.error,
      });
      return;
    } catch (error) {
      console.error('Error testing WhatsApp config:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to test WhatsApp configuration',
      });
      return;
    }
  },
);

// Send test WhatsApp message
router.post(
  '/whatsapp/test-message',
  authService.authenticateAdmin,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { to, message, type = 'text', imageUrl } = req.body;

      if (
        !to ||
        !to.trim() ||
        (type === 'text' && (!message || !message.trim())) ||
        (type === 'image' && (!imageUrl || !imageUrl.trim()))
      ) {
        res.status(400).json({
          error: 'Validation error',
          message: 'Invalid request parameters',
        });
        return;
      }

      let result;
      if (type === 'image') {
        result = await whatsappService.sendImageMessage(to, imageUrl, message);
      } else {
        result = await whatsappService.sendTextMessage(to, message);
      }

      if (result.success) {
        res.json({
          success: true,
          messageId: result.messageId,
          message: 'Test message sent successfully',
        });
        return;
      }
      res.status(400).json({
        success: false,
        error: result.error,
      });
      return;
    } catch (error) {
      console.error('Error sending test message:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to send test message',
      });
      return;
    }
  },
);

// Get WhatsApp message history for a phone number
router.get(
  '/whatsapp/messages/:phoneNumber',
  authService.authenticateAdmin,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { phoneNumber } = req.params;
      const _limit = Number.parseInt(req.query.limit as string) || 50;

      // const messages = await databaseService.getWhatsAppMessageHistory(phoneNumber, limit);

      res.json({
        success: true,
        messages: [],
      });
      return;
    } catch (error) {
      console.error('Error getting message history:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to get message history',
      });
      return;
    }
  },
);

// Get webhook URL for WhatsApp configuration
router.get(
  '/whatsapp/webhook-url',
  authService.authenticateAdmin,
  (_req: Request, res: Response): void => {
    const baseUrl =
      process.env.WEBHOOK_BASE_URL ||
      `http://localhost:${process.env.PORT || 16001}`;
    const webhookUrl = `${baseUrl}/api/whatsapp/webhook`;

    res.json({
      webhookUrl,
      instructions: [
        '1. Copy the webhook URL above',
        '2. Go to your WhatsApp Business API configuration',
        '3. Set the webhook URL to the URL above',
        '4. Set the verify token to match your configuration',
        '5. Subscribe to "messages" events',
      ],
    });
  },
);

// Facebook Messenger Admin Routes

// Get all Facebook configurations
router.get(
  '/facebook-configs',
  authService.authenticateAdmin,
  async (_req: Request, res: Response): Promise<void> => {
    try {
      const configs = await databaseService.getAllFacebookConfigs();
      res.json(configs);
      return;
    } catch (error) {
      console.error('Error getting Facebook configs:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to get Facebook configurations',
      });
      return;
    }
  },
);

// Get Facebook configuration
router.get(
  '/facebook/config',
  authService.authenticateAdmin,
  async (_req: Request, res: Response): Promise<void> => {
    try {
      const config = facebookService.getConfig();

      res.json({
        configured: facebookService.isConfigured(),
        config: config
          ? {
              pageId: config.pageId,
              isActive: config.isActive,
            }
          : null,
      });
      return;
    } catch (error) {
      console.error('Error getting Facebook config:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to get Facebook configuration',
      });
      return;
    }
  },
);

// Save Facebook configuration
router.post(
  '/facebook/config',
  authService.authenticateAdmin,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { pageAccessToken, pageId, appSecret, verifyToken } = req.body;

      if (!pageAccessToken || !pageId || !appSecret || !verifyToken) {
        res.status(400).json({
          error: 'Validation error',
          message:
            'Page access token, page ID, app secret, and verify token are required',
        });
        return;
      }

      const config: Omit<FacebookConfig, 'id' | 'createdAt' | 'updatedAt'> = {
        siteId: 1, // Default site ID
        pageAccessToken,
        pageId,
        appSecret,
        verifyToken,
        isActive: true,
      };

      await facebookService.updateConfig(config);

      res.json({
        success: true,
        message: 'Facebook configuration saved successfully',
      });
      return;
    } catch (error) {
      console.error('Error saving Facebook config:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to save Facebook configuration',
      });
      return;
    }
  },
);

// Test Facebook configuration
router.post(
  '/facebook/test',
  authService.authenticateAdmin,
  async (_req: Request, res: Response): Promise<void> => {
    try {
      const result = await facebookService.testConfiguration();

      if (result.success) {
        res.json({
          success: true,
          message: 'Facebook configuration is valid',
        });
        return;
      }
      res.status(400).json({
        success: false,
        error: result.error,
      });
      return;
    } catch (error) {
      console.error('Error testing Facebook config:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to test Facebook configuration',
      });
      return;
    }
  },
);

// Get recent Facebook conversations
router.get(
  '/facebook/conversations',
  authService.authenticateAdmin,
  async (_req: Request, res: Response): Promise<void> => {
    try {
      const result = await facebookService.getRecentConversations();

      if (result.success) {
        res.json({
          success: true,
          conversations: result.conversations,
        });
        return;
      }
      res.status(400).json({
        success: false,
        error: result.error,
      });
      return;
    } catch (error) {
      console.error('Error getting Facebook conversations:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to get conversations',
      });
      return;
    }
  },
);

// Send test Facebook message
router.post(
  '/facebook/test-message',
  authService.authenticateAdmin,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { to, message, type = 'text', imageUrl } = req.body;

      if (
        !to ||
        !to.trim() ||
        (type === 'text' && (!message || !message.trim())) ||
        (type === 'image' && (!imageUrl || !imageUrl.trim()))
      ) {
        res.status(400).json({
          error: 'Validation error',
          message: 'Invalid request parameters',
        });
        return;
      }

      let result;
      if (type === 'image') {
        result = await facebookService.sendImageMessage(to, imageUrl, message);
      } else {
        result = await facebookService.sendTextMessage(to, message);
      }

      if (result.success) {
        res.json({
          success: true,
          messageId: result.messageId,
          message: 'Test message sent successfully',
        });
        return;
      }
      res.status(400).json({
        success: false,
        error: result.error,
      });
      return;
    } catch (error) {
      console.error('Error sending test message:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to send test message',
      });
      return;
    }
  },
);

// Get Facebook message history for a user
router.get(
  '/facebook/messages/:userId',
  authService.authenticateAdmin,
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { userId } = req.params;
      const _limit = Number.parseInt(req.query.limit as string) || 50;

      // TODO: Implement when database is ready
      // const messages = await databaseService.getFacebookMessageHistory(userId, limit);

      res.json({
        success: true,
        messages: [],
      });
      return;
    } catch (error) {
      console.error('Error getting message history:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Failed to get message history',
      });
      return;
    }
  },
);

// Get webhook URL for Facebook configuration
router.get(
  '/facebook/webhook-url',
  authService.authenticateAdmin,
  (_req: Request, res: Response): void => {
    const baseUrl =
      process.env.WEBHOOK_BASE_URL ||
      `http://localhost:${process.env.PORT || 16001}`;
    const webhookUrl = `${baseUrl}/api/facebook/webhook`;

    res.json({
      webhookUrl,
      instructions: [
        '1. Copy the webhook URL above',
        '2. Go to your Facebook App configuration in Meta for Developers',
        '3. Set the webhook URL to the URL above',
        '4. Set the verify token to match your configuration',
        '5. Subscribe to "messages", "messaging_postbacks", and "messaging_optins" events',
      ],
    });
  },
);

// S3 Configuration Routes

// Collection Routes
const collectionAuthMiddleware = [
  authService.authenticateAdmin,
  authService.authorizeRoles(['ADMIN', 'EDITOR']),
];

// Create Collection
router.post(
  '/collections',
  collectionAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const { name, status }: CollectionCreationRequest = req.body;
      if (!name) {
        return res.status(400).json({ error: 'Collection name is required.' });
      }
      if (
        status &&
        !Object.values(CollectionStatus).includes(status as CollectionStatus)
      ) {
        return res.status(400).json({ error: 'Invalid collection status.' });
      }
      const newCollection = await dbService.createCollection({
        name,
        status: status || CollectionStatus.ACTIVE,
      });
      if (!newCollection) {
        return res.status(500).json({
          error: 'Failed to create collection. Database service returned null.',
        });
      }
      res.status(201).json(newCollection);
    } catch (error: any) {
      console.error('Error creating collection:', error);
      if (
        error.code === '23505' ||
        error.message?.includes('unique constraint')
      ) {
        // PostgreSQL unique constraint violation
        return res
          .status(409)
          .json({ error: 'A collection with this name already exists.' });
      }
      res
        .status(500)
        .json({ error: 'Failed to create collection', message: error.message });
    }
  },
);

// Get all Collections
router.get(
  '/collections',
  collectionAuthMiddleware,
  async (_req: Request, res: Response) => {
    try {
      const collections = await dbService.getAllCollections();
      res.json(collections);
    } catch (error: any) {
      console.error('Error fetching collections:', error);
      res
        .status(500)
        .json({ error: 'Failed to fetch collections', message: error.message });
    }
  },
);

// Get Collection by ID
router.get(
  '/collections/:id',
  collectionAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const id = Number.parseInt(req.params.id, 10);
      if (Number.isNaN(id)) {
        return res.status(400).json({ error: 'Invalid collection ID.' });
      }
      const collection = await dbService.getCollectionById(id);
      if (collection) {
        res.json(collection);
      } else {
        res.status(404).json({ error: 'Collection not found.' });
      }
    } catch (error: any) {
      console.error(`Error fetching collection ${req.params.id}:`, error);
      res
        .status(500)
        .json({ error: 'Failed to fetch collection', message: error.message });
    }
  },
);

// Update Collection
router.put(
  '/collections/:id',
  collectionAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const id = Number.parseInt(req.params.id, 10);
      if (Number.isNaN(id)) {
        return res.status(400).json({ error: 'Invalid collection ID.' });
      }
      const { name, status }: CollectionUpdateRequest = req.body;
      if (!name && !status) {
        return res.status(400).json({
          error:
            'No update data provided for collection (name or status required).',
        });
      }
      if (
        status &&
        !Object.values(CollectionStatus).includes(status as CollectionStatus)
      ) {
        return res.status(400).json({ error: 'Invalid collection status.' });
      }

      const updatedCollection = await dbService.updateCollection(id, {
        name,
        status,
      });
      if (updatedCollection) {
        res.json(updatedCollection);
      } else {
        res
          .status(404)
          .json({ error: 'Collection not found or update failed.' });
      }
    } catch (error: any) {
      console.error(`Error updating collection ${req.params.id}:`, error);
      if (
        error.code === '23505' ||
        error.message?.includes('unique constraint')
      ) {
        // PostgreSQL unique constraint violation
        return res
          .status(409)
          .json({ error: 'A collection with this name already exists.' });
      }
      res
        .status(500)
        .json({ error: 'Failed to update collection', message: error.message });
    }
  },
);

// Delete Collection
router.delete(
  '/collections/:id',
  collectionAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const id = Number.parseInt(req.params.id, 10);
      if (Number.isNaN(id)) {
        return res.status(400).json({ error: 'Invalid collection ID.' });
      }
      const deletedCollection = await dbService.deleteCollection(id);
      if (deletedCollection) {
        res.status(200).json({ message: 'Collection deleted successfully.' });
      } else {
        res
          .status(404)
          .json({ error: 'Collection not found or delete failed.' });
      }
    } catch (error: any) {
      console.error(`Error deleting collection ${req.params.id}:`, error);
      // Handle record not found errors
      if (
        error.message?.includes('not found') ||
        error.message?.includes('does not exist')
      ) {
        return res.status(404).json({ error: 'Collection not found.' });
      }
      res
        .status(500)
        .json({ error: 'Failed to delete collection', message: error.message });
    }
  },
);

// Document Routes
const docAuthMiddleware = [
  authService.authenticateAdmin,
  authService.authorizeRoles(['ADMIN', 'EDITOR']),
];

// Upload Document
router.post(
  '/documents/upload',
  docAuthMiddleware,
  upload.single('file'),
  async (req: Request, res: Response) => {
    try {
      const { collectionId, s3ConfigurationId } = req.body;
      const file = req.file;

      if (!file) {
        return res.status(400).json({ error: 'No file uploaded.' });
      }
      if (!collectionId || !s3ConfigurationId) {
        return res
          .status(400)
          .json({ error: 'collectionId and s3ConfigurationId are required.' });
      }

      const collId = Number.parseInt(collectionId, 10);
      const s3ConfigId = Number.parseInt(s3ConfigurationId, 10);

      if (Number.isNaN(collId) || Number.isNaN(s3ConfigId)) {
        return res
          .status(400)
          .json({ error: 'Invalid collectionId or s3ConfigurationId.' });
      }

      // Fetch S3 configuration
      const s3Config = await dbService.getS3ConfigurationById(s3ConfigId);
      if (!s3Config) {
        return res.status(404).json({ error: 'S3 Configuration not found.' });
      }

      // Fetch Collection to ensure it exists (optional, FK constraint will catch it too)
      const collection = await dbService.getCollectionById(collId);
      if (!collection) {
        return res.status(404).json({ error: 'Collection not found.' });
      }

      const s3Service = new S3Service(s3Config);
      // Sanitize filename and create a unique S3 key
      const originalFilename = file.originalname;
      const sanitizedFilename = originalFilename.replace(
        /[^a-zA-Z0-9._-]/g,
        '_',
      );
      const s3Key = `collections/${collId}/${Date.now()}_${sanitizedFilename}`;

      // Upload to S3
      await s3Service.uploadFile(file.buffer, s3Key, file.mimetype);

      // Create document record in DB
      const documentData: DocumentCreationRequest = {
        collectionId: collId,
        s3ConfigurationId: s3ConfigId,
        s3Key,
        filename: originalFilename, // Store original filename
        filesize: file.size,
        mimetype: file.mimetype,
      };
      const newDocument = await dbService.createDocument(documentData);
      if (!newDocument) {
        // Potentially try to delete the uploaded S3 object if DB entry fails
        try {
          await s3Service.deleteFile(s3Key);
        } catch (s3DeleteError) {
          console.error(
            `Failed to rollback S3 upload for key ${s3Key} after DB error:`,
            s3DeleteError,
          );
        }
        return res
          .status(500)
          .json({ error: 'Failed to create document record after S3 upload.' });
      }

      res.status(201).json(newDocument);
    } catch (error: any) {
      console.error('Error uploading document:', error);
      res
        .status(500)
        .json({ error: 'Failed to upload document', message: error.message });
    }
  },
);

// Get all Documents (paginated)
router.get(
  '/documents',
  docAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const page = Number.parseInt(req.query.page as string) || 1;
      const limit = Number.parseInt(req.query.limit as string) || 10;

      const documents = await dbService.getAllDocuments(page, limit);
      const total = await dbService.countAllDocuments();

      const response = {
        items: documents.map(
          (d) =>
            ({
              // Spread the original document fields
              // Ensure all fields from DocumentType are explicitly mapped or spread from d
              // For example, if d is Drizzle's Document model type:
              id: d.id,
              collectionId: d.collectionId,
              s3ConfigurationId: d.s3ConfigurationId,
              s3Key: d.s3Key,
              filename: d.filename,
              filesize: d.filesize,
              mimetype: d.mimetype,
              createdAt: d.createdAt,
              updatedAt: d.updatedAt,
              // Add the joined names
              collectionName: d.collection?.name,
              s3ConfigurationServiceName: d.s3Configuration?.serviceName,
            }) as DocumentType,
        ), // Changed from 'documents' to 'items' to match frontend expectations
        total,
        page,
        limit,
      };
      res.json(response);
    } catch (error: any) {
      console.error('Error fetching all documents:', error);
      res
        .status(500)
        .json({ error: 'Failed to fetch documents', message: error.message });
    }
  },
);

// Admin User Management Routes
const adminUserAuthMiddleware = [
  authService.authenticateAdmin,
  authService.authorizeRoles([UserRole.ADMIN]),
];

// Create Admin User
router.post(
  '/users',
  adminUserAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const data: AdminUserCreationRequest = req.body;
      console.log(
        'Received user creation data:',
        JSON.stringify(data, null, 2),
      );
      if (
        !data.username ||
        !data.password ||
        !data.roles ||
        data.roles.length === 0
      ) {
        console.log('Validation failed:', {
          username: !!data.username,
          password: !!data.password,
          roles: data.roles,
          rolesLength: data.roles?.length,
        });
        return res.status(400).json({
          error: 'Username, password, and at least one role are required.',
        });
      }
      // Validate all roles
      for (const role of data.roles) {
        if (!Object.values(UserRole).includes(role)) {
          return res
            .status(400)
            .json({ error: `Invalid role specified: ${role}` });
        }
      }
      const newUser = await dbService.createAdminUser(data);
      res.status(201).json(newUser);
    } catch (error: any) {
      console.error('Error creating admin user:', error);
      if (error.message?.includes('Username already exists')) {
        return res.status(409).json({ error: error.message });
      }
      res
        .status(500)
        .json({ error: 'Failed to create admin user', message: error.message });
    }
  },
);

// Get all Admin Users
router.get(
  '/users',
  adminUserAuthMiddleware,
  async (_req: Request, res: Response) => {
    try {
      const users = await dbService.getAllAdminUsers();
      res.json(users);
    } catch (error: any) {
      console.error('Error fetching admin users:', error);
      res
        .status(500)
        .json({ error: 'Failed to fetch admin users', message: error.message });
    }
  },
);

// Get Admin User by ID
router.get(
  '/users/:id',
  adminUserAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const id = Number.parseInt(req.params.id, 10);
      if (Number.isNaN(id)) {
        return res.status(400).json({ error: 'Invalid user ID.' });
      }
      // Prevent users from fetching their own details via this admin endpoint if they are not admin?
      // For now, any admin can fetch any user.
      const user = await dbService.getAdminUserById(id);
      if (user) {
        res.json(user);
      } else {
        res.status(404).json({ error: 'Admin user not found.' });
      }
    } catch (error: any) {
      console.error(`Error fetching admin user ${req.params.id}:`, error);
      res
        .status(500)
        .json({ error: 'Failed to fetch admin user', message: error.message });
    }
  },
);

// Update Admin User
router.put(
  '/users/:id',
  adminUserAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const id = Number.parseInt(req.params.id, 10);
      if (Number.isNaN(id)) {
        return res.status(400).json({ error: 'Invalid user ID.' });
      }
      const data: AdminUserUpdateRequest = req.body;
      if (data.roles && data.roles.length > 0) {
        // Validate all roles
        for (const role of data.roles) {
          if (!Object.values(UserRole).includes(role)) {
            return res
              .status(400)
              .json({ error: `Invalid role specified: ${role}` });
          }
        }
      }
      // Add logic to prevent non-admins from changing roles or critical fields if necessary

      const updatedUser = await dbService.updateAdminUser(id, data);
      if (updatedUser) {
        res.json(updatedUser);
      } else {
        res
          .status(404)
          .json({ error: 'Admin user not found or update failed.' });
      }
    } catch (error: any) {
      console.error(`Error updating admin user ${req.params.id}:`, error);
      if (error.message?.includes('Username already exists')) {
        return res.status(409).json({ error: error.message });
      }
      res
        .status(500)
        .json({ error: 'Failed to update admin user', message: error.message });
    }
  },
);

// Delete Admin User
router.delete(
  '/users/:id',
  adminUserAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const id = Number.parseInt(req.params.id, 10);
      if (Number.isNaN(id)) {
        return res.status(400).json({ error: 'Invalid user ID.' });
      }

      // Add logic here to prevent deleting oneself or the last admin user
      const currentUser = authService.getCurrentUser(req);
      if (currentUser && currentUser.userId === id) {
        return res.status(403).json({
          error: 'Cannot delete your own user account through this endpoint.',
        });
      }

      // Check if this is the last admin user
      const allUsers = await dbService.getAllAdminUsers();
      const adminUsers = allUsers.filter((u) => u.role === UserRole.ADMIN);
      if (adminUsers.length === 1 && adminUsers[0].id === id) {
        const userToDelete = await dbService.getAdminUserById(id); // Fetch to check role before deleting
        if (userToDelete && userToDelete.role === UserRole.ADMIN) {
          return res
            .status(403)
            .json({ error: 'Cannot delete the last admin user.' });
        }
      }

      const deletedUser = await dbService.deleteAdminUser(id);
      if (deletedUser) {
        res.status(200).json({ message: 'Admin user deleted successfully.' });
      } else {
        res
          .status(404)
          .json({ error: 'Admin user not found or delete failed.' });
      }
    } catch (error: any) {
      console.error(`Error deleting admin user ${req.params.id}:`, error);
      res
        .status(500)
        .json({ error: 'Failed to delete admin user', message: error.message });
    }
  },
);

// Agent Management Routes
const agentAuthMiddleware = [
  authService.authenticateAdmin,
  authService.authorizeRoles([
    UserRole.ADMIN,
    UserRole.AGENT,
    UserRole.SUPERVISOR,
  ]),
];

// Get agent statistics (must be before parameterized routes)
router.get(
  '/agents/stats',
  agentAuthMiddleware,
  async (_req: Request, res: Response) => {
    try {
      const agents = await dbService.getAllAgents();
      const onlineAgents = agents.filter((agent) => agent.isOnline);

      // TODO: Get real session and handover data when implemented
      const stats = {
        totalAgents: agents.length,
        onlineAgents: onlineAgents.length,
        activeSessions: 0, // Placeholder
        pendingHandovers: 0, // Placeholder
      };

      res.json(stats);
    } catch (error: any) {
      console.error('Error fetching agent stats:', error);
      res.status(500).json({
        error: 'Failed to fetch agent statistics',
        message: error.message,
      });
    }
  },
);

// Get all agents (users with AGENT or SUPERVISOR roles)
router.get(
  '/agents',
  agentAuthMiddleware,
  async (_req: Request, res: Response) => {
    try {
      const agents = await dbService.getAllAgents();
      res.json(agents);
    } catch (error: any) {
      console.error('Error fetching agents:', error);
      res
        .status(500)
        .json({ error: 'Failed to fetch agents', message: error.message });
    }
  },
);

// Get agent by ID
router.get(
  '/agents/:id',
  agentAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const id = Number.parseInt(req.params.id, 10);
      if (Number.isNaN(id)) {
        return res.status(400).json({ error: 'Invalid agent ID.' });
      }

      const agent = await dbService.getAgentById(id);
      if (agent) {
        res.json(agent);
      } else {
        res.status(404).json({ error: 'Agent not found.' });
      }
    } catch (error: any) {
      console.error(`Error fetching agent ${req.params.id}:`, error);
      res
        .status(500)
        .json({ error: 'Failed to fetch agent', message: error.message });
    }
  },
);

// Create new agent
router.post(
  '/agents',
  [authService.authenticateAdmin, authService.authorizeRoles([UserRole.ADMIN])],
  async (req: Request, res: Response) => {
    try {
      const { username, email, password, firstName, lastName, role } = req.body;

      if (!username || !password || !role) {
        return res
          .status(400)
          .json({ error: 'Username, password, and role are required.' });
      }

      if (!['AGENT', 'SUPERVISOR'].includes(role)) {
        return res
          .status(400)
          .json({ error: 'Role must be either AGENT or SUPERVISOR.' });
      }

      const userData = {
        username,
        email: email || null,
        password,
        firstName: firstName || null,
        lastName: lastName || null,
        roles: [role],
        isActive: true,
      };

      const newUser = await dbService.createUser(userData);
      if (!newUser) {
        return res.status(500).json({ error: 'Failed to create agent.' });
      }

      // Convert to AgentUser format for response
      const agentResponse = {
        id: newUser.id,
        username: newUser.username,
        email: newUser.email || '',
        firstName: newUser.firstName || '',
        lastName: newUser.lastName || '',
        role: role.toLowerCase() as 'agent' | 'supervisor',
        isActive: newUser.isActive,
        isOnline: newUser.isOnline,
        lastSeenAt: newUser.lastSeenAt || undefined,
        createdAt: newUser.createdAt,
        updatedAt: newUser.updatedAt,
      };

      res.status(201).json(agentResponse);
    } catch (error: any) {
      console.error('Error creating agent:', error);
      if (error.code === '23505') {
        if (error.message?.includes('username')) {
          return res.status(409).json({ error: 'Username already exists.' });
        }
        if (error.message?.includes('email')) {
          return res.status(409).json({ error: 'Email already exists.' });
        }
      }
      res
        .status(500)
        .json({ error: 'Failed to create agent', message: error.message });
    }
  },
);

// Update agent
router.put(
  '/agents/:id',
  [authService.authenticateAdmin, authService.authorizeRoles([UserRole.ADMIN])],
  async (req: Request, res: Response) => {
    try {
      const id = Number.parseInt(req.params.id, 10);
      if (Number.isNaN(id)) {
        return res.status(400).json({ error: 'Invalid agent ID.' });
      }

      const { username, email, password, firstName, lastName, role, isActive } =
        req.body;

      if (role && !['AGENT', 'SUPERVISOR'].includes(role)) {
        return res
          .status(400)
          .json({ error: 'Role must be either AGENT or SUPERVISOR.' });
      }

      const updateData: any = {};
      if (username !== undefined) {
        updateData.username = username;
      }
      if (email !== undefined) {
        updateData.email = email;
      }
      if (password !== undefined) {
        updateData.password = password;
      }
      if (firstName !== undefined) {
        updateData.firstName = firstName;
      }
      if (lastName !== undefined) {
        updateData.lastName = lastName;
      }
      if (role !== undefined) {
        updateData.roles = [role];
      }
      if (isActive !== undefined) {
        updateData.isActive = isActive;
      }

      if (Object.keys(updateData).length === 0) {
        return res.status(400).json({ error: 'No update data provided.' });
      }

      const updatedUser = await dbService.updateUser(id, updateData);
      if (!updatedUser) {
        return res
          .status(404)
          .json({ error: 'Agent not found or update failed.' });
      }

      // Convert to AgentUser format for response
      const primaryRole = updatedUser.roles.includes('SUPERVISOR')
        ? 'supervisor'
        : 'agent';
      const agentResponse = {
        id: updatedUser.id,
        username: updatedUser.username,
        email: updatedUser.email || '',
        firstName: updatedUser.firstName || '',
        lastName: updatedUser.lastName || '',
        role: primaryRole,
        isActive: updatedUser.isActive,
        isOnline: updatedUser.isOnline,
        lastSeenAt: updatedUser.lastSeenAt || undefined,
        createdAt: updatedUser.createdAt,
        updatedAt: updatedUser.updatedAt,
      };

      res.json(agentResponse);
    } catch (error: any) {
      console.error(`Error updating agent ${req.params.id}:`, error);
      if (error.code === '23505') {
        if (error.message?.includes('username')) {
          return res.status(409).json({ error: 'Username already exists.' });
        }
        if (error.message?.includes('email')) {
          return res.status(409).json({ error: 'Email already exists.' });
        }
      }
      res
        .status(500)
        .json({ error: 'Failed to update agent', message: error.message });
    }
  },
);

// Delete agent
router.delete(
  '/agents/:id',
  [authService.authenticateAdmin, authService.authorizeRoles([UserRole.ADMIN])],
  async (req: Request, res: Response) => {
    try {
      const id = Number.parseInt(req.params.id, 10);
      if (Number.isNaN(id)) {
        return res.status(400).json({ error: 'Invalid agent ID.' });
      }

      // Prevent deleting oneself
      const currentUser = authService.getCurrentUser(req);
      if (currentUser && currentUser.userId === id) {
        return res
          .status(403)
          .json({ error: 'Cannot delete your own account.' });
      }

      // Check if agent exists and has agent roles
      const agent = await dbService.getAgentById(id);
      if (!agent) {
        return res.status(404).json({ error: 'Agent not found.' });
      }

      const deletedUser = await dbService.deleteUser(id);
      if (deletedUser) {
        res.status(200).json({ message: 'Agent deleted successfully.' });
      } else {
        res.status(404).json({ error: 'Agent not found or delete failed.' });
      }
    } catch (error: any) {
      console.error(`Error deleting agent ${req.params.id}:`, error);
      res
        .status(500)
        .json({ error: 'Failed to delete agent', message: error.message });
    }
  },
);

// Get Documents by Collection ID (paginated)
router.get(
  '/collections/:collectionId/documents',
  docAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const collectionId = Number.parseInt(req.params.collectionId, 10);
      const page = Number.parseInt(req.query.page as string) || 1;
      const limit = Number.parseInt(req.query.limit as string) || 10;

      if (Number.isNaN(collectionId)) {
        return res.status(400).json({ error: 'Invalid Collection ID.' });
      }

      // Optional: Check if collection exists
      const collection = await dbService.getCollectionById(collectionId);
      if (!collection) {
        return res.status(404).json({ error: 'Collection not found.' });
      }

      const documents = await dbService.getDocumentsByCollectionId(
        collectionId,
        page,
        limit,
      );
      const total = await dbService.countDocumentsByCollectionId(collectionId);

      const response = {
        items: documents as DocumentType[], // Changed from 'documents' to 'items' to match frontend expectations
        total,
        page,
        limit,
      };
      res.json(response);
    } catch (error: any) {
      console.error(
        `Error fetching documents for collection ${req.params.collectionId}:`,
        error,
      );
      res
        .status(500)
        .json({ error: 'Failed to fetch documents', message: error.message });
    }
  },
);

// Delete Document
router.delete(
  '/documents/:id',
  docAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const id = Number.parseInt(req.params.id, 10);
      if (Number.isNaN(id)) {
        return res.status(400).json({ error: 'Invalid document ID.' });
      }

      const document = await dbService.getDocumentById(id);
      if (!document) {
        return res.status(404).json({ error: 'Document not found.' });
      }
      if (!document.s3ConfigurationId) {
        return res.status(500).json({
          error:
            'Document is missing S3 configuration reference, cannot delete from S3.',
        });
      }

      // Fetch S3 configuration to initialize S3Service
      const s3Config = await dbService.getS3ConfigurationById(
        document.s3ConfigurationId,
      );
      if (!s3Config) {
        // This case should ideally not happen if data integrity is maintained
        // Log this as a critical issue, but proceed to delete DB record if possible
        console.error(
          `Critical: S3 Configuration not found for document ${id} during delete, but s3ConfigurationId ${document.s3ConfigurationId} was present.`,
        );
        // Decide if you want to block DB deletion or allow orphan S3 objects.
        // For now, we'll attempt S3 deletion if s3Key is present and then DB deletion.
      }

      if (s3Config && document.s3Key) {
        const s3Service = new S3Service(s3Config);
        await s3Service.deleteFile(document.s3Key);
      } else if (document.s3Key) {
        // Attempt to delete S3 object even if full S3 config is missing, if we have a key. This is a fallback.
        console.warn(
          `Attempting S3 deletion for key ${document.s3Key} without full S3 config details. This might fail if default AWS config is not set up.`,
        );
        // This part would require a default S3 client or more complex logic. For simplicity, we assume s3Config is found.
        // If s3Config is absolutely required, this block would be an error.
      }

      await dbService.deleteDocument(id);
      res.status(200).json({
        message: 'Document deleted successfully from S3 and database.',
      });
    } catch (error: any) {
      console.error(`Error deleting document ${req.params.id}:`, error);
      // Handle record not found errors
      if (
        error.message?.includes('not found') ||
        error.message?.includes('does not exist')
      ) {
        return res
          .status(404)
          .json({ error: 'Document not found in database.' });
      }
      res
        .status(500)
        .json({ error: 'Failed to delete document', message: error.message });
    }
  },
);

// Site Management Endpoints (SUPERADMIN only)
const superAdminAuthMiddleware = [
  authService.authenticateAdmin,
  authService.authorizeRoles([UserRole.SUPERADMIN]),
];

// Get all sites
router.get(
  '/sites',
  superAdminAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const sites = await dbService.getAllSites();
      res.json({
        data: sites,
        total: sites.length,
        page: 1,
        limit: sites.length,
        totalPages: 1,
      });
    } catch (error: any) {
      console.error('Error fetching sites:', error);
      res
        .status(500)
        .json({ error: 'Failed to fetch sites', message: error.message });
    }
  },
);

// Get site by ID
router.get(
  '/sites/:id',
  superAdminAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const id = Number.parseInt(req.params.id, 10);
      if (Number.isNaN(id)) {
        return res.status(400).json({ error: 'Invalid site ID.' });
      }

      const site = await dbService.getSiteById(id);
      if (!site) {
        return res.status(404).json({ error: 'Site not found.' });
      }

      res.json(site);
    } catch (error: any) {
      console.error('Error fetching site:', error);
      res
        .status(500)
        .json({ error: 'Failed to fetch site', message: error.message });
    }
  },
);

// Create new site
router.post(
  '/sites',
  superAdminAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const { name, code, domains, status } = req.body;

      if (!name || !code || !domains) {
        return res
          .status(400)
          .json({ error: 'Name, code, and domains are required.' });
      }

      if (!Array.isArray(domains)) {
        return res.status(400).json({ error: 'Domains must be an array.' });
      }

      const newSite = await dbService.createSite({
        name,
        code,
        domains,
        status,
      });
      res.status(201).json(newSite);
    } catch (error: any) {
      console.error('Error creating site:', error);
      if (error.message?.includes('already exists')) {
        return res.status(409).json({ error: error.message });
      }
      res
        .status(500)
        .json({ error: 'Failed to create site', message: error.message });
    }
  },
);

// Update site
router.put(
  '/sites/:id',
  superAdminAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const id = Number.parseInt(req.params.id, 10);
      if (Number.isNaN(id)) {
        return res.status(400).json({ error: 'Invalid site ID.' });
      }

      const { name, code, domains, status } = req.body;

      if (!name && !code && !domains && status === undefined) {
        return res
          .status(400)
          .json({ error: 'At least one field must be provided for update.' });
      }

      if (domains && !Array.isArray(domains)) {
        return res.status(400).json({ error: 'Domains must be an array.' });
      }

      const updatedSite = await dbService.updateSite(id, {
        name,
        code,
        domains,
        status,
      });
      if (!updatedSite) {
        return res.status(404).json({ error: 'Site not found.' });
      }

      res.json(updatedSite);
    } catch (error: any) {
      console.error('Error updating site:', error);
      if (error.message?.includes('already exists')) {
        return res.status(409).json({ error: error.message });
      }
      res
        .status(500)
        .json({ error: 'Failed to update site', message: error.message });
    }
  },
);

// Delete site
router.delete(
  '/sites/:id',
  superAdminAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const id = Number.parseInt(req.params.id, 10);
      if (Number.isNaN(id)) {
        return res.status(400).json({ error: 'Invalid site ID.' });
      }

      const success = await dbService.deleteSite(id);
      if (!success) {
        return res.status(404).json({ error: 'Site not found.' });
      }

      res.json({ message: 'Site deleted successfully.' });
    } catch (error: any) {
      console.error('Error deleting site:', error);
      if (error.message?.includes('existing users')) {
        return res.status(400).json({ error: error.message });
      }
      res
        .status(500)
        .json({ error: 'Failed to delete site', message: error.message });
    }
  },
);

// Service Management Routes

// Get all services
router.get(
  '/services',
  adminUserAuthMiddleware,
  async (_req: Request, res: Response) => {
    try {
      const services = await dbService.getAllServices();
      res.json(services);
    } catch (error: any) {
      console.error('Error fetching services:', error);
      res
        .status(500)
        .json({ error: 'Failed to fetch services', message: error.message });
    }
  },
);

// Get service by ID
router.get(
  '/services/:id',
  adminUserAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const id = Number.parseInt(req.params.id, 10);
      if (Number.isNaN(id)) {
        return res.status(400).json({ error: 'Invalid service ID.' });
      }

      const service = await dbService.getServiceById(id);
      if (!service) {
        return res.status(404).json({ error: 'Service not found.' });
      }

      res.json(service);
    } catch (error: any) {
      console.error('Error fetching service:', error);
      res
        .status(500)
        .json({ error: 'Failed to fetch service', message: error.message });
    }
  },
);

// Create new service
router.post(
  '/services',
  adminUserAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const { name, type, description, isActive, configuration } = req.body;

      if (!name || !type || !configuration) {
        return res
          .status(400)
          .json({ error: 'Name, type, and configuration are required.' });
      }

      // Validate service type
      const validTypes = ['R2R_RAG', 'SMTP_PROVIDER', 'EXTERNAL_API'];
      if (!validTypes.includes(type)) {
        return res.status(400).json({ error: 'Invalid service type.' });
      }

      const newService = await dbService.createService({
        name,
        type,
        description,
        isActive,
        configuration,
      });
      res.status(201).json(newService);
    } catch (error: any) {
      console.error('Error creating service:', error);
      res
        .status(500)
        .json({ error: 'Failed to create service', message: error.message });
    }
  },
);

// Update service
router.put(
  '/services/:id',
  adminUserAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const id = Number.parseInt(req.params.id, 10);
      if (Number.isNaN(id)) {
        return res.status(400).json({ error: 'Invalid service ID.' });
      }

      const { name, description, isActive, configuration } = req.body;

      const updatedService = await dbService.updateService(id, {
        name,
        description,
        isActive,
        configuration,
      });
      if (!updatedService) {
        return res.status(404).json({ error: 'Service not found.' });
      }

      res.json(updatedService);
    } catch (error: any) {
      console.error('Error updating service:', error);
      res
        .status(500)
        .json({ error: 'Failed to update service', message: error.message });
    }
  },
);

// Delete service
router.delete(
  '/services/:id',
  adminUserAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const id = Number.parseInt(req.params.id, 10);
      if (Number.isNaN(id)) {
        return res.status(400).json({ error: 'Invalid service ID.' });
      }

      const success = await dbService.deleteService(id);
      if (!success) {
        return res.status(404).json({ error: 'Service not found.' });
      }

      res.json({ message: 'Service deleted successfully.' });
    } catch (error: any) {
      console.error('Error deleting service:', error);
      res
        .status(500)
        .json({ error: 'Failed to delete service', message: error.message });
    }
  },
);

export default router;
