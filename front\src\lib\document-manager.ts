export interface Document {
  id: string;
  title: string;
  titleBM?: string;
  description?: string;
  descriptionBM?: string;
  category: string;
  categoryBM?: string;
  fileUrl: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  uploadDate: string;
  lastModified?: string;
  downloadCount: number;
  tags?: string[];
  featured?: boolean;
  language?: 'en' | 'bm' | 'both';
  status: 'published' | 'draft' | 'archived';
  version?: string;
  author?: string;
  department?: string;
  relatedDocuments?: string[];
}

export interface DocumentFilter {
  category?: string;
  fileType?: string;
  language?: 'en' | 'bm' | 'both';
  status?: 'published' | 'draft' | 'archived';
  featured?: boolean;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  tags?: string[];
  department?: string;
  minSize?: number;
  maxSize?: number;
}

export interface DocumentSort {
  field: 'title' | 'uploadDate' | 'downloadCount' | 'fileSize' | 'category';
  order: 'asc' | 'desc';
}

export interface DocumentResult {
  documents: Document[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Mock documents data
export const documents: Document[] = [
  {
    id: 'doc-001',
    title: 'Halal Certification Guidelines 2024',
    titleBM: 'Garis Panduan Pensijilan Halal 2024',
    description: 'Comprehensive guidelines for halal certification process',
    descriptionBM: 'Garis panduan komprehensif untuk proses pensijilan halal',
    category: 'Guidelines',
    categoryBM: 'Garis Panduan',
    fileUrl: '/documents/halal-guidelines-2024.pdf',
    fileName: 'halal-guidelines-2024.pdf',
    fileSize: 2048576, // 2MB
    fileType: 'application/pdf',
    uploadDate: '2024-01-15',
    lastModified: '2024-01-20',
    downloadCount: 1250,
    tags: ['halal', 'certification', 'guidelines', '2024'],
    featured: true,
    language: 'both',
    status: 'published',
    version: '2024.1',
    author: 'JAKIM Certification Division',
    department: 'Halal Certification',
    relatedDocuments: ['doc-002', 'doc-003'],
  },
  {
    id: 'doc-002',
    title: 'Application Form for Halal Certificate',
    titleBM: 'Borang Permohonan Sijil Halal',
    description: 'Official application form for halal certification',
    descriptionBM: 'Borang permohonan rasmi untuk pensijilan halal',
    category: 'Forms',
    categoryBM: 'Borang',
    fileUrl: '/documents/halal-application-form.pdf',
    fileName: 'halal-application-form.pdf',
    fileSize: 512000, // 500KB
    fileType: 'application/pdf',
    uploadDate: '2024-01-10',
    downloadCount: 3200,
    tags: ['application', 'form', 'halal', 'certificate'],
    featured: true,
    language: 'both',
    status: 'published',
    version: '2024.1',
    author: 'JAKIM Administration',
    department: 'Halal Certification',
  },
  {
    id: 'doc-003',
    title: 'Halal Standard MS 1500:2019',
    titleBM: 'Standard Halal MS 1500:2019',
    description: 'Malaysian Standard for Halal Food Production',
    descriptionBM: 'Standard Malaysia untuk Pengeluaran Makanan Halal',
    category: 'Standards',
    categoryBM: 'Standard',
    fileUrl: '/documents/ms-1500-2019.pdf',
    fileName: 'ms-1500-2019.pdf',
    fileSize: 4194304, // 4MB
    fileType: 'application/pdf',
    uploadDate: '2023-12-01',
    downloadCount: 890,
    tags: ['standard', 'ms1500', 'halal', 'food', 'production'],
    featured: false,
    language: 'both',
    status: 'published',
    version: '2019',
    author: 'Department of Standards Malaysia',
    department: 'Standards',
  },
  {
    id: 'doc-004',
    title: 'Halal Audit Checklist',
    titleBM: 'Senarai Semak Audit Halal',
    description: 'Comprehensive checklist for halal audit process',
    descriptionBM: 'Senarai semak komprehensif untuk proses audit halal',
    category: 'Audit',
    categoryBM: 'Audit',
    fileUrl: '/documents/halal-audit-checklist.xlsx',
    fileName: 'halal-audit-checklist.xlsx',
    fileSize: 256000, // 250KB
    fileType:
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    uploadDate: '2024-02-01',
    downloadCount: 567,
    tags: ['audit', 'checklist', 'halal', 'inspection'],
    featured: false,
    language: 'both',
    status: 'published',
    version: '2024.1',
    author: 'JAKIM Audit Division',
    department: 'Audit & Inspection',
  },
  {
    id: 'doc-005',
    title: 'Halal Logo Usage Guidelines',
    titleBM: 'Garis Panduan Penggunaan Logo Halal',
    description:
      'Guidelines for proper usage of halal logo and certification marks',
    descriptionBM:
      'Garis panduan untuk penggunaan logo halal dan tanda pensijilan yang betul',
    category: 'Guidelines',
    categoryBM: 'Garis Panduan',
    fileUrl: '/documents/halal-logo-guidelines.pdf',
    fileName: 'halal-logo-guidelines.pdf',
    fileSize: 1536000, // 1.5MB
    fileType: 'application/pdf',
    uploadDate: '2024-01-25',
    downloadCount: 2100,
    tags: ['logo', 'guidelines', 'halal', 'branding', 'certification'],
    featured: true,
    language: 'both',
    status: 'published',
    version: '2024.1',
    author: 'JAKIM Communications',
    department: 'Communications',
  },
];

export class DocumentManager {
  static filterDocuments(
    docs: Document[],
    filter: DocumentFilter = {},
    sort: DocumentSort = { field: 'uploadDate', order: 'desc' },
    pagination: { page: number; limit: number } = { page: 1, limit: 10 },
  ): DocumentResult {
    let filtered = [...docs];

    // Apply filters
    if (filter.category) {
      filtered = filtered.filter(
        (doc) =>
          doc.category === filter.category ||
          doc.categoryBM === filter.category,
      );
    }

    if (filter.fileType) {
      filtered = filtered.filter((doc) => doc.fileType === filter.fileType);
    }

    if (filter.language) {
      filtered = filtered.filter(
        (doc) => doc.language === filter.language || doc.language === 'both',
      );
    }

    if (filter.status) {
      filtered = filtered.filter((doc) => doc.status === filter.status);
    }

    if (filter.featured !== undefined) {
      filtered = filtered.filter((doc) => !!doc.featured === filter.featured);
    }

    if (filter.dateFrom) {
      filtered = filtered.filter((doc) => doc.uploadDate >= filter.dateFrom!);
    }

    if (filter.dateTo) {
      filtered = filtered.filter((doc) => doc.uploadDate <= filter.dateTo!);
    }

    if (filter.search) {
      const searchLower = filter.search.toLowerCase();
      filtered = filtered.filter(
        (doc) =>
          doc.title.toLowerCase().includes(searchLower) ||
          doc.titleBM?.toLowerCase().includes(searchLower) ||
          doc.description?.toLowerCase().includes(searchLower) ||
          doc.descriptionBM?.toLowerCase().includes(searchLower) ||
          doc.fileName.toLowerCase().includes(searchLower) ||
          doc.tags?.some((tag) => tag.toLowerCase().includes(searchLower)),
      );
    }

    if (filter.tags && filter.tags.length > 0) {
      filtered = filtered.filter((doc) =>
        filter.tags?.some((tag) => doc.tags?.includes(tag)),
      );
    }

    if (filter.department) {
      filtered = filtered.filter((doc) => doc.department === filter.department);
    }

    if (filter.minSize) {
      filtered = filtered.filter((doc) => doc.fileSize >= filter.minSize!);
    }

    if (filter.maxSize) {
      filtered = filtered.filter((doc) => doc.fileSize <= filter.maxSize!);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sort.field) {
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'uploadDate':
          aValue = new Date(a.uploadDate);
          bValue = new Date(b.uploadDate);
          break;
        case 'downloadCount':
          aValue = a.downloadCount;
          bValue = b.downloadCount;
          break;
        case 'fileSize':
          aValue = a.fileSize;
          bValue = b.fileSize;
          break;
        case 'category':
          aValue = a.category.toLowerCase();
          bValue = b.category.toLowerCase();
          break;
        default:
          return 0;
      }

      if (aValue < bValue) {
        return sort.order === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sort.order === 'asc' ? 1 : -1;
      }
      return 0;
    });

    // Apply pagination
    const total = filtered.length;
    const totalPages = Math.ceil(total / pagination.limit);
    const startIndex = (pagination.page - 1) * pagination.limit;
    const endIndex = startIndex + pagination.limit;
    const paginatedDocs = filtered.slice(startIndex, endIndex);

    return {
      documents: paginatedDocs,
      total,
      page: pagination.page,
      limit: pagination.limit,
      totalPages,
      hasNext: pagination.page < totalPages,
      hasPrev: pagination.page > 1,
    };
  }

  static getDocumentById(id: string): Document | undefined {
    return documents.find((doc) => doc.id === id);
  }

  static getRelatedDocuments(documentId: string, limit = 5): Document[] {
    const document = DocumentManager.getDocumentById(documentId);
    if (!document) {
      return [];
    }

    // Get explicitly related documents
    const explicitlyRelated = document.relatedDocuments
      ? documents.filter((doc) => document.relatedDocuments?.includes(doc.id))
      : [];

    // Get documents from same category
    const sameCategory = documents.filter(
      (doc) =>
        doc.id !== documentId &&
        doc.category === document.category &&
        doc.status === 'published',
    );

    // Get documents with similar tags
    const similarTags = documents.filter(
      (doc) =>
        doc.id !== documentId &&
        doc.tags?.some((tag) => document.tags?.includes(tag)) &&
        doc.status === 'published',
    );

    // Combine and deduplicate
    const related = [
      ...explicitlyRelated,
      ...sameCategory,
      ...similarTags,
    ].filter(
      (doc, index, self) => index === self.findIndex((d) => d.id === doc.id),
    );

    return related.slice(0, limit);
  }

  static getDocumentStats() {
    const total = documents.length;
    const published = documents.filter(
      (doc) => doc.status === 'published',
    ).length;
    const featured = documents.filter((doc) => doc.featured).length;

    const categories = [...new Set(documents.map((doc) => doc.category))];
    const fileTypes = [...new Set(documents.map((doc) => doc.fileType))];
    const departments = [
      ...new Set(documents.map((doc) => doc.department).filter(Boolean)),
    ];

    const totalDownloads = documents.reduce(
      (sum, doc) => sum + doc.downloadCount,
      0,
    );
    const totalSize = documents.reduce((sum, doc) => sum + doc.fileSize, 0);

    const categoryStats = categories.map((category) => ({
      category,
      count: documents.filter((doc) => doc.category === category).length,
      downloads: documents
        .filter((doc) => doc.category === category)
        .reduce((sum, doc) => sum + doc.downloadCount, 0),
    }));

    return {
      total,
      published,
      featured,
      categories: categoryStats,
      fileTypes,
      departments,
      totalDownloads,
      totalSize,
      averageSize: Math.round(totalSize / total),
      averageDownloads: Math.round(totalDownloads / total),
    };
  }

  static formatFileSize(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) {
      return '0 Bytes';
    }
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round((bytes / 1024 ** i) * 100) / 100} ${sizes[i]}`;
  }

  static getFileTypeIcon(fileType: string): string {
    const iconMap: Record<string, string> = {
      'application/pdf': '📄',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        '📝',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '📊',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        '📈',
      'image/jpeg': '🖼️',
      'image/png': '🖼️',
      'text/plain': '📄',
      'application/zip': '🗜️',
    };
    return iconMap[fileType] || '📄';
  }
}
