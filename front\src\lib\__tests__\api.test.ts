import {
  cleanupMocks,
  mockAnnouncements,
  mockNews,
  mockSearchResults,
} from '@/test-utils';
import { api } from '../api';

describe('API Service', () => {
  beforeEach(() => {
    cleanupMocks();
  });

  describe('Search API', () => {
    it('should perform basic search with query parameters', async () => {
      const mockResponse = { success: true, data: mockSearchResults };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await api.search.search({ query: 'test company' });

      expect(global.fetch).toHaveBeenCalledWith(
        '/api/search?q=test%20company',
        expect.objectContaining({
          headers: { 'Content-Type': 'application/json' },
        }),
      );
      expect(result).toEqual(mockResponse);
    });

    it('should perform advanced search with filters', async () => {
      const mockResponse = { success: true, data: mockSearchResults };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await api.search.advancedSearch('test', {
        status: 'valid',
      });

      expect(global.fetch).toHaveBeenCalledWith(
        '/api/search',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({ query: 'test', filters: { status: 'valid' } }),
        }),
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle search API errors', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(
        new Error('Network error'),
      );

      await expect(api.search.search({ query: 'test' })).rejects.toThrow(
        'Network error',
      );
    });
  });

  describe('Announcements API', () => {
    it('should fetch all announcements', async () => {
      const mockResponse = { success: true, data: mockAnnouncements };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await api.announcements.getAll();

      expect(global.fetch).toHaveBeenCalledWith(
        '/api/announcements?',
        expect.objectContaining({
          headers: { 'Content-Type': 'application/json' },
        }),
      );
      expect(result).toEqual(mockResponse);
    });

    it('should fetch announcements with filters', async () => {
      const mockResponse = { success: true, data: mockAnnouncements };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await api.announcements.getAll({
        category: 'announcement',
        featured: true,
        page: 1,
        limit: 10,
      });

      expect(global.fetch).toHaveBeenCalledWith(
        '/api/announcements?category=announcement&featured=true&page=1&limit=10',
        expect.objectContaining({
          headers: { 'Content-Type': 'application/json' },
        }),
      );
      expect(result).toEqual(mockResponse);
    });

    it('should fetch single announcement by ID', async () => {
      const mockResponse = { success: true, data: mockAnnouncements[0] };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await api.announcements.getById('1');

      expect(global.fetch).toHaveBeenCalledWith(
        '/api/announcements/1',
        expect.objectContaining({
          headers: { 'Content-Type': 'application/json' },
        }),
      );
      expect(result).toEqual(mockResponse);
    });

    it('should create new announcement', async () => {
      const newAnnouncement = {
        title: 'New Announcement',
        titleBM: 'Pengumuman Baru',
        content: 'Content',
        contentBM: 'Kandungan',
        category: 'announcement' as const,
      };
      const mockResponse = {
        success: true,
        data: { ...newAnnouncement, id: '3', date: '2024-01-20' },
      };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await api.announcements.create(newAnnouncement);

      expect(global.fetch).toHaveBeenCalledWith(
        '/api/announcements',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(newAnnouncement),
        }),
      );
      expect(result).toEqual(mockResponse);
    });

    it('should update announcement', async () => {
      const updateData = { title: 'Updated Title' };
      const mockResponse = {
        success: true,
        data: { ...mockAnnouncements[0], ...updateData },
      };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await api.announcements.update('1', updateData);

      expect(global.fetch).toHaveBeenCalledWith(
        '/api/announcements/1',
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(updateData),
        }),
      );
      expect(result).toEqual(mockResponse);
    });

    it('should delete announcement', async () => {
      const mockResponse = { success: true, message: 'Deleted successfully' };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await api.announcements.delete('1');

      expect(global.fetch).toHaveBeenCalledWith(
        '/api/announcements/1',
        expect.objectContaining({
          method: 'DELETE',
        }),
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('News API', () => {
    it('should fetch all news', async () => {
      const mockResponse = { success: true, data: mockNews };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await api.news.getAll();

      expect(global.fetch).toHaveBeenCalledWith(
        '/api/news?',
        expect.objectContaining({
          headers: { 'Content-Type': 'application/json' },
        }),
      );
      expect(result).toEqual(mockResponse);
    });

    it('should fetch news with search and filters', async () => {
      const mockResponse = { success: true, data: mockNews };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await api.news.getAll({
        category: 'Event',
        search: 'test',
        page: 1,
        limit: 10,
      });

      expect(global.fetch).toHaveBeenCalledWith(
        '/api/news?category=Event&search=test&page=1&limit=10',
        expect.objectContaining({
          headers: { 'Content-Type': 'application/json' },
        }),
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors with proper error messages', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ success: false, error: 'Not found' }),
      });

      await expect(api.news.getById('999')).rejects.toThrow('Not found');
    });
  });

  describe('API Utils', () => {
    it('should handle error messages correctly', () => {
      expect(api.utils.handleError('String error')).toBe('String error');
      expect(api.utils.handleError(new Error('Error object'))).toBe(
        'Error object',
      );
      expect(api.utils.handleError({ message: 'Object with message' })).toBe(
        'Object with message',
      );
      expect(api.utils.handleError({})).toBe('An unexpected error occurred');
    });

    it('should format API responses correctly', () => {
      const response = {
        success: true,
        data: mockAnnouncements[0],
        message: 'Success',
      };

      const formatted = api.utils.formatResponse(response);

      expect(formatted).toEqual({
        data: mockAnnouncements[0],
        success: true,
        error: undefined,
        message: 'Success',
        pagination: undefined,
      });
    });

    it('should check response success correctly', () => {
      const successResponse = { success: true, data: mockAnnouncements[0] };
      const errorResponse = { success: false, error: 'Error' };

      expect(api.utils.isSuccess(successResponse)).toBe(true);
      expect(api.utils.isSuccess(errorResponse)).toBe(false);
    });
  });
});
