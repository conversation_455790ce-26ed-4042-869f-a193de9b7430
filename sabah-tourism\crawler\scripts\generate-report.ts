#!/usr/bin/env bun

import 'dotenv/config';
import chalk from 'chalk';
import { Command } from 'commander';
import fs from 'fs';
import path from 'path';
import { createDatabaseConfig } from '../config/database.config.js';
import { DatabaseManager } from '../src/crawlers/base/DatabaseManager.js';
import {
  ReportGenerator,
  type ReportOptions,
} from '../src/reporting/ReportGenerator.js';
import logger from '../src/utils/logger.js';

const program = new Command();

program
  .name('generate-report')
  .description('Generate reports from crawled social media data')
  .version('1.0.0');

program
  .option(
    '-p, --platform <platform>',
    'Filter by platform (douyin, tiktok, etc.)',
  )
  .option('-k, --keywords <keywords>', 'Filter by keywords (comma-separated)')
  .option('-a, --authors <authors>', 'Filter by authors (comma-separated)')
  .option('--min-likes <number>', 'Minimum likes threshold', Number.parseInt)
  .option('--min-views <number>', 'Minimum views threshold', Number.parseInt)
  .option('--from <date>', 'Start date (YYYY-MM-DD)')
  .option('--to <date>', 'End date (YYYY-MM-DD)')
  .option('-f, --format <format>', 'Output format (json, csv, html)', 'json')
  .option('-o, --output <path>', 'Output file path')
  .option('--include-media', 'Include media file information', false)
  .option('--preview', 'Show preview without saving file', false)
  .action(async (options) => {
    try {
      console.log(chalk.blue('📊 Generating Sabah Tourism Crawler Report'));

      // Validate format
      const validFormats = ['json', 'csv', 'html'];
      if (!validFormats.includes(options.format)) {
        console.error(chalk.red(`❌ Invalid format: ${options.format}`));
        console.log(chalk.yellow(`Valid formats: ${validFormats.join(', ')}`));
        process.exit(1);
      }

      // Parse date options
      let dateRange: { from: Date; to: Date } | undefined;
      if (options.from || options.to) {
        if (!options.from || !options.to) {
          console.error(
            chalk.red('❌ Both --from and --to dates must be provided'),
          );
          process.exit(1);
        }

        dateRange = {
          from: new Date(options.from),
          to: new Date(options.to),
        };

        if (isNaN(dateRange.from.getTime()) || isNaN(dateRange.to.getTime())) {
          console.error(chalk.red('❌ Invalid date format. Use YYYY-MM-DD'));
          process.exit(1);
        }
      }

      // Build report options
      const reportOptions: ReportOptions = {
        platform: options.platform,
        keywords: options.keywords
          ? options.keywords.split(',').map((k: string) => k.trim())
          : undefined,
        authors: options.authors
          ? options.authors.split(',').map((a: string) => a.trim())
          : undefined,
        minLikes: options.minLikes,
        minViews: options.minViews,
        dateRange,
        includeMedia: options.includeMedia,
        format: options.format,
        outputPath: options.output,
      };

      console.log(chalk.green('✅ Report configuration:'));
      if (reportOptions.platform) {
        console.log(chalk.cyan(`   Platform: ${reportOptions.platform}`));
      }
      if (reportOptions.keywords) {
        console.log(
          chalk.cyan(`   Keywords: ${reportOptions.keywords.join(', ')}`),
        );
      }
      if (reportOptions.authors) {
        console.log(
          chalk.cyan(`   Authors: <AUTHORS>
        );
      }
      if (reportOptions.minLikes) {
        console.log(chalk.cyan(`   Min likes: ${reportOptions.minLikes}`));
      }
      if (reportOptions.minViews) {
        console.log(chalk.cyan(`   Min views: ${reportOptions.minViews}`));
      }
      if (reportOptions.dateRange) {
        console.log(
          chalk.cyan(
            `   Date range: ${reportOptions.dateRange.from.toDateString()} to ${reportOptions.dateRange.to.toDateString()}`,
          ),
        );
      }
      console.log(chalk.cyan(`   Format: ${reportOptions.format}`));
      console.log(
        chalk.cyan(
          `   Include media: ${reportOptions.includeMedia ? 'Yes' : 'No'}`,
        ),
      );

      // Initialize database
      console.log(chalk.blue('📊 Connecting to database...'));
      const dbConfig = createDatabaseConfig();
      const database = new DatabaseManager(dbConfig);

      // Check if database has data
      const stats = database.getStats();
      if (stats.totalPosts === 0) {
        console.log(
          chalk.yellow('⚠️  No posts found in database. Run a crawl first.'),
        );
        database.close();
        process.exit(0);
      }

      console.log(
        chalk.green(
          `✅ Database connected (${stats.totalPosts} posts available)`,
        ),
      );

      // Generate report
      console.log(chalk.blue('📈 Generating report...'));
      const reportGenerator = new ReportGenerator(database);
      const reportData = await reportGenerator.generateReport(reportOptions);

      // Show summary
      console.log(chalk.green('\n📊 Report Summary:'));
      console.log(
        chalk.cyan(`   Total posts: ${reportData.summary.totalPosts}`),
      );
      console.log(
        chalk.cyan(`   Unique authors: ${reportData.summary.totalAuthors}`),
      );
      console.log(
        chalk.cyan(`   Total media files: ${reportData.summary.totalMedia}`),
      );
      console.log(
        chalk.cyan(
          `   Total likes: ${reportData.summary.totalEngagement.likes.toLocaleString()}`,
        ),
      );
      console.log(
        chalk.cyan(
          `   Total comments: ${reportData.summary.totalEngagement.comments.toLocaleString()}`,
        ),
      );
      console.log(
        chalk.cyan(
          `   Total shares: ${reportData.summary.totalEngagement.shares.toLocaleString()}`,
        ),
      );

      if (reportData.summary.topKeywords.length > 0) {
        console.log(chalk.blue('\n🏷️  Top Keywords:'));
        reportData.summary.topKeywords.slice(0, 5).forEach((keyword, index) => {
          console.log(
            chalk.cyan(
              `   ${index + 1}. ${keyword.keyword} (${keyword.count} posts)`,
            ),
          );
        });
      }

      if (reportData.summary.topAuthors.length > 0) {
        console.log(chalk.blue('\n👥 Top Authors: <AUTHORS>
        reportData.summary.topAuthors.slice(0, 5).forEach((author, index) => {
          console.log(
            chalk.cyan(
              `   ${index + 1}. ${author.author} (${author.posts} posts, ${author.totalLikes.toLocaleString()} likes)`,
            ),
          );
        });
      }

      // Preview mode - just show summary
      if (options.preview) {
        console.log(chalk.green('\n✅ Preview complete (no file saved)'));
        database.close();
        return;
      }

      // Export report
      console.log(chalk.blue('\n💾 Exporting report...'));

      // Ensure reports directory exists
      const reportsDir = path.join(process.cwd(), 'reports');
      if (!fs.existsSync(reportsDir)) {
        fs.mkdirSync(reportsDir, { recursive: true });
      }

      const outputPath = await reportGenerator.exportReport(
        reportData,
        reportOptions,
      );

      console.log(chalk.green('✅ Report exported successfully!'));
      console.log(chalk.cyan(`   File: ${outputPath}`));
      console.log(chalk.cyan(`   Format: ${options.format.toUpperCase()}`));
      console.log(chalk.cyan(`   Size: ${fs.statSync(outputPath).size} bytes`));

      // Show next steps
      console.log(chalk.blue('\n📝 Next steps:'));
      if (options.format === 'html') {
        console.log(chalk.cyan(`   Open ${outputPath} in your web browser`));
      } else if (options.format === 'csv') {
        console.log(
          chalk.cyan(`   Open ${outputPath} in Excel or Google Sheets`),
        );
      } else {
        console.log(chalk.cyan(`   View ${outputPath} with any text editor`));
      }

      database.close();
    } catch (error) {
      console.error(
        chalk.red(`❌ Report generation failed: ${(error as Error).message}`),
      );
      logger.error('Report generation error', { error, options });
      process.exit(1);
    }
  });

// Add some example commands
program.addHelpText(
  'after',
  `

Examples:
  Generate HTML report for all data:
    bun run scripts/generate-report.ts --format html

  Generate CSV report for Douyin posts with minimum 100 likes:
    bun run scripts/generate-report.ts --platform douyin --min-likes 100 --format csv

  Generate JSON report for specific keywords in date range:
    bun run scripts/generate-report.ts --keywords "sabah,tourism" --from 2024-01-01 --to 2024-12-31

  Preview report without saving:
    bun run scripts/generate-report.ts --preview

  Generate report for specific authors:
    bun run scripts/generate-report.ts --authors "user1,user2" --include-media --format html
`,
);

// Parse command line arguments
program.parse();

// If no arguments provided, show help
if (process.argv.length <= 2) {
  program.help();
}
