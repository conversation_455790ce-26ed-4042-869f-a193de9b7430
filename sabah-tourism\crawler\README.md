# Sabah Tourism Social Media Crawler

A comprehensive social media crawler built with Stagehand for browser automation, designed to crawl tourism-related content from platforms like Douyin, TikTok, Instagram, and more.

## Features

- 🤖 **AI-Powered Crawling**: Uses Stagehand for intelligent element detection and interaction
- 📊 **SQLite Database**: Local caching and resume functionality
- 📥 **Media Downloads**: Automatic download of images, videos, and audio files
- 🔄 **Resume Capability**: Resume interrupted crawls from where they left off
- 📈 **Progress Tracking**: Real-time progress monitoring and reporting
- 🛡️ **Rate Limiting**: Built-in rate limiting to respect platform policies
- 🔧 **Configurable**: Extensive configuration options for different use cases
- 📝 **Comprehensive Logging**: Detailed logging for debugging and monitoring

## Supported Platforms

- ✅ **Douyin** - Chinese TikTok platform
- 🚧 **TikTok** - Coming soon
- 🚧 **Instagram** - Coming soon
- 🚧 **Facebook** - Coming soon
- 🚧 **YouTube** - Coming soon

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd sabah-tourism/crawler

# Install dependencies
bun install

# Set up the database and directories
bun run setup:db
```

### 2. Configuration

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` with your preferred settings:

```env
# Database Configuration
DATABASE_PATH=./data/crawler.db

# Crawler Configuration
DEFAULT_MAX_POSTS=100
DEFAULT_DOWNLOAD_CONCURRENCY=3
DEFAULT_REQUESTS_PER_MINUTE=30

# Stagehand Configuration
STAGEHAND_HEADLESS=true
STAGEHAND_LOG_LEVEL=info

# Media Download Configuration
MEDIA_OUTPUT_DIR=./output
MAX_FILE_SIZE_MB=100
```

### 3. Basic Usage

```bash
# Crawl Douyin for tourism content
bun run crawl --platform douyin --keywords "sabah,tourism,travel" --max-posts 50

# Run with media downloads disabled
bun run crawl --platform douyin --keywords "sabah" --no-media

# Resume a previous crawl session
bun run crawl --platform douyin --keywords "sabah" --resume 123

# Check crawler status and statistics
bun run status
```

## Advanced Usage

### Command Line Options

```bash
bun run crawl [options]

Options:
  -p, --platform <platform>     Platform to crawl (douyin, tiktok, instagram, etc.)
  -k, --keywords <keywords>     Comma-separated list of keywords
  -m, --max-posts <number>      Maximum number of posts to crawl (default: 100)
  --no-media                    Skip media downloads
  --media-types <types>         Media types to download (image,video,audio)
  -o, --output <dir>            Output directory (default: ./output)
  --headless                    Run browser in headless mode
  --resume <sessionId>          Resume a previous crawl session
  --dry-run                     Perform a dry run without downloading
  --verbose                     Enable verbose logging
```

### Programmatic Usage

```typescript
import { DouyinCrawler } from './src/crawlers/douyin/DouyinCrawler.js';
import { DatabaseManager } from './src/crawlers/base/DatabaseManager.js';
import { createCrawlerConfig } from './config/crawler.config.js';
import { createDatabaseConfig } from './config/database.config.js';

// Initialize database
const database = new DatabaseManager(createDatabaseConfig());

// Create crawler configuration
const config = createCrawlerConfig('douyin', ['sabah', 'tourism'], {
  maxPosts: 100,
  downloadMedia: true,
  mediaTypes: ['image', 'video']
});

// Create and configure crawler
const crawler = new DouyinCrawler(config, database);

// Set up event listeners
crawler.on('progress', (progress) => {
  console.log(`Progress: ${progress.progress.processedPosts}/${progress.progress.totalPosts}`);
});

crawler.on('completed', (summary) => {
  console.log(`Completed! Found ${summary.totalPosts} posts`);
});

// Start crawling
await crawler.crawl();
```

## Project Structure

```
sabah-tourism/crawler/
├── src/
│   ├── crawlers/
│   │   ├── base/                   # Base crawler classes
│   │   │   ├── BaseCrawler.ts      # Abstract base crawler
│   │   │   ├── DatabaseManager.ts  # SQLite operations
│   │   │   └── MediaDownloader.ts  # Media file handler
│   │   ├── douyin/                 # Douyin-specific implementation
│   │   │   ├── DouyinCrawler.ts    # Main crawler class
│   │   │   ├── DouyinParser.ts     # Content extraction logic
│   │   │   └── DouyinConfig.ts     # Platform configuration
│   │   └── index.ts                # Crawler factory
│   ├── types/                      # TypeScript type definitions
│   ├── utils/                      # Utility functions
│   └── index.ts                    # Main CLI entry point
├── config/                         # Configuration files
├── scripts/                        # Setup and utility scripts
├── output/                         # Downloaded media files
├── data/                          # SQLite database
└── logs/                          # Log files
```

## Database Schema

The crawler uses SQLite with the following main tables:

- **social_posts**: Stores post metadata and content
- **media_files**: Tracks downloaded media files
- **crawl_sessions**: Manages crawl sessions for resume functionality
- **crawl_attempts**: Individual post processing attempts
- **search_keywords**: Keyword tracking and statistics

## Configuration

### Crawler Configuration

```typescript
interface CrawlerConfig {
  platform: 'douyin' | 'tiktok' | 'instagram';
  keywords: string[];
  maxPosts: number;
  downloadMedia: boolean;
  mediaTypes: ('image' | 'video' | 'audio')[];
  rateLimiting: {
    requestsPerMinute: number;
    downloadConcurrency: number;
  };
  retryConfig: {
    maxRetries: number;
    backoffMultiplier: number;
    initialDelay: number;
  };
  browserConfig: {
    headless: boolean;
    timeout: number;
  };
  filters?: {
    minLikes?: number;
    minViews?: number;
    dateRange?: { from: Date; to: Date };
    authors?: string[];
    excludeAuthors?: string[];
  };
}
```

### Platform-Specific Settings

Each platform has its own configuration for selectors, rate limits, and behavior:

```typescript
const douyinConfig = {
  rateLimits: {
    requestsPerMinute: 20,
    downloadConcurrency: 2
  },
  waitTimes: {
    pageLoad: 10000,
    searchDelay: 2000,
    scrollDelay: 1000
  }
};
```

## Development

### Adding New Platforms

1. Create a new directory under `src/crawlers/[platform]/`
2. Implement the platform-specific crawler extending `BaseCrawler`
3. Create parser and configuration files
4. Add the platform to the crawler factory
5. Update tests and documentation

### Running Tests

```bash
# Run all tests
bun test

# Run unit tests only
bun run test:unit

# Run integration tests
bun run test:integration
```

### Building

```bash
# Build the project
bun run build

# Type checking
bun run type-check

# Linting
bun run lint
```

## Troubleshooting

### Common Issues

1. **Browser not found**: Run `bun run setup` to ensure all dependencies are installed
2. **Rate limiting**: Adjust `requestsPerMinute` in configuration
3. **Memory issues**: Reduce `downloadConcurrency` or `maxPosts`
4. **Network timeouts**: Increase timeout values in browser configuration

### Debugging

Enable verbose logging:

```bash
bun run crawl --platform douyin --keywords "sabah" --verbose
```

Check log files in `./logs/` directory for detailed error information.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- Check the troubleshooting section
- Review log files in `./logs/`
- Open an issue on GitHub
