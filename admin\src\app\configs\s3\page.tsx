'use client';

import { Cloud, Search } from 'lucide-react';
import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useAuthStore } from '@/stores/auth';
import type { S3Configuration } from '@/types';

export default function S3ConfigsPage() {
  const { token } = useAuthStore();
  const [configs, setConfigs] = useState<S3Configuration[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const API_BASE_URL =
    process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8787';

  useEffect(() => {
    const fetchConfigs = async () => {
      if (!token) return;

      try {
        setIsLoading(true);
        const response = await fetch(
          `${API_BASE_URL}/api/admin/s3-configurations`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          },
        );

        if (response.ok) {
          const data = await response.json();
          setConfigs(data);
        } else {
          setError('Failed to fetch S3 configurations');
        }
      } catch (error) {
        setError('Network error');
      } finally {
        setIsLoading(false);
      }
    };

    fetchConfigs();
  }, [token, API_BASE_URL]);

  const filteredConfigs = configs.filter(
    (config) =>
      config.serviceName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      config.bucketName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      config.region?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      config.endpointUrl?.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const maskSecret = (secret: string) => {
    if (secret.length <= 8) return secret;
    return secret.substring(0, 4) + '...' + secret.substring(secret.length - 4);
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            S3 Configurations
          </h1>
          <p className="text-gray-600">View all S3 storage configurations</p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            {error}
          </div>
        )}

        <Card>
          <CardHeader>
            <CardTitle>All S3 Configurations</CardTitle>
            <CardDescription>
              S3-compatible storage configurations for file management
            </CardDescription>
            <div className="flex items-center space-x-2">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search configurations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <p className="text-gray-500">Loading S3 configurations...</p>
              </div>
            ) : filteredConfigs.length === 0 ? (
              <div className="text-center py-8">
                <Cloud className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  No configurations found
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchQuery
                    ? 'No configurations match your search.'
                    : 'No S3 configurations have been created yet.'}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredConfigs.map((config) => (
                  <div
                    key={config.id}
                    className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-lg font-medium text-gray-900">
                            {config.serviceName}
                          </h3>
                        </div>
                        <div className="mt-2 space-y-1">
                          <p className="text-sm text-gray-600">
                            <strong>Endpoint:</strong>{' '}
                            {config.endpointUrl || 'Default'}
                          </p>
                          <p className="text-sm text-gray-600">
                            <strong>Region:</strong> {config.region || 'N/A'}
                          </p>
                          <p className="text-sm text-gray-600">
                            <strong>Bucket:</strong> {config.bucketName}
                          </p>
                          <p className="text-sm text-gray-600">
                            <strong>Access Key ID:</strong> {config.accessKeyId}
                          </p>
                          <p className="text-sm text-gray-600">
                            <strong>Secret Access Key:</strong>{' '}
                            {maskSecret(config.secretAccessKey)}
                          </p>
                          <p className="text-xs text-gray-500 mt-2">
                            Created:{' '}
                            {new Date(config.createdAt).toLocaleDateString()}
                            {config.updatedAt !== config.createdAt && (
                              <span>
                                {' '}
                                • Updated:{' '}
                                {new Date(
                                  config.updatedAt,
                                ).toLocaleDateString()}
                              </span>
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
