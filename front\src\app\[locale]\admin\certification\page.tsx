'use client';

export const runtime = 'edge';

import {
  Award,
  Calendar,
  CheckCircle,
  Clock,
  Download,
  Edit3,
  Eye,
  FileText,
  Filter,
  PlusCircle,
  Search,
  Trash2,
  Upload,
  XCircle,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Link, useRouter } from '@/i18n/navigation';
import { api } from '@/lib/api';

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

interface Certificate {
  id: number;
  certificateNumber: string;
  companyName: string;
  productName?: string;
  certificateType: string;
  status: 'valid' | 'expired' | 'suspended' | 'revoked' | 'pending';
  issuedDate: string;
  expiryDate: string;
  certificationBody: string;
  category?: string;
  subcategory?: string;
  documentUrl?: string;
  createdAt: string;
  updatedAt: string;
}

interface CertificateResponse {
  certificates: Certificate[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
    totalPages: number;
  };
}

export default function CertificationPage() {
  const router = useRouter();
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    hasMore: false,
    totalPages: 0,
  });

  useEffect(() => {
    fetchCertificates();
  }, [pagination.page, searchTerm, statusFilter, typeFilter]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchCertificates = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });

      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (typeFilter !== 'all') params.append('type', typeFilter);

      const response = await api.get<CertificateResponse>(
        `/admin/certificates?${params}`,
      );
      setCertificates(response.data.certificates);
      setPagination(response.data.pagination);
    } catch (err) {
      console.error('Error fetching certificates:', err);
      setError('Failed to load certificates');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this certificate?')) {
      return;
    }

    try {
      await api.delete(`/admin/certificates/${id}`);
      await fetchCertificates(); // Refresh the list
    } catch (err) {
      console.error('Error deleting certificate:', err);
      setError('Failed to delete certificate');
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchCertificates();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'valid':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'expired':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'suspended':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'revoked':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-blue-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'valid':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'expired':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'suspended':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'revoked':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'pending':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const isExpiringSoon = (expiryDate: string) => {
    const expiry = new Date(expiryDate);
    const now = new Date();
    const thirtyDaysFromNow = new Date(
      now.getTime() + 30 * 24 * 60 * 60 * 1000,
    );
    return expiry <= thirtyDaysFromNow && expiry > now;
  };

  if (isLoading && certificates.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">
            Certification Management
          </h1>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading certificates...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <Button variant="ghost" size="sm" onClick={() => setError(null)}>
              ×
            </Button>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Certification Management
          </h1>
          <p className="text-gray-600 mt-2">
            Manage halal certificates, approvals, and certification processes
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push('/admin/certification/upload')}
          >
            <Upload className="mr-2 h-4 w-4" />
            Upload Certificate
          </Button>
          <Button onClick={() => router.push('/admin/certification/new')}>
            <PlusCircle className="mr-2 h-4 w-4" />
            New Certificate
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter Certificates</CardTitle>
          <CardDescription>
            Find certificates by number, company name, or product name
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search certificates..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Button type="submit">
                <Search className="mr-2 h-4 w-4" />
                Search
              </Button>
            </div>
            <div className="flex gap-4">
              <div className="flex-1">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="valid">Valid</SelectItem>
                    <SelectItem value="expired">Expired</SelectItem>
                    <SelectItem value="suspended">Suspended</SelectItem>
                    <SelectItem value="revoked">Revoked</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex-1">
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="halal">Halal Certificate</SelectItem>
                    <SelectItem value="premise">Premise Certificate</SelectItem>
                    <SelectItem value="product">Product Certificate</SelectItem>
                    <SelectItem value="slaughter">
                      Slaughter Certificate
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Certificates Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {certificates.map((certificate) => (
          <Card
            key={certificate.id}
            className="hover:shadow-md transition-shadow"
          >
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-yellow-600" />
                  <CardTitle className="text-lg truncate">
                    {certificate.certificateNumber}
                  </CardTitle>
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      router.push(`/admin/certification/${certificate.id}`)
                    }
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      router.push(`/admin/certification/${certificate.id}/edit`)
                    }
                  >
                    <Edit3 className="h-4 w-4" />
                  </Button>
                  {certificate.documentUrl && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        window.open(certificate.documentUrl, '_blank')
                      }
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDelete(certificate.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardDescription>
                <div className="flex items-center gap-2">
                  {getStatusIcon(certificate.status)}
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(certificate.status)}`}
                  >
                    {certificate.status.toUpperCase()}
                  </span>
                  {isExpiringSoon(certificate.expiryDate) && (
                    <span className="px-2 py-1 rounded-full text-xs font-medium bg-orange-50 text-orange-600 border border-orange-200">
                      EXPIRING SOON
                    </span>
                  )}
                </div>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Company:</span>{' '}
                  <span className="text-gray-600">
                    {certificate.companyName}
                  </span>
                </div>
                {certificate.productName && (
                  <div>
                    <span className="font-medium">Product:</span>{' '}
                    <span className="text-gray-600">
                      {certificate.productName}
                    </span>
                  </div>
                )}
                <div>
                  <span className="font-medium">Type:</span>{' '}
                  <span className="text-gray-600">
                    {certificate.certificateType}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3 text-gray-400" />
                  <span className="text-gray-600 text-xs">
                    Issued:{' '}
                    {new Date(certificate.issuedDate).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3 text-gray-400" />
                  <span className="text-gray-600 text-xs">
                    Expires:{' '}
                    {new Date(certificate.expiryDate).toLocaleDateString()}
                  </span>
                </div>
                {certificate.certificationBody && (
                  <div>
                    <span className="font-medium">Issued by:</span>{' '}
                    <span className="text-gray-600 text-xs">
                      {certificate.certificationBody}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}

        {certificates.length === 0 && !isLoading && (
          <Card className="col-span-full">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Award className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Certificates Found
              </h3>
              <p className="text-gray-600 text-center mb-4">
                {searchTerm
                  ? `No certificates found matching "${searchTerm}"`
                  : 'Get started by adding your first certificate.'}
              </p>
              <Button onClick={() => router.push('/admin/certification/new')}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Certificate
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
