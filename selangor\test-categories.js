#!/usr/bin/env node

/**
 * Test script to check what categories are available in the database
 * Run this to see what categories you have for the dropdown filter
 */

const { config } = require('dotenv');
const postgres = require('postgres');

// Load environment variables
config({ path: '.env.local' });

const DATABASE_URL = process.env.DATABASE_URL;

if (!DATABASE_URL) {
  console.error('DATABASE_URL not found in .env.local');
  process.exit(1);
}

async function testCategories() {
  const sql = postgres(DATABASE_URL);

  try {
    console.log('🔍 Checking available categories in the database...\n');

    // Check products categories
    console.log('📦 PRODUCT CATEGORIES:');
    const productCategories = await sql`
      SELECT 
        category,
        COUNT(*) as count
      FROM products 
      WHERE category IS NOT NULL 
        AND category != '' 
        AND category != 'null'
      GROUP BY category 
      ORDER BY count DESC, category ASC
      LIMIT 20
    `;

    if (productCategories.length > 0) {
      productCategories.forEach(row => {
        console.log(`  • ${row.category} (${row.count} products)`);
      });
    } else {
      console.log('  No product categories found');
    }

    console.log('\n📦 PRODUCT SUBCATEGORIES:');
    const productSubcategories = await sql`
      SELECT 
        subcategory,
        COUNT(*) as count
      FROM products 
      WHERE subcategory IS NOT NULL 
        AND subcategory != '' 
        AND subcategory != 'null'
      GROUP BY subcategory 
      ORDER BY count DESC, subcategory ASC
      LIMIT 20
    `;

    if (productSubcategories.length > 0) {
      productSubcategories.forEach(row => {
        console.log(`  • ${row.subcategory} (${row.count} products)`);
      });
    } else {
      console.log('  No product subcategories found');
    }

    // Check companies categories
    console.log('\n🏢 COMPANY CATEGORIES:');
    const companyCategories = await sql`
      SELECT 
        category,
        COUNT(*) as count
      FROM companies 
      WHERE category IS NOT NULL 
        AND category != '' 
        AND category != 'null'
      GROUP BY category 
      ORDER BY count DESC, category ASC
      LIMIT 20
    `;

    if (companyCategories.length > 0) {
      companyCategories.forEach(row => {
        console.log(`  • ${row.category} (${row.count} companies)`);
      });
    } else {
      console.log('  No company categories found');
    }

    console.log('\n🏢 COMPANY SUBCATEGORIES:');
    const companySubcategories = await sql`
      SELECT 
        subcategory,
        COUNT(*) as count
      FROM companies 
      WHERE subcategory IS NOT NULL 
        AND subcategory != '' 
        AND subcategory != 'null'
      GROUP BY subcategory 
      ORDER BY count DESC, subcategory ASC
      LIMIT 20
    `;

    if (companySubcategories.length > 0) {
      companySubcategories.forEach(row => {
        console.log(`  • ${row.subcategory} (${row.count} companies)`);
      });
    } else {
      console.log('  No company subcategories found');
    }

    // Check total counts
    console.log('\n📊 SUMMARY:');
    const [productCount] = await sql`SELECT COUNT(*) as count FROM products`;
    const [companyCount] = await sql`SELECT COUNT(*) as count FROM companies`;
    
    console.log(`  • Total products: ${productCount.count}`);
    console.log(`  • Total companies: ${companyCount.count}`);
    console.log(`  • Product categories: ${productCategories.length}`);
    console.log(`  • Product subcategories: ${productSubcategories.length}`);
    console.log(`  • Company categories: ${companyCategories.length}`);
    console.log(`  • Company subcategories: ${companySubcategories.length}`);

    console.log('\n✅ Categories test completed!');
    console.log('\n💡 You can now use these categories in your search filter dropdown.');
    console.log('   The most common categories will appear at the top of the dropdown.');

  } catch (error) {
    console.error('❌ Error testing categories:', error);
  } finally {
    await sql.end();
  }
}

testCategories();
