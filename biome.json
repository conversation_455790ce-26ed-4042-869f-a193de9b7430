{"$schema": "https://biomejs.dev/schemas/2.0.5/schema.json", "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noUnusedTemplateLiteral": "warn", "useBlockStatements": "warn", "noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}, "correctness": {"noUnusedVariables": "warn", "noUnusedImports": "warn"}, "suspicious": {"noExplicitAny": "warn"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 80}, "javascript": {"formatter": {"quoteStyle": "single"}}, "json": {"formatter": {}}, "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}}