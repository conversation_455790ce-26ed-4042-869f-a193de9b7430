# Halal Malaysia Portal Clone - Project Plan

## Project Overview
Clone the official Halal Malaysia Portal (https://myehalal.halal.gov.my/portal-halal/v1/index.php) using Next.js with TypeScript and Tailwind CSS.

## Target Website Analysis
- **Original Site**: Malaysian government portal for Halal certification
- **Key Features**: Halal status verification, announcements, news, certification info
- **Languages**: English and Bahasa Malaysia (BM)
- **Main Sections**: Corporate, Certification, Procedure, Contact Us
- **Special Features**: QR code scanning, e-complaint system, MYeHALAL system links

## Project Structure
```
halal/
├── front/                    # Next.js application
│   ├── src/
│   │   ├── app/             # App router pages
│   │   ├── components/      # Reusable components
│   │   ├── lib/            # Utilities and configurations
│   │   └── types/          # TypeScript type definitions
├── assets/                  # Static assets (images, documents)
└── PLAN.md                 # This file
```

## Phase 1: Project Setup & Infrastructure ✅
- [x] Initialize Next.js project with TypeScript and Tailwind CSS
- [ ] Set up project structure and folders
- [ ] Configure ESLint and Prettier
- [ ] Set up environment variables
- [ ] Install additional dependencies (icons, UI components)

## Phase 2: Asset Collection & Analysis
- [ ] Download and organize images from original site
- [ ] Analyze CSS styles and color schemes
- [ ] Document fonts and typography used
- [ ] Create asset inventory
- [ ] Set up public assets folder structure

## Phase 3: Core Layout & Navigation
- [ ] Create main layout component
- [ ] Implement header with logo and navigation
- [ ] Build responsive mobile menu
- [ ] Create footer with links and contact info
- [ ] Implement language switcher (EN/BM)
- [ ] Set up routing structure

## Phase 4: Homepage Components
- [ ] Hero section with carousel/slider
- [ ] Halal status verification search
- [ ] Announcements section
- [ ] News section
- [ ] Quick links grid (MYeHALAL, FHCB, etc.)
- [ ] Counter operation hours
- [ ] E-Aduan (complaint) section with QR code

## Phase 5: Content Pages
- [ ] Corporate information pages
- [ ] Certification pages and procedures
- [ ] Contact us page with forms
- [ ] Announcement detail pages
- [ ] News detail pages
- [ ] Circular/document pages

## Phase 6: Interactive Features
- [ ] Halal status search functionality
- [ ] Dynamic content loading
- [ ] Form submissions (contact, complaints)
- [ ] PDF document viewing
- [ ] Social media integration
- [ ] QR code generation/scanning

## Phase 7: Responsive Design & Styling
- [ ] Mobile-first responsive design
- [ ] Tablet optimization
- [ ] Desktop layout refinement
- [ ] Cross-browser compatibility
- [ ] Performance optimization
- [ ] Accessibility improvements

## Phase 8: Content Management
- [ ] Set up content structure for announcements
- [ ] Implement news management
- [ ] Document/PDF management system
- [ ] Image optimization and lazy loading
- [ ] SEO optimization

## Phase 9: Testing & Quality Assurance
- [ ] Unit testing for components
- [ ] Integration testing
- [ ] Cross-browser testing
- [ ] Mobile device testing
- [ ] Performance testing
- [ ] Accessibility testing

## Phase 10: Deployment & Documentation
- [ ] Production build optimization
- [ ] Deployment configuration
- [ ] Documentation for maintenance
- [ ] User guide creation
- [ ] Code documentation

## Technical Stack
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React or Heroicons
- **Forms**: React Hook Form with Zod validation
- **State Management**: React Context/Zustand (if needed)
- **HTTP Client**: Fetch API or Axios
- **Testing**: Jest + React Testing Library

## Key Components to Build
1. **Layout Components**
   - Header with navigation
   - Footer
   - Mobile menu
   - Language switcher

2. **Homepage Components**
   - Hero carousel
   - Search widget
   - Announcement cards
   - News cards
   - Quick links grid
   - Operation hours
   - QR code section

3. **Content Components**
   - Page layouts
   - Article/news detail
   - Document viewer
   - Contact forms
   - Search results

4. **Utility Components**
   - Loading states
   - Error boundaries
   - Modal/dialog
   - Toast notifications

## Data Structure Planning
- Announcements (title, date, content, category)
- News articles (title, date, content, images)
- Documents/PDFs (title, category, file path)
- Contact information
- Operation hours
- Quick links configuration

## Responsive Breakpoints
- Mobile: 320px - 768px
- Tablet: 768px - 1024px
- Desktop: 1024px+

## Performance Goals
- First Contentful Paint: < 2s
- Largest Contentful Paint: < 3s
- Cumulative Layout Shift: < 0.1
- Time to Interactive: < 3s

## Accessibility Goals
- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Focus management

## Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

---

## Next Steps
1. Start with Phase 1 completion
2. Analyze original website structure in detail
3. Begin asset collection and organization
4. Set up development environment
5. Create initial component structure

## Notes
- Maintain government website standards
- Ensure bilingual support (EN/BM)
- Focus on accessibility and usability
- Keep design consistent with original
- Optimize for mobile users
