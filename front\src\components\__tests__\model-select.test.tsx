import { render, screen } from '@testing-library/react';
import { ModelSelect } from '@/components/ui/model-select';
import { ProviderSelect } from '@/components/ui/provider-select';

describe('ModelSelect Component', () => {
  it('renders with default placeholder', () => {
    render(<ModelSelect />);
    expect(screen.getByText('Select model')).toBeInTheDocument();
  });

  it('renders with custom placeholder', () => {
    render(<ModelSelect placeholder="Choose AI model" />);
    expect(screen.getByText('Choose AI model')).toBeInTheDocument();
  });

  it('filters models by provider', () => {
    const { container } = render(<ModelSelect provider="openai" />);

    // This is a basic test - in a real scenario you'd need to open the dropdown
    // and verify only OpenAI models are shown
    expect(
      container.querySelector('[data-slot="select-trigger"]'),
    ).toBeInTheDocument();
  });
});

describe('ProviderSelect Component', () => {
  it('renders with default placeholder', () => {
    render(<ProviderSelect />);
    expect(screen.getByText('Select provider')).toBeInTheDocument();
  });

  it('renders with custom placeholder', () => {
    render(<ProviderSelect placeholder="Choose provider" />);
    expect(screen.getByText('Choose provider')).toBeInTheDocument();
  });

  it('excludes Azure when includeAzure is false', () => {
    const { container } = render(<ProviderSelect includeAzure={false} />);

    // This is a basic test - in a real scenario you'd need to open the dropdown
    // and verify Azure is not shown
    expect(
      container.querySelector('[data-slot="select-trigger"]'),
    ).toBeInTheDocument();
  });
});
