import type {
  HalalKnowledgeRequest,
  HalalKnowledgeResponse,
  ParseR2rOptions,
  TextResult,
} from '../types';
import openaiService from './openai';
import { R2RService } from './r2r';

export class HalalKnowledgeService {
  private r2rService: R2RService;

  constructor() {
    this.r2rService = new R2RService();
  }

  /**
   * Search for halal knowledge and provide an AI-generated answer
   */
  async searchAndAnswer(
    request: HalalKnowledgeRequest,
    env?: any,
  ): Promise<HalalKnowledgeResponse> {
    try {
      const {
        query,
        sessionId,
        maxResults = 5,
        minScore = 0.3,
        includeContext = true,
      } = request;

      // First, search for relevant knowledge in R2R
      const searchOptions = {
        retrieveDocument: true,
        maxWordCount: 2000,
        includeGraph: true,
        collectionId: env?.R2R_COLLECTION_ID || process.env?.R2R_COLLECTION_ID,
      };

      const { chunks, graph } = await this.r2rService.search(
        query,
        searchOptions,
        env,
      );

      // Parse and filter the results
      const parseOptions: ParseR2rOptions = {
        retrieveDocument: true,
        maxWordCount: 2000,
        includeGraph: true,
        minScore,
        limit: maxResults,
      };

      const client = this.r2rService.getClient(env);
      const searchResult = await this.r2rService.parseR2rResult(
        client,
        chunks,
        graph,
        parseOptions,
      );

      // If no relevant results found, return success but no answer - let MessageHandler handle the message
      if (searchResult.texts.length === 0) {
        return {
          success: true,
          answer: null,
          sources: [],
          query,
          sessionId,
        };
      }

      // Prepare context from search results
      const context = this.prepareContext(searchResult.texts);

      // Generate AI response using the context
      const aiResponse = await this.generateAnswer(query, context, env);

      if (!aiResponse.success) {
        return {
          success: false,
          error: aiResponse.error || 'Failed to generate answer',
          query,
          sessionId,
        };
      }

      return {
        success: true,
        answer: aiResponse.message,
        sources: includeContext ? searchResult.texts : [],
        query,
        sessionId,
        usage: aiResponse.usage,
      };
    } catch (error) {
      console.error('Halal knowledge search error:', error);

      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Unknown error occurred',
        query: request.query,
        sessionId: request.sessionId,
      };
    }
  }

  /**
   * Prepare context from search results for the AI prompt
   */
  private prepareContext(results: TextResult[]): string {
    if (results.length === 0) {
      return '';
    }

    const contextParts = results.map((result, index) => {
      const source = result.document_id
        ? `[Source ${index + 1}: ${result.document_id}]`
        : `[Source ${index + 1}]`;
      return `${source}\n${result.text}\n`;
    });

    return contextParts.join('\n---\n\n');
  }

  /**
   * Generate an AI answer using OpenAI with the provided context
   */
  private async generateAnswer(query: string, context: string, env?: any) {
    const systemPrompt = `You are a knowledgeable Islamic scholar and halal expert. Your role is to provide accurate, well-informed answers about halal (permissible) and haram (forbidden) matters in Islam based on the Quran, Sunnah, and established Islamic jurisprudence.

Guidelines for your responses:
1. Always base your answers on authentic Islamic sources
2. Be clear about what is halal (permissible) and what is haram (forbidden)
3. When there are differences of opinion among scholars, mention this
4. If you're uncertain about something, say so rather than guessing
5. Provide practical guidance when appropriate
6. Be respectful and considerate of different Islamic schools of thought
7. Use the provided context from the knowledge base to inform your answer

Context from knowledge base:
${context}

Please answer the following question about halal matters:`;

    const messages = [
      {
        role: 'system' as const,
        content: systemPrompt,
      },
      {
        role: 'user' as const,
        content: query,
      },
    ];

    return await openaiService.sendTextMessage(messages, 'gpt-4o-mini', env);
  }

  /**
   * Check if a query is related to halal topics
   */
  isHalalRelatedQuery(query: string): boolean {
    const halalKeywords = [
      'halal',
      'haram',
      'islamic',
      'islam',
      'muslim',
      'quran',
      'sunnah',
      'prophet',
      'muhammad',
      'allah',
      'permissible',
      'forbidden',
      'makruh',
      'mustahab',
      'fiqh',
      'shariah',
      'sharia',
      'fatwa',
      'imam',
      'scholar',
      'prayer',
      'salah',
      'zakat',
      'hajj',
      'ramadan',
      'fasting',
      'sawm',
      'food',
      'meat',
      'pork',
      'alcohol',
      'wine',
      'gambling',
      'riba',
      'interest',
      'usury',
      'marriage',
      'divorce',
      'inheritance',
      'hijab',
      'modest',
      'clothing',
      'music',
      'art',
      'business',
      'finance',
      'banking',
      'investment',
      'charity',
      'sadaqah',
      'wudu',
      'ghusl',
      'taharah',
      'najis',
      'pure',
      'impure',
      'clean',
      'unclean',
    ];

    const queryLower = query.toLowerCase();
    return halalKeywords.some((keyword) => queryLower.includes(keyword));
  }
}

// Export the class instead of a singleton instance to avoid env access issues
export default HalalKnowledgeService;
