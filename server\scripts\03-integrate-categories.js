/**
 * Category Integration Script
 * 
 * This script randomly assigns categories to existing products and companies
 * based on intelligent keyword matching and random selection.
 * 
 * Usage: node scripts/integrate-categories.js
 * 
 * Features:
 * - Smart keyword-based category assignment
 * - Multiple categories per item (1-3 categories)
 * - Fallback random assignment if no keywords match
 * - Conflict prevention with ON CONFLICT DO NOTHING
 * - Progress tracking and detailed reporting
 */

const postgres = require('postgres');
require('dotenv').config();

// Category mapping based on keywords in product/company names and categories
const CATEGORY_KEYWORDS = {
  1: ['meat', 'poultry', 'chicken', 'beef', 'lamb', 'mutton', 'turkey', 'duck', 'sausage', 'burger', 'patty'],
  2: ['seafood', 'fish', 'salmon', 'tuna', 'shrimp', 'prawn', 'crab', 'lobster', 'squid', 'anchovy'],
  3: ['packaged', 'processed', 'canned', 'frozen', 'ready', 'instant', 'preserved', 'sauce', 'paste'],
  4: ['snack', 'confectionery', 'candy', 'chocolate', 'biscuit', 'cookie', 'cake', 'sweet', 'gum', 'chip'],
  5: ['beverage', 'drink', 'juice', 'water', 'tea', 'coffee', 'soda', 'milk', 'yogurt', 'smoothie'],
  6: ['dairy', 'milk', 'cheese', 'butter', 'cream', 'yogurt', 'ice cream', 'condensed'],
  7: ['ingredient', 'additive', 'flavoring', 'preservative', 'coloring', 'seasoning', 'spice', 'extract']
};

/**
 * Get random categories for an item based on text analysis
 * @param {string} text - Text to analyze (product name, company name, etc.)
 * @param {number} minCategories - Minimum number of categories to assign
 * @param {number} maxCategories - Maximum number of categories to assign
 * @returns {number[]} Array of category IDs
 */
function getRandomCategories(text, minCategories = 1, maxCategories = 3) {
  const availableCategories = Object.keys(CATEGORY_KEYWORDS).map(Number);
  const matchedCategories = [];
  
  // Find categories based on keywords
  const lowerText = text.toLowerCase();
  for (const [categoryId, keywords] of Object.entries(CATEGORY_KEYWORDS)) {
    for (const keyword of keywords) {
      if (lowerText.includes(keyword)) {
        matchedCategories.push(parseInt(categoryId));
        break;
      }
    }
  }
  
  // If no matches found, randomly select categories
  if (matchedCategories.length === 0) {
    const numCategories = Math.floor(Math.random() * (maxCategories - minCategories + 1)) + minCategories;
    const shuffled = availableCategories.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, numCategories);
  }
  
  // Add some random categories to matched ones
  const additionalCount = Math.floor(Math.random() * 2); // 0-1 additional categories
  const remainingCategories = availableCategories.filter(id => !matchedCategories.includes(id));
  const shuffled = remainingCategories.sort(() => 0.5 - Math.random());
  
  return [...matchedCategories, ...shuffled.slice(0, additionalCount)];
}

async function integrateCategories() {
  const sql = postgres(process.env.DATABASE_URL);

  try {
    console.log('🔄 Starting category integration...');

    // Get all categories
    const categories = await sql`SELECT id, category_name FROM categories ORDER BY id`;
    console.log(`\n📋 Available categories (${categories.length}):`);
    categories.forEach(cat => {
      console.log(`  ${cat.id}. ${cat.category_name}`);
    });

    // Get all products
    const products = await sql`
      SELECT id, product_name, company_name, category 
      FROM products 
      WHERE id IS NOT NULL
      LIMIT 100
    `;
    console.log(`\n📦 Found ${products.length} products to categorize`);

    // Get all companies
    const companies = await sql`
      SELECT id, company_name, category 
      FROM companies 
      WHERE id IS NOT NULL
      LIMIT 50
    `;
    console.log(`🏢 Found ${companies.length} companies to categorize`);

    // Clear existing relationships (for clean start)
    console.log('\n🧹 Clearing existing category relationships...');
    await sql`DELETE FROM product_categories`;
    await sql`DELETE FROM company_categories`;

    // Integrate products with categories
    console.log('\n📦 Integrating products with categories...');
    let productCount = 0;
    
    for (const product of products) {
      const searchText = `${product.product_name} ${product.company_name} ${product.category || ''}`;
      const selectedCategories = getRandomCategories(searchText, 1, 3);
      
      for (const categoryId of selectedCategories) {
        try {
          await sql`
            INSERT INTO product_categories (product_id, category_id) 
            VALUES (${product.id}, ${categoryId})
            ON CONFLICT (product_id, category_id) DO NOTHING
          `;
        } catch (error) {
          console.log(`⚠️  Could not assign category ${categoryId} to product ${product.id}: ${error.message}`);
        }
      }
      
      productCount++;
      if (productCount % 20 === 0) {
        console.log(`   Processed ${productCount}/${products.length} products...`);
      }
    }

    // Integrate companies with categories
    console.log('\n🏢 Integrating companies with categories...');
    let companyCount = 0;
    
    for (const company of companies) {
      const searchText = `${company.company_name} ${company.category || ''}`;
      const selectedCategories = getRandomCategories(searchText, 1, 2);
      
      for (const categoryId of selectedCategories) {
        try {
          await sql`
            INSERT INTO company_categories (company_id, category_id) 
            VALUES (${company.id}, ${categoryId})
            ON CONFLICT (company_id, category_id) DO NOTHING
          `;
        } catch (error) {
          console.log(`⚠️  Could not assign category ${categoryId} to company ${company.id}: ${error.message}`);
        }
      }
      
      companyCount++;
      if (companyCount % 10 === 0) {
        console.log(`   Processed ${companyCount}/${companies.length} companies...`);
      }
    }

    // Display results
    console.log('\n📊 Integration Results:');
    
    const productCategoryStats = await sql`
      SELECT c.category_name, COUNT(pc.product_id) as product_count
      FROM categories c
      LEFT JOIN product_categories pc ON c.id = pc.category_id
      GROUP BY c.id, c.category_name
      ORDER BY product_count DESC, c.category_name
    `;
    
    console.log('\n📦 Products per category:');
    productCategoryStats.forEach(stat => {
      console.log(`  ${stat.category_name}: ${stat.product_count} products`);
    });

    const companyCategoryStats = await sql`
      SELECT c.category_name, COUNT(cc.company_id) as company_count
      FROM categories c
      LEFT JOIN company_categories cc ON c.id = cc.category_id
      GROUP BY c.id, c.category_name
      ORDER BY company_count DESC, c.category_name
    `;
    
    console.log('\n🏢 Companies per category:');
    companyCategoryStats.forEach(stat => {
      console.log(`  ${stat.category_name}: ${stat.company_count} companies`);
    });

    // Show some examples
    console.log('\n🔍 Sample product-category relationships:');
    const sampleProducts = await sql`
      SELECT p.product_name, p.company_name, 
             ARRAY_AGG(c.category_name ORDER BY c.category_name) as categories
      FROM products p
      JOIN product_categories pc ON p.id = pc.product_id
      JOIN categories c ON pc.category_id = c.id
      GROUP BY p.id, p.product_name, p.company_name
      LIMIT 5
    `;
    
    sampleProducts.forEach(product => {
      console.log(`  "${product.product_name}" by ${product.company_name}`);
      console.log(`    Categories: ${product.categories.join(', ')}`);
    });

    console.log('\n🎉 Category integration completed successfully!');
    console.log(`✅ Integrated ${productCount} products and ${companyCount} companies with categories`);

  } catch (error) {
    console.error('❌ Error during category integration:', error.message);
  } finally {
    await sql.end();
  }
}

// Run the integration if this script is executed directly
if (require.main === module) {
  integrateCategories();
}

module.exports = { integrateCategories, getRandomCategories, CATEGORY_KEYWORDS };
