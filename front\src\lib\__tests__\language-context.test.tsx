import { act, renderHook } from '@testing-library/react';
import type React from 'react';
import { render } from '@/test-utils';
import { LanguageProvider, useLanguage } from '../language-context';

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('LanguageContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <LanguageProvider>{children}</LanguageProvider>
  );

  describe('useLanguage hook', () => {
    it('should provide default language as English', () => {
      mockLocalStorage.getItem.mockReturnValue(null);

      const { result } = renderHook(() => useLanguage(), { wrapper });

      expect(result.current.language).toBe('en');
    });

    it('should load saved language from localStorage', () => {
      mockLocalStorage.getItem.mockReturnValue('bm');

      const { result } = renderHook(() => useLanguage(), { wrapper });

      expect(result.current.language).toBe('bm');
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith(
        'halal-portal-language',
      );
    });

    it('should change language and save to localStorage', () => {
      mockLocalStorage.getItem.mockReturnValue('en');

      const { result } = renderHook(() => useLanguage(), { wrapper });

      act(() => {
        result.current.setLanguage('bm');
      });

      expect(result.current.language).toBe('bm');
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'halal-portal-language',
        'bm',
      );
    });

    it('should provide translation function', () => {
      const { result } = renderHook(() => useLanguage(), { wrapper });

      const translation = result.current.t(
        'test-key',
        'English text',
        'Bahasa Malaysia text',
      );

      expect(translation).toBe('English text');
    });

    it('should return Bahasa Malaysia translation when language is BM', () => {
      mockLocalStorage.getItem.mockReturnValue('bm');

      const { result } = renderHook(() => useLanguage(), { wrapper });

      const translation = result.current.t(
        'test-key',
        'English text',
        'Bahasa Malaysia text',
      );

      expect(translation).toBe('Bahasa Malaysia text');
    });

    it('should fallback to English if BM translation is not provided', () => {
      mockLocalStorage.getItem.mockReturnValue('bm');

      const { result } = renderHook(() => useLanguage(), { wrapper });

      const translation = result.current.t('test-key', 'English text');

      expect(translation).toBe('English text');
    });

    it('should handle localStorage errors gracefully', () => {
      mockLocalStorage.getItem.mockImplementation(() => {
        throw new Error('localStorage error');
      });

      const { result } = renderHook(() => useLanguage(), { wrapper });

      expect(result.current.language).toBe('en');
    });

    it('should handle invalid language values from localStorage', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid-language');

      const { result } = renderHook(() => useLanguage(), { wrapper });

      expect(result.current.language).toBe('en');
    });
  });

  describe('LanguageProvider', () => {
    it('should throw error when useLanguage is used outside provider', () => {
      // Suppress console.error for this test
      const originalError = console.error;
      console.error = jest.fn();

      expect(() => {
        renderHook(() => useLanguage());
      }).toThrow('useLanguage must be used within a LanguageProvider');

      console.error = originalError;
    });

    it('should provide context value to children', () => {
      const TestComponent = () => {
        const { language } = useLanguage();
        return <div data-testid="language">{language}</div>;
      };

      const { getByTestId } = render(
        <LanguageProvider>
          <TestComponent />
        </LanguageProvider>,
      );

      expect(getByTestId('language')).toHaveTextContent('en');
    });
  });
});
