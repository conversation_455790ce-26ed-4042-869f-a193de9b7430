export class Logger {
  private static instance: Logger;
  private startTime: Date;

  private constructor() {
    this.startTime = new Date();
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private getTimestamp(): string {
    return new Date().toISOString();
  }

  private getElapsed(): string {
    const elapsed = Date.now() - this.startTime.getTime();
    const seconds = Math.floor(elapsed / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    }
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    return `${seconds}s`;
  }

  public info(message: string, ...args: any[]): void {
    console.log(
      `[${this.getTimestamp()}] [${this.getElapsed()}] INFO: ${message}`,
      ...args,
    );
  }

  public error(message: string, ...args: any[]): void {
    console.error(
      `[${this.getTimestamp()}] [${this.getElapsed()}] ERROR: ${message}`,
      ...args,
    );
  }

  public warn(message: string, ...args: any[]): void {
    console.warn(
      `[${this.getTimestamp()}] [${this.getElapsed()}] WARN: ${message}`,
      ...args,
    );
  }

  public debug(message: string, ...args: any[]): void {
    console.debug(
      `[${this.getTimestamp()}] [${this.getElapsed()}] DEBUG: ${message}`,
      ...args,
    );
  }

  public success(message: string, ...args: any[]): void {
    console.log(
      `[${this.getTimestamp()}] [${this.getElapsed()}] ✅ ${message}`,
      ...args,
    );
  }

  public progress(current: number, total: number, message: string): void {
    const percentage = Math.round((current / total) * 100);
    const progressBar =
      '█'.repeat(Math.floor(percentage / 5)) +
      '░'.repeat(20 - Math.floor(percentage / 5));
    console.log(
      `[${this.getTimestamp()}] [${this.getElapsed()}] [${progressBar}] ${percentage}% ${message}`,
    );
  }
}
