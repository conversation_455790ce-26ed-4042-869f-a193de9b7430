// Mock data generators for Halal Selangor admin dashboard

export interface SearchQuery {
  query: string;
  count: number;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
  category: 'restaurants' | 'products' | 'services' | 'certification';
  successRate: number;
}

export interface SearchPerformance {
  avgSearchDuration: number;
  zeroResultsQueries: Array<{
    query: string;
    count: number;
    category: string;
  }>;
  benchmarkComparison: {
    ourAverage: number;
    industryStandard: number;
    performance: 'above' | 'below' | 'meeting';
  };
}

export interface UserBehavior {
  geographicData: Array<{
    location: string;
    searchCount: number;
    percentage: number;
  }>;
  temporalData: {
    peakDays: Array<{
      day: string;
      count: number;
    }>;
    peakHours: Array<{
      hour: number;
      count: number;
    }>;
  };
}

export interface SearchHistory {
  refinementFunnel: Array<{
    step: number;
    query: string;
    resultsCount: number;
    nextQuery?: string;
  }>;
  repeatSearches: Array<{
    query: string;
    frequency: number;
    avgDaysBetween: number;
    userLoyalty: 'high' | 'medium' | 'low';
  }>;
}

// Mock data generators
export const generateTopKeywords = (): SearchQuery[] => {
  const keywords = [
    { query: 'halal chicken', category: 'products' as const, baseCount: 450 },
    {
      query: 'halal restaurant near me',
      category: 'restaurants' as const,
      baseCount: 380,
    },
    {
      query: 'halal certification process',
      category: 'certification' as const,
      baseCount: 320,
    },
    {
      query: 'halal beef supplier',
      category: 'products' as const,
      baseCount: 280,
    },
    {
      query: 'halal catering services',
      category: 'services' as const,
      baseCount: 250,
    },
    {
      query: 'halal food delivery',
      category: 'restaurants' as const,
      baseCount: 220,
    },
    {
      query: 'halal ingredients list',
      category: 'products' as const,
      baseCount: 200,
    },
    {
      query: 'halal certificate renewal',
      category: 'certification' as const,
      baseCount: 180,
    },
    { query: 'halal seafood', category: 'products' as const, baseCount: 160 },
    { query: 'halal bakery', category: 'restaurants' as const, baseCount: 140 },
    {
      query: 'halal meat processing',
      category: 'services' as const,
      baseCount: 120,
    },
    {
      query: 'halal audit checklist',
      category: 'certification' as const,
      baseCount: 100,
    },
  ];

  return keywords.map((keyword) => ({
    ...keyword,
    count: keyword.baseCount + Math.floor(Math.random() * 50) - 25,
    trend: Math.random() > 0.6 ? 'up' : Math.random() > 0.3 ? 'stable' : 'down',
    trendPercentage: Math.floor(Math.random() * 30) + 1,
    successRate: 0.7 + Math.random() * 0.3, // 70-100% success rate
  }));
};

export const generateCategoryBreakdown = () => {
  const total = 1500;
  return [
    { category: 'Restaurants', count: 650, percentage: 43.3 },
    { category: 'Consumer Products', count: 480, percentage: 32.0 },
    { category: 'B2B Services', count: 240, percentage: 16.0 },
    { category: 'Certification Info', count: 130, percentage: 8.7 },
  ];
};

export const generateSearchPerformance = (): SearchPerformance => {
  return {
    avgSearchDuration: 2.3 + Math.random() * 1.5, // 2.3-3.8 seconds
    zeroResultsQueries: [
      { query: 'halal vegan cheese', count: 45, category: 'products' },
      { query: 'halal wine substitute', count: 38, category: 'products' },
      { query: 'halal pet food', count: 32, category: 'products' },
      { query: 'halal cosmetics selangor', count: 28, category: 'products' },
      { query: 'halal pharmaceutical', count: 25, category: 'services' },
      { query: 'halal gelatin supplier', count: 22, category: 'products' },
      { query: 'halal logistics company', count: 18, category: 'services' },
      {
        query: 'halal food truck permit',
        count: 15,
        category: 'certification',
      },
    ],
    benchmarkComparison: {
      ourAverage: 2.8,
      industryStandard: 3.2,
      performance: 'above',
    },
  };
};

export const generateGeographicData = () => {
  return [
    { location: 'Shah Alam', searchCount: 420, percentage: 28.0 },
    { location: 'Petaling Jaya', searchCount: 380, percentage: 25.3 },
    { location: 'Subang Jaya', searchCount: 280, percentage: 18.7 },
    { location: 'Klang', searchCount: 180, percentage: 12.0 },
    { location: 'Kajang', searchCount: 120, percentage: 8.0 },
    { location: 'Selayang', searchCount: 80, percentage: 5.3 },
    { location: 'Other Areas', searchCount: 40, percentage: 2.7 },
  ];
};

export const generateTemporalData = () => {
  return {
    peakDays: [
      { day: 'Friday', count: 280 },
      { day: 'Thursday', count: 250 },
      { day: 'Wednesday', count: 220 },
      { day: 'Tuesday', count: 200 },
      { day: 'Monday', count: 180 },
      { day: 'Saturday', count: 160 },
      { day: 'Sunday', count: 140 },
    ],
    peakHours: [
      { hour: 11, count: 85 }, // 11 AM
      { hour: 12, count: 95 }, // 12 PM
      { hour: 13, count: 88 }, // 1 PM
      { hour: 14, count: 75 }, // 2 PM
      { hour: 15, count: 65 }, // 3 PM
      { hour: 16, count: 70 }, // 4 PM
      { hour: 17, count: 60 }, // 5 PM
      { hour: 18, count: 55 }, // 6 PM
      { hour: 19, count: 45 }, // 7 PM
      { hour: 20, count: 40 }, // 8 PM
    ],
  };
};

export const generateRefinementFunnel = () => {
  return [
    {
      step: 1,
      query: 'halal food',
      resultsCount: 1250,
      nextQuery: 'halal restaurant',
    },
    {
      step: 2,
      query: 'halal restaurant',
      resultsCount: 380,
      nextQuery: 'halal restaurant shah alam',
    },
    {
      step: 3,
      query: 'halal restaurant shah alam',
      resultsCount: 45,
      nextQuery: 'halal chinese restaurant shah alam',
    },
    {
      step: 4,
      query: 'halal chinese restaurant shah alam',
      resultsCount: 12,
    },
  ];
};

export const generateRepeatSearches = () => {
  return [
    {
      query: 'halal chicken supplier',
      frequency: 15,
      avgDaysBetween: 7,
      userLoyalty: 'high' as const,
    },
    {
      query: 'halal certification status',
      frequency: 12,
      avgDaysBetween: 14,
      userLoyalty: 'high' as const,
    },
    {
      query: 'halal restaurant near me',
      frequency: 8,
      avgDaysBetween: 3,
      userLoyalty: 'medium' as const,
    },
    {
      query: 'halal catering services',
      frequency: 6,
      avgDaysBetween: 21,
      userLoyalty: 'medium' as const,
    },
    {
      query: 'halal food delivery',
      frequency: 4,
      avgDaysBetween: 2,
      userLoyalty: 'low' as const,
    },
  ];
};

// Dashboard overview data
export const generateDashboardOverview = () => {
  const totalSearches = 1500 + Math.floor(Math.random() * 200);
  const successRate = 0.85 + Math.random() * 0.1;

  return {
    totalSearches,
    successfulSearches: Math.floor(totalSearches * successRate),
    successRate,
    avgResponseTime: 2.8 + Math.random() * 0.5,
    topCategories: generateCategoryBreakdown(),
    recentTrends: {
      searchVolume: Math.random() > 0.5 ? 'up' : 'down',
      searchVolumeChange: Math.floor(Math.random() * 15) + 5,
      successRate: Math.random() > 0.7 ? 'up' : 'stable',
      successRateChange: Math.floor(Math.random() * 5) + 1,
    },
  };
};
