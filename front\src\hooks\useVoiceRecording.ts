'use client';

import { useCallback, useRef, useState } from 'react';

interface UseVoiceRecordingProps {
  onTranscription?: (text: string) => void;
}

interface VoiceRecordingState {
  isRecording: boolean;
  isProcessing: boolean;
  error: string | null;
}

// Feature detection function
function isVoiceRecordingSupported(): { supported: boolean; reason?: string } {
  if (typeof window === 'undefined') {
    return { supported: false, reason: 'Not running in browser environment' };
  }

  if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
    return { supported: false, reason: 'getUserMedia not supported' };
  }

  if (!window.MediaRecorder) {
    return { supported: false, reason: 'MediaRecorder not supported' };
  }

  // Check if we're on HTTPS (required for getUserMedia in production)
  if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
    return { supported: false, reason: 'HTTPS required for microphone access' };
  }

  return { supported: true };
}

export function useVoiceRecording({
  onTranscription,
}: UseVoiceRecordingProps = {}) {
  const [state, setState] = useState<VoiceRecordingState>({
    isRecording: false,
    isProcessing: false,
    error: null,
  });

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);

  // Check feature support on mount
  const featureCheck = isVoiceRecordingSupported();
  if (!featureCheck.supported && !state.error) {
    setState((prev) => ({
      ...prev,
      error: featureCheck.reason || 'Voice recording not supported',
    }));
  }

  const processAudio = useCallback(
    async (audioBlob: Blob) => {
      try {
        setState((prev) => ({ ...prev, isProcessing: true, error: null }));

        // Validate audio blob
        if (!audioBlob || audioBlob.size === 0) {
          throw new Error('No audio data recorded');
        }

        // Create form data
        const formData = new FormData();
        formData.append('file', audioBlob, 'recording.webm');

        // Check if API base URL is configured
        const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
        if (!apiBaseUrl) {
          throw new Error('API base URL not configured');
        }

        // Send to transcription API
        const response = await fetch(`${apiBaseUrl}/api/upload`, {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(
            `Server error (${response.status}): ${errorText || response.statusText}`,
          );
        }

        const data = await response.json();

        if (data.error) {
          throw new Error(data.error);
        }

        if (data.transcription && onTranscription) {
          onTranscription(data.transcription);
        } else if (!data.transcription) {
          console.warn('No transcription received from server');
          setState((prev) => ({
            ...prev,
            error: 'No speech detected in the recording',
            isProcessing: false,
          }));
          return;
        }

        setState((prev) => ({ ...prev, isProcessing: false }));
      } catch (error) {
        console.error('Failed to process audio:', error);

        let errorMessage = 'Failed to process audio';
        if (error instanceof Error) {
          if (error.message.includes('fetch')) {
            errorMessage =
              'Network error. Please check your connection and try again.';
          } else if (error.message.includes('Server error')) {
            errorMessage = 'Server error. Please try again later.';
          } else {
            errorMessage = error.message;
          }
        }

        setState((prev) => ({
          ...prev,
          error: errorMessage,
          isProcessing: false,
        }));
      }
    },
    [onTranscription],
  );

  const startRecording = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, error: null }));

      // Check if MediaRecorder is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Media recording is not supported in this browser');
      }

      if (!window.MediaRecorder) {
        throw new Error('MediaRecorder is not supported in this browser');
      }

      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100,
        },
      });

      streamRef.current = stream;

      // Determine supported MIME type
      let mimeType = 'audio/webm;codecs=opus';
      if (!MediaRecorder.isTypeSupported(mimeType)) {
        mimeType = 'audio/webm';
        if (!MediaRecorder.isTypeSupported(mimeType)) {
          mimeType = 'audio/mp4';
          if (!MediaRecorder.isTypeSupported(mimeType)) {
            mimeType = ''; // Let browser choose
          }
        }
      }

      // Create MediaRecorder with fallback options
      const mediaRecorder = mimeType
        ? new MediaRecorder(stream, { mimeType })
        : new MediaRecorder(stream);

      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      // Handle data available
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      // Handle recording stop
      mediaRecorder.onstop = async () => {
        try {
          const audioBlob = new Blob(audioChunksRef.current, {
            type: mediaRecorder.mimeType || 'audio/webm',
          });
          await processAudio(audioBlob);
        } catch (error) {
          console.error('Failed to process audio after recording:', error);
          setState((prev) => ({
            ...prev,
            error: 'Failed to process recorded audio',
            isProcessing: false,
          }));
        }

        // Clean up stream
        if (streamRef.current) {
          streamRef.current.getTracks().forEach((track) => track.stop());
          streamRef.current = null;
        }
      };

      // Handle recording errors
      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        setState((prev) => ({
          ...prev,
          error: 'Recording failed due to an error',
          isRecording: false,
        }));
      };

      // Start recording
      mediaRecorder.start(100); // Collect data every 100ms
      setState((prev) => ({ ...prev, isRecording: true }));
    } catch (error) {
      console.error('Failed to start recording:', error);

      let errorMessage = 'Failed to start recording';
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          errorMessage =
            'Microphone access denied. Please allow microphone access and try again.';
        } else if (error.name === 'NotFoundError') {
          errorMessage =
            'No microphone found. Please connect a microphone and try again.';
        } else if (error.name === 'NotSupportedError') {
          errorMessage = 'Audio recording is not supported in this browser.';
        } else if (error.name === 'NotReadableError') {
          errorMessage = 'Microphone is already in use by another application.';
        } else {
          errorMessage = error.message;
        }
      }

      setState((prev) => ({
        ...prev,
        error: errorMessage,
        isRecording: false,
      }));
    }
  }, [processAudio]);

  const stopRecording = useCallback(() => {
    try {
      if (mediaRecorderRef.current && state.isRecording) {
        mediaRecorderRef.current.stop();
        setState((prev) => ({
          ...prev,
          isRecording: false,
          isProcessing: true,
        }));
      }
    } catch (error) {
      console.error('Failed to stop recording:', error);
      setState((prev) => ({
        ...prev,
        error: 'Failed to stop recording',
        isRecording: false,
        isProcessing: false,
      }));

      // Clean up stream if there's an error
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
        streamRef.current = null;
      }
    }
  }, [state.isRecording]);

  const toggleRecording = useCallback(() => {
    if (state.isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  }, [state.isRecording, startRecording, stopRecording]);

  const clearError = useCallback(() => {
    setState((prev) => ({ ...prev, error: null }));
  }, []);

  return {
    ...state,
    startRecording,
    stopRecording,
    toggleRecording,
    clearError,
    isSupported: featureCheck.supported,
  };
}
