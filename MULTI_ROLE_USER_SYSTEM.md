# Multi-Role User System Implementation

## Overview

The Halal Malaysia AI Chat System has been upgraded from separate `admin_users` and `agent_users` tables to a unified `users` table with multi-role support. This allows users to have multiple roles simultaneously, providing greater flexibility in permission management.

## Key Changes

### 1. Database Schema Changes

#### Before (Separate Tables)
- `admin_users` table with roles: ADMIN, EDITOR
- `agent_users` table with roles: 'agent', 'supervisor'

#### After (Unified Table)
- `users` table with roles array: [ADMIN, EDITOR, AGENT, SUPERVISOR]
- Users can have multiple roles: e.g., `['ADMIN', 'AGENT']`

### 2. User Roles

| Role | Description | Capabilities |
|------|-------------|--------------|
| **ADMIN** | Full system access | User management, configuration, all admin features |
| **EDITOR** | Content management | Limited admin access, content editing |
| **AGENT** | Chat support | Handle chat sessions, agent dashboard |
| **SUPERVISOR** | Agent management | Agent oversight, advanced chat features |

### 3. Multi-Role Examples

#### Default Users Created by Seed
- `admin` - `['ADMIN']` - Full administrative access
- `testadmin` - `['EDITOR']` - Limited admin access
- `agent1` - `['AGENT']` - Chat support agent
- `agent2` - `['AGENT']` - Chat support agent
- `supervisor1` - `['SUPERVISOR']` - Agent supervisor
- `superuser` - `['ADMIN', 'AGENT']` - **Multi-role example**

#### Multi-Role Benefits
- **Flexibility**: Users can have combinations of roles
- **Scalability**: Easy to add new roles without schema changes
- **Granular Permissions**: Fine-grained access control
- **Future-Proof**: Supports complex organizational structures

### 4. Authentication Changes

#### Admin Login (`/api/admin/login`)
- Accepts users with `ADMIN` or `EDITOR` roles
- Primary role determination: `ADMIN` takes precedence over `EDITOR`
- Multi-role users can access admin dashboard if they have admin roles

#### Agent Login (`/api/agent/login`)
- Accepts users with `AGENT` or `SUPERVISOR` roles
- Primary role determination: `SUPERVISOR` takes precedence over `AGENT`
- Multi-role users can access agent dashboard if they have agent roles

#### Multi-Role Access
- Users with both admin and agent roles can access both dashboards
- Role-based UI components show/hide based on user's roles
- JWT tokens include primary role for backward compatibility

### 5. Database Migration

The migration process:
1. Creates new unified `users` table with `roles` array
2. Migrates data from `admin_users` table
3. Migrates data from `agent_users` table
4. Updates foreign key references in related tables
5. Preserves all existing data and relationships

### 6. API Changes

#### New Unified User Management
```typescript
// Create user with multiple roles
POST /api/admin/users
{
  "username": "multiuser",
  "password": "password123",
  "email": "<EMAIL>",
  "firstName": "Multi",
  "lastName": "User",
  "roles": ["ADMIN", "AGENT"]
}

// Update user roles
PUT /api/admin/users/123
{
  "roles": ["EDITOR", "SUPERVISOR"]
}
```

#### Backward Compatibility
- Existing admin and agent APIs continue to work
- Legacy interfaces maintained for smooth transition
- Primary role extraction for single-role contexts

### 7. Frontend Changes

#### Role-Based UI Components
```typescript
// Check if user has specific role
const hasAdminRole = user.roles.includes('ADMIN');
const hasAgentRole = user.roles.includes('AGENT');

// Multi-role navigation
if (hasAdminRole) {
  // Show admin menu items
}
if (hasAgentRole) {
  // Show agent menu items
}
```

#### Multi-Role User Experience
- Users see combined navigation for all their roles
- Context switching between admin and agent modes
- Role indicators in UI to show current context

### 8. Benefits of Multi-Role System

#### Operational Benefits
- **Reduced User Management**: One user account for multiple functions
- **Simplified Training**: Users learn one system with multiple capabilities
- **Better Workflow**: Seamless switching between admin and agent tasks
- **Cost Effective**: Fewer user accounts to manage

#### Technical Benefits
- **Simplified Database**: Single user table instead of multiple
- **Consistent Authentication**: One auth system for all roles
- **Easier Maintenance**: Single codebase for user management
- **Scalable Architecture**: Easy to add new roles and permissions

#### Security Benefits
- **Centralized Access Control**: All permissions in one place
- **Audit Trail**: Single user activity tracking
- **Role Inheritance**: Complex permission hierarchies possible
- **Principle of Least Privilege**: Granular role assignment

### 9. Migration Commands


### 10. Testing Multi-Role System

#### Test Scenarios
1. **Single Role Users**: Verify existing functionality works
2. **Multi-Role Users**: Test access to multiple dashboards
3. **Role-Based Permissions**: Verify UI shows/hides correctly
4. **Authentication**: Test login with different role combinations
5. **API Access**: Verify role-based API endpoint access

#### Test Users
- Login as `superuser` / `password123` to test multi-role functionality
- Access both `/admin` and `/agent` dashboards with same user
- Verify role-based UI components display correctly

### 11. Future Enhancements

#### Planned Features
- **Role Hierarchies**: Parent-child role relationships
- **Dynamic Permissions**: Runtime permission assignment
- **Role Templates**: Predefined role combinations
- **Audit Logging**: Detailed role-based activity tracking
- **SSO Integration**: Single sign-on with role mapping

#### Extensibility
- Easy to add new roles (e.g., MODERATOR, ANALYST)
- Support for organization-specific roles
- Integration with external identity providers
- Custom permission frameworks

## Conclusion

The multi-role user system provides a flexible, scalable foundation for user management in the Halal Malaysia AI Chat System. It maintains backward compatibility while enabling powerful new capabilities for complex organizational structures and workflows.
