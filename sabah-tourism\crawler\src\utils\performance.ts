import { performance } from 'perf_hooks';
import { crawlerLogger } from './logger.js';

export interface PerformanceMetrics {
  operation: string;
  duration: number;
  startTime: number;
  endTime: number;
  memoryUsage?: NodeJS.MemoryUsage;
  metadata?: Record<string, any>;
}

export class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private activeOperations = new Map<
    string,
    { startTime: number; metadata?: any }
  >();

  public startOperation(
    operationId: string,
    metadata?: Record<string, any>,
  ): void {
    const startTime = performance.now();
    this.activeOperations.set(operationId, { startTime, metadata });

    crawlerLogger.debug('Started operation', { operationId, metadata });
  }

  public endOperation(
    operationId: string,
    additionalMetadata?: Record<string, any>,
  ): PerformanceMetrics | null {
    const operation = this.activeOperations.get(operationId);
    if (!operation) {
      crawlerLogger.warn('Attempted to end non-existent operation', {
        operationId,
      });
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - operation.startTime;
    const memoryUsage = process.memoryUsage();

    const metrics: PerformanceMetrics = {
      operation: operationId,
      duration,
      startTime: operation.startTime,
      endTime,
      memoryUsage,
      metadata: { ...operation.metadata, ...additionalMetadata },
    };

    this.metrics.push(metrics);
    this.activeOperations.delete(operationId);

    crawlerLogger.debug('Completed operation', {
      operationId,
      duration: Math.round(duration),
      memoryMB: Math.round(memoryUsage.heapUsed / 1024 / 1024),
    });

    return metrics;
  }

  public async measureAsync<T>(
    operationId: string,
    operation: () => Promise<T>,
    metadata?: Record<string, any>,
  ): Promise<{ result: T; metrics: PerformanceMetrics }> {
    this.startOperation(operationId, metadata);

    try {
      const result = await operation();
      const metrics = this.endOperation(operationId, { success: true })!;
      return { result, metrics };
    } catch (error) {
      const metrics = this.endOperation(operationId, {
        success: false,
        error: (error as Error).message,
      })!;
      throw error;
    }
  }

  public measure<T>(
    operationId: string,
    operation: () => T,
    metadata?: Record<string, any>,
  ): { result: T; metrics: PerformanceMetrics } {
    this.startOperation(operationId, metadata);

    try {
      const result = operation();
      const metrics = this.endOperation(operationId, { success: true })!;
      return { result, metrics };
    } catch (error) {
      const metrics = this.endOperation(operationId, {
        success: false,
        error: (error as Error).message,
      })!;
      throw error;
    }
  }

  public getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  public getMetricsByOperation(operation: string): PerformanceMetrics[] {
    return this.metrics.filter((m) => m.operation === operation);
  }

  public getAverageMetrics(operation: string): {
    operation: string;
    count: number;
    avgDuration: number;
    minDuration: number;
    maxDuration: number;
    totalDuration: number;
  } | null {
    const operationMetrics = this.getMetricsByOperation(operation);
    if (operationMetrics.length === 0) return null;

    const durations = operationMetrics.map((m) => m.duration);

    return {
      operation,
      count: operationMetrics.length,
      avgDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      totalDuration: durations.reduce((a, b) => a + b, 0),
    };
  }

  public getSummary(): {
    totalOperations: number;
    totalDuration: number;
    avgDuration: number;
    operationBreakdown: Record<string, number>;
    memoryPeak: number;
  } {
    const totalOperations = this.metrics.length;
    const totalDuration = this.metrics.reduce((sum, m) => sum + m.duration, 0);
    const avgDuration =
      totalOperations > 0 ? totalDuration / totalOperations : 0;

    const operationBreakdown: Record<string, number> = {};
    this.metrics.forEach((m) => {
      operationBreakdown[m.operation] =
        (operationBreakdown[m.operation] || 0) + 1;
    });

    const memoryPeak = Math.max(
      ...this.metrics
        .filter((m) => m.memoryUsage)
        .map((m) => m.memoryUsage!.heapUsed),
    );

    return {
      totalOperations,
      totalDuration,
      avgDuration,
      operationBreakdown,
      memoryPeak,
    };
  }

  public clearMetrics(): void {
    this.metrics = [];
    crawlerLogger.debug('Performance metrics cleared');
  }

  public logSummary(): void {
    const summary = this.getSummary();

    crawlerLogger.info('Performance Summary', {
      totalOperations: summary.totalOperations,
      totalDurationMs: Math.round(summary.totalDuration),
      avgDurationMs: Math.round(summary.avgDuration),
      memoryPeakMB: Math.round(summary.memoryPeak / 1024 / 1024),
      operationBreakdown: summary.operationBreakdown,
    });
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions for common operations
export function measureCrawlOperation<T>(
  operation: string,
  fn: () => Promise<T>,
  metadata?: Record<string, any>,
): Promise<T> {
  return performanceMonitor
    .measureAsync(operation, fn, metadata)
    .then((r) => r.result);
}

export function measureSyncOperation<T>(
  operation: string,
  fn: () => T,
  metadata?: Record<string, any>,
): T {
  return performanceMonitor.measure(operation, fn, metadata).result;
}

// Memory monitoring utilities
export function getMemoryUsage(): {
  heapUsedMB: number;
  heapTotalMB: number;
  externalMB: number;
  rssMB: number;
} {
  const usage = process.memoryUsage();
  return {
    heapUsedMB: Math.round(usage.heapUsed / 1024 / 1024),
    heapTotalMB: Math.round(usage.heapTotal / 1024 / 1024),
    externalMB: Math.round(usage.external / 1024 / 1024),
    rssMB: Math.round(usage.rss / 1024 / 1024),
  };
}

export function logMemoryUsage(context?: string): void {
  const usage = getMemoryUsage();
  crawlerLogger.info('Memory usage', { context, ...usage });
}

// Performance optimization utilities
export function createBatchProcessor<T, R>(
  batchSize: number,
  processor: (batch: T[]) => Promise<R[]>,
  delayBetweenBatches = 0,
) {
  return async function processBatches(items: T[]): Promise<R[]> {
    const results: R[] = [];

    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchResults = await processor(batch);
      results.push(...batchResults);

      // Add delay between batches if specified
      if (delayBetweenBatches > 0 && i + batchSize < items.length) {
        await new Promise((resolve) =>
          setTimeout(resolve, delayBetweenBatches),
        );
      }
    }

    return results;
  };
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastExecTime = 0;

  return (...args: Parameters<T>) => {
    const currentTime = Date.now();

    if (currentTime - lastExecTime > delay) {
      func(...args);
      lastExecTime = currentTime;
    } else {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(
        () => {
          func(...args);
          lastExecTime = Date.now();
        },
        delay - (currentTime - lastExecTime),
      );
    }
  };
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null;

  return (...args: Parameters<T>) => {
    if (timeoutId) clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}
