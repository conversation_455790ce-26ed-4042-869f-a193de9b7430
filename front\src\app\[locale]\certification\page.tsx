'use client';

export const runtime = 'edge';

import { PageWrapper } from '@/components/page-wrapper';
import { Link } from '@/i18n/navigation';
import { useLanguage } from '@/lib/language-context';

export default function CertificationPage() {
  const { language } = useLanguage();

  const breadcrumbs = [
    {
      label: 'Certification',
      labelBM: 'Pensijilan',
    },
  ];

  return (
    <PageWrapper
      title="Halal Certification"
      titleBM="Pensijilan Halal"
      description="Information about Halal certification processes for domestic and international organizations."
      descriptionBM="Maklumat mengenai proses pensijilan Halal untuk organisasi domestik dan antarabangsa."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        <div className="card">
          <h3 className="text-2xl font-bold mb-4 text-primary-green">
            {language === 'en'
              ? 'Domestic Certification'
              : 'Pensijilan Domestik'}
          </h3>
          <p className="text-gray-600 mb-6">
            {language === 'en'
              ? 'Halal certification for Malaysian companies and organizations operating within Malaysia.'
              : 'Pensijilan Halal untuk syarikat dan organisasi Malaysia yang beroperasi di dalam Malaysia.'}
          </p>
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary-green rounded-full mt-2" />
              <div>
                <h4 className="font-semibold text-gray-900">
                  {language === 'en' ? 'Food & Beverages' : 'Makanan & Minuman'}
                </h4>
                <p className="text-sm text-gray-600">
                  {language === 'en'
                    ? 'Certification for food processing, manufacturing, and catering services'
                    : 'Pensijilan untuk pemprosesan makanan, pembuatan, dan perkhidmatan katering'}
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary-green rounded-full mt-2" />
              <div>
                <h4 className="font-semibold text-gray-900">
                  {language === 'en' ? 'Consumer Products' : 'Produk Pengguna'}
                </h4>
                <p className="text-sm text-gray-600">
                  {language === 'en'
                    ? 'Cosmetics, pharmaceuticals, and personal care products'
                    : 'Kosmetik, farmaseutikal, dan produk penjagaan diri'}
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary-green rounded-full mt-2" />
              <div>
                <h4 className="font-semibold text-gray-900">
                  {language === 'en'
                    ? 'Logistics & Services'
                    : 'Logistik & Perkhidmatan'}
                </h4>
                <p className="text-sm text-gray-600">
                  {language === 'en'
                    ? 'Transportation, storage, and handling of Halal products'
                    : 'Pengangkutan, penyimpanan, dan pengendalian produk Halal'}
                </p>
              </div>
            </div>
          </div>
          <div className="mt-6">
            <Link
              href="https://myehalal.halal.gov.my/domestik/v1/"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-primary inline-block"
            >
              {language === 'en' ? 'Apply Now' : 'Mohon Sekarang'}
            </Link>
          </div>
        </div>

        <div className="card">
          <h3 className="text-2xl font-bold mb-4 text-primary-green">
            {language === 'en'
              ? 'International Certification'
              : 'Pensijilan Antarabangsa'}
          </h3>
          <p className="text-gray-600 mb-6">
            {language === 'en'
              ? 'Halal certification for international companies seeking to export to Malaysia or obtain Malaysian Halal recognition.'
              : 'Pensijilan Halal untuk syarikat antarabangsa yang ingin mengeksport ke Malaysia atau mendapat pengiktirafan Halal Malaysia.'}
          </p>
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-islamic-green rounded-full mt-2" />
              <div>
                <h4 className="font-semibold text-gray-900">
                  {language === 'en'
                    ? 'Export to Malaysia'
                    : 'Eksport ke Malaysia'}
                </h4>
                <p className="text-sm text-gray-600">
                  {language === 'en'
                    ? 'Certification for products intended for the Malaysian market'
                    : 'Pensijilan untuk produk yang ditujukan untuk pasaran Malaysia'}
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-islamic-green rounded-full mt-2" />
              <div>
                <h4 className="font-semibold text-gray-900">
                  {language === 'en'
                    ? 'Global Recognition'
                    : 'Pengiktirafan Global'}
                </h4>
                <p className="text-sm text-gray-600">
                  {language === 'en'
                    ? 'Malaysian Halal certification recognized worldwide'
                    : 'Pensijilan Halal Malaysia diiktiraf di seluruh dunia'}
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-islamic-green rounded-full mt-2" />
              <div>
                <h4 className="font-semibold text-gray-900">
                  {language === 'en' ? 'FHCB Network' : 'Rangkaian FHCB'}
                </h4>
                <p className="text-sm text-gray-600">
                  {language === 'en'
                    ? 'Certification through recognized Foreign Halal Certification Bodies'
                    : 'Pensijilan melalui Badan Pensijilan Halal Asing yang diiktiraf'}
                </p>
              </div>
            </div>
          </div>
          <div className="mt-6">
            <Link
              href="https://myehalal.halal.gov.my/international/v1/pemohon/"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-secondary inline-block"
            >
              {language === 'en' ? 'Learn More' : 'Ketahui Lebih Lanjut'}
            </Link>
          </div>
        </div>
      </div>

      <div className="card">
        <h3 className="text-2xl font-bold mb-6 text-gray-900">
          {language === 'en' ? 'Certification Benefits' : 'Faedah Pensijilan'}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-primary-green rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-2xl font-bold">✓</span>
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">
              {language === 'en' ? 'Consumer Trust' : 'Kepercayaan Pengguna'}
            </h4>
            <p className="text-sm text-gray-600">
              {language === 'en'
                ? 'Build confidence among Muslim consumers'
                : 'Membina keyakinan di kalangan pengguna Muslim'}
            </p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-islamic-green rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-2xl font-bold">🌍</span>
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">
              {language === 'en' ? 'Market Access' : 'Akses Pasaran'}
            </h4>
            <p className="text-sm text-gray-600">
              {language === 'en'
                ? 'Access to global Halal markets'
                : 'Akses kepada pasaran Halal global'}
            </p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-primary-green rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-2xl font-bold">⭐</span>
            </div>
            <h4 className="font-semibold text-gray-900 mb-2">
              {language === 'en' ? 'Quality Assurance' : 'Jaminan Kualiti'}
            </h4>
            <p className="text-sm text-gray-600">
              {language === 'en'
                ? 'Ensure highest quality and compliance standards'
                : 'Memastikan kualiti tertinggi dan piawaian pematuhan'}
            </p>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}
