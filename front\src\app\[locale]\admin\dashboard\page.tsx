'use client';

export const runtime = 'edge';

import {
  Alert<PERSON>riangle,
  BarChart3,
  Bell,
  CheckCircle,
  Database, // For S3 Configs
  Folder, // For Collections
  LogOut,
  MessageSquare,
  Server, // For Services
  Settings,
  Shield,
  TestTube,
  Users,
  UsersRound, // For User Management
  XCircle,
} from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { useAdminAuthGuard } from '@/hooks/useAuthGuard';
import { useRouter } from '@/i18n/navigation';
import { useAdminAuth, useAuthStore } from '@/stores/auth';
import { UserRole } from '@/types/roles'; // Import UserRole

interface AdminDashboardUser {
  // Renamed to avoid conflict if AdminUser type is imported
  id: number;
  username: string;
  role?: UserRole; // Role is now expected
}

interface WhatsAppConfig {
  configured: boolean;
  config: {
    phoneNumberId: string;
    businessAccountId?: string;
    isActive: boolean;
  } | null;
}

interface FacebookConfig {
  configured: boolean;
  config: {
    pageId: string;
    isActive: boolean;
  } | null;
}

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

export default function AdminDashboard() {
  const [whatsappConfig, setWhatsappConfig] = useState<WhatsAppConfig | null>(
    null,
  );
  const [facebookConfig, setFacebookConfig] = useState<FacebookConfig | null>(
    null,
  );

  const { user, logout } = useAdminAuth();
  const { isAuthenticated, isLoading } = useAdminAuthGuard();
  const router = useRouter();

  const loadWhatsAppConfig = useCallback(async () => {
    const { adminToken } = useAuthStore.getState();
    if (!adminToken) {
      return;
    }

    try {
      const response = await fetch('/api/admin/whatsapp/config', {
        headers: {
          Authorization: `Bearer ${adminToken}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setWhatsappConfig(data);
      }
    } catch (error) {
      console.error('Failed to load WhatsApp config:', error);
    }
  }, []);

  const loadFacebookConfig = useCallback(async () => {
    const { adminToken } = useAuthStore.getState();
    if (!adminToken) {
      return;
    }

    try {
      const response = await fetch('/api/admin/facebook/config', {
        headers: {
          Authorization: `Bearer ${adminToken}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setFacebookConfig(data);
      }
    } catch (error) {
      console.error('Failed to load Facebook config:', error);
    }
  }, []);

  const handleLogout = async () => {
    await logout();
    router.push('/admin');
  };

  useEffect(() => {
    loadWhatsAppConfig();
    loadFacebookConfig();
  }, [loadWhatsAppConfig, loadFacebookConfig]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">
                Admin Dashboard
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Welcome, {user?.username}
              </span>
              <button
                onClick={handleLogout}
                className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
              >
                <LogOut className="h-4 w-4 mr-1" />
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {/* WhatsApp Status */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <MessageSquare className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      WhatsApp Business API
                    </dt>
                    <dd className="flex items-center">
                      {whatsappConfig?.configured ? (
                        <>
                          <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                          <span className="text-sm text-green-600">
                            Configured
                          </span>
                        </>
                      ) : (
                        <>
                          <XCircle className="h-4 w-4 text-red-500 mr-1" />
                          <span className="text-sm text-red-600">
                            Not Configured
                          </span>
                        </>
                      )}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* Facebook Status */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <MessageSquare className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Facebook Messenger
                    </dt>
                    <dd className="flex items-center">
                      {facebookConfig?.configured ? (
                        <>
                          <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                          <span className="text-sm text-green-600">
                            Configured
                          </span>
                        </>
                      ) : (
                        <>
                          <XCircle className="h-4 w-4 text-red-500 mr-1" />
                          <span className="text-sm text-red-600">
                            Not Configured
                          </span>
                        </>
                      )}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* Active Sessions */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Active Chat Sessions
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">0</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* System Status */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      System Status
                    </dt>
                    <dd className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-sm text-green-600">
                        Operational
                      </span>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* User Management Card (ADMIN only) */}
          {user?.role === UserRole.ADMIN && (
            <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <UsersRound className="h-8 w-8 text-teal-600" />
                  </div>
                  <div className="ml-5">
                    <h3 className="text-lg font-medium text-gray-900">
                      User Management
                    </h3>
                    <p className="text-sm text-gray-500">
                      Manage admin users and roles
                    </p>
                  </div>
                </div>
                <div className="mt-4">
                  <button
                    onClick={() => router.push('/admin/users')}
                    className="w-full bg-teal-600 text-white px-4 py-2 rounded-md hover:bg-teal-700 transition-colors"
                  >
                    Manage Users
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* S3 Configurations Card (ADMIN and EDITOR) */}
          {(user?.role === UserRole.ADMIN ||
            user?.role === UserRole.EDITOR) && (
            <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Database className="h-8 w-8 text-sky-600" />
                  </div>
                  <div className="ml-5">
                    <h3 className="text-lg font-medium text-gray-900">
                      S3 Configurations
                    </h3>
                    <p className="text-sm text-gray-500">
                      Manage S3 bucket credentials and settings
                    </p>
                  </div>
                </div>
                <div className="mt-4">
                  <button
                    onClick={() => router.push('/admin/s3-configurations')}
                    className="w-full bg-sky-600 text-white px-4 py-2 rounded-md hover:bg-sky-700 transition-colors"
                  >
                    Configure S3
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Collections Card (ADMIN and EDITOR) */}
          {(user?.role === UserRole.ADMIN ||
            user?.role === UserRole.EDITOR) && (
            <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Folder className="h-8 w-8 text-amber-600" />
                  </div>
                  <div className="ml-5">
                    <h3 className="text-lg font-medium text-gray-900">
                      Collections
                    </h3>
                    <p className="text-sm text-gray-500">
                      Manage document collections and their content
                    </p>
                  </div>
                </div>
                <div className="mt-4">
                  <button
                    onClick={() => router.push('/admin/collections')}
                    className="w-full bg-amber-600 text-white px-4 py-2 rounded-md hover:bg-amber-700 transition-colors"
                  >
                    Manage Collections
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Services Card (ADMIN and EDITOR) */}
          {(user?.role === UserRole.ADMIN ||
            user?.role === UserRole.EDITOR) && (
            <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Server className="h-8 w-8 text-purple-600" />
                  </div>
                  <div className="ml-5">
                    <h3 className="text-lg font-medium text-gray-900">
                      Services
                    </h3>
                    <p className="text-sm text-gray-500">
                      Manage R2R RAG, SMTP providers, and external API services
                    </p>
                  </div>
                </div>
                <div className="mt-4">
                  <button
                    onClick={() => router.push('/admin/services')}
                    className="w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors"
                  >
                    Manage Services
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* WhatsApp Configuration */}
          <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Settings className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-5">
                  <h3 className="text-lg font-medium text-gray-900">
                    WhatsApp Configuration
                  </h3>
                  <p className="text-sm text-gray-500">
                    Set up WhatsApp Business API credentials and webhook
                  </p>
                </div>
              </div>
              <div className="mt-4">
                <button
                  onClick={() => router.push('/admin/whatsapp/config')}
                  className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Configure WhatsApp
                </button>
              </div>
            </div>
          </div>

          {/* Facebook Configuration */}
          <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Settings className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-5">
                  <h3 className="text-lg font-medium text-gray-900">
                    Facebook Configuration
                  </h3>
                  <p className="text-sm text-gray-500">
                    Set up Facebook Messenger API credentials and webhook
                  </p>
                </div>
              </div>
              <div className="mt-4">
                <button
                  onClick={() => router.push('/admin/facebook/config')}
                  className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Configure Facebook
                </button>
              </div>
            </div>
          </div>

          {/* Test WhatsApp */}
          <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TestTube className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-5">
                  <h3 className="text-lg font-medium text-gray-900">
                    Test WhatsApp
                  </h3>
                  <p className="text-sm text-gray-500">
                    Send test messages and verify integration
                  </p>
                </div>
              </div>
              <div className="mt-4">
                <button
                  onClick={() => router.push('/admin/whatsapp/test')}
                  className="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
                  disabled={!whatsappConfig?.configured}
                >
                  Test Integration
                </button>
              </div>
            </div>
          </div>

          {/* Test Facebook */}
          <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TestTube className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-5">
                  <h3 className="text-lg font-medium text-gray-900">
                    Test Facebook
                  </h3>
                  <p className="text-sm text-gray-500">
                    Send test messages and verify integration
                  </p>
                </div>
              </div>
              <div className="mt-4">
                <button
                  onClick={() => router.push('/admin/facebook/test')}
                  className="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
                  disabled={!facebookConfig?.configured}
                >
                  Test Integration
                </button>
              </div>
            </div>
          </div>

          {/* Agent Management */}
          <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-8 w-8 text-indigo-600" />
                </div>
                <div className="ml-5">
                  <h3 className="text-lg font-medium text-gray-900">
                    Agent Management
                  </h3>
                  <p className="text-sm text-gray-500">
                    Manage agent users and monitor handover sessions
                  </p>
                </div>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  onClick={() => router.push('/admin/agents')}
                  className="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors"
                >
                  Manage Agents
                </button>
              </div>
            </div>
          </div>

          {/* Agent Analytics */}
          <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <BarChart3 className="h-8 w-8 text-emerald-600" />
                </div>
                <div className="ml-5">
                  <h3 className="text-lg font-medium text-gray-900">
                    Agent Analytics
                  </h3>
                  <p className="text-sm text-gray-500">
                    View performance metrics and detailed analytics
                  </p>
                </div>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  onClick={() => router.push('/admin/analytics')}
                  className="w-full bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700 transition-colors"
                >
                  View Analytics
                </button>
              </div>
            </div>
          </div>

          {/* Notification Management */}
          <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Bell className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-5">
                  <h3 className="text-lg font-medium text-gray-900">
                    Notifications
                  </h3>
                  <p className="text-sm text-gray-500">
                    Manage system notifications and alerts
                  </p>
                </div>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  onClick={() => router.push('/admin/notifications')}
                  className="w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors"
                >
                  Manage Notifications
                </button>
              </div>
            </div>
          </div>

          {/* Chat Sessions */}
          <div className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <MessageSquare className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-5">
                  <h3 className="text-lg font-medium text-gray-900">
                    Chat Sessions
                  </h3>
                  <p className="text-sm text-gray-500">
                    View and manage active conversations
                  </p>
                </div>
              </div>
              <div className="mt-4">
                <button
                  onClick={() => router.push('/admin/sessions')}
                  className="w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors"
                >
                  View Sessions
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
