// User and Authentication Types
export enum UserRole {
  SUPERADMIN = 'SUPERADMIN',
  ADMIN = 'ADMIN',
  EDITOR = 'EDITOR',
  AGENT = 'AGENT',
  SUPERVISOR = 'SUPERVISOR',
}

export interface User {
  id: number;
  siteId: number;
  username: string;
  email?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  roles: UserRole[];
  isActive: boolean;
  isOnline: boolean;
  lastSeenAt?: Date | null;
  lastLoginAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  token?: string;
  user?: User;
  error?: string;
}

// User Management Types
export interface UserCreateRequest {
  username: string;
  email?: string;
  password: string;
  firstName?: string;
  lastName?: string;
  roles: UserRole[];
  isActive?: boolean;
}

export interface UserUpdateRequest {
  username?: string;
  email?: string;
  password?: string;
  firstName?: string;
  lastName?: string;
  roles?: UserRole[];
  isActive?: boolean;
}

// Site Management Types
export interface Site {
  id: number;
  name: string;
  code: string;
  domains: string[];
  status: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface SiteCreateRequest {
  name: string;
  code: string;
  domains: string[];
  status?: boolean;
}

export interface SiteUpdateRequest {
  name?: string;
  code?: string;
  domains?: string[];
  status?: boolean;
}

// Collection Types
export interface Collection {
  id: number;
  name: string;
  description?: string;
  status: 'ACTIVE' | 'INACTIVE';
  createdAt: Date;
  updatedAt: Date;
}

// WhatsApp Configuration Types
export interface WhatsAppConfig {
  id: number;
  siteId: number;
  accessToken: string;
  phoneNumberId: string;
  webhookVerifyToken: string;
  businessAccountId: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Facebook Messenger Configuration Types
export interface FacebookConfig {
  id: number;
  siteId: number;
  pageAccessToken: string;
  pageId: string;
  appSecret: string;
  verifyToken: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// S3 Configuration Types
export interface S3Configuration {
  id: number;
  serviceName: string;
  endpointUrl: string | null;
  region: string | null;
  bucketName: string;
  accessKeyId: string;
  secretAccessKey: string;
  createdAt: Date;
  updatedAt: Date;
}

// Standardized API Response Types
export interface ApiResponse<T = any> {
  success: boolean; // Required - indicates if the request was successful
  data?: T; // The actual response data
  error?: string; // Error message if success is false
  message?: string; // Optional additional message
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Search and Filter Types
export interface SearchParams {
  query?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
