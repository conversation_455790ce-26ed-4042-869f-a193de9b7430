/**
 * Integration test for user API endpoints
 * Tests the firstName and lastName functionality end-to-end
 */

import { UserRole } from '@/types';

// Mock fetch for testing
global.fetch = jest.fn();

const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

describe('User API Integration', () => {
  const API_BASE_URL = 'http://localhost:8787';
  const mockToken = 'mock-jwt-token';

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock successful responses by default
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ success: true }),
    } as Response);
  });

  describe('Create User', () => {
    it('should send firstName and lastName in create request', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe',
        roles: [UserRole.EDITOR],
        isActive: true,
      };

      // Simulate the API call that the frontend makes
      await fetch(`${API_BASE_URL}/api/admin/users`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        body: JSON.stringify(userData),
      });

      expect(mockFetch).toHaveBeenCalledWith(
        `${API_BASE_URL}/api/admin/users`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${mockToken}`,
          },
          body: JSON.stringify(userData),
        },
      );

      // Verify that firstName and lastName are included in the request body
      const requestBody = JSON.parse(
        mockFetch.mock.calls[0][1]?.body as string,
      );
      expect(requestBody.firstName).toBe('John');
      expect(requestBody.lastName).toBe('Doe');
    });

    it('should handle empty firstName and lastName', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        firstName: '',
        lastName: '',
        roles: [UserRole.EDITOR],
        isActive: true,
      };

      await fetch(`${API_BASE_URL}/api/admin/users`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        body: JSON.stringify(userData),
      });

      const requestBody = JSON.parse(
        mockFetch.mock.calls[0][1]?.body as string,
      );
      expect(requestBody.firstName).toBe('');
      expect(requestBody.lastName).toBe('');
    });
  });

  describe('Update User', () => {
    it('should send firstName and lastName in update request', async () => {
      const userId = 123;
      const updateData = {
        username: 'updateduser',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        roles: [UserRole.ADMIN],
        isActive: true,
      };

      await fetch(`${API_BASE_URL}/api/admin/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        body: JSON.stringify(updateData),
      });

      expect(mockFetch).toHaveBeenCalledWith(
        `${API_BASE_URL}/api/admin/users/${userId}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${mockToken}`,
          },
          body: JSON.stringify(updateData),
        },
      );

      // Verify that firstName and lastName are included in the request body
      const requestBody = JSON.parse(
        mockFetch.mock.calls[0][1]?.body as string,
      );
      expect(requestBody.firstName).toBe('Jane');
      expect(requestBody.lastName).toBe('Smith');
    });

    it('should handle partial updates with only firstName', async () => {
      const userId = 123;
      const updateData = {
        firstName: 'UpdatedFirstName',
      };

      await fetch(`${API_BASE_URL}/api/admin/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        body: JSON.stringify(updateData),
      });

      const requestBody = JSON.parse(
        mockFetch.mock.calls[0][1]?.body as string,
      );
      expect(requestBody.firstName).toBe('UpdatedFirstName');
      expect(requestBody.lastName).toBeUndefined();
    });

    it('should handle partial updates with only lastName', async () => {
      const userId = 123;
      const updateData = {
        lastName: 'UpdatedLastName',
      };

      await fetch(`${API_BASE_URL}/api/admin/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        body: JSON.stringify(updateData),
      });

      const requestBody = JSON.parse(
        mockFetch.mock.calls[0][1]?.body as string,
      );
      expect(requestBody.firstName).toBeUndefined();
      expect(requestBody.lastName).toBe('UpdatedLastName');
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ error: 'Invalid data' }),
      } as Response);

      const userData = {
        username: 'testuser',
        firstName: 'John',
        lastName: 'Doe',
        roles: [UserRole.EDITOR],
      };

      const response = await fetch(`${API_BASE_URL}/api/admin/users`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mockToken}`,
        },
        body: JSON.stringify(userData),
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(400);
    });
  });
});
