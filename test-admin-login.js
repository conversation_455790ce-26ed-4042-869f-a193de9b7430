#!/usr/bin/env node

// Test admin login through the proxy route
async function testAdminLoginProxy() {
  console.log('🔐 Testing admin login through proxy...');
  
  try {
    const response = await fetch('http://localhost:16005/api/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      }),
    });

    const data = await response.json();
    console.log('Proxy login response:', { status: response.status, data });
    
    if (response.ok && data.token) {
      console.log('✅ Proxy login successful');
      return data.token;
    } else {
      console.log('❌ Proxy login failed:', data.error);
      return null;
    }
  } catch (error) {
    console.error('❌ Proxy login error:', error.message);
    return null;
  }
}

// Test direct server login
async function testDirectServerLogin() {
  console.log('🔐 Testing direct server login...');
  
  try {
    const response = await fetch('http://localhost:16001/api/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      }),
    });

    const data = await response.json();
    console.log('Direct login response:', { status: response.status, data });
    
    if (response.ok && data.token) {
      console.log('✅ Direct login successful');
      return data.token;
    } else {
      console.log('❌ Direct login failed:', data.error);
      return null;
    }
  } catch (error) {
    console.error('❌ Direct login error:', error.message);
    return null;
  }
}

async function main() {
  console.log('🚀 Testing Admin Login Routes\n');
  
  // Test direct server login first
  await testDirectServerLogin();
  console.log('');
  
  // Test proxy login
  await testAdminLoginProxy();
}

main().catch(console.error);
