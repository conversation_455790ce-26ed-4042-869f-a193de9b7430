#!/usr/bin/env bun

import { HalalProductCrawler } from './src/crawler';

async function main() {
  console.log('🚀 Starting Halal Product Crawler for Selangor (Site ID: 2)...');

  const crawler = new HalalProductCrawler({
    maxPages: 15,
    headless: true,
    delayBetweenPages: 2000,
  });

  // Set site ID to 2 for Selangor
  await crawler.setSiteId(2);

  await crawler.run();
}

main().catch(console.error);
