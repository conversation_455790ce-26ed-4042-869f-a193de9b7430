'use client';

import type { ReactNode } from 'react';
import { useLanguage } from '@/lib/language-context';
import { cn } from '@/lib/utils';
import {
  Breadcrumb,
  type BreadcrumbItem,
  BreadcrumbStructuredData,
} from './breadcrumb';

interface PageWrapperProps {
  children: ReactNode;
  title?: string;
  titleBM?: string;
  description?: string;
  descriptionBM?: string;
  breadcrumbs?: BreadcrumbItem[];
  className?: string;
  containerClassName?: string;
  showBreadcrumbs?: boolean;
  showTitle?: boolean;
  headerActions?: ReactNode;
}

export function PageWrapper({
  children,
  title,
  titleBM,
  description,
  descriptionBM,
  breadcrumbs = [],
  className,
  containerClassName,
  showBreadcrumbs = true,
  showTitle = true,
  headerActions,
}: PageWrapperProps) {
  const { language } = useLanguage();

  const pageTitle = language === 'bm' && titleBM ? titleBM : title;
  const pageDescription =
    language === 'bm' && descriptionBM ? descriptionBM : description;

  return (
    <div className={cn('min-h-screen bg-gray-50', className)}>
      {/* Breadcrumbs */}
      {showBreadcrumbs && breadcrumbs.length > 0 && (
        <div className="bg-white border-b">
          <div
            className={cn('container mx-auto px-4 py-3', containerClassName)}
          >
            <Breadcrumb items={breadcrumbs} />
            <BreadcrumbStructuredData items={breadcrumbs} />
          </div>
        </div>
      )}

      {/* Page Header */}
      {showTitle && pageTitle && (
        <div className="bg-white border-b">
          <div
            className={cn('container mx-auto px-4 py-8', containerClassName)}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  {pageTitle}
                </h1>
                {pageDescription && (
                  <p className="text-lg text-gray-600 max-w-3xl">
                    {pageDescription}
                  </p>
                )}
              </div>
              {headerActions && (
                <div className="ml-6 flex-shrink-0">{headerActions}</div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Page Content */}
      <div className={cn('container mx-auto px-4 py-8', containerClassName)}>
        {children}
      </div>
    </div>
  );
}

// Simplified page wrapper for basic pages
export function SimplePage({
  children,
  title,
  titleBM,
  className,
}: {
  children: ReactNode;
  title: string;
  titleBM?: string;
  className?: string;
}) {
  const { language } = useLanguage();
  const pageTitle = language === 'bm' && titleBM ? titleBM : title;

  return (
    <div className={cn('container mx-auto px-4 py-8', className)}>
      <h1 className="text-3xl font-bold text-gray-900 mb-8">{pageTitle}</h1>
      {children}
    </div>
  );
}

// Content wrapper with consistent spacing
export function ContentWrapper({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) {
  return (
    <div className={cn('prose prose-lg max-w-none', className)}>{children}</div>
  );
}

// Section wrapper for organizing page content
export function Section({
  children,
  title,
  titleBM,
  description,
  descriptionBM,
  className,
  id,
}: {
  children: ReactNode;
  title?: string;
  titleBM?: string;
  description?: string;
  descriptionBM?: string;
  className?: string;
  id?: string;
}) {
  const { language } = useLanguage();
  const sectionTitle = language === 'bm' && titleBM ? titleBM : title;
  const sectionDescription =
    language === 'bm' && descriptionBM ? descriptionBM : description;

  return (
    <section id={id} className={cn('mb-12', className)}>
      {sectionTitle && (
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {sectionTitle}
          </h2>
          {sectionDescription && (
            <p className="text-gray-600">{sectionDescription}</p>
          )}
        </div>
      )}
      {children}
    </section>
  );
}

// Card wrapper for content sections
export function Card({
  children,
  title,
  titleBM,
  className,
  padding = true,
}: {
  children: ReactNode;
  title?: string;
  titleBM?: string;
  className?: string;
  padding?: boolean;
}) {
  const { language } = useLanguage();
  const cardTitle = language === 'bm' && titleBM ? titleBM : title;

  return (
    <div className={cn('card', className)}>
      {cardTitle && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {cardTitle}
        </h3>
      )}
      <div className={padding ? '' : '-m-6'}>{children}</div>
    </div>
  );
}

// Two-column layout
export function TwoColumnLayout({
  sidebar,
  children,
  sidebarPosition = 'right',
  className,
}: {
  sidebar: ReactNode;
  children: ReactNode;
  sidebarPosition?: 'left' | 'right';
  className?: string;
}) {
  return (
    <div className={cn('grid grid-cols-1 lg:grid-cols-4 gap-8', className)}>
      {sidebarPosition === 'left' && (
        <aside className="lg:col-span-1">{sidebar}</aside>
      )}
      <main className="lg:col-span-3">{children}</main>
      {sidebarPosition === 'right' && (
        <aside className="lg:col-span-1">{sidebar}</aside>
      )}
    </div>
  );
}
