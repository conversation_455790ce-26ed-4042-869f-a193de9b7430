import { Hono } from 'hono';
import agentAuthService from '../services/agentAuth';
import databaseService from '../services/database';
import type {
  AgentDashboardStats,
  AgentMessageRequest,
  HandoverRequest,
  SessionHandoverRequest,
  SessionHandoverResponse,
} from '../types';

const router = new Hono();

// Get agent dashboard statistics
router.get('/stats', agentAuthService.authenticateAgentHono, async (c) => {
  try {
    const agent = c.get('agent');
    if (!agent) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    // Get actual stats from database
    const stats = await databaseService.getSessionStats();

    return c.json(stats);
  } catch (error) {
    console.error('Get dashboard stats error:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Get pending handover requests
router.get('/handovers/pending', agentAuthService.authenticateAgentHono, async (c) => {
  try {
    const requests = await databaseService.getPendingHandoverRequests();
    return c.json(requests);
  } catch (error) {
    console.error('Get pending handovers error:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Create handover request
router.post('/handover', async (c) => {
  try {
    const body = await c.req.json();
    const { sessionId, reason, priority }: SessionHandoverRequest = body;

    if (!sessionId) {
      return c.json({
        success: false,
        error: 'Session ID is required',
      }, 400);
    }

    const handoverRequestId = await databaseService.createHandoverRequest(
      sessionId,
      'user',
      reason,
      priority || 'normal',
    );

    return c.json({
      success: true,
      handoverRequestId,
    });
  } catch (error) {
    console.error('Create handover request error:', error);
    return c.json({
      success: false,
      error: 'Internal server error',
    }, 500);
  }
});

// Assign handover request to agent
router.post('/handovers/:requestId/assign', agentAuthService.authenticateAgentHono, async (c) => {
  try {
    const agent = c.get('agent');
    if (!agent) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    const requestId = Number.parseInt(c.req.param('requestId'));

    if (Number.isNaN(requestId)) {
      return c.json({ error: 'Invalid request ID' }, 400);
    }

    await databaseService.assignHandoverRequest(requestId, agent.agentId);

    // Also assign the session to the agent
    const requests = await databaseService.getPendingHandoverRequests();
    const request = requests.find((r) => r.id === requestId);

    if (request) {
      await databaseService.assignSessionToAgent(
        request.sessionId,
        agent.agentId,
      );
    }

    return c.json({ success: true });
  } catch (error) {
    console.error('Assign handover request error:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Get agent's active sessions
router.get('/active', agentAuthService.authenticateAgentHono, async (c) => {
  try {
    const agent = c.get('agent');
    if (!agent) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    const sessions = await databaseService.getAgentActiveSessions(
      agent.agentId,
    );
    return c.json(sessions);
  } catch (error) {
    console.error('Get active sessions error:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Get all active sessions (for agent takeover)
router.get('/all-active', agentAuthService.authenticateAgentHono, async (c) => {
  try {
    const agent = c.get('agent');
    if (!agent) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    const sessions = await databaseService.getAllActiveSessions();
    return c.json(sessions);
  } catch (error) {
    console.error('Get all active sessions error:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Get specific session details
router.get('/:sessionId', agentAuthService.authenticateAgentHono, async (c) => {
  try {
    const sessionId = c.req.param('sessionId');
    const session = await databaseService.getChatSession(sessionId);

    if (!session) {
      return c.json({ error: 'Session not found' }, 404);
    }

    return c.json(session);
  } catch (error) {
    console.error('Get session details error:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Take over a session (assign to agent)
router.post('/:sessionId/takeover', agentAuthService.authenticateAgentHono, async (c) => {
  try {
    const agent = c.get('agent');
    if (!agent) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    const sessionId = c.req.param('sessionId');

    // Check if session exists and is active
    const session = await databaseService.getChatSession(sessionId);
    if (!session) {
      return c.json({ error: 'Session not found' }, 404);
    }

    if (session.status !== 'active') {
      return c.json({ error: 'Session is not active' }, 400);
    }

    // Check if session is already assigned to an agent
    const existingAssignment = await databaseService.getAgentActiveSessions(
      agent.agentId,
    );
    const isAlreadyAssigned = existingAssignment.some(
      (s) => s.id === sessionId,
    );

    if (isAlreadyAssigned) {
      return c.json({ error: 'Session is already assigned to you' }, 400);
    }

    // Create session assignment (this also marks session as handed over)
    await databaseService.assignSessionToAgent(sessionId, agent.agentId);

    return c.json({ success: true, message: 'Session taken over successfully' });
  } catch (error) {
    console.error('Take over session error:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

// Send message as agent
router.post('/:sessionId/message', agentAuthService.authenticateAgentHono, async (c) => {
  try {
    const agent = c.get('agent');
    if (!agent) {
      return c.json({
        success: false,
        error: 'Authentication required',
      }, 401);
    }

    const sessionId = c.req.param('sessionId');
    const body = await c.req.json();
    const {
      content,
      fileUrl,
      fileName,
      audioUrl,
    }: AgentMessageRequest = body;

    if (!content) {
      return c.json({
        success: false,
        error: 'Message content is required',
      }, 400);
    }

    const messageId = await databaseService.addChatMessage(
      sessionId,
      'agent',
      content,
      agent.agentId,
      undefined, // imageUrl
      audioUrl,
      fileUrl,
      fileName,
    );

    return c.json({
      success: true,
      messageId,
    });
  } catch (error) {
    console.error('Send agent message error:', error);
    return c.json({
      success: false,
      error: 'Internal server error',
    }, 500);
  }
});

// Complete session (return to bot)
router.post('/:sessionId/complete', agentAuthService.authenticateAgentHono, async (c) => {
  try {
    const agent = c.get('agent');
    if (!agent) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    const sessionId = c.req.param('sessionId');

    await databaseService.completeSessionAssignment(sessionId);

    return c.json({ success: true });
  } catch (error) {
    console.error('Complete session error:', error);
    return c.json({ error: 'Internal server error' }, 500);
  }
});

export default router;
