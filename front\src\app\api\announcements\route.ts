import { type NextRequest, NextResponse } from 'next/server';
import { announcements } from '@/data/content';
export const runtime = 'edge';
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const featured = searchParams.get('featured');
    const page = Number.parseInt(searchParams.get('page') || '1');
    const limit = Number.parseInt(searchParams.get('limit') || '10');
    const sortBy = searchParams.get('sortBy') || 'date';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Filter announcements
    const filteredAnnouncements = announcements.filter((announcement) => {
      const matchesCategory = !category || announcement.category === category;
      const matchesFeatured =
        featured === null ||
        (featured === 'true' && announcement.featured) ||
        (featured === 'false' && !announcement.featured);

      return matchesCategory && matchesFeatured;
    });

    // Sort announcements
    filteredAnnouncements.sort((a, b) => {
      let aValue: any = a[sortBy as keyof typeof a];
      let bValue: any = b[sortBy as keyof typeof b];

      // Handle date sorting
      if (sortBy === 'date') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
      }
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
    });

    // Paginate results
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedAnnouncements = filteredAnnouncements.slice(
      startIndex,
      endIndex,
    );

    return NextResponse.json({
      success: true,
      data: paginatedAnnouncements,
      pagination: {
        page,
        limit,
        total: filteredAnnouncements.length,
        totalPages: Math.ceil(filteredAnnouncements.length / limit),
      },
    });
  } catch (error) {
    console.error('Announcements API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch announcements',
      },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      title,
      titleBM,
      content,
      contentBM,
      category,
      featured = false,
      pdfUrl,
    } = body;

    // Validate required fields
    if (!title || !content || !category) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation error',
          message: 'Title, content, and category are required',
        },
        { status: 400 },
      );
    }

    // Create new announcement
    const newAnnouncement = {
      id: `${Date.now()}${Math.random().toString(36).substr(2, 9)}`,
      title,
      titleBM: titleBM || title,
      content,
      contentBM: contentBM || content,
      date: new Date().toISOString().split('T')[0],
      category: category as
        | 'announcement'
        | 'media-statement'
        | 'withdrawal'
        | 'general',
      featured,
      ...(pdfUrl && { pdfUrl }),
    };

    // In a real application, save to database
    // For now, we'll just return the created announcement

    return NextResponse.json(
      {
        success: true,
        data: newAnnouncement,
        message: 'Announcement created successfully',
      },
      { status: 201 },
    );
  } catch (error) {
    console.error('Create announcement API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to create announcement',
      },
      { status: 500 },
    );
  }
}
