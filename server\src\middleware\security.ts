import type { NextFunction, Request, Response } from 'express';
import rateLimit from 'express-rate-limit';
import validator from 'validator';

// Security logging utility
interface SecurityLogData {
  event: string;
  level: 'info' | 'warn' | 'error' | 'critical';
  ip?: string;
  userAgent?: string;
  userId?: string;
  sessionId?: string;
  endpoint?: string;
  method?: string;
  details?: Record<string, any>;
  timestamp: string;
}

const logSecurityEvent = (data: Omit<SecurityLogData, 'timestamp'>) => {
  const logEntry: SecurityLogData = {
    ...data,
    timestamp: new Date().toISOString(),
  };

  // Log to console with appropriate level
  const logMessage = `[SECURITY] ${data.level.toUpperCase()}: ${data.event}`;
  const logDetails = {
    ...logEntry,
    // Remove sensitive data from logs
    userAgent: data.userAgent ? data.userAgent.substring(0, 100) : undefined,
  };

  switch (data.level) {
    case 'critical':
    case 'error':
      console.error(logMessage, logDetails);
      break;
    case 'warn':
      console.warn(logMessage, logDetails);
      break;
    case 'info':
    default:
      console.log(logMessage, logDetails);
      break;
  }

  // In production, you might want to send to external logging service
  // Example: sendToLogService(logEntry);
};

// Enhanced rate limiting configurations
export const chatLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 60, // 60 requests per minute for chat
  message: {
    error: 'Rate limit exceeded',
    message: 'Too many chat requests, please slow down.',
    retryAfter: 60,
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Use IP + user ID if authenticated for better rate limiting
    const userId = req.user?.userId || 'anonymous';
    return `${req.ip}-${userId}`;
  },
  handler: (req, res, next, options) => {
    logSecurityEvent({
      event: 'CHAT_RATE_LIMIT_EXCEEDED',
      level: 'warn',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.userId?.toString(),
      endpoint: req.path,
      method: req.method,
      details: {
        limit: options.max,
        windowMs: options.windowMs,
        rateLimitKey: `${req.ip}-${req.user?.userId || 'anonymous'}`,
      },
    });
  },
});

export const authLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 5, // 5 login attempts per minute
  message: {
    error: 'Authentication rate limit exceeded',
    message: 'Too many login attempts, please try again later.',
    retryAfter: 60,
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful logins
  handler: (req, res, next, options) => {
    logSecurityEvent({
      event: 'AUTH_RATE_LIMIT_EXCEEDED',
      level: 'error', // Authentication rate limiting is more serious
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
      method: req.method,
      details: {
        limit: options.max,
        windowMs: options.windowMs,
        attemptedUsername: req.body?.username, // Log attempted username (not password)
        suspiciousActivity: true,
      },
    });
  },
});

export const uploadLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 uploads per hour
  message: {
    error: 'Upload rate limit exceeded',
    message: 'Upload limit exceeded, please try again later.',
    retryAfter: 3600,
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res, next, options) => {
    logSecurityEvent({
      event: 'UPLOAD_RATE_LIMIT_EXCEEDED',
      level: 'warn',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.userId?.toString(),
      endpoint: req.path,
      method: req.method,
      details: {
        limit: options.max,
        windowMs: options.windowMs,
        fileSize: req.headers['content-length'],
        contentType: req.headers['content-type'],
      },
    });
  },
});

export const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // 1000 requests per 15 minutes (existing limit)
  message: {
    error: 'Rate limit exceeded',
    message: 'Too many requests from this IP, please try again later.',
    retryAfter: 900,
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res, next, options) => {
    logSecurityEvent({
      event: 'GENERAL_RATE_LIMIT_EXCEEDED',
      level: 'warn',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.userId?.toString(),
      endpoint: req.path,
      method: req.method,
      details: {
        limit: options.max,
        windowMs: options.windowMs,
        possibleDDoS: true,
      },
    });
  },
});

// Input validation and sanitization
export const validateChatMessage = (message: string, req?: Request): string => {
  if (!message || typeof message !== 'string') {
    const error = new Error('Message is required and must be a string');
    if (req) {
      logSecurityEvent({
        event: 'INVALID_MESSAGE_FORMAT',
        level: 'warn',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        userId: req.user?.userId?.toString(),
        endpoint: req.path,
        method: req.method,
        details: {
          messageType: typeof message,
          messageLength: message ? message.length : 0,
        },
      });
    }
    throw error;
  }

  // Length validation
  if (message.length > 4000) {
    const error = new Error('Message too long (max 4000 characters)');
    if (req) {
      logSecurityEvent({
        event: 'MESSAGE_TOO_LONG',
        level: 'warn',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        userId: req.user?.userId?.toString(),
        endpoint: req.path,
        method: req.method,
        details: {
          messageLength: message.length,
          maxLength: 4000,
          possibleAttack: message.length > 10000, // Flag extremely long messages
        },
      });
    }
    throw error;
  }

  if (message.length < 1) {
    throw new Error('Message cannot be empty');
  }

  // Basic sanitization - remove potentially dangerous characters
  let sanitized = message.trim();
  const originalLength = sanitized.length;

  // Remove null bytes and control characters except newlines and tabs
  sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

  // Log if suspicious characters were removed
  if (req && sanitized.length !== originalLength) {
    logSecurityEvent({
      event: 'SUSPICIOUS_CHARACTERS_REMOVED',
      level: 'warn',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.userId?.toString(),
      endpoint: req.path,
      method: req.method,
      details: {
        originalLength,
        sanitizedLength: sanitized.length,
        charactersRemoved: originalLength - sanitized.length,
        possibleInjectionAttempt: true,
      },
    });
  }

  // Validate length after sanitization
  if (!validator.isLength(sanitized, { min: 1, max: 4000 })) {
    throw new Error('Invalid message length after sanitization');
  }

  return sanitized;
};

export const validateImageUrl = (url: string, req?: Request): boolean => {
  if (!url || typeof url !== 'string') {
    if (req) {
      logSecurityEvent({
        event: 'INVALID_IMAGE_URL_FORMAT',
        level: 'warn',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        userId: req.user?.userId?.toString(),
        endpoint: req.path,
        method: req.method,
        details: {
          urlType: typeof url,
          urlLength: url ? url.length : 0,
        },
      });
    }
    return false;
  }

  // Basic URL validation
  if (
    !validator.isURL(url, {
      protocols: ['http', 'https'],
      require_protocol: true,
      require_valid_protocol: true,
    })
  ) {
    if (req) {
      logSecurityEvent({
        event: 'INVALID_IMAGE_URL_STRUCTURE',
        level: 'warn',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        userId: req.user?.userId?.toString(),
        endpoint: req.path,
        method: req.method,
        details: {
          url: url.substring(0, 100), // Log first 100 chars only
          urlLength: url.length,
          possibleMaliciousUrl:
            url.includes('<script>') || url.includes('javascript:'),
        },
      });
    }
    return false;
  }

  // Check if URL points to an image (basic check)
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
  const urlLower = url.toLowerCase();
  const hasImageExtension = imageExtensions.some((ext) =>
    urlLower.includes(ext),
  );

  // Allow data URLs for base64 images
  const isDataUrl = url.startsWith('data:image/');

  const isValid = hasImageExtension || isDataUrl;

  if (!isValid && req) {
    logSecurityEvent({
      event: 'NON_IMAGE_URL_REJECTED',
      level: 'info',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.userId?.toString(),
      endpoint: req.path,
      method: req.method,
      details: {
        url: url.substring(0, 100),
        hasImageExtension,
        isDataUrl,
        suspiciousFileType: true,
      },
    });
  }

  return isValid;
};

export const validateSessionId = (
  sessionId: string,
  req?: Request,
): boolean => {
  if (!sessionId || typeof sessionId !== 'string') {
    if (req) {
      logSecurityEvent({
        event: 'INVALID_SESSION_ID_FORMAT',
        level: 'warn',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        userId: req.user?.userId?.toString(),
        endpoint: req.path,
        method: req.method,
        details: {
          sessionIdType: typeof sessionId,
          sessionIdLength: sessionId ? sessionId.length : 0,
        },
      });
    }
    return false;
  }

  // UUID v4 validation
  const isValid = validator.isUUID(sessionId, 4);

  if (!isValid && req) {
    logSecurityEvent({
      event: 'INVALID_SESSION_ID_UUID',
      level: 'warn',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.userId?.toString(),
      endpoint: req.path,
      method: req.method,
      details: {
        sessionId: sessionId.substring(0, 20), // Log partial ID for debugging
        sessionIdLength: sessionId.length,
        possibleSessionHijacking: true,
      },
    });
  }

  return isValid;
};

// Enhanced security headers middleware
export const securityHeaders = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isHttps = req.secure || req.headers['x-forwarded-proto'] === 'https';

  // Content Security Policy - more restrictive in production
  const cspDirectives = [
    "default-src 'self'",
    isProduction
      ? "script-src 'self'"
      : "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self' data:",
    "connect-src 'self' https:",
    "media-src 'self' data:",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    'upgrade-insecure-requests',
  ];

  res.setHeader('Content-Security-Policy', cspDirectives.join('; '));

  // Security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
  res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
  res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
  res.setHeader('Cross-Origin-Resource-Policy', 'same-origin');

  // Permissions Policy (Feature Policy)
  const permissionsPolicy = [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'payment=()',
    'usb=()',
    'magnetometer=()',
    'gyroscope=()',
    'accelerometer=()',
    'ambient-light-sensor=()',
  ];
  res.setHeader('Permissions-Policy', permissionsPolicy.join(', '));

  // HSTS for HTTPS
  if (isHttps) {
    res.setHeader(
      'Strict-Transport-Security',
      'max-age=31536000; includeSubDomains; preload',
    );
  }

  // Remove server information
  res.removeHeader('X-Powered-By');
  res.setHeader('Server', 'HalalChat');

  // Cache control for sensitive endpoints
  if (req.path.includes('/api/admin') || req.path.includes('/api/auth')) {
    res.setHeader(
      'Cache-Control',
      'no-store, no-cache, must-revalidate, proxy-revalidate',
    );
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  }

  // Log security headers application
  logSecurityEvent({
    event: 'SECURITY_HEADERS_APPLIED',
    level: 'info',
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    endpoint: req.path,
    method: req.method,
    details: {
      isProduction,
      isHttps,
      hasCSP: true,
      headerCount: Object.keys(res.getHeaders()).length,
    },
  });

  next();
};

// Request validation middleware
export const validateChatRequest = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  try {
    const { sessionId, message, model } = req.body;

    // Validate session ID
    if (!validateSessionId(sessionId, req)) {
      logSecurityEvent({
        event: 'CHAT_REQUEST_VALIDATION_FAILED',
        level: 'warn',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        userId: req.user?.userId?.toString(),
        endpoint: req.path,
        method: req.method,
        details: {
          reason: 'Invalid session ID',
          sessionId: sessionId ? sessionId.substring(0, 20) : 'missing',
        },
      });

      res.status(400).json({
        error: 'Invalid request',
        message: 'Invalid session ID format',
      });
      return;
    }

    // Validate and sanitize message
    if (message) {
      req.body.message = validateChatMessage(message, req);
    }

    // Validate model if provided
    if (model && typeof model === 'string') {
      const allowedModels = ['gpt-4o-mini', 'gpt-4o', 'gpt-3.5-turbo'];
      if (!allowedModels.includes(model)) {
        logSecurityEvent({
          event: 'INVALID_MODEL_SPECIFIED',
          level: 'warn',
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          userId: req.user?.userId?.toString(),
          endpoint: req.path,
          method: req.method,
          details: {
            requestedModel: model,
            allowedModels,
            possibleEnumeration: true,
          },
        });

        res.status(400).json({
          error: 'Invalid request',
          message: 'Invalid model specified',
        });
        return;
      }
    }

    // Log successful validation for monitoring
    logSecurityEvent({
      event: 'CHAT_REQUEST_VALIDATED',
      level: 'info',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.userId?.toString(),
      endpoint: req.path,
      method: req.method,
      details: {
        sessionId: sessionId.substring(0, 20),
        messageLength: message ? message.length : 0,
        model: model || 'default',
      },
    });

    next();
  } catch (error) {
    logSecurityEvent({
      event: 'CHAT_REQUEST_VALIDATION_ERROR',
      level: 'error',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.userId?.toString(),
      endpoint: req.path,
      method: req.method,
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        requestBody: {
          hasSessionId: !!req.body.sessionId,
          hasMessage: !!req.body.message,
          hasModel: !!req.body.model,
        },
      },
    });

    res.status(400).json({
      error: 'Validation failed',
      message: error instanceof Error ? error.message : 'Invalid request data',
    });
  }
};

// Image request validation middleware
export const validateImageRequest = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  try {
    const { sessionId, imageUrl, prompt } = req.body;

    // Validate session ID
    if (!validateSessionId(sessionId, req)) {
      res.status(400).json({
        error: 'Invalid request',
        message: 'Invalid session ID format',
      });
      return;
    }

    // Validate image URL
    if (!validateImageUrl(imageUrl, req)) {
      res.status(400).json({
        error: 'Invalid request',
        message: 'Invalid image URL format',
      });
      return;
    }

    // Validate prompt if provided
    if (prompt && typeof prompt === 'string') {
      if (prompt.length > 1000) {
        logSecurityEvent({
          event: 'IMAGE_PROMPT_TOO_LONG',
          level: 'warn',
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          userId: req.user?.userId?.toString(),
          endpoint: req.path,
          method: req.method,
          details: {
            promptLength: prompt.length,
            maxLength: 1000,
            sessionId: sessionId.substring(0, 20),
          },
        });

        res.status(400).json({
          error: 'Invalid request',
          message: 'Prompt too long (max 1000 characters)',
        });
        return;
      }
      req.body.prompt = prompt.trim();
    }

    // Log successful image request validation
    logSecurityEvent({
      event: 'IMAGE_REQUEST_VALIDATED',
      level: 'info',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.userId?.toString(),
      endpoint: req.path,
      method: req.method,
      details: {
        sessionId: sessionId.substring(0, 20),
        imageUrlLength: imageUrl ? imageUrl.length : 0,
        promptLength: prompt ? prompt.length : 0,
        isDataUrl: imageUrl ? imageUrl.startsWith('data:image/') : false,
      },
    });

    next();
  } catch (error) {
    logSecurityEvent({
      event: 'IMAGE_REQUEST_VALIDATION_ERROR',
      level: 'error',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.userId?.toString(),
      endpoint: req.path,
      method: req.method,
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        requestBody: {
          hasSessionId: !!req.body.sessionId,
          hasImageUrl: !!req.body.imageUrl,
          hasPrompt: !!req.body.prompt,
        },
      },
    });

    res.status(400).json({
      error: 'Validation failed',
      message: error instanceof Error ? error.message : 'Invalid request data',
    });
  }
};

// Error handling middleware that doesn't leak sensitive information
export const secureErrorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  // Determine error severity
  const status = error.status || 500;
  const isClientError = status >= 400 && status < 500;
  const isServerError = status >= 500;

  // Log security-relevant errors
  logSecurityEvent({
    event: 'APPLICATION_ERROR',
    level: isServerError ? 'error' : isClientError ? 'warn' : 'info',
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.userId?.toString(),
    endpoint: req.path,
    method: req.method,
    details: {
      errorMessage: error.message,
      errorStatus: status,
      errorCode: error.code,
      isClientError,
      isServerError,
      // Don't log full stack trace in security logs
      hasStack: !!error.stack,
      errorType: error.constructor.name,
    },
  });

  // Log the full error for debugging (separate from security logs)
  console.error('Application error:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.userId,
    timestamp: new Date().toISOString(),
  });

  // Send generic error response to client
  const isDevelopment = process.env.NODE_ENV === 'development';

  res.status(status).json({
    success: false,
    error: isClientError ? 'Bad request' : 'Internal server error',
    message: isDevelopment
      ? error.message
      : isClientError
        ? 'Invalid request data'
        : 'An error occurred while processing your request',
    ...(isDevelopment && {
      stack: error.stack,
      errorCode: error.code,
    }),
    timestamp: new Date().toISOString(),
  });
};

// Export the logging function for use in other modules
export { logSecurityEvent };
