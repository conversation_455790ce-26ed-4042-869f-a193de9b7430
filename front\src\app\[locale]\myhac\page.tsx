'use client';

export const runtime = 'edge';

import { CheckCircle, ExternalLink, Globe, Shield, Users } from 'lucide-react';
import type { BreadcrumbItem } from '@/components/breadcrumb';
import { PageWrapper } from '@/components/page-wrapper';
import { useLanguage } from '@/lib/language-context';

export default function MyHACPage() {
  const { language } = useLanguage();

  const breadcrumbs: BreadcrumbItem[] = [
    {
      label: language === 'en' ? 'Home' : 'Laman Utama',
      href: '/',
    },
    {
      label: language === 'en' ? 'Info MyHAC' : 'Info MyHAC',
      href: '/myhac',
    },
  ];

  const features = [
    {
      title: 'Halal Assurance System',
      titleBM: 'Sistem Jaminan Halal',
      description:
        'Comprehensive system for managing Halal compliance and quality assurance',
      descriptionBM:
        'Sistem komprehensif untuk menguruskan pematuhan Halal dan jaminan kualiti',
      icon: Shield,
    },
    {
      title: 'Industry Collaboration',
      titleBM: 'Kerjasama Industri',
      description:
        'Platform for industry stakeholders to collaborate and share best practices',
      descriptionBM:
        'Platform untuk pihak berkepentingan industri bekerjasama dan berkongsi amalan terbaik',
      icon: Users,
    },
    {
      title: 'Global Standards',
      titleBM: 'Piawaian Global',
      description:
        'Alignment with international Halal standards and certification requirements',
      descriptionBM:
        'Penjajaran dengan piawaian Halal antarabangsa dan keperluan pensijilan',
      icon: Globe,
    },
    {
      title: 'Quality Assurance',
      titleBM: 'Jaminan Kualiti',
      description:
        'Robust quality assurance mechanisms and continuous improvement processes',
      descriptionBM:
        'Mekanisme jaminan kualiti yang kukuh dan proses penambahbaikan berterusan',
      icon: CheckCircle,
    },
  ];

  const benefits = [
    {
      title: 'Enhanced Credibility',
      titleBM: 'Kredibiliti Dipertingkat',
      description:
        "Strengthen your organization's credibility in the Halal market",
      descriptionBM: 'Kukuhkan kredibiliti organisasi anda dalam pasaran Halal',
    },
    {
      title: 'Market Access',
      titleBM: 'Akses Pasaran',
      description:
        'Improved access to domestic and international Halal markets',
      descriptionBM:
        'Akses yang lebih baik kepada pasaran Halal domestik dan antarabangsa',
    },
    {
      title: 'Operational Efficiency',
      titleBM: 'Kecekapan Operasi',
      description: 'Streamlined processes and improved operational efficiency',
      descriptionBM:
        'Proses yang diperkemas dan kecekapan operasi yang dipertingkat',
    },
    {
      title: 'Consumer Confidence',
      titleBM: 'Keyakinan Pengguna',
      description:
        'Build stronger consumer trust and confidence in your products',
      descriptionBM:
        'Bina kepercayaan dan keyakinan pengguna yang lebih kuat terhadap produk anda',
    },
  ];

  const requirements = [
    {
      category: 'Organizational',
      categoryBM: 'Organisasi',
      items: [
        {
          text: 'Valid business registration',
          textBM: 'Pendaftaran perniagaan yang sah',
        },
        { text: 'Halal policy commitment', textBM: 'Komitmen polisi Halal' },
        { text: 'Management support', textBM: 'Sokongan pengurusan' },
        {
          text: 'Designated Halal team',
          textBM: 'Pasukan Halal yang ditetapkan',
        },
      ],
    },
    {
      category: 'Documentation',
      categoryBM: 'Dokumentasi',
      items: [
        {
          text: 'Quality management system',
          textBM: 'Sistem pengurusan kualiti',
        },
        {
          text: 'Standard operating procedures',
          textBM: 'Prosedur operasi standard',
        },
        { text: 'Training records', textBM: 'Rekod latihan' },
        { text: 'Supplier documentation', textBM: 'Dokumentasi pembekal' },
      ],
    },
    {
      category: 'Operational',
      categoryBM: 'Operasi',
      items: [
        {
          text: 'Halal compliance monitoring',
          textBM: 'Pemantauan pematuhan Halal',
        },
        { text: 'Internal audit system', textBM: 'Sistem audit dalaman' },
        {
          text: 'Corrective action procedures',
          textBM: 'Prosedur tindakan pembetulan',
        },
        { text: 'Continuous improvement', textBM: 'Penambahbaikan berterusan' },
      ],
    },
  ];

  return (
    <PageWrapper
      title="Info MyHAC"
      titleBM="Info MyHAC"
      description="Information about MyHAC (Malaysia Halal Assurance Certification) system and its benefits for industry stakeholders."
      descriptionBM="Maklumat mengenai sistem MyHAC (Malaysia Halal Assurance Certification) dan faedahnya untuk pihak berkepentingan industri."
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-8">
        {/* Introduction */}
        <div className="card">
          <h2 className="text-2xl font-bold mb-4 text-gray-900">
            {language === 'en'
              ? 'Malaysia Halal Assurance Certification (MyHAC)'
              : 'Malaysia Halal Assurance Certification (MyHAC)'}
          </h2>
          <p className="text-gray-600 leading-relaxed mb-4">
            {language === 'en'
              ? 'MyHAC is a comprehensive Halal assurance system developed by JAKIM to enhance the integrity and credibility of Halal certification in Malaysia. This system provides a structured framework for organizations to implement robust Halal assurance mechanisms and maintain the highest standards of Halal compliance.'
              : 'MyHAC adalah sistem jaminan Halal komprehensif yang dibangunkan oleh JAKIM untuk meningkatkan integriti dan kredibiliti pensijilan Halal di Malaysia. Sistem ini menyediakan rangka kerja berstruktur untuk organisasi melaksanakan mekanisme jaminan Halal yang kukuh dan mengekalkan piawaian pematuhan Halal tertinggi.'}
          </p>
          <p className="text-gray-600 leading-relaxed">
            {language === 'en'
              ? 'The MyHAC system is designed to support industry stakeholders in building sustainable Halal businesses while ensuring consumer confidence and facilitating international trade.'
              : 'Sistem MyHAC direka untuk menyokong pihak berkepentingan industri dalam membina perniagaan Halal yang mampan sambil memastikan keyakinan pengguna dan memudahkan perdagangan antarabangsa.'}
          </p>
        </div>

        {/* Key Features */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Key Features' : 'Ciri-ciri Utama'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {features.map((feature, index) => (
              <div
                key={index}
                className="p-4 border border-gray-200 rounded-lg hover:border-primary-green transition-colors"
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-10 h-10 bg-primary-green bg-opacity-10 rounded-lg flex items-center justify-center">
                    <feature.icon className="w-5 h-5 text-primary-green" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 mb-2">
                      {language === 'bm' ? feature.titleBM : feature.title}
                    </h4>
                    <p className="text-gray-600 text-sm">
                      {language === 'bm'
                        ? feature.descriptionBM
                        : feature.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Benefits */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Benefits of MyHAC Implementation'
              : 'Faedah Pelaksanaan MyHAC'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'bm' ? benefit.titleBM : benefit.title}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'bm'
                      ? benefit.descriptionBM
                      : benefit.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Implementation Requirements */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Implementation Requirements'
              : 'Keperluan Pelaksanaan'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {requirements.map((req, index) => (
              <div key={index} className="p-4 bg-bg-light-green rounded-lg">
                <h4 className="font-semibold text-primary-green mb-3">
                  {language === 'bm' ? req.categoryBM : req.category}
                </h4>
                <ul className="space-y-2">
                  {req.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start space-x-2">
                      <div className="w-1.5 h-1.5 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm text-gray-700">
                        {language === 'bm' ? item.textBM : item.text}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Implementation Process */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Implementation Process'
              : 'Proses Pelaksanaan'}
          </h3>
          <div className="space-y-4">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">1</span>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-1">
                  {language === 'en' ? 'Initial Assessment' : 'Penilaian Awal'}
                </h4>
                <p className="text-gray-600 text-sm">
                  {language === 'en'
                    ? 'Conduct a comprehensive assessment of current Halal practices and identify areas for improvement.'
                    : 'Jalankan penilaian komprehensif amalan Halal semasa dan kenal pasti bidang untuk penambahbaikan.'}
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">2</span>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-1">
                  {language === 'en'
                    ? 'System Development'
                    : 'Pembangunan Sistem'}
                </h4>
                <p className="text-gray-600 text-sm">
                  {language === 'en'
                    ? 'Develop and implement MyHAC system components including policies, procedures, and documentation.'
                    : 'Bangun dan laksanakan komponen sistem MyHAC termasuk polisi, prosedur, dan dokumentasi.'}
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">3</span>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-1">
                  {language === 'en'
                    ? 'Training & Implementation'
                    : 'Latihan & Pelaksanaan'}
                </h4>
                <p className="text-gray-600 text-sm">
                  {language === 'en'
                    ? 'Provide comprehensive training to staff and implement the MyHAC system across all operations.'
                    : 'Sediakan latihan komprehensif kepada kakitangan dan laksanakan sistem MyHAC merentas semua operasi.'}
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">4</span>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-1">
                  {language === 'en'
                    ? 'Monitoring & Evaluation'
                    : 'Pemantauan & Penilaian'}
                </h4>
                <p className="text-gray-600 text-sm">
                  {language === 'en'
                    ? 'Establish ongoing monitoring and evaluation mechanisms to ensure continuous compliance and improvement.'
                    : 'Wujudkan mekanisme pemantauan dan penilaian berterusan untuk memastikan pematuhan dan penambahbaikan berterusan.'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Certification Levels */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'MyHAC Certification Levels'
              : 'Tahap Pensijilan MyHAC'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-6 border border-gray-200 rounded-lg">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🥉</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en' ? 'Bronze Level' : 'Tahap Gangsa'}
              </h4>
              <p className="text-gray-600 text-sm">
                {language === 'en'
                  ? 'Basic MyHAC implementation with fundamental Halal assurance practices'
                  : 'Pelaksanaan MyHAC asas dengan amalan jaminan Halal fundamental'}
              </p>
            </div>
            <div className="text-center p-6 border border-gray-200 rounded-lg">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🥈</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en' ? 'Silver Level' : 'Tahap Perak'}
              </h4>
              <p className="text-gray-600 text-sm">
                {language === 'en'
                  ? 'Advanced MyHAC implementation with enhanced quality assurance mechanisms'
                  : 'Pelaksanaan MyHAC lanjutan dengan mekanisme jaminan kualiti yang dipertingkat'}
              </p>
            </div>
            <div className="text-center p-6 border border-gray-200 rounded-lg">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🥇</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en' ? 'Gold Level' : 'Tahap Emas'}
              </h4>
              <p className="text-gray-600 text-sm">
                {language === 'en'
                  ? 'Excellence in MyHAC implementation with industry leadership and innovation'
                  : 'Kecemerlangan dalam pelaksanaan MyHAC dengan kepimpinan dan inovasi industri'}
              </p>
            </div>
          </div>
        </div>

        {/* Support Services */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Support Services' : 'Perkhidmatan Sokongan'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">
                {language === 'en' ? 'Training Programs' : 'Program Latihan'}
              </h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'MyHAC awareness workshops'
                    : 'Bengkel kesedaran MyHAC'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Implementation training'
                    : 'Latihan pelaksanaan'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Internal auditor certification'
                    : 'Pensijilan juruaudit dalaman'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Continuous improvement programs'
                    : 'Program penambahbaikan berterusan'}
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">
                {language === 'en'
                  ? 'Technical Assistance'
                  : 'Bantuan Teknikal'}
              </h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'System development guidance'
                    : 'Panduan pembangunan sistem'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Documentation support'
                    : 'Sokongan dokumentasi'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Implementation consulting'
                    : 'Perundingan pelaksanaan'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Performance monitoring'
                    : 'Pemantauan prestasi'}
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="card bg-bg-light-green">
          <h3 className="text-xl font-bold mb-4 text-gray-900">
            {language === 'en'
              ? 'Get Started with MyHAC'
              : 'Mulakan dengan MyHAC'}
          </h3>
          <p className="text-gray-600 mb-4">
            {language === 'en'
              ? 'Ready to implement MyHAC in your organization? Contact our team for more information about the system, implementation support, and training programs.'
              : 'Bersedia untuk melaksanakan MyHAC dalam organisasi anda? Hubungi pasukan kami untuk maklumat lanjut mengenai sistem, sokongan pelaksanaan, dan program latihan.'}
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en'
                  ? 'MyHAC Information Center'
                  : 'Pusat Maklumat MyHAC'}
              </h4>
              <div className="space-y-1 text-sm text-gray-600">
                <p>
                  <span className="font-medium">
                    {language === 'en' ? 'Phone:' : 'Telefon:'}
                  </span>{' '}
                  03-8892 5000
                </p>
                <p>
                  <span className="font-medium">
                    {language === 'en' ? 'Email:' : 'E-mel:'}
                  </span>{' '}
                  <EMAIL>
                </p>
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en'
                  ? 'Training & Support'
                  : 'Latihan & Sokongan'}
              </h4>
              <div className="space-y-1 text-sm text-gray-600">
                <p>
                  <span className="font-medium">
                    {language === 'en' ? 'Training:' : 'Latihan:'}
                  </span>{' '}
                  <EMAIL>
                </p>
                <p>
                  <span className="font-medium">
                    {language === 'en' ? 'Support:' : 'Sokongan:'}
                  </span>{' '}
                  <EMAIL>
                </p>
              </div>
            </div>
          </div>
          <div className="mt-6">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center gap-2 px-6 py-3 bg-primary-green text-white rounded-lg hover:bg-primary-green-dark transition-colors"
            >
              {language === 'en' ? 'Contact Us Today' : 'Hubungi Kami Hari Ini'}
              <ExternalLink className="w-4 h-4" />
            </a>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}
