'use client';

import {
  Building2,
  ChevronDown,
  ChevronUp,
  FileText,
  Globe,
  Loader2,
  Package,
} from 'lucide-react';
import { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import type { CompanySearchResponse } from '@/types/company';
import type {
  ProductSearchResponse,
  SemanticProductSearchResponse,
} from '@/types/product';
import type { SearchResponse } from '@/types/search';
import { CompanyResults } from './CompanyResults';
import { ProductResults } from './ProductResults';
import { SearchResults } from './SearchResults';

interface SummaryResponse {
  query: string;
  summary: string;
  chunks: Array<{
    text: string;
    score: number;
  }>;
  totalChunks: number;
  wordsUsed: number;
}

interface CombinedSearchResultsProps {
  searchResponse: SearchResponse | null;
  productResponse: ProductSearchResponse | SemanticProductSearchResponse | null;
  companyResponse: CompanySearchResponse | null;
  summaryResponse: SummaryResponse | null;
  onPageChange?: (page: number, type: 'web' | 'products' | 'companies') => void;
  isLoading?: boolean;
  isProductLoading?: boolean;
  isCompanyLoading?: boolean;
  isSummaryLoading?: boolean;
}

export type TabType = 'summary' | 'web' | 'products' | 'companies';

export function CombinedSearchResults({
  searchResponse,
  productResponse,
  companyResponse,
  summaryResponse,
  onPageChange,
  isLoading,
  isProductLoading,
  isCompanyLoading,
  isSummaryLoading,
}: CombinedSearchResultsProps) {
  const [activeTab, setActiveTab] = useState<TabType>('summary');
  const [isSummaryExpanded, setIsSummaryExpanded] = useState(false);

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
  };
  // Show component if any search is in progress or has results
  if (
    !searchResponse &&
    !productResponse &&
    !companyResponse &&
    !summaryResponse &&
    !isLoading &&
    !isProductLoading &&
    !isCompanyLoading &&
    !isSummaryLoading
  ) {
    return null;
  }

  const webResultsCount = searchResponse?.results?.length || 0;
  const productResultsCount = productResponse?.products?.length || 0;
  const companyResultsCount = companyResponse?.companies?.length || 0;
  const query =
    searchResponse?.query ||
    productResponse?.query ||
    companyResponse?.query ||
    '';

  const tabs = [
    {
      id: 'summary' as TabType,
      label: 'Summary',
      icon: Globe,
      count: webResultsCount + productResultsCount + companyResultsCount,
    },
    {
      id: 'web' as TabType,
      label: 'Documents Results',
      icon: FileText,
      count: webResultsCount,
    },
    {
      id: 'products' as TabType,
      label: 'Products',
      icon: Package,
      count: productResultsCount,
    },
    {
      id: 'companies' as TabType,
      label: 'Companies',
      icon: Building2,
      count: companyResultsCount,
    },
  ];

  const handlePageChange = (page: number) => {
    if (activeTab === 'web') {
      onPageChange?.(page, 'web');
    } else if (activeTab === 'products') {
      onPageChange?.(page, 'products');
    } else if (activeTab === 'companies') {
      onPageChange?.(page, 'companies');
    }
  };

  const renderSummary = () => {
    return (
      <div className="space-y-8">
        {/* AI-Generated Summary */}
        {query && (isSummaryLoading || summaryResponse) && (
          <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-lg border border-blue-200 p-4 my-2">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="w-4 h-4 rounded-full flex items-center justify-center mr-1">
                  {isSummaryLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
                  ) : (
                    <span className="text-white text-sm font-bold">✨</span>
                  )}
                </div>
                <h2 className="text-xl font-semibold text-gray-900">
                  AI Overview "{query}"
                </h2>
              </div>
              {!isSummaryLoading && summaryResponse && (
                <button
                  type="button"
                  onClick={() => setIsSummaryExpanded(!isSummaryExpanded)}
                  className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
                  aria-label={
                    isSummaryExpanded ? 'Collapse summary' : 'Expand summary'
                  }
                >
                  {isSummaryExpanded ? (
                    <ChevronUp className="w-5 h-5" />
                  ) : (
                    <ChevronDown className="w-5 h-5" />
                  )}
                </button>
              )}
            </div>
            {isSummaryLoading ? (
              <div className="text-center py-8">
                <Loader2 className="w-8 h-8 text-blue-600 mx-auto mb-3 animate-spin" />
                <p className="text-gray-600 text-sm">
                  Generating AI summary...
                </p>
              </div>
            ) : summaryResponse ? (
              <>
                <div
                  className={`prose prose-sm max-w-none text-gray-700 mb-4 text-sm summary transition-all duration-300 overflow-hidden ${
                    isSummaryExpanded ? 'max-h-none' : 'max-h-32'
                  }`}
                >
                  <ReactMarkdown>{summaryResponse.summary}</ReactMarkdown>
                </div>
                {!isSummaryExpanded && (
                  <div className="text-center">
                    <button
                      type="button"
                      onClick={() => setIsSummaryExpanded(true)}
                      className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                    >
                      Click to expand full summary
                    </button>
                  </div>
                )}
                {false && (
                  <div className="text-xs text-gray-500 border-t border-gray-200 pt-3">
                    <p>
                      Based on {summaryResponse?.totalChunks} relevant documents
                      •{summaryResponse?.wordsUsed} words analyzed • Generated
                      using AI analysis
                    </p>
                  </div>
                )}
              </>
            ) : null}
          </div>
        )}

        {/* Search Summary */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          {false && (
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              {query
                ? `Search Results for "${query}"`
                : 'Browse All Halal Selangor'}
            </h2>
          )}

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Web Results Summary */}
            <div
              className={`rounded-lg p-4 transition-all duration-300 ${
                isLoading
                  ? 'bg-blue-50 border-2 border-blue-200 border-dashed'
                  : searchResponse && webResultsCount > 0
                    ? 'bg-blue-50 border-2 border-blue-300'
                    : 'bg-blue-50'
              }`}
            >
              <div className="flex items-center mb-2">
                {isLoading ? (
                  <Loader2 className="w-5 h-5 text-blue-600 mr-2 animate-spin" />
                ) : searchResponse && webResultsCount > 0 ? (
                  <div className="w-5 h-5 text-blue-600 mr-2 flex items-center justify-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  </div>
                ) : (
                  <FileText className="w-5 h-5 text-blue-600 mr-2" />
                )}
                <h3 className="text-lg font-medium text-blue-900">
                  Documentation
                </h3>
                {searchResponse && webResultsCount > 0 && !isLoading && (
                  <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                    Ready
                  </span>
                )}
              </div>
              {isLoading ? (
                <div className="text-center py-4">
                  <p className="text-blue-700 text-sm">Loading documents...</p>
                  <div className="mt-2 w-full bg-blue-200 rounded-full h-1">
                    <div
                      className="bg-blue-600 h-1 rounded-full animate-pulse"
                      style={{ width: '60%' }}
                    />
                  </div>
                </div>
              ) : (
                <>
                  <p className="text-blue-700 text-sm mb-2">
                    {query
                      ? `Found ${webResultsCount} relevant documents and articles`
                      : 'Browse halal documentation and articles'}
                  </p>
                  {searchResponse && webResultsCount > 0 && (
                    <div className="text-xs text-blue-600">
                      <p>
                        • {searchResponse.totalChunks} total content chunks
                        analyzed
                      </p>
                      <p>
                        • High relevance score:{' '}
                        {Math.round(
                          (searchResponse.results[0]?.score || 0) * 100,
                        )}
                        %
                      </p>
                    </div>
                  )}
                  <button
                    type="button"
                    onClick={() => setActiveTab('web')}
                    className="mt-3 text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    View all document results →
                  </button>
                </>
              )}
              <button
                type="button"
                onClick={() => handleTabChange('web')}
                className="mt-3 text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                View all document results →
              </button>
            </div>

            {/* Product Results Summary */}
            <div
              className={`rounded-lg p-4 transition-all duration-300 ${
                isProductLoading
                  ? 'bg-green-50 border-2 border-green-200 border-dashed'
                  : productResponse && productResultsCount > 0
                    ? 'bg-green-50 border-2 border-green-300'
                    : 'bg-green-50'
              }`}
            >
              <div className="flex items-center mb-2">
                {isProductLoading ? (
                  <Loader2 className="w-5 h-5 text-green-600 mr-2 animate-spin" />
                ) : productResponse && productResultsCount > 0 ? (
                  <div className="w-5 h-5 text-green-600 mr-2 flex items-center justify-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  </div>
                ) : (
                  <Package className="w-5 h-5 text-green-600 mr-2" />
                )}
                <h3 className="text-lg font-medium text-green-900">
                  Halal Products
                </h3>
                {productResponse &&
                  productResultsCount > 0 &&
                  !isProductLoading && (
                    <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                      Ready
                    </span>
                  )}
              </div>
              {isProductLoading ? (
                <div className="text-center py-4">
                  <p className="text-green-700 text-sm">Loading products...</p>
                  <div className="mt-2 w-full bg-green-200 rounded-full h-1">
                    <div
                      className="bg-green-600 h-1 rounded-full animate-pulse"
                      style={{ width: '40%' }}
                    />
                  </div>
                </div>
              ) : (
                <>
                  <p className="text-green-700 text-sm mb-2">
                    {query
                      ? `Found ${productResultsCount} certified halal products`
                      : `Browse ${productResultsCount} certified halal products`}
                  </p>
                  {productResponse && productResultsCount > 0 && (
                    <div className="text-xs text-green-600">
                      <p>• All products are JAKIM certified</p>
                      <p>
                        • Categories:{' '}
                        {[
                          ...new Set(
                            productResponse.products.map((p) => p.category),
                          ),
                        ].join(', ')}
                      </p>
                      {/* Show semantic search info */}
                      {'searchType' in productResponse &&
                        (productResponse.searchType === 'semantic' ||
                          productResponse.searchType ===
                            'multilingual-semantic') && (
                          <p>
                            • AI-powered similarity search
                            {productResponse.searchType ===
                              'multilingual-semantic' && ' (multilingual)'}
                          </p>
                        )}
                    </div>
                  )}
                  <button
                    type="button"
                    onClick={() => setActiveTab('products')}
                    className="mt-3 text-green-600 hover:text-green-700 text-sm font-medium"
                  >
                    View all products →
                  </button>
                </>
              )}
              <button
                type="button"
                onClick={() => handleTabChange('products')}
                className="mt-3 text-green-600 hover:text-green-700 text-sm font-medium"
              >
                View all products →
              </button>
            </div>

            {/* Company Results Summary */}
            <div
              className={`rounded-lg p-4 transition-all duration-300 ${
                isCompanyLoading
                  ? 'bg-orange-50 border-2 border-orange-200 border-dashed'
                  : companyResponse && companyResultsCount > 0
                    ? 'bg-orange-50 border-2 border-orange-300'
                    : 'bg-orange-50'
              }`}
            >
              <div className="flex items-center mb-2">
                {isCompanyLoading ? (
                  <Loader2 className="w-5 h-5 text-orange-600 mr-2 animate-spin" />
                ) : companyResponse && companyResultsCount > 0 ? (
                  <div className="w-5 h-5 text-orange-600 mr-2 flex items-center justify-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  </div>
                ) : (
                  <Building2 className="w-5 h-5 text-orange-600 mr-2" />
                )}
                <h3 className="text-lg font-medium text-orange-900">
                  Halal Companies
                </h3>
                {companyResponse &&
                  companyResultsCount > 0 &&
                  !isCompanyLoading && (
                    <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                      Ready
                    </span>
                  )}
              </div>
              {isCompanyLoading ? (
                <div className="text-center py-4">
                  <p className="text-orange-700 text-sm">
                    Loading companies...
                  </p>
                  <div className="mt-2 w-full bg-orange-200 rounded-full h-1">
                    <div
                      className="bg-orange-600 h-1 rounded-full animate-pulse"
                      style={{ width: '80%' }}
                    />
                  </div>
                </div>
              ) : (
                <>
                  <p className="text-orange-700 text-sm mb-2">
                    {query
                      ? `Found ${companyResultsCount} certified halal companies`
                      : `Browse ${companyResultsCount} certified halal companies`}
                  </p>
                  {companyResponse && companyResultsCount > 0 && (
                    <div className="text-xs text-orange-600">
                      <p>• All companies are JAKIM certified</p>
                      <p>
                        • Business Types:{' '}
                        {[
                          ...new Set(
                            companyResponse.companies
                              .map((c) => c.businessType)
                              .filter(Boolean),
                          ),
                        ]
                          .slice(0, 3)
                          .join(', ')}
                      </p>
                    </div>
                  )}
                  <button
                    type="button"
                    onClick={() => setActiveTab('companies')}
                    className="mt-3 text-orange-600 hover:text-orange-700 text-sm font-medium"
                  >
                    View all companies →
                  </button>
                </>
              )}
              <button
                type="button"
                onClick={() => handleTabChange('companies')}
                className="mt-3 text-orange-600 hover:text-orange-700 text-sm font-medium"
              >
                View all companies →
              </button>
            </div>
          </div>

          {/* Quick Results Preview */}
          {(webResultsCount > 0 ||
            productResultsCount > 0 ||
            companyResultsCount > 0 ||
            isLoading ||
            isProductLoading ||
            isCompanyLoading) && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h4 className="text-md font-medium text-gray-900 mb-4">
                {query ? 'Quick Preview' : 'Sample Listings'}
              </h4>

              <div className="space-y-6">
                {/* Top 3 Web Results */}
                {query &&
                  (isLoading ||
                    (searchResponse && searchResponse.results.length > 0)) && (
                    <div>
                      <div className="flex items-center mb-3">
                        {isLoading ? (
                          <Loader2 className="w-4 h-4 text-blue-600 mr-2 animate-spin" />
                        ) : (
                          <FileText className="w-4 h-4 text-blue-600 mr-2" />
                        )}
                        <h5 className="font-medium text-blue-900">
                          {query ? 'Top Files' : 'Sample Files'}
                        </h5>
                      </div>
                      {isLoading ? (
                        <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
                          <div className="text-center py-4">
                            <Loader2 className="w-6 h-6 text-blue-600 mx-auto mb-2 animate-spin" />
                            <p className="text-blue-700 text-sm">
                              Loading documents...
                            </p>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-3">
                          {searchResponse?.results
                            .slice(0, 3)
                            .map((result, index) => (
                              <div
                                key={`web-${result.document_id || index}`}
                                className="bg-blue-50 rounded-lg p-3 border border-blue-100"
                              >
                                <div className="flex items-start justify-between">
                                  <div className="flex-1">
                                    <h6 className="font-medium text-gray-900 text-sm">
                                      {result.metadata?.title || 'Document'}
                                    </h6>
                                    <p className="text-gray-600 text-xs mt-1 line-clamp-2">
                                      {result.text.substring(0, 120)}...
                                    </p>
                                    <div className="flex items-center mt-2 text-xs text-blue-600">
                                      <span className="bg-blue-100 px-2 py-1 rounded">
                                        {Math.round(result.score * 100)}%
                                        relevance
                                      </span>
                                      {result.metadata?.source && (
                                        <span className="ml-2 text-gray-500">
                                          •{' '}
                                          {result.metadata.source
                                            .split('/')
                                            .pop()}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                        </div>
                      )}
                    </div>
                  )}

                {/* Top 3 Product Results */}
                {(isProductLoading ||
                  (productResponse && productResponse.products.length > 0)) && (
                  <div>
                    <div className="flex items-center mb-3">
                      {isProductLoading ? (
                        <Loader2 className="w-4 h-4 text-green-600 mr-2 animate-spin" />
                      ) : (
                        <Package className="w-4 h-4 text-green-600 mr-2" />
                      )}
                      <h5 className="font-medium text-green-900">
                        Sample Halal Products
                      </h5>
                    </div>
                    {isProductLoading ? (
                      <div className="bg-green-50 rounded-lg p-4 border border-green-100">
                        <div className="text-center py-4">
                          <Loader2 className="w-6 h-6 text-green-600 mx-auto mb-2 animate-spin" />
                          <p className="text-green-700 text-sm">
                            Loading products...
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {productResponse?.products
                          .slice(0, 3)
                          .map((product) => {
                            // Check if this is a semantic search result with similarity score and search term
                            const similarityScore =
                              'similarityScore' in product
                                ? product.similarityScore
                                : undefined;
                            const foundBySearchTerm =
                              'foundBySearchTerm' in product
                                ? product.foundBySearchTerm
                                : undefined;
                            const searchType =
                              'searchType' in productResponse
                                ? productResponse.searchType
                                : 'keyword';
                            const isSemanticSearch =
                              searchType === 'semantic' ||
                              searchType === 'multilingual-semantic';

                            return (
                              <div
                                key={product.id}
                                className="bg-green-50 rounded-lg p-3 border border-green-100"
                              >
                                <div className="flex items-start justify-between">
                                  <div className="flex-1">
                                    <h6 className="font-medium text-gray-900 text-sm">
                                      {product.productName}
                                    </h6>
                                    <p className="text-gray-600 text-xs mt-1 line-clamp-2">
                                      {product.companyName}
                                      {product.subcategory &&
                                        ` • ${product.subcategory}`}
                                    </p>
                                    <div className="flex items-center justify-between mt-2">
                                      <div className="flex items-center text-xs gap-2">
                                        {product.certificateNumber && (
                                          <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                                            ✓ {product.certificateNumber}
                                          </span>
                                        )}
                                        {/* Similarity Score for Semantic Search */}
                                        {isSemanticSearch &&
                                        similarityScore !== undefined &&
                                        similarityScore !== null &&
                                        typeof similarityScore === 'number' ? (
                                          <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded flex items-center">
                                            <span className="text-xs">⚡</span>
                                            <span className="ml-1">
                                              {Math.round(
                                                similarityScore * 100,
                                              )}
                                              %
                                            </span>
                                          </span>
                                        ) : null}
                                        {/* Search Term Tag for Semantic Search */}
                                        {isSemanticSearch &&
                                        foundBySearchTerm &&
                                        typeof foundBySearchTerm === 'string' &&
                                        foundBySearchTerm !== query ? (
                                          <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded flex items-center text-xs">
                                            <span className="mr-1">🔍</span>
                                            {foundBySearchTerm}
                                          </span>
                                        ) : null}
                                        <span className="text-gray-500">
                                          {product.companyName} •{' '}
                                          {product.category}
                                        </span>
                                      </div>
                                      {product.status && (
                                        <span className="text-sm font-bold text-green-600">
                                          {product.status}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                      </div>
                    )}
                  </div>
                )}

                {/* Top 3 Company Results */}
                {(isCompanyLoading ||
                  (companyResponse &&
                    companyResponse.companies.length > 0)) && (
                  <div>
                    <div className="flex items-center mb-3">
                      {isCompanyLoading ? (
                        <Loader2 className="w-4 h-4 text-orange-600 mr-2 animate-spin" />
                      ) : (
                        <Building2 className="w-4 h-4 text-orange-600 mr-2" />
                      )}
                      <h5 className="font-medium text-orange-900">
                        Sample Halal Companies
                      </h5>
                    </div>
                    {isCompanyLoading ? (
                      <div className="bg-orange-50 rounded-lg p-4 border border-orange-100">
                        <div className="text-center py-4">
                          <Loader2 className="w-6 h-6 text-orange-600 mx-auto mb-2 animate-spin" />
                          <p className="text-orange-700 text-sm">
                            Loading companies...
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {companyResponse?.companies
                          .slice(0, 3)
                          .map((company) => (
                            <div
                              key={company.id}
                              className="bg-orange-50 rounded-lg p-3 border border-orange-100"
                            >
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <h6 className="font-medium text-gray-900 text-sm">
                                    {company.name}
                                  </h6>
                                  <p className="text-gray-600 text-xs mt-1 line-clamp-2">
                                    {company.address}
                                  </p>
                                  <div className="flex items-center justify-between mt-2">
                                    <div className="flex items-center text-xs">
                                      {company.certificateStatus && (
                                        <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded">
                                          ✓ {company.certificateStatus}
                                        </span>
                                      )}
                                      <span className="ml-2 text-gray-500">
                                        {company.businessType} •{' '}
                                        {company.category}
                                      </span>
                                    </div>
                                    {company.registrationNumber && (
                                      <span className="text-xs text-gray-500">
                                        {company.registrationNumber}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div>
      {/* Tab Navigation */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;

              // Determine if this specific tab is loading
              const isTabLoading =
                (tab.id === 'summary' &&
                  (isLoading ||
                    isProductLoading ||
                    isCompanyLoading ||
                    isSummaryLoading)) ||
                (tab.id === 'web' && isLoading) ||
                (tab.id === 'products' && isProductLoading) ||
                (tab.id === 'companies' && isCompanyLoading);

              // Determine if this tab has results ready
              const hasResults =
                (tab.id === 'summary' &&
                  (searchResponse ||
                    productResponse ||
                    companyResponse ||
                    summaryResponse)) ||
                (tab.id === 'web' && searchResponse && webResultsCount > 0) ||
                (tab.id === 'products' &&
                  productResponse &&
                  productResultsCount > 0) ||
                (tab.id === 'companies' &&
                  companyResponse &&
                  companyResultsCount > 0);

              return (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => handleTabChange(tab.id)}
                  className={`
                    flex items-center py-2 px-1 border-b-2 font-medium text-sm cursor-pointer
                    ${
                      isActive
                        ? 'border-green-500 text-green-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  {isTabLoading ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : hasResults && !isTabLoading ? (
                    <div className="w-4 h-4 mr-2 flex items-center justify-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                    </div>
                  ) : (
                    <Icon className="w-4 h-4 mr-2" />
                  )}
                  {tab.label}
                  {isTabLoading && (
                    <span
                      className={`
                      ml-2 px-2 py-0.5 text-xs rounded-full
                      ${
                        isActive
                          ? 'bg-green-100 text-green-600'
                          : 'bg-gray-100 text-gray-600'
                      }
                    `}
                    >
                      Loading...
                    </span>
                  )}
                  {!isTabLoading && tab.count > 0 && (
                    <span
                      className={`
                      ml-2 px-2 py-0.5 text-xs rounded-full
                      ${
                        isActive
                          ? 'bg-green-100 text-green-600'
                          : 'bg-gray-100 text-gray-600'
                      }
                    `}
                    >
                      {tab.count}
                    </span>
                  )}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'summary' && renderSummary()}

        {activeTab === 'web' && searchResponse && (
          <SearchResults
            searchResponse={searchResponse}
            onPageChange={handlePageChange}
            isLoading={isLoading}
          />
        )}

        {activeTab === 'products' && productResponse && (
          <ProductResults
            productResponse={productResponse}
            onPageChange={handlePageChange}
            isLoading={isProductLoading}
          />
        )}

        {activeTab === 'companies' && companyResponse && (
          <CompanyResults
            companyResponse={companyResponse}
            onPageChange={handlePageChange}
            isLoading={isCompanyLoading}
          />
        )}
      </div>
    </div>
  );
}
