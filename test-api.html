<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chat Sessions API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .message {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .message.user {
            background-color: #007bff;
            color: white;
            text-align: right;
        }
        .message.assistant {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        .message.agent {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <h1>Chat Sessions API Test</h1>
    
    <div class="section">
        <h2>1. Test Sessions List</h2>
        <button class="button" onclick="testSessionsList()">Get All Sessions</button>
        <div id="sessions-result" class="result"></div>
    </div>
    
    <div class="section">
        <h2>2. Test Session Details</h2>
        <input type="text" id="session-id" placeholder="Enter session ID (e.g., session-1)" value="session-1">
        <button class="button" onclick="testSessionDetails()">Get Session Details</button>
        <div id="session-details-result" class="result"></div>
    </div>
    
    <div class="section">
        <h2>3. Test Session Stats</h2>
        <button class="button" onclick="testSessionStats()">Get Session Stats</button>
        <div id="stats-result" class="result"></div>
    </div>
    
    <div class="section">
        <h2>4. Test Handover Requests</h2>
        <button class="button" onclick="testHandoverRequests()">Get Handover Requests</button>
        <div id="handover-result" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:16001';

        async function makeRequest(endpoint, method = 'GET', body = null) {
            try {
                const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: body ? JSON.stringify(body) : null,
                });
                
                const data = await response.json();
                return {
                    success: response.ok,
                    status: response.status,
                    data: data
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        async function testSessionsList() {
            const resultDiv = document.getElementById('sessions-result');
            resultDiv.textContent = 'Loading...';
            
            const result = await makeRequest('/api/admin/sessions');
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `Success! Found ${result.data.length} sessions:\n\n${JSON.stringify(result.data, null, 2)}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${result.error || result.data?.error || 'Unknown error'}`;
            }
        }

        async function testSessionDetails() {
            const sessionId = document.getElementById('session-id').value;
            const resultDiv = document.getElementById('session-details-result');
            
            if (!sessionId) {
                resultDiv.className = 'result error';
                resultDiv.textContent = 'Please enter a session ID';
                return;
            }
            
            resultDiv.textContent = 'Loading...';
            
            const result = await makeRequest(`/api/admin/sessions/${sessionId}`);
            
            if (result.success) {
                resultDiv.className = 'result success';
                const session = result.data;
                
                let html = `<strong>Session Details:</strong>\n`;
                html += `ID: ${session.id}\n`;
                html += `Platform: ${session.platform}\n`;
                html += `Status: ${session.status}\n`;
                html += `Created: ${new Date(session.createdAt).toLocaleString()}\n`;
                html += `Messages: ${session.messages?.length || 0}\n\n`;
                
                if (session.messages && session.messages.length > 0) {
                    html += `<strong>Messages:</strong>\n`;
                    session.messages.forEach((msg, index) => {
                        html += `\n${index + 1}. [${msg.role.toUpperCase()}] ${new Date(msg.timestamp).toLocaleString()}\n`;
                        html += `   ${msg.content}\n`;
                        if (msg.agentName) {
                            html += `   (Agent: ${msg.agentName})\n`;
                        }
                    });
                }
                
                resultDiv.innerHTML = html.replace(/\n/g, '<br>');
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${result.error || result.data?.error || 'Unknown error'}`;
            }
        }

        async function testSessionStats() {
            const resultDiv = document.getElementById('stats-result');
            resultDiv.textContent = 'Loading...';
            
            const result = await makeRequest('/api/admin/sessions/stats');
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `Session Statistics:\n\n${JSON.stringify(result.data, null, 2)}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${result.error || result.data?.error || 'Unknown error'}`;
            }
        }

        async function testHandoverRequests() {
            const resultDiv = document.getElementById('handover-result');
            resultDiv.textContent = 'Loading...';
            
            const result = await makeRequest('/api/admin/sessions/handovers/pending');
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `Handover Requests:\n\n${JSON.stringify(result.data, null, 2)}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${result.error || result.data?.error || 'Unknown error'}`;
            }
        }

        // Auto-test on page load
        window.onload = function() {
            console.log('Testing API endpoints...');
            testSessionsList();
        };
    </script>
</body>
</html>
