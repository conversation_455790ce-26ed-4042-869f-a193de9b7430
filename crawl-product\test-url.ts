#!/usr/bin/env bun

async function testUrl() {
  const url =
    'https://myehalal.halal.gov.my/portal-halal/v1/index.php?data=ZGlyZWN0b3J5L2luZGV4X2RpcmVjdG9yeTs7Ozs=&negeri=&category=&cari=';

  console.log('🌐 Testing URL accessibility...');
  console.log('📍 URL:', url);

  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
    });

    console.log('✅ Response status:', response.status);
    console.log(
      '📄 Response headers:',
      Object.fromEntries(response.headers.entries()),
    );

    if (response.ok) {
      const html = await response.text();
      console.log('📝 HTML length:', html.length);
      console.log('🔍 HTML sample (first 500 chars):', html.substring(0, 500));

      // Check for common elements
      const hasTable = html.includes('<table');
      const hasForm = html.includes('<form');
      const hasScript = html.includes('<script');

      console.log('📊 Content analysis:');
      console.log('  - Has tables:', hasTable);
      console.log('  - Has forms:', hasForm);
      console.log('  - Has scripts:', hasScript);
    } else {
      console.log('❌ Request failed with status:', response.status);
    }
  } catch (error) {
    console.error('❌ Error fetching URL:', error);
  }
}

testUrl();
