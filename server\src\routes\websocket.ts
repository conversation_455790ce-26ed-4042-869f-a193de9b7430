import { Hono } from 'hono';
import { WebSocketServer } from 'ws';
import websocketService, { MESSAGE_TYPES } from '../services/websocket';
import { logger } from '../utils/logger';

const websocket = new Hono();

// WebSocket server instance (will be initialized in server.ts)
let wss: WebSocketServer | null = null;

// Initialize WebSocket server
export function initializeWebSocketServer(server: any) {
  wss = new WebSocketServer({ server, path: '/ws' });

  wss.on('connection', (ws, request) => {
    const connectionId = websocketService.generateConnectionId();
    logger.info('WebSocket connection opened', { connectionId });

    // Store the WebSocket connection in the service
    websocketService.addWebSocketConnection(connectionId, ws);

    // Send connection established message
    ws.send(
      JSON.stringify({
        type: MESSAGE_TYPES.CONNECTION_ESTABLISHED,
        data: { connectionId },
      }),
    );

    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        logger.info('WebSocket message received', { message });

        // Handle different message types
        switch (message.type) {
          case 'register':
            handleRegistration(message, ws, connectionId);
            break;
          case 'ping':
            handlePing(message, ws, connectionId);
            break;
          case 'agent_message':
            handleAgentMessage(message, ws);
            break;
          case 'user_message':
            handleUserMessage(message, ws);
            break;
          default:
            logger.warn('Unknown WebSocket message type', {
              type: message.type,
            });
        }
      } catch (error) {
        logger.error('Error processing WebSocket message', { error });
        ws.send(
          JSON.stringify({
            type: 'error',
            data: { message: 'Invalid message format' },
          }),
        );
      }
    });

    ws.on('close', () => {
      logger.info('WebSocket connection closed', { connectionId });
      websocketService.removeConnection(connectionId);
    });

    ws.on('error', (error) => {
      logger.error('WebSocket error', { connectionId, error });
    });
  });

  logger.info('WebSocket server initialized');
  return wss;
}

// Handle connection registration
function handleRegistration(message: any, ws: any, connectionId: string) {
  const { data } = message;

  if (data.connectionType === 'user' && data.sessionId) {
    websocketService.registerConnection(connectionId, 'user', {
      sessionId: data.sessionId,
    });
    logger.info('User connection registered', {
      connectionId,
      sessionId: data.sessionId,
    });
  } else if (data.connectionType === 'agent' && data.agentId) {
    websocketService.registerConnection(connectionId, 'agent', {
      agentId: data.agentId,
      sessionId: data.sessionId,
    });
    logger.info('Agent connection registered', {
      connectionId,
      agentId: data.agentId,
    });
  }

  // Send registration confirmation
  ws.send(
    JSON.stringify({
      type: 'registration_confirmed',
      data: { connectionType: data.connectionType },
    }),
  );
}

// Handle ping messages
function handlePing(message: any, ws: any, connectionId: string) {
  websocketService.updateLastSeen(connectionId);

  ws.send(
    JSON.stringify({
      type: MESSAGE_TYPES.PONG,
      data: { timestamp: new Date().toISOString() },
    }),
  );
}

// Handle agent messages
function handleAgentMessage(message: any, ws: any) {
  const { data } = message;
  const { sessionId, content, agentId } = data;

  if (!sessionId || !content) {
    logger.error('Invalid agent message data', { data });
    return;
  }

  // Broadcast to all connections in the session
  websocketService.sendToSession(sessionId, {
    type: MESSAGE_TYPES.AGENT_MESSAGE,
    data: {
      sessionId,
      content,
      agentId,
      timestamp: new Date().toISOString(),
    },
  });

  logger.info('Agent message broadcasted', { sessionId, agentId });
}

// Handle user messages
function handleUserMessage(message: any, ws: any) {
  const { data } = message;
  const { sessionId, content, userId } = data;

  if (!sessionId || !content) {
    logger.error('Invalid user message data', { data });
    return;
  }

  // Broadcast to agents monitoring this session
  websocketService.sendToSession(sessionId, {
    type: MESSAGE_TYPES.USER_MESSAGE,
    data: {
      sessionId,
      content,
      userId,
      timestamp: new Date().toISOString(),
    },
  });

  logger.info('User message broadcasted', { sessionId, userId });
}

export default websocket;
