'use client';

import { AnnouncementList } from '@/components/announcement-card';
import { EAduan, SystemAccess } from '@/components/e-aduan';
import { HeroCarousel } from '@/components/hero-carousel';
import { NewsList } from '@/components/news-card';
import { OperationHours } from '@/components/operation-hours';
import { FeaturedQuickLinks } from '@/components/quick-links';
import { SearchWidget } from '@/components/search-widget';
import { announcements, news } from '@/data/content';
import { Link, useRouter } from '@/i18n/navigation';
import { useLanguage } from '@/lib/language-context';
export const runtime = 'edge';

export default function Home() {
  const { language, t } = useLanguage();
  const router = useRouter();

  const handleSearch = (query: string) => {
    // Redirect to search page with query
    console.log('Searching for:', query);
    router.push(`/search?q=${encodeURIComponent(query)}`);
  };

  const handleResultSelect = (result: any) => {
    console.log('Selected result:', result);
    // Handle result selection - could navigate to certificate details
  };

  return (
    <>
      {/* Hero Carousel */}
      <HeroCarousel />

      {/* Search Section */}
      <section className="py-12 sm:py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-6 sm:mb-8 text-gray-900">
              {t('search-title', 'Verify Halal Status', 'Sahkan Status Halal')}
            </h2>
            <p className="text-gray-600 mb-8 text-sm sm:text-base">
              {language === 'en'
                ? 'Search for halal certified companies and products in Malaysia'
                : 'Cari syarikat dan produk yang diperakui halal di Malaysia'}
            </p>
            <SearchWidget
              onSearch={handleSearch}
              onResultSelect={handleResultSelect}
            />
          </div>
        </div>
      </section>

      {/* Quick Links */}
      <section className="py-12 sm:py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl sm:text-3xl font-bold text-center mb-8 sm:mb-12 text-gray-900">
            {language === 'en' ? 'Quick Access' : 'Akses Pantas'}
          </h2>
          <FeaturedQuickLinks />
        </div>
      </section>

      {/* Announcements and News */}
      <section className="py-12 sm:py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
            {/* Announcements */}
            <div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 sm:mb-8 gap-2">
                <h2 className="text-xl sm:text-2xl font-bold text-gray-900">
                  {language === 'en'
                    ? 'Latest Announcements'
                    : 'Pengumuman Terkini'}
                </h2>
                <Link
                  href="/announcements"
                  className="text-primary-green hover:text-primary-green-dark font-medium text-sm self-start sm:self-auto"
                >
                  {language === 'en' ? 'View All' : 'Lihat Semua'}
                </Link>
              </div>
              <AnnouncementList
                announcements={announcements.slice(0, 3)}
                variant="compact"
              />
            </div>

            {/* News */}
            <div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 sm:mb-8 gap-2">
                <h2 className="text-xl sm:text-2xl font-bold text-gray-900">
                  {language === 'en' ? 'Latest News' : 'Berita Terkini'}
                </h2>
                <Link
                  href="/news"
                  className="text-primary-green hover:text-primary-green-dark font-medium text-sm self-start sm:self-auto"
                >
                  {language === 'en' ? 'View All' : 'Lihat Semua'}
                </Link>
              </div>
              <NewsList
                news={news.slice(0, 3)}
                variant="compact"
                showImage={false}
              />
            </div>
          </div>
        </div>
      </section>

      {/* System Access and E-Aduan */}
      <section className="py-12 sm:py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8">
            {/* System Access */}
            <div className="lg:col-span-2">
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-6 sm:mb-8">
                {language === 'en' ? 'System MYeHALAL' : 'Sistem MYeHALAL'}
              </h2>
              <SystemAccess />
            </div>

            {/* E-Aduan */}
            <div>
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-6 sm:mb-8">
                {language === 'en' ? 'E-Complaint' : 'E-Aduan'}
              </h2>
              <EAduan variant="card" />
            </div>
          </div>
        </div>
      </section>

      {/* Operation Hours */}
      <section className="py-12 sm:py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <OperationHours />
        </div>
      </section>
    </>
  );
}
