import { type NextRequest, NextResponse } from 'next/server';
// Use Node.js runtime instead of edge for localhost fetch
// export const runtime = 'edge';

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:16001';
const DEFAULT_SITE_ID = process.env.NEXT_PUBLIC_DEFAULT_SITE_ID || '1';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const response = await fetch(
      `${API_BASE_URL}/api/sites/${DEFAULT_SITE_ID}/admin/login`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      },
    );

    const data = await response.json();

    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Admin login proxy error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
