'use client';

export const runtime = 'edge';

import type { BreadcrumbItem } from '@/components/breadcrumb';
import { PageWrapper } from '@/components/page-wrapper';
import { useLanguage } from '@/lib/language-context';

export default function VisionMissionPage() {
  const { language } = useLanguage();

  const breadcrumbs: BreadcrumbItem[] = [
    {
      label: language === 'en' ? 'Home' : 'Laman <PERSON>',
      href: '/',
    },
    {
      label: language === 'en' ? 'Corporate' : 'Korporat',
      href: '/corporate',
    },
    {
      label: language === 'en' ? 'Vision & Mission' : 'Visi & Misi',
      href: '/corporate/vision-mission',
    },
  ];

  return (
    <PageWrapper
      title="Vision & Mission"
      titleBM="Visi & Misi"
      description="Our vision and mission in promoting and maintaining Halal standards in Malaysia and globally."
      descriptionBM="Visi dan misi kami dalam mempromosi dan mengekalkan piawaian Halal di Malaysia dan secara global."
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-8">
        {/* Vision Section */}
        <div className="card">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-green rounded-full mb-4">
              <svg
                className="w-8 h-8 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                />
              </svg>
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {language === 'en' ? 'Our Vision' : 'Visi Kami'}
            </h2>
          </div>
          <div className="prose prose-lg max-w-none text-center">
            <blockquote className="text-xl text-gray-700 italic border-l-4 border-primary-green pl-6 py-4 bg-bg-light-green rounded-r-lg">
              {language === 'en'
                ? '"To be the global leader in Halal certification, ensuring the highest standards of Halal integrity and promoting Malaysia as the world\'s Halal hub."'
                : '"Menjadi pemimpin global dalam pensijilan Halal, memastikan piawaian integriti Halal tertinggi dan mempromosikan Malaysia sebagai hab Halal dunia."'}
            </blockquote>
          </div>
        </div>

        {/* Mission Section */}
        <div className="card">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-green rounded-full mb-4">
              <svg
                className="w-8 h-8 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
                />
              </svg>
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-6">
              {language === 'en' ? 'Our Mission' : 'Misi Kami'}
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">1</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {language === 'en'
                      ? 'Ensure Halal Integrity'
                      : 'Memastikan Integriti Halal'}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'To maintain the highest standards of Halal certification and ensure the integrity of Halal products and services in accordance with Islamic principles.'
                      : 'Mengekalkan piawaian pensijilan Halal tertinggi dan memastikan integriti produk dan perkhidmatan Halal mengikut prinsip Islam.'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">2</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {language === 'en'
                      ? 'Facilitate Industry Growth'
                      : 'Memudahkan Pertumbuhan Industri'}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'To support and facilitate the growth of the Halal industry by providing efficient certification processes and comprehensive guidance.'
                      : 'Menyokong dan memudahkan pertumbuhan industri Halal dengan menyediakan proses pensijilan yang cekap dan panduan menyeluruh.'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">3</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {language === 'en'
                      ? 'Build Consumer Confidence'
                      : 'Membina Keyakinan Pengguna'}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'To build and maintain consumer confidence in Halal-certified products through transparent and reliable certification processes.'
                      : 'Membina dan mengekalkan keyakinan pengguna terhadap produk yang disijilkan Halal melalui proses pensijilan yang telus dan boleh dipercayai.'}
                  </p>
                </div>
              </div>
            </div>
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">4</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {language === 'en'
                      ? 'Promote Global Recognition'
                      : 'Mempromosi Pengiktirafan Global'}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'To promote Malaysian Halal certification globally and establish mutual recognition agreements with international bodies.'
                      : 'Mempromosikan pensijilan Halal Malaysia secara global dan mewujudkan perjanjian pengiktirafan bersama dengan badan antarabangsa.'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">5</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {language === 'en'
                      ? 'Continuous Improvement'
                      : 'Penambahbaikan Berterusan'}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'To continuously improve our certification processes, standards, and services through research, innovation, and stakeholder feedback.'
                      : 'Menambah baik secara berterusan proses pensijilan, piawaian, dan perkhidmatan kami melalui penyelidikan, inovasi, dan maklum balas pihak berkepentingan.'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-primary-green rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">6</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {language === 'en'
                      ? 'Education & Awareness'
                      : 'Pendidikan & Kesedaran'}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'To educate and raise awareness about Halal requirements, standards, and best practices among industry players and consumers.'
                      : 'Mendidik dan meningkatkan kesedaran mengenai keperluan, piawaian, dan amalan terbaik Halal dalam kalangan pemain industri dan pengguna.'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Core Values Section */}
        <div className="card">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-green rounded-full mb-4">
              <svg
                className="w-8 h-8 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                />
              </svg>
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-6">
              {language === 'en' ? 'Our Core Values' : 'Nilai Teras Kami'}
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-6 bg-bg-light-green rounded-lg">
              <div className="w-12 h-12 bg-primary-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h3 className="font-bold text-gray-900 mb-2">
                {language === 'en' ? 'Integrity' : 'Integriti'}
              </h3>
              <p className="text-gray-600 text-sm">
                {language === 'en'
                  ? 'Upholding the highest ethical standards in all our operations'
                  : 'Menjunjung piawaian etika tertinggi dalam semua operasi kami'}
              </p>
            </div>
            <div className="text-center p-6 bg-bg-light-green rounded-lg">
              <div className="w-12 h-12 bg-primary-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
              </div>
              <h3 className="font-bold text-gray-900 mb-2">
                {language === 'en' ? 'Excellence' : 'Kecemerlangan'}
              </h3>
              <p className="text-gray-600 text-sm">
                {language === 'en'
                  ? 'Striving for excellence in service delivery and quality assurance'
                  : 'Berusaha untuk kecemerlangan dalam penyampaian perkhidmatan dan jaminan kualiti'}
              </p>
            </div>
            <div className="text-center p-6 bg-bg-light-green rounded-lg">
              <div className="w-12 h-12 bg-primary-green rounded-full flex items-center justify-center mx-auto mb-4">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                  />
                </svg>
              </div>
              <h3 className="font-bold text-gray-900 mb-2">
                {language === 'en' ? 'Collaboration' : 'Kerjasama'}
              </h3>
              <p className="text-gray-600 text-sm">
                {language === 'en'
                  ? 'Working together with stakeholders to achieve common goals'
                  : 'Bekerjasama dengan pihak berkepentingan untuk mencapai matlamat bersama'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}
