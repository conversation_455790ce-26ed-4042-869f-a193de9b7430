#!/usr/bin/env bun

import { HalalProductScraper } from './src/scraper';
import type { ScrapedProduct } from './src/types';

async function testStreaming() {
  console.log('Testing streaming scraper...');

  const scraper = new HalalProductScraper({
    maxPages: 2, // Just test with 2 pages
    headless: true,
    timeout: 15000,
  });

  let productCount = 0;
  const products: ScrapedProduct[] = [];

  // Listen for product events
  scraper.on('product', (product: ScrapedProduct) => {
    productCount++;
    products.push(product);
    console.log(
      `Product ${productCount}: ${product.companyName} - ${product.productName}`,
    );
  });

  // Listen for finished event
  scraper.on('finished', () => {
    console.log(
      `\nScraping finished! Total products received: ${productCount}`,
    );
    console.log('First 3 products:');
    products.slice(0, 3).forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.companyName}`);
      console.log(`     Product: ${product.productName}`);
      console.log(`     State: ${product.state || 'N/A'}`);
      console.log(`     Expiry: ${product.expiryDate || 'N/A'}`);
      console.log('');
    });
  });

  try {
    await scraper.initialize();
    await scraper.scrapeAllPages();
    await scraper.cleanup();

    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
    await scraper.cleanup();
  }
}

testStreaming();
