'use client';

import {
  Cloud,
  Database,
  Facebook,
  Globe,
  MessageSquare,
  Users,
} from 'lucide-react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

const stats = [
  {
    name: 'Total Sites',
    value: '0',
    icon: Globe,
    description: 'Active sites in the system',
  },
  {
    name: 'Total Users',
    value: '0',
    icon: Users,
    description: 'Registered users across all sites',
  },
  {
    name: 'Collections',
    value: '0',
    icon: Database,
    description: 'Document collections',
  },
  {
    name: 'WhatsApp Configs',
    value: '0',
    icon: MessageSquare,
    description: 'WhatsApp integrations',
  },
  {
    name: 'Messenger Configs',
    value: '0',
    icon: Facebook,
    description: 'Facebook Messenger integrations',
  },
  {
    name: 'S3 Configs',
    value: '0',
    icon: Cloud,
    description: 'S3 storage configurations',
  },
];

export default function DashboardPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Welcome to the Halal Admin Dashboard</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {stats.map((stat) => (
            <Card key={stat.name}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.name}
                </CardTitle>
                <stat.icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common administrative tasks</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="text-sm text-gray-600">
                <p>• Manage sites and their configurations</p>
                <p>• View and manage users across all sites</p>
                <p>• Configure WhatsApp and Messenger integrations</p>
                <p>• Manage document collections and S3 storage</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>System Status</CardTitle>
              <CardDescription>
                Current system health and status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">API Status</span>
                  <span className="text-sm text-green-600">Online</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Database</span>
                  <span className="text-sm text-green-600">Connected</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Storage</span>
                  <span className="text-sm text-green-600">Available</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
