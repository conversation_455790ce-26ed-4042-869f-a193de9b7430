'use client';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from './select';

interface ProviderOption {
  value: string;
  label: string;
}

const PROVIDER_OPTIONS: ProviderOption[] = [
  { value: 'openai', label: 'OpenAI' },
  { value: 'anthropic', label: 'Anthropic' },
  { value: 'google', label: 'Google' },
  { value: 'azure', label: 'Azure OpenAI' },
];

interface ProviderSelectProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  includeAzure?: boolean; // Whether to include Azure option
}

export function ProviderSelect({
  value,
  onValueChange,
  placeholder = 'Select provider',
  disabled = false,
  className,
  includeAzure = true,
}: ProviderSelectProps) {
  // Filter providers based on includeAzure flag
  const filteredProviders = includeAzure
    ? PROVIDER_OPTIONS
    : PROVIDER_OPTIONS.filter((provider) => provider.value !== 'azure');

  return (
    <Select value={value} onValueChange={onValueChange} disabled={disabled}>
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {filteredProviders.map((provider) => (
          <SelectItem key={provider.value} value={provider.value}>
            {provider.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

// Export the provider options for use elsewhere if needed
export { PROVIDER_OPTIONS };
export type { ProviderOption };
