'use client';

import { ChevronDown, ChevronRight, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { mainNavigation } from '@/data/navigation';
import { Link } from '@/i18n/navigation';
import { useLanguage } from '@/lib/language-context';
import { cn } from '@/lib/utils';
import { MobileLanguageToggle } from './language-switcher';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

export function MobileMenu({ isOpen, onClose }: MobileMenuProps) {
  const { language } = useLanguage();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  // Close menu on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  const toggleExpanded = (itemId: string) => {
    setExpandedItems((prev) =>
      prev.includes(itemId)
        ? prev.filter((id) => id !== itemId)
        : [...prev, itemId],
    );
  };

  if (!isOpen) {
    return null;
  }

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden transition-opacity duration-300"
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Menu Panel */}
      <div className="fixed inset-y-0 right-0 w-80 max-w-[85vw] sm:max-w-sm bg-white shadow-xl z-50 lg:hidden">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
            <h2 className="text-lg font-semibold text-gray-900">
              {language === 'en' ? 'Menu' : 'Menu'}
            </h2>
            <div className="flex items-center gap-2">
              <MobileLanguageToggle />
              <button
                type="button"
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors rounded-md hover:bg-gray-200"
                aria-label={language === 'en' ? 'Close menu' : 'Tutup menu'}
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 overflow-y-auto py-4">
            <div className="space-y-1">
              {mainNavigation.map((item) => (
                <div key={item.id}>
                  {item.children ? (
                    // Parent item with children
                    <div>
                      <button
                        type="button"
                        onClick={() => toggleExpanded(item.id)}
                        className="flex items-center justify-between w-full px-4 py-3 text-left text-gray-700 hover:bg-gray-50 transition-colors"
                      >
                        <span className="font-medium">
                          {language === 'bm' ? item.labelBM : item.label}
                        </span>
                        {expandedItems.includes(item.id) ? (
                          <ChevronDown className="w-5 h-5 text-gray-400" />
                        ) : (
                          <ChevronRight className="w-5 h-5 text-gray-400" />
                        )}
                      </button>

                      {/* Submenu */}
                      <div
                        className={cn(
                          'overflow-hidden transition-all duration-200 ease-in-out',
                          expandedItems.includes(item.id)
                            ? 'max-h-96'
                            : 'max-h-0',
                        )}
                      >
                        <div className="bg-gray-50 border-l-2 border-primary-green ml-4">
                          {item.children.map((child) => (
                            <Link
                              key={child.id}
                              href={child.href}
                              onClick={onClose}
                              className="block px-4 py-2 text-sm text-gray-600 hover:text-primary-green hover:bg-white transition-colors"
                            >
                              {language === 'bm' ? child.labelBM : child.label}
                            </Link>
                          ))}
                        </div>
                      </div>
                    </div>
                  ) : (
                    // Simple link
                    <Link
                      href={item.href}
                      onClick={onClose}
                      className="block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary-green transition-colors font-medium"
                    >
                      {language === 'bm' ? item.labelBM : item.label}
                    </Link>
                  )}
                </div>
              ))}
              <Link
                href="/admin"
                onClick={onClose}
                className="block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary-green transition-colors font-medium"
              >
                Admin
              </Link>
            </div>
          </nav>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4">
            <div className="text-xs text-gray-500 text-center">
              © 2025 JAKIM -{' '}
              {language === 'en'
                ? 'All rights reserved'
                : 'Hak cipta terpelihara'}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// Hook for managing mobile menu state
export function useMobileMenu() {
  const [isOpen, setIsOpen] = useState(false);

  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);
  const toggle = () => setIsOpen((prev) => !prev);

  return {
    isOpen,
    open,
    close,
    toggle,
  };
}
