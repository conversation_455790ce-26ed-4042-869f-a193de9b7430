#!/usr/bin/env bun

import { DatabaseService } from './db/service';
import { HalalProductScraper } from './scraper';
import type { CrawlerConfig } from './types';
import { <PERSON>rrorHandler } from './utils/errorHandler';
import { Logger } from './utils/logger';

class HalalProductCrawler {
  private scraper: HalalProductScraper;
  private dbService: DatabaseService;
  private logger: Logger;
  private errorHandler: ErrorHandler;

  constructor(config?: Partial<CrawlerConfig>) {
    this.logger = Logger.getInstance();
    this.errorHandler = new ErrorHandler();
    this.scraper = new HalalProductScraper(config);
    this.dbService = new DatabaseService();
  }

  async setSiteId(siteId: number): Promise<void> {
    await this.dbService.setSiteId(siteId);
    this.logger.info(`Crawler configured for site ID: ${siteId}`);
  }

  async validateEnvironment(): Promise<void> {
    this.logger.info('Validating environment...');

    // Check required environment variables
    this.errorHandler.validateEnvironment(['DATABASE_URL']);

    // Test database connection
    const dbConnected = await this.dbService.testConnection();
    if (!dbConnected) {
      throw new Error('Database connection failed');
    }

    this.logger.success('Environment validation completed');
  }

  async run(): Promise<void> {
    const startTime = Date.now();
    this.logger.info('🚀 Starting Halal Product Crawler...');

    try {
      // Validate environment
      await this.validateEnvironment();

      // Initialize scraper
      await this.errorHandler.withRetry(
        () => this.scraper.initialize(),
        'Browser initialization',
        { maxRetries: 2 },
      );

      // Scrape products and save them as they come
      this.logger.info('📊 Starting product scraping and real-time saving...');

      let totalSaved = 0;
      let totalDuplicates = 0;
      let totalErrors = 0;

      // Set up event listeners for real-time product saving
      this.scraper.on('product', async (product) => {
        const result = await this.errorHandler.safeExecute(
          () => this.dbService.saveProduct(product),
          'Save individual product',
        );

        if (result?.success) {
          if (result.isDuplicate) {
            totalDuplicates++;
          } else {
            totalSaved++;
          }
        } else {
          totalErrors++;
        }
      });

      // Wait for scraping to finish
      const scrapingPromise = new Promise<void>((resolve) => {
        this.scraper.on('finished', () => {
          resolve();
        });
      });

      // Start scraping and wait for completion
      await Promise.all([
        this.errorHandler.withRetry(
          () => this.scraper.scrapeAllPages(),
          'Product scraping',
          { maxRetries: 1, baseDelay: 5000 },
        ),
        scrapingPromise,
      ]);

      if (this.scraper.getStats().totalProducts === 0) {
        this.logger.warn(
          'No products were scraped. Please check the website structure or selectors.',
        );
        return;
      }

      // Prepare save results for statistics
      const saveResults = {
        totalProcessed: this.scraper.getStats().totalProducts,
        successfulSaves: totalSaved,
        duplicates: totalDuplicates,
        errors: totalErrors,
      };

      // Print final statistics
      await this.printFinalStats(saveResults, startTime);
    } catch (error) {
      this.errorHandler.handleError(error, 'Main crawler execution');
      process.exit(1);
    } finally {
      // Cleanup
      await this.errorHandler.safeExecute(
        () => this.scraper.cleanup(),
        'Scraper cleanup',
      );
    }
  }

  private async printFinalStats(
    saveResults: {
      totalProcessed: number;
      successfulSaves: number;
      duplicates: number;
      errors: number;
    },
    startTime: number,
  ): Promise<void> {
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    const scraperStats = this.scraper.getStats();

    this.logger.info('\n' + '='.repeat(60));
    this.logger.info('📈 CRAWLING COMPLETED - FINAL STATISTICS');
    this.logger.info('='.repeat(60));

    this.logger.info(
      `⏱️  Total Duration: ${Math.floor(duration / 60)}m ${duration % 60}s`,
    );
    this.logger.info(`📄 Pages Processed: ${scraperStats.totalPages}`);
    this.logger.info(`✅ Successful Pages: ${scraperStats.successfulPages}`);
    this.logger.info(`❌ Failed Pages: ${scraperStats.failedPages}`);
    this.logger.info(
      `🏷️  Total Products Scraped: ${scraperStats.totalProducts}`,
    );

    if (saveResults) {
      this.logger.info(`💾 Products Saved: ${saveResults.successfulSaves}`);
      this.logger.info(`🔄 Duplicates Skipped: ${saveResults.duplicates}`);
      this.logger.info(`⚠️  Save Errors: ${saveResults.errors}`);
    }

    if (scraperStats.errors.length > 0) {
      this.logger.info('\n🚨 Errors encountered:');
      scraperStats.errors.forEach((error, index) => {
        this.logger.error(`  ${index + 1}. ${error}`);
      });
    }

    // Get current database count
    const totalProductsInDb = await this.dbService.getProductCount();
    this.logger.info(`📊 Total Products in Database: ${totalProductsInDb}`);

    this.logger.info('='.repeat(60));
    this.logger.success('🎉 Crawling process completed successfully!');
    process.exit(0);
  }
}

// CLI execution
async function main() {
  const args = process.argv.slice(2);
  const config: Partial<CrawlerConfig> = {};

  // Parse command line arguments
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i];
    const value = args[i + 1];

    switch (key) {
      case '--max-pages':
        config.maxPages = Number.parseInt(value || '15') || 15;
        break;
      case '--concurrent':
        config.concurrentPages = Number.parseInt(value || '3') || 3;
        break;
      case '--delay':
        config.delayBetweenPages = Number.parseInt(value || '2000') || 2000;
        break;
      case '--headless':
        config.headless = value?.toLowerCase() !== 'false';
        break;
      case '--timeout':
        config.timeout = Number.parseInt(value || '30000') || 30000;
        break;
      case '--help':
        console.log(`
Halal Product Crawler

Usage: bun run src/crawler.ts [options]

Options:
  --max-pages <number>     Maximum pages to scrape (default: 15)
  --concurrent <number>    Concurrent pages (default: 3)
  --delay <number>         Delay between pages in ms (default: 2000)
  --headless <boolean>     Run browser in headless mode (default: true)
  --timeout <number>       Page timeout in ms (default: 30000)
  --help                   Show this help message

Environment Variables:
  DATABASE_URL            PostgreSQL connection string (required)
  MAX_PAGES              Default max pages to scrape
  CONCURRENT_PAGES       Default concurrent pages
  DELAY_BETWEEN_PAGES    Default delay between pages

Examples:
  bun run src/crawler.ts
  bun run src/crawler.ts --max-pages 20 --headless false
  bun run src/crawler.ts --delay 3000 --timeout 45000
        `);
        process.exit(0);
    }
  }

  const crawler = new HalalProductCrawler(config);
  await crawler.run();
}

// Run if this file is executed directly
if (import.meta.main) {
  main().catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { HalalProductCrawler };
