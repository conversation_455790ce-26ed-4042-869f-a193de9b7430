import type { Context } from 'hono';

/**
 * Standardized API response format
 */
export interface StandardApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

/**
 * Create a successful API response for Hono
 */
export function createSuccessResponse<T>(
  c: Context,
  data: T,
  message?: string,
  status: number = 200
): Response {
  const response: StandardApiResponse<T> = {
    success: true,
    data,
    ...(message && { message })
  };
  
  return c.json(response, status);
}

/**
 * Create an error API response for Hono
 */
export function createErrorResponse(
  c: Context,
  error: string,
  message?: string,
  status: number = 500
): Response {
  const response: StandardApiResponse = {
    success: false,
    error,
    ...(message && { message })
  };
  
  return c.json(response, status);
}

/**
 * Handle API errors consistently for Hono
 */
export function handleApiError(
  c: Context,
  error: unknown,
  defaultMessage: string = 'Internal server error',
  status: number = 500
): Response {
  console.error('API Error:', error);
  
  const errorMessage = error instanceof Error ? error.message : defaultMessage;
  
  return createErrorResponse(c, errorMessage, undefined, status);
}

/**
 * Create a successful API response for Express
 */
export function createExpressSuccessResponse<T>(
  data: T,
  message?: string
): { success: boolean; data: T; message?: string } {
  return {
    success: true,
    data,
    ...(message && { message })
  };
}

/**
 * Create an error API response for Express
 */
export function createExpressErrorResponse(
  error: string,
  message?: string
): { success: boolean; error: string; message?: string } {
  return {
    success: false,
    error,
    ...(message && { message })
  };
}
