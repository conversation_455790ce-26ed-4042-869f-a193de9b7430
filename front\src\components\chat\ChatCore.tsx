'use client';

import axios from 'axios';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useRef, useState } from 'react';
import { axiosApi, axiosApiData, processApiResponse } from '@/lib/api-client';
import { useDropzone } from 'react-dropzone';
import ReactMarkdown from 'react-markdown';
import { useVoiceRecording } from '../../hooks/useVoiceRecording';
import { useWebSocket } from '../../lib/websocket';

export interface TextResult {
  text: string;
  type: 'vector' | 'graph' | string;
  document_id?: string | null;
  score: number;
  wordCount: number;
  metadata?: {
    title?: string;
    url?: string;
    source?: string;
    chunk_index?: number;
    document_type?: string;
    [key: string]: any;
  };
}

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'agent';
  content: string;
  timestamp: Date;
  imageUrl?: string;
  agentName?: string;
  sources?: TextResult[];
  audioUrl?: string;
  fileUrl?: string;
  fileName?: string;
}

export interface IntegrationStatus {
  whatsappEnabled: boolean;
  phoneNumber: string | null;
  facebookEnabled: boolean;
  pageId: string | null;
}

export interface WebSocketMessage {
  type: string;
  data: {
    agentName?: string;
    messageId?: string;
    content?: string;
    timestamp?: number;
    audioUrl?: string;
    fileUrl?: string;
    fileName?: string;
    sessionId?: string;
    reason?: string;
    [key: string]: any;
  };
}

export interface ChatCoreProps {
  botSlug?: string;
  onSessionCreated?: (sessionId: string) => void;
  onMessageSent?: (message: Message) => void;
  onMessageReceived?: (message: Message) => void;
  onHandoverRequested?: () => void;
  onHandoverCompleted?: () => void;
  className?: string;
  children: (props: ChatCoreRenderProps) => React.ReactNode;
}

export interface ChatCoreRenderProps {
  // State
  messages: Message[];
  inputText: string;
  isLoading: boolean;
  sessionId: string | null;
  uploadedImage: { url: string; file: File } | null;
  integrationStatus: IntegrationStatus;
  isHandedOver: boolean;
  agentName: string | null;
  showHandoverButton: boolean;
  hasNewMessage: boolean;

  // Voice recording
  isRecording: boolean;
  isProcessingVoice: boolean;
  voiceError: string | null;
  isVoiceSupported: boolean;

  // Drag and drop
  isDragActive: boolean;
  getRootProps: () => any;
  getInputProps: () => any;

  // Actions
  setInputText: (text: string) => void;
  sendMessage: (text?: string, imageUrl?: string) => Promise<void>;
  clearUploadedImage: () => void;
  requestHandover: () => Promise<void>;
  toggleRecording: () => void;
  clearVoiceError: () => void;

  // Refs for custom scroll behavior
  messagesContainerRef: React.RefObject<HTMLDivElement | null>;
  inputRef: React.RefObject<HTMLInputElement | null>;

  // Utilities
  t: (key: string) => string;
}

export function ChatCore({
  botSlug,
  onSessionCreated,
  onMessageSent,
  onMessageReceived,
  onHandoverRequested,
  onHandoverCompleted,
  className,
  children,
}: ChatCoreProps) {
  const t = useTranslations('chat');
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [uploadedImage, setUploadedImage] = useState<{
    url: string;
    file: File;
  } | null>(null);
  const [integrationStatus, setIntegrationStatus] = useState<IntegrationStatus>(
    {
      whatsappEnabled: false,
      phoneNumber: null,
      facebookEnabled: false,
      pageId: null,
    },
  );
  const [isHandedOver, setIsHandedOver] = useState(false);
  const [agentName, setAgentName] = useState<string | null>(null);
  const [showHandoverButton, setShowHandoverButton] = useState(true);
  const [hasNewMessage, setHasNewMessage] = useState(false);

  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Voice recording hook
  const {
    isRecording,
    isProcessing: isProcessingVoice,
    error: voiceError,
    toggleRecording,
    clearError: clearVoiceError,
    isSupported: isVoiceSupported,
  } = useVoiceRecording({
    onTranscription: (text) => {
      setInputText(text);
    },
  });

  // Initialize session
  const initializeSession = useCallback(async () => {
    try {
      const API_BASE_URL =
        process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:16001';

      const url = `${API_BASE_URL}/api/chat/session`;
      const body: any = {};

      if (botSlug) {
        const DEFAULT_SITE_ID = process.env.NEXT_PUBLIC_DEFAULT_SITE_ID || '1';
        // First get bot info
        const botResponse = await fetch(
          `${API_BASE_URL}/api/sites/${DEFAULT_SITE_ID}/bots/slug/${botSlug}`,
        );
        if (botResponse.ok) {
          const botData = await botResponse.json();
          body.botId = botData.id;
        }
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (response.ok) {
        const data = await response.json();
        setSessionId(data.sessionId);
        onSessionCreated?.(data.sessionId);
        return data.sessionId;
      }
      console.error(
        'Failed to create session:',
        response.status,
        response.statusText,
      );
      return null;
    } catch (error) {
      console.error('Error creating session:', error);
      return null;
    }
  }, [botSlug, onSessionCreated]);

  // Load integration status
  const loadIntegrationStatus = useCallback(async () => {
    try {
      const API_BASE_URL =
        process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:16001';

      // Load WhatsApp status
      const whatsappData = await axiosApiData(
        `${API_BASE_URL}/api/chat/whatsapp-status`,
        { method: 'GET' },
        { whatsappEnabled: false, phoneNumber: null }
      );

      // Load Facebook status
      const facebookData = await axiosApiData(
        `${API_BASE_URL}/api/chat/facebook-status`,
        { method: 'GET' },
        { facebookEnabled: false, pageId: null }
      );

      setIntegrationStatus({
        whatsappEnabled: whatsappData?.whatsappEnabled || false,
        phoneNumber: whatsappData?.phoneNumber || null,
        facebookEnabled: facebookData?.facebookEnabled || false,
        pageId: facebookData?.pageId || null,
      });
    } catch (error) {
      console.error('Failed to load integration status:', error);
    }
  }, []);

  // Handle WebSocket messages - memoized to prevent infinite reconnections
  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'session_assigned': {
        setIsHandedOver(true);
        setAgentName(message.data.agentName || null);
        setShowHandoverButton(false);
        // Add system message about agent assignment
        const assignmentMessage: Message = {
          id: Date.now().toString(),
          role: 'assistant',
          content: `You are now connected to ${message.data.agentName || 'an agent'}. They will assist you with your inquiry.`,
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, assignmentMessage]);
        break;
      }

      case 'agent_message': {
        // Add agent message to chat
        const agentMessage: Message = {
          id: message.data.messageId || Date.now().toString(),
          role: 'agent',
          content: message.data.content || '',
          timestamp: new Date(message.data.timestamp || Date.now()),
          agentName: message.data.agentName,
          audioUrl: message.data.audioUrl,
          fileUrl: message.data.fileUrl,
          fileName: message.data.fileName,
        };
        setMessages((prev) => [...prev, agentMessage]);
        onMessageReceived?.(agentMessage);
        break;
      }

      case 'session_completed': {
        setIsHandedOver(false);
        setAgentName(null);
        setShowHandoverButton(true);
        // Add system message about session completion
        const completionMessage: Message = {
          id: Date.now().toString(),
          role: 'assistant',
          content:
            'The agent has ended the session. You can continue chatting with me or request another agent if needed.',
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, completionMessage]);
        onHandoverCompleted?.();
        break;
      }

      default:
        console.log('Unknown WebSocket message type:', message.type);
    }
  }, [onMessageReceived, onHandoverCompleted]);

  // Memoized WebSocket callbacks to prevent infinite reconnections
  const onMessage = useCallback((message: any) => {
    console.log('Received WebSocket message:', message);
    handleWebSocketMessage(message);
  }, [handleWebSocketMessage]);

  const onDisconnect = useCallback(() => {
    console.log('WebSocket disconnected');
  }, []);

  // WebSocket connection for real-time updates
  const { isConnected: wsConnected, sendMessage: sendWsMessage } = useWebSocket(
    `${process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:16001'}/ws`,
    {
      onMessage,
      onConnect: () => {
        console.log('WebSocket connected');
      },
      onDisconnect,
    },
  );

  // Register user connection when WebSocket connects and we have sessionId
  useEffect(() => {
    if (wsConnected && sessionId && sendWsMessage) {
      sendWsMessage({
        type: 'register',
        data: {
          connectionType: 'user',
          sessionId,
        },
      });
    }
  }, [wsConnected, sessionId, sendWsMessage]);

  // Initialize on mount
  useEffect(() => {
    initializeSession();
    loadIntegrationStatus();
  }, [initializeSession, loadIntegrationStatus]);

  // Send message function
  const sendMessage = async (text?: string, imageUrl?: string) => {
    if (!sessionId || (!text && !imageUrl)) {
      return;
    }

    const messageText = text || inputText;
    if (!messageText && !imageUrl) {
      return;
    }

    // Add user message to UI
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: messageText,
      timestamp: new Date(),
      imageUrl,
    };
    setMessages((prev) => [...prev, userMessage]);
    setInputText('');
    setUploadedImage(null);
    setIsLoading(true);
    onMessageSent?.(userMessage);

    try {
      const API_BASE_URL =
        process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:16001';
      let responseData;

      if (imageUrl) {
        // Send image analysis request
        responseData = await axiosApiData(`${API_BASE_URL}/api/chat/image`, {
          method: 'POST',
          data: {
            sessionId,
            imageUrl,
            prompt: messageText || t('imagePrompt'),
          }
        });
      } else {
        // Send text message
        responseData = await axiosApiData(`${API_BASE_URL}/api/chat/message`, {
          method: 'POST',
          data: {
            sessionId,
            message: messageText,
            model: 'gpt-4o-mini', // Default model
          }
        });
      }

      // Add assistant response to UI
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content:
          responseData?.message ||
          responseData?.answer ||
          'No response received',
        timestamp: new Date(),
        sources: responseData?.sources || undefined,
      };
      setMessages((prev) => [...prev, assistantMessage]);
      onMessageReceived?.(assistantMessage);
    } catch (error) {
      console.error('Failed to send message:', error);
      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
      onMessageReceived?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Request handover to human agent
  const requestHandover = async () => {
    if (!sessionId) {
      return;
    }

    try {
      const API_BASE_URL =
        process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:16001';
      const response = await axios.post(
        `${API_BASE_URL}/api/sessions/handover`,
        {
          sessionId,
          reason: 'User requested human assistance',
          priority: 'normal',
        },
      );

      if (response.data.success) {
        setShowHandoverButton(false);
        // Add system message about handover request
        const systemMessage: Message = {
          id: Date.now().toString(),
          role: 'assistant',
          content:
            "I've requested a human agent to assist you. Please wait while we connect you to an available agent.",
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, systemMessage]);
        onHandoverRequested?.();

        // Notify via WebSocket
        if (wsConnected) {
          sendWsMessage({
            type: 'handover_requested',
            data: {
              sessionId,
              reason: 'User requested human assistance',
            },
          });
        }
      }
    } catch (error) {
      console.error('Failed to request handover:', error);
    }
  };

  // File upload handling
  const onDrop = async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) {
      return;
    }

    try {
      const API_BASE_URL =
        process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:16001';
      const formData = new FormData();
      formData.append('file', file);

      const response = await axios.post(
        `${API_BASE_URL}/api/upload`,
        formData,
        {
          headers: { 'Content-Type': 'multipart/form-data' },
        },
      );

      if (response.data.type === 'image') {
        setUploadedImage({ url: response.data.url, file });
      } else if (
        response.data.type === 'audio' &&
        response.data.transcription
      ) {
        setInputText(response.data.transcription);
      }
    } catch (error) {
      console.error('Failed to upload file:', error);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp'],
      'audio/*': ['.mp3', '.wav', '.webm', '.ogg'],
    },
    maxFiles: 1,
  });

  // Clear uploaded image
  const clearUploadedImage = () => {
    setUploadedImage(null);
  };

  // Render props pattern
  const renderProps: ChatCoreRenderProps = {
    // State
    messages,
    inputText,
    isLoading,
    sessionId,
    uploadedImage,
    integrationStatus,
    isHandedOver,
    agentName,
    showHandoverButton,
    hasNewMessage,

    // Voice recording
    isRecording,
    isProcessingVoice,
    voiceError,
    isVoiceSupported,

    // Drag and drop
    isDragActive,
    getRootProps,
    getInputProps,

    // Actions
    setInputText,
    sendMessage,
    clearUploadedImage,
    requestHandover,
    toggleRecording,
    clearVoiceError,

    // Refs
    messagesContainerRef,
    inputRef,

    // Utilities
    t,
  };

  return <div className={className}>{children(renderProps)}</div>;
}
