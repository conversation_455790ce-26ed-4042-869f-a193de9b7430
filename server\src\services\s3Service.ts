import type { Readable } from 'node:stream';
import {
  DeleteO<PERSON>Command,
  GetO<PERSON><PERSON>ommand,
  PutO<PERSON>Command,
  S3Client,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import type { S3Configuration } from '../types';

class S3Service {
  private s3Client: S3Client;
  private bucketName: string;

  constructor(config: S3Configuration) {
    if (!config.accessKeyId || !config.secretAccessKey || !config.bucketName) {
      throw new Error(
        'S3Service: Missing required S3 configuration (accessKeyId, secretAccessKey, bucketName).',
      );
    }

    this.bucketName = config.bucketName;
    this.initializeS3Client(config);
  }

  private initializeS3Client(config: S3Configuration) {
    try {
      this.s3Client = new S3Client({
        credentials: {
          accessKeyId: config.accessKeyId,
          secretAccessKey: config.secretAccessKey,
        },
        region: config.region || 'us-east-1',
        endpoint: config.endpointUrl || undefined,
        forcePathStyle: !!config.endpointUrl, // Required for some S3-compatible services
      });
      console.log('S3Service: AWS SDK v3 initialized successfully');
    } catch (error) {
      console.error('S3Service: Failed to initialize AWS SDK v3:', error);
      throw new Error('S3Service: AWS SDK initialization failed');
    }
  }

  /**
   * Uploads a file to S3.
   * @param fileBuffer Buffer content of the file.
   * @param s3Key The key (path/filename) for the object in S3.
   * @param mimetype The MIME type of the file.
   * @returns Promise resolving to the S3 upload result.
   */
  async uploadFile(
    fileBuffer: Buffer,
    s3Key: string,
    mimetype: string,
  ): Promise<any> {
    if (!this.s3Client) {
      throw new Error('S3Service: AWS SDK not initialized');
    }

    const command = new PutObjectCommand({
      Bucket: this.bucketName,
      Key: s3Key,
      Body: fileBuffer,
      ContentType: mimetype,
      ACL: 'public-read',
    });

    try {
      const result = await this.s3Client.send(command);
      return {
        Location: `https://${this.bucketName}.s3.amazonaws.com/${s3Key}`,
        ETag: result.ETag,
        Bucket: this.bucketName,
        Key: s3Key,
      };
    } catch (error) {
      console.error(
        `S3Service: Error uploading file ${s3Key} to bucket ${this.bucketName}:`,
        error,
      );
      throw error; // Re-throw for controller to handle
    }
  }

  /**
   * Uploads a file stream to S3.
   * @param fileStream Readable stream of the file.
   * @param s3Key The key (path/filename) for the object in S3.
   * @param mimetype The MIME type of the file.
   * @returns Promise resolving to the S3 upload result.
   */
  async uploadFileStream(
    fileStream: Readable,
    s3Key: string,
    mimetype: string,
  ): Promise<any> {
    if (!this.s3Client) {
      throw new Error('S3Service: AWS SDK not initialized');
    }

    const command = new PutObjectCommand({
      Bucket: this.bucketName,
      Key: s3Key,
      Body: fileStream,
      ContentType: mimetype,
      ACL: 'public-read',
    });

    try {
      const result = await this.s3Client.send(command);
      return {
        Location: `https://${this.bucketName}.s3.amazonaws.com/${s3Key}`,
        ETag: result.ETag,
        Bucket: this.bucketName,
        Key: s3Key,
      };
    } catch (error) {
      console.error(
        `S3Service: Error uploading file stream ${s3Key} to bucket ${this.bucketName}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Deletes a file from S3.
   * @param s3Key The key of the object to delete in S3.
   * @returns Promise resolving to the S3 delete result.
   */
  async deleteFile(s3Key: string): Promise<any> {
    if (!this.s3Client) {
      throw new Error('S3Service: AWS SDK not initialized');
    }

    const command = new DeleteObjectCommand({
      Bucket: this.bucketName,
      Key: s3Key,
    });

    try {
      return await this.s3Client.send(command);
    } catch (error) {
      console.error(
        `S3Service: Error deleting file ${s3Key} from bucket ${this.bucketName}:`,
        error,
      );
      throw error; // Re-throw for controller to handle
    }
  }

  /**
   * Generates a pre-signed URL for temporary access to an S3 object (e.g., for downloading).
   * @param s3Key The key of the object in S3.
   * @param expiresSeconds The duration for which the URL is valid (default: 3600 seconds / 1 hour).
   * @returns A pre-signed URL string.
   */
  async getPresignedUrl(s3Key: string, expiresSeconds = 3600): Promise<string> {
    if (!this.s3Client) {
      throw new Error('S3Service: AWS SDK not initialized');
    }

    // Only support getObject operation for now (most common use case)
    const command = new GetObjectCommand({
      Bucket: this.bucketName,
      Key: s3Key,
    });

    try {
      return await getSignedUrl(this.s3Client, command, {
        expiresIn: expiresSeconds,
      });
    } catch (error) {
      console.error(
        `S3Service: Error generating pre-signed URL for ${s3Key}:`,
        error,
      );
      throw error;
    }
  }
}

export default S3Service;
export { S3Service };
