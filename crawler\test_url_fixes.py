#!/usr/bin/env python3
"""
Test script to verify URL handling fixes
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from crawl_website import WebsiteCrawler

def test_url_validation():
    """Test URL validation with recursive patterns"""
    crawler = WebsiteCrawler("https://example.com", max_depth=1, max_pages=10)
    
    test_cases = [
        # Valid URLs
        ("https://example.com/page.php", True),
        ("https://example.com/dir/page.html", True),
        ("https://example.com/", True),
        
        # Invalid recursive URLs
        ("https://example.com/index.php/index.php", False),
        ("https://example.com/page.php/page.php/page.php", False),
        ("https://example.com/dir/file.html/file.html", False),
        ("https://example.com/index.php/annpage.php/index.php", False),  # Non-consecutive duplicates
        
        # Edge cases
        ("https://example.com/very/deep/path/with/many/segments/that/should/be/rejected", False),
        ("", False),
        ("not-a-url", False),
    ]
    
    print("Testing URL validation...")
    for url, expected in test_cases:
        result = crawler.is_valid_url(url)
        status = "✓" if result == expected else "✗"
        print(f"{status} {url} -> {result} (expected {expected})")

def test_safe_urljoin():
    """Test safe URL joining"""
    crawler = WebsiteCrawler("https://example.com", max_depth=1, max_pages=10)
    
    test_cases = [
        # Base URL, relative URL, expected result
        ("https://example.com/index.php", "page.html", "https://example.com/page.html"),
        ("https://example.com/dir/index.php", "../other.html", "https://example.com/other.html"),
        ("https://example.com/index.php", "index.php", ""),  # Should detect recursive pattern
        ("https://example.com/page.php", "page.php", ""),   # Should detect recursive pattern
        ("https://example.com/", "index.php", "https://example.com/index.php"),
        ("https://example.com/dir/", "file.html", "https://example.com/dir/file.html"),
    ]
    
    print("\nTesting safe URL joining...")
    for base, relative, expected in test_cases:
        result = crawler.safe_urljoin(base, relative)
        status = "✓" if result == expected else "✗"
        print(f"{status} {base} + {relative} -> {result}")
        if result != expected:
            print(f"    Expected: {expected}")

def test_normalize_url():
    """Test URL normalization"""
    crawler = WebsiteCrawler("https://example.com", max_depth=1, max_pages=10)
    
    test_cases = [
        # Input URL, expected normalized URL
        ("https://example.com/page.html#fragment", "https://example.com/page.html"),
        ("https://example.com/dir/", "https://example.com/dir"),
        ("https://example.com//double//slash", "https://example.com/double/slash"),
        ("https://example.com/index.php/index.php", "https://example.com/index.php"),
        ("https://example.com/page.php/page.php/page.php", "https://example.com/page.php"),
    ]
    
    print("\nTesting URL normalization...")
    for input_url, expected in test_cases:
        result = crawler.normalize_url(input_url)
        status = "✓" if result == expected else "✗"
        print(f"{status} {input_url} -> {result}")
        if result != expected:
            print(f"    Expected: {expected}")

if __name__ == "__main__":
    test_url_validation()
    test_safe_urljoin()
    test_normalize_url()
    print("\nURL handling tests completed!")
