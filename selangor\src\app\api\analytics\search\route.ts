import { and, count, desc, eq, gte, lte, sql } from 'drizzle-orm';
import { type NextRequest, NextResponse } from 'next/server';
import { getHalalSelangorSiteId } from '@/lib/analytics';
import { db } from '@/lib/db';
import { searchAnalytics } from '@/lib/db/schema';

// Remove edge runtime to use Node.js runtime for database access

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const page = Number.parseInt(searchParams.get('page') || '1');
  const limit = Number.parseInt(searchParams.get('limit') || '50');
  const searchType = searchParams.get('searchType'); // Filter by search type
  const startDate = searchParams.get('startDate'); // ISO date string
  const endDate = searchParams.get('endDate'); // ISO date string
  const hasResults = searchParams.get('hasResults'); // 'true', 'false', or null for all

  try {
    const siteId = getHalalSelangorSiteId();

    // Build where conditions
    const whereConditions = [eq(searchAnalytics.siteId, siteId)];

    if (searchType) {
      whereConditions.push(eq(searchAnalytics.searchType, searchType));
    }

    if (startDate) {
      whereConditions.push(gte(searchAnalytics.createdAt, new Date(startDate)));
    }

    if (endDate) {
      whereConditions.push(lte(searchAnalytics.createdAt, new Date(endDate)));
    }

    if (hasResults !== null) {
      whereConditions.push(
        eq(searchAnalytics.hasResults, hasResults === 'true'),
      );
    }

    // Get total count for pagination
    const totalResults = await db
      .select({ count: count() })
      .from(searchAnalytics)
      .where(and(...whereConditions));

    const total = totalResults[0]?.count || 0;

    // Calculate pagination
    const offset = (page - 1) * limit;
    const hasMore = offset + limit < total;

    // Get paginated results
    const analytics = await db
      .select({
        id: searchAnalytics.id,
        searchQuery: searchAnalytics.searchQuery,
        searchType: searchAnalytics.searchType,
        resultsCount: searchAnalytics.resultsCount,
        hasResults: searchAnalytics.hasResults,
        responseTime: searchAnalytics.responseTime,
        userAgent: searchAnalytics.userAgent,
        ipAddress: searchAnalytics.ipAddress,
        sessionId: searchAnalytics.sessionId,
        userId: searchAnalytics.userId,
        searchFilters: searchAnalytics.searchFilters,
        createdAt: searchAnalytics.createdAt,
      })
      .from(searchAnalytics)
      .where(and(...whereConditions))
      .orderBy(desc(searchAnalytics.createdAt))
      .limit(limit)
      .offset(offset);

    return NextResponse.json({
      analytics,
      pagination: {
        page,
        limit,
        total,
        hasMore,
        totalPages: Math.ceil(total / limit),
      },
      filters: {
        searchType,
        startDate,
        endDate,
        hasResults,
      },
    });
  } catch (error) {
    console.error('Analytics API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics data' },
      { status: 500 },
    );
  }
}
