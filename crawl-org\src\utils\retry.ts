import { Logger } from './logger';

export interface RetryOptions {
  maxAttempts: number;
  delayMs: number;
  backoffMultiplier?: number;
  maxDelayMs?: number;
  retryCondition?: (error: any) => boolean;
}

export class RetryManager {
  /**
   * Retry a function with exponential backoff
   */
  static async retry<T>(
    fn: () => Promise<T>,
    options: RetryOptions,
    context = 'operation',
  ): Promise<T> {
    const {
      maxAttempts,
      delayMs,
      backoffMultiplier = 2,
      maxDelayMs = 50000,
      retryCondition = () => true,
    } = options;

    let lastError: any;
    let currentDelay = delayMs;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const result = await fn();
        if (attempt > 1) {
          Logger.success(`${context} succeeded on attempt ${attempt}`);
        }
        return result;
      } catch (error) {
        lastError = error;

        if (attempt === maxAttempts || !retryCondition(error)) {
          Logger.error(`${context} failed after ${attempt} attempts:`, error);
          throw error;
        }

        Logger.warn(
          `${context} failed on attempt ${attempt}/${maxAttempts}, retrying in ${currentDelay}ms...`,
        );

        await RetryManager.delay(currentDelay);
        currentDelay = Math.min(currentDelay * backoffMultiplier, maxDelayMs);
      }
    }

    throw lastError;
  }

  /**
   * Retry with circuit breaker pattern
   */
  static async retryWithCircuitBreaker<T>(
    fn: () => Promise<T>,
    options: RetryOptions & {
      failureThreshold?: number;
      resetTimeoutMs?: number;
    },
    context = 'operation',
  ): Promise<T> {
    // Simple circuit breaker implementation
    const { failureThreshold = 5, resetTimeoutMs = 60000 } = options;

    // In a real implementation, this would be stored in a shared state
    // For now, we'll just use the regular retry mechanism
    return RetryManager.retry(fn, options, context);
  }

  /**
   * Delay execution
   */
  private static delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Check if error is retryable
   */
  static isRetryableError(error: any): boolean {
    // Network errors
    if (
      error.code === 'ECONNRESET' ||
      error.code === 'ENOTFOUND' ||
      error.code === 'ECONNREFUSED' ||
      error.code === 'ETIMEDOUT'
    ) {
      return true;
    }

    // HTTP errors that are typically retryable
    if (error.response?.status) {
      const status = error.response.status;
      return status >= 500 || status === 429 || status === 408;
    }

    // Puppeteer specific errors
    if (
      error.message?.includes('Navigation timeout') ||
      error.message?.includes('net::ERR_') ||
      error.message?.includes('Protocol error')
    ) {
      return true;
    }

    return false;
  }

  /**
   * Create retry options for different scenarios
   */
  static createRetryOptions(
    scenario: 'network' | 'database' | 'parsing' | 'navigation',
  ): RetryOptions {
    switch (scenario) {
      case 'network':
        return {
          maxAttempts: 3,
          delayMs: 1000,
          backoffMultiplier: 2,
          maxDelayMs: 10000,
          retryCondition: RetryManager.isRetryableError,
        };

      case 'database':
        return {
          maxAttempts: 3,
          delayMs: 500,
          backoffMultiplier: 1.5,
          maxDelayMs: 5000,
          retryCondition: (error) => {
            // Retry on connection errors but not on constraint violations
            return (
              error.code === 'ECONNRESET' ||
              error.code === 'ECONNREFUSED' ||
              error.message?.includes('connection')
            );
          },
        };

      case 'parsing':
        return {
          maxAttempts: 2,
          delayMs: 500,
          backoffMultiplier: 1,
          retryCondition: () => false, // Don't retry parsing errors
        };

      case 'navigation':
        return {
          maxAttempts: 3,
          delayMs: 2000,
          backoffMultiplier: 1.5,
          maxDelayMs: 8000,
          retryCondition: (error) => {
            return (
              error.message?.includes('Navigation timeout') ||
              error.message?.includes('net::ERR_') ||
              error.message?.includes('Protocol error')
            );
          },
        };

      default:
        return {
          maxAttempts: 3,
          delayMs: 1000,
          backoffMultiplier: 2,
          maxDelayMs: 10000,
          retryCondition: RetryManager.isRetryableError,
        };
    }
  }
}

export class ErrorHandler {
  private static errorCounts: Map<string, number> = new Map();
  private static maxErrorsPerType = 10;

  /**
   * Handle and categorize errors
   */
  static handleError(
    error: any,
    context: string,
  ): {
    shouldContinue: boolean;
    errorType: string;
    message: string;
  } {
    const errorType = ErrorHandler.categorizeError(error);
    const errorKey = `${context}:${errorType}`;

    // Track error frequency
    const currentCount = ErrorHandler.errorCounts.get(errorKey) || 0;
    ErrorHandler.errorCounts.set(errorKey, currentCount + 1);

    const shouldContinue = currentCount < ErrorHandler.maxErrorsPerType;

    let message = `${errorType} in ${context}`;
    if (error.message) {
      message += `: ${error.message}`;
    }

    if (!shouldContinue) {
      message += ' (max errors reached for this type)';
    }

    Logger.error(message);

    return {
      shouldContinue,
      errorType,
      message,
    };
  }

  /**
   * Categorize error types
   */
  private static categorizeError(error: any): string {
    if (
      error.code === 'ECONNRESET' ||
      error.code === 'ENOTFOUND' ||
      error.code === 'ECONNREFUSED'
    ) {
      return 'NETWORK_ERROR';
    }

    if (error.message?.includes('Navigation timeout')) {
      return 'NAVIGATION_TIMEOUT';
    }

    if (error.message?.includes('Protocol error')) {
      return 'BROWSER_ERROR';
    }

    if (
      error.message?.includes('duplicate key') ||
      error.message?.includes('constraint')
    ) {
      return 'DATABASE_CONSTRAINT';
    }

    if (error.message?.includes('connection')) {
      return 'DATABASE_CONNECTION';
    }

    if (error.name === 'ValidationError') {
      return 'VALIDATION_ERROR';
    }

    return 'UNKNOWN_ERROR';
  }

  /**
   * Reset error counts
   */
  static resetErrorCounts(): void {
    ErrorHandler.errorCounts.clear();
  }

  /**
   * Get error statistics
   */
  static getErrorStats(): Record<string, number> {
    const stats: Record<string, number> = {};
    ErrorHandler.errorCounts.forEach((count, key) => {
      stats[key] = count;
    });
    return stats;
  }

  /**
   * Check if we should abort due to too many errors
   */
  static shouldAbort(): boolean {
    const totalErrors = Array.from(ErrorHandler.errorCounts.values()).reduce(
      (sum, count) => sum + count,
      0,
    );
    return totalErrors > 50; // Abort if more than 50 total errors
  }
}
