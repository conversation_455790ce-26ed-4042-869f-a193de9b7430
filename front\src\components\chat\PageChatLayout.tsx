'use client';

import { Head<PERSON>, Mic, MicOff, Send, User, X } from 'lucide-react';
import { useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import { useMergeRefs } from 'use-callback-ref';
import type { ChatCoreRenderProps } from './ChatCore';
import { TypingIndicator } from './LoadingSpinner';
import { SourcesDisplay } from './SourcesDisplay';

interface PageChatLayoutProps {
  renderProps: ChatCoreRenderProps;
  botName?: string;
  className?: string;
}

export function PageChatLayout({
  renderProps,
  botName = 'AI Assistant',
  className = '',
}: PageChatLayoutProps) {
  const {
    messages,
    inputText,
    isLoading,
    sessionId,
    uploadedImage,
    integrationStatus,
    isHandedOver,
    agentName,
    showHandoverButton,
    isRecording,
    isProcessingVoice,
    voiceError,
    isVoiceSupported,
    isDragActive,
    getRootProps,
    getInputProps,
    setInputText,
    sendMessage,
    clearUploadedImage,
    requestHandover,
    toggleRecording,
    clearVoiceError,
    messagesContainerRef,
    inputRef,
    t,
  } = renderProps;

  // Merge refs for drag and drop and auto-scroll
  const mergedRef = useMergeRefs([messagesContainerRef, getRootProps().ref]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop =
        messagesContainerRef.current.scrollHeight;
    }
  }, [messages]);

  // Focus input when component mounts
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputText.length > 0 || uploadedImage) {
      sendMessage(inputText, uploadedImage?.url);
    }
  };

  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div className={`flex flex-col h-[calc(100vh-20rem)] ${className}`}>
      {/* Header */}
      <header className="bg-gray-800 text-white p-4 flex-shrink-0">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-xl font-bold">Chat with {botName}</h1>
            <p className="text-gray-300 text-sm">
              Send text, voice messages, or upload images
            </p>
          </div>

          {/* Agent Status & Handover Button */}
          <div className="flex flex-col items-end space-y-2">
            {isHandedOver && agentName ? (
              <div className="flex items-center bg-green-500 bg-opacity-20 border border-green-400 rounded-full px-3 py-1">
                <User className="w-4 h-4 mr-2 text-green-300" />
                <span className="text-xs text-green-100">
                  Connected to {agentName}
                </span>
              </div>
            ) : (
              showHandoverButton && (
                <button
                  type="button"
                  onClick={requestHandover}
                  className="flex items-center bg-orange-500 bg-opacity-20 border border-orange-400 rounded-full px-3 py-1 hover:bg-opacity-30 transition-colors"
                >
                  <Headphones className="w-4 h-4 mr-2 text-orange-300" />
                  <span className="text-xs text-orange-100">Talk to Human</span>
                </button>
              )
            )}
          </div>
        </div>

        {/* Integration Status */}
        {(integrationStatus.whatsappEnabled ||
          integrationStatus.facebookEnabled) && (
          <div className="mt-3 pt-3 border-t border-gray-600">
            <p className="text-gray-300 text-xs mb-2">Also available on:</p>
            <div className="flex flex-wrap gap-2">
              {integrationStatus.whatsappEnabled && (
                <div className="bg-green-500 bg-opacity-20 border border-green-400 rounded-full px-3 py-1 flex items-center">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2" />
                  <span className="text-xs text-green-100">
                    WhatsApp{' '}
                    {integrationStatus.phoneNumber &&
                      `(${integrationStatus.phoneNumber})`}
                  </span>
                </div>
              )}
              {integrationStatus.facebookEnabled && (
                <div className="bg-blue-500 bg-opacity-20 border border-blue-400 rounded-full px-3 py-1 flex items-center">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mr-2" />
                  <span className="text-xs text-blue-100">
                    Facebook Messenger
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </header>

      {/* Messages */}
      <main
        ref={mergedRef}
        className="flex-1 overflow-y-auto p-4 space-y-4 relative"
        {...getRootProps()}
      >
        <input {...getInputProps()} />
        {isDragActive && (
          <div className="absolute inset-0 bg-blue-50 border-2 border-dashed border-blue-300 rounded-lg flex items-center justify-center z-10">
            <p className="text-blue-600 font-medium text-center">
              Drop images or audio files here...
            </p>
          </div>
        )}

        {messages.map((message) => (
          <div
            key={message.id}
            className={`mb-4 ${message.role === 'user' ? 'text-right' : 'text-left'}`}
          >
            <div
              className={`inline-block p-3 rounded-lg max-w-[70%] ${
                message.role === 'user'
                  ? 'bg-blue-500 text-white'
                  : message.role === 'agent'
                    ? 'bg-green-100 text-green-800 border border-green-200'
                    : 'bg-gray-300 text-gray-800'
              }`}
            >
              {message.role === 'agent' && message.agentName && (
                <div className="flex items-center mb-2">
                  <User className="w-4 h-4 mr-2 text-green-600" />
                  <span className="text-sm font-medium text-green-600">
                    {message.agentName}
                  </span>
                </div>
              )}

              {message.imageUrl && (
                <img
                  src={message.imageUrl}
                  alt="Uploaded"
                  className="w-full max-w-64 h-auto object-cover rounded mb-2"
                />
              )}

              <div className="prose prose-sm max-w-none">
                <ReactMarkdown>{message.content}</ReactMarkdown>
              </div>

              {/* Display sources if available */}
              {message.sources && message.sources.length > 0 && (
                <SourcesDisplay sources={message.sources} />
              )}

              <p className="text-xs opacity-70 mt-2">
                {message.timestamp.toLocaleTimeString()}
              </p>
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="mb-4 text-left">
            <div className="inline-block p-3 rounded-lg bg-gray-300">
              <div className="flex items-center space-x-2">
                <TypingIndicator />
                <span>Thinking...</span>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Image Preview */}
      {uploadedImage && (
        <div className="p-4 bg-gray-50 border-t flex-shrink-0">
          <div className="flex items-center space-x-3">
            <img
              src={uploadedImage.url}
              alt="Preview"
              className="w-16 h-16 object-cover rounded"
            />
            <span className="text-sm text-gray-600">Image ready to send</span>
            <button
              type="button"
              onClick={clearUploadedImage}
              className="text-red-500 hover:text-red-700"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Input Area */}
      <footer className="p-4 border-t flex-shrink-0">
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Drag and drop hint */}
          <div className="text-center text-sm text-gray-500">
            {isDragActive
              ? 'Drop files here...'
              : 'Drag & drop images or audio files, or use the input below'}
          </div>

          {/* Input row */}
          <div className="flex space-x-2">
            <input
              ref={inputRef}
              type="text"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyDown={handleKeyPress}
              className="flex-1 border rounded-l-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-800"
              placeholder={t('placeholder') || 'Type your message...'}
              disabled={isLoading}
            />

            <button
              type="button"
              onClick={toggleRecording}
              className={`p-2 rounded transition-colors ${
                !isVoiceSupported
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : isRecording
                    ? 'bg-red-500 hover:bg-red-600 text-white'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
              }`}
              disabled={isLoading || isProcessingVoice || !isVoiceSupported}
              title={
                !isVoiceSupported
                  ? 'Voice recording not supported'
                  : isRecording
                    ? 'Stop recording'
                    : 'Start recording'
              }
            >
              {isRecording ? (
                <MicOff className="w-5 h-5" />
              ) : (
                <Mic className="w-5 h-5" />
              )}
            </button>

            <button
              type="submit"
              disabled={isLoading || (!inputText.trim() && !uploadedImage)}
              className="bg-blue-500 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-bold py-2 px-4 rounded-r-lg transition-colors"
            >
              {isLoading ? 'Sending...' : 'Send'}
            </button>
          </div>

          {/* Voice error display */}
          {voiceError && (
            <div className="text-sm text-red-600 bg-red-50 p-3 rounded border border-red-200">
              <div className="flex items-start justify-between">
                <span className="flex-1">{voiceError}</span>
                <button
                  type="button"
                  onClick={clearVoiceError}
                  className="ml-2 text-red-500 hover:text-red-700"
                  title="Dismiss error"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>
          )}

          {/* Voice processing indicator */}
          {isProcessingVoice && (
            <div className="text-sm text-blue-600 bg-blue-50 p-3 rounded border border-blue-200">
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2" />
                Processing voice recording...
              </div>
            </div>
          )}
        </form>
      </footer>
    </div>
  );
}
