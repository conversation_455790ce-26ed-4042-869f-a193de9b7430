import { Hono } from 'hono';
import chatRoutes from '../chat';

// Create test app
const createTestApp = () => {
  const app = new Hono();
  app.route('/api/chat', chatRoutes);
  return app;
};

describe('Chat Routes', () => {
  let app: Hono;

  beforeEach(() => {
    app = createTestApp();
  });

  describe('POST /session', () => {
    it('should create a new chat session', async () => {
      const response = await app.request('/api/chat/session', {
        method: 'POST',
      });

      expect(response.status).toBe(200);
      const body = await response.json();
      expect(body).toHaveProperty('sessionId');
      expect(body.sessionId).toBeDefined();
      expect(typeof body.sessionId).toBe('string');
      expect(body.sessionId.length).toBeGreaterThan(0);
    });

    it('should create unique session IDs', async () => {
      const response1 = await app.request('/api/chat/session', {
        method: 'POST',
      });
      const response2 = await app.request('/api/chat/session', {
        method: 'POST',
      });

      expect(response1.status).toBe(200);
      expect(response2.status).toBe(200);

      const body1 = await response1.json();
      const body2 = await response2.json();
      expect(body1.sessionId).not.toBe(body2.sessionId);
    });
  });

  describe('POST /message', () => {
    let sessionId: string;

    beforeEach(async () => {
      // Create a session for testing
      const sessionResponse = await app.request('/api/chat/session', {
        method: 'POST',
      });
      const sessionBody = await sessionResponse.json();
      sessionId = sessionBody.sessionId;
    });

    it('should handle basic text messages', async () => {
      const message = {
        sessionId,
        message: 'Hello, how are you?',
        model: 'gpt-4o-mini',
      };

      const response = await app.request('/api/chat/message', {
        method: 'POST',
        body: JSON.stringify(message),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      });

      expect(response.status).toBe(200);
      const body = await response.json();
      expect(body).toHaveProperty('message');
      expect(body).toHaveProperty('sessionId');
      expect(body.sessionId).toBe(sessionId);
      expect(body.message).toBeDefined();
      expect(typeof body.message).toBe('string');
      expect(body.message.length).toBeGreaterThan(0);
    }, 15000);

    it('should handle halal-related messages with knowledge integration', async () => {
      const message = {
        sessionId,
        message: 'Is beef halal in Islam?',
        model: 'gpt-4o-mini',
      };

      const response = await app.request('/api/chat/message', {
        method: 'POST',
        body: JSON.stringify(message),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      });

      expect(response.status).toBe(200);
      const body = await response.json();
      expect(body).toHaveProperty('message');
      expect(body.sessionId).toBe(sessionId);
      expect(body.message).toBeDefined();
      expect(body.message.toLowerCase()).toMatch(/halal|islam|beef/);
    }, 20000);

    it('should handle non-halal messages appropriately', async () => {
      const message = {
        sessionId,
        message: "What's the capital of France?",
        model: 'gpt-4o-mini',
      };

      const response = await app.request('/api/chat/message', {
        method: 'POST',
        body: JSON.stringify(message),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      });

      expect(response.status).toBe(200);
      const body = await response.json();
      expect(body).toHaveProperty('message');
      expect(body.sessionId).toBe(sessionId);
      expect(body.message).toBeDefined();
      expect(body.message.toLowerCase()).toMatch(/paris|france/);
    }, 15000);

    it('should validate required fields', async () => {
      const invalidMessage = {
        // Missing sessionId and message
        model: 'gpt-4o-mini',
      };

      const response = await app.request('/api/chat/message', {
        method: 'POST',
        body: JSON.stringify(invalidMessage),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      });

      expect(response.status).toBe(400);
      const body = await response.json();
      expect(body).toHaveProperty('error');
    });

    it('should handle empty messages', async () => {
      const message = {
        sessionId,
        message: '',
        model: 'gpt-4o-mini',
      };

      const response = await app.request('/api/chat/message', {
        method: 'POST',
        body: JSON.stringify(message),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      });

      expect(response.status).toBe(400);
      const body = await response.json();
      expect(body).toHaveProperty('error');
    });

    it('should handle invalid session ID', async () => {
      const message = {
        sessionId: 'invalid-session-id',
        message: 'Hello',
        model: 'gpt-4o-mini',
      };

      const response = await app.request('/api/chat/message', {
        method: 'POST',
        body: JSON.stringify(message),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      });

      // Should either work (creating new session) or return appropriate error
      const body = await response.json();
      expect(body).toBeDefined();
    });

    it('should maintain conversation context', async () => {
      // Send first message
      const message1 = {
        sessionId,
        message: 'My name is John',
        model: 'gpt-4o-mini',
      };

      const response1 = await app.request('/api/chat/message', {
        method: 'POST',
        body: JSON.stringify(message1),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      });
      expect(response1.status).toBe(200);

      // Send follow-up message
      const message2 = {
        sessionId,
        message: 'What is my name?',
        model: 'gpt-4o-mini',
      };

      const response2 = await app.request('/api/chat/message', {
        method: 'POST',
        body: JSON.stringify(message2),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      });

      expect(response2.status).toBe(200);
      const body = await response2.json();
      expect(body.message.toLowerCase()).toMatch(/john/);
    }, 25000);

    it('should handle different models', async () => {
      const models = ['gpt-4o-mini', 'gpt-4o'];

      for (const model of models) {
        const message = {
          sessionId,
          message: `Hello from ${model}`,
          model,
        };

        const response = await app.request('/api/chat/message', {
          method: 'POST',
          body: JSON.stringify(message),
          headers: new Headers({ 'Content-Type': 'application/json' }),
        });

        // Should work with different models or return appropriate error
        const body = await response.json();
        expect(body).toBeDefined();
      }
    }, 20000);

    it('should handle concurrent messages in same session', async () => {
      const messages = [
        'What is halal food?',
        'Tell me about Islamic prayer',
        'What are the five pillars of Islam?',
      ].map((msg) => ({
        sessionId,
        message: msg,
        model: 'gpt-4o-mini',
      }));

      const promises = messages.map((message) =>
        app.request('/api/chat/message', {
          method: 'POST',
          body: JSON.stringify(message),
          headers: new Headers({ 'Content-Type': 'application/json' }),
        })
      );

      const responses = await Promise.all(promises);

      for (const response of responses) {
        expect(response.status).toBe(200);
        const body = await response.json();
        expect(body.message).toBeDefined();
        expect(body.sessionId).toBe(sessionId);
      }
    }, 30000);
  });

  describe('Error Handling', () => {
    it('should handle malformed JSON', async () => {
      const response = await app.request('/api/chat/message', {
        method: 'POST',
        body: 'invalid json',
        headers: new Headers({ 'Content-Type': 'application/json' }),
      });

      expect(response.status).toBe(400);
      const body = await response.json();
      expect(body).toHaveProperty('error');
    });

    it('should handle very long messages', async () => {
      const sessionResponse = await app.request('/api/chat/session', {
        method: 'POST',
      });
      const sessionBody = await sessionResponse.json();
      const sessionId = sessionBody.sessionId;

      const longMessage = {
        sessionId,
        message: 'A'.repeat(10000), // Very long message
        model: 'gpt-4o-mini',
      };

      const response = await app.request('/api/chat/message', {
        method: 'POST',
        body: JSON.stringify(longMessage),
        headers: new Headers({ 'Content-Type': 'application/json' }),
      });

      // Should handle gracefully, either process or reject with appropriate error
      const body = await response.json();
      expect(body).toBeDefined();
    });
  });
});
