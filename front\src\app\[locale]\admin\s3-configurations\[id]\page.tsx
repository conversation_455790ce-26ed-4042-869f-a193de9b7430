'use client';

export const runtime = 'edge';

import { ArrowLeft, Save } from 'lucide-react';
import { useParams } from 'next/navigation';
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';
import { Link, useRouter } from '@/i18n/navigation';
import { api } from '@/lib/api';
import type {
  S3ConfigurationFull, // To display existing data (admin view)
  S3ConfigurationUpdateRequest,
} from '@/types';
import { UserRole } from '@/types/roles';

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

export default function EditS3ConfigurationPage() {
  const router = useRouter();
  const params = useParams();
  const configId = params.id ? Number.parseInt(params.id as string, 10) : null;

  const [formData, setFormData] = useState<
    Partial<S3ConfigurationUpdateRequest>
  >({
    serviceName: '',
    bucketName: '',
    accessKeyId: '',
    // secretAccessKey is not pre-filled for editing for security
    region: '',
    endpointUrl: '',
  });
  const [originalConfigName, setOriginalConfigName] = useState('');

  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [isAdmin, setIsAdmin] = useState(false);
  const [checkingAuth, setCheckingAuth] = useState(true);

  const fetchConfig = useCallback(async (id: number) => {
    setIsFetching(true);
    try {
      // Admin fetches full config to pre-fill form (except secret)
      const response = await api.admin.getS3ConfigurationById(id);
      // Assuming response.data or response contains the config object
      const configData = (response.data || response) as
        | S3ConfigurationFull
        | undefined;

      if (configData) {
        setOriginalConfigName(configData.serviceName);
        setFormData({
          serviceName: configData.serviceName,
          bucketName: configData.bucketName,
          accessKeyId: configData.accessKeyId,
          region: configData.region || '',
          endpointUrl: configData.endpointUrl || '',
          // DO NOT pre-fill secretAccessKey: configData.secretAccessKey,
        });
      } else {
        setError('S3 Configuration not found.');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch S3 configuration details.');
      console.error(err);
    } finally {
      setIsFetching(false);
    }
  }, []);

  useEffect(() => {
    const checkAdminRoleAndFetchData = async () => {
      setCheckingAuth(true);
      try {
        const meResponse = await api.admin.getMe();
        if (meResponse.user?.role === UserRole.ADMIN) {
          setIsAdmin(true);
          if (configId) {
            fetchConfig(configId);
          } else {
            setError('Configuration ID not found.');
            setIsFetching(false); // Stop fetching if no ID
          }
        } else {
          setError(
            'Access Denied: You do not have permission to edit S3 configurations.',
          );
          setIsFetching(false); // Stop fetching if not admin
        }
      } catch (err) {
        setError('Failed to verify user role or fetch configuration.');
        console.error(err);
        setIsFetching(false);
      } finally {
        setCheckingAuth(false);
      }
    };
    checkAdminRoleAndFetchData();
  }, [configId, fetchConfig]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!configId) {
      setError('Configuration ID is missing.');
      return;
    }
    if (
      !formData.serviceName ||
      !formData.bucketName ||
      !formData.accessKeyId
    ) {
      setError('Service Name, Bucket Name, and Access Key ID are required.');
      return;
    }
    setIsLoading(true);
    setError(null);

    const payload: S3ConfigurationUpdateRequest = {
      serviceName: formData.serviceName,
      bucketName: formData.bucketName,
      accessKeyId: formData.accessKeyId,
      region: formData.region || null,
      endpointUrl: formData.endpointUrl || null,
    };
    // Only include secretAccessKey if user has typed something into the field
    if (formData.secretAccessKey) {
      payload.secretAccessKey = formData.secretAccessKey;
    }

    try {
      await api.admin.updateS3Configuration(configId, payload);
      router.push('/admin/s3-configurations');
    } catch (err: any) {
      setError(
        err.response?.data?.error ||
          err.message ||
          'Failed to update S3 configuration.',
      );
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  if (checkingAuth || (isAdmin && isFetching && configId)) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500" />
      </div>
    );
  }

  if (!isAdmin) {
    const accessError =
      error ||
      'Access Denied: You do not have permission to edit S3 configurations.';
    return (
      <div className="container mx-auto p-4">
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
          role="alert"
        >
          <strong className="font-bold">Error: </strong>{' '}
          <span className="block sm:inline">{accessError}</span>
        </div>
        <Link
          href="/admin/s3-configurations"
          className="text-blue-500 hover:text-blue-700 inline-flex items-center"
        >
          <ArrowLeft size={18} className="mr-1" /> Back to S3 Configurations
        </Link>
      </div>
    );
  }

  if (error && isAdmin && !formData.serviceName) {
    // Error likely from fetching, and form data isn't populated
    return (
      <div className="container mx-auto p-4">
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
          role="alert"
        >
          <strong className="font-bold">Error fetching data: </strong>{' '}
          <span className="block sm:inline">{error}</span>
        </div>
        <Link
          href="/admin/s3-configurations"
          className="text-blue-500 hover:text-blue-700 inline-flex items-center"
        >
          <ArrowLeft size={18} className="mr-1" /> Back to S3 Configurations
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="mb-6">
        <Link
          href="/admin/s3-configurations"
          className="text-blue-500 hover:text-blue-700 inline-flex items-center"
        >
          <ArrowLeft size={18} className="mr-1" />
          Back to S3 Configurations
        </Link>
      </div>
      <h1 className="text-2xl md:text-3xl font-semibold text-gray-800 mb-6">
        Edit S3 Configuration: {originalConfigName}
      </h1>

      <form
        onSubmit={handleSubmit}
        className="bg-white shadow-md rounded-lg p-6 md:p-8"
      >
        {error &&
          isAdmin && ( // For form submission errors
            <div
              className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6"
              role="alert"
            >
              <strong className="font-bold">Error: </strong>{' '}
              <span className="block sm:inline">{error}</span>
            </div>
          )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="mb-4">
            <label
              htmlFor="serviceName"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Service Name*
            </label>
            <input
              type="text"
              name="serviceName"
              id="serviceName"
              value={formData.serviceName || ''}
              onChange={handleInputChange}
              required
              disabled={isLoading}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div className="mb-4">
            <label
              htmlFor="bucketName"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Bucket Name*
            </label>
            <input
              type="text"
              name="bucketName"
              id="bucketName"
              value={formData.bucketName || ''}
              onChange={handleInputChange}
              required
              disabled={isLoading}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        <div className="mb-4">
          <label
            htmlFor="accessKeyId"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Access Key ID*
          </label>
          <input
            type="text"
            name="accessKeyId"
            id="accessKeyId"
            value={formData.accessKeyId || ''}
            onChange={handleInputChange}
            required
            disabled={isLoading}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div className="mb-4">
          <label
            htmlFor="secretAccessKey"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            New Secret Access Key (optional)
          </label>
          <input
            type="password"
            name="secretAccessKey"
            id="secretAccessKey"
            value={formData.secretAccessKey || ''}
            onChange={handleInputChange}
            placeholder="Leave blank to keep current secret"
            disabled={isLoading}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="mb-4">
            <label
              htmlFor="region"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Region (optional)
            </label>
            <input
              type="text"
              name="region"
              id="region"
              value={formData.region || ''}
              onChange={handleInputChange}
              disabled={isLoading}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div className="mb-4">
            <label
              htmlFor="endpointUrl"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Endpoint URL (optional)
            </label>
            <input
              type="text"
              name="endpointUrl"
              id="endpointUrl"
              value={formData.endpointUrl || ''}
              onChange={handleInputChange}
              disabled={isLoading}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        <div className="flex items-center justify-end mt-6">
          <button
            type="submit"
            className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg inline-flex items-center transition duration-150 ease-in-out disabled:opacity-50"
            disabled={isLoading || !isAdmin}
          >
            <Save size={18} className="mr-2" />
            {isLoading ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
}
