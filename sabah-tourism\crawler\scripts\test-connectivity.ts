#!/usr/bin/env tsx

import { Stagehand } from '@browserbasehq/stagehand';
import chalk from 'chalk';

async function testConnectivity() {
  console.log(chalk.blue('🔍 Testing connectivity to various sites...'));

  const stagehand = new Stagehand({
    env: 'LOCAL',
    headless: false, // Keep visible for debugging
    verbose: 1, // Enable verbose logging
    debugDom: true, // Enable DOM debugging
  });

  try {
    await stagehand.init();
    const page = stagehand.page;

    // Test sites with different characteristics
    const testSites = [
      {
        name: 'Google (Basic connectivity)',
        url: 'https://www.google.com',
        timeout: 10000,
      },
      {
        name: 'YouTube (Google property with popups)',
        url: 'https://www.youtube.com',
        timeout: 15000,
      },
      {
        name: '<PERSON><PERSON><PERSON> (Target site)',
        url: 'https://www.douyin.com',
        timeout: 30000,
      },
      {
        name: 'TikTok (Alternative)',
        url: 'https://www.tiktok.com',
        timeout: 20000,
      },
    ];

    for (const site of testSites) {
      console.log(chalk.yellow(`\n📡 Testing: ${site.name}`));
      console.log(chalk.gray(`URL: ${site.url}`));

      try {
        const startTime = Date.now();

        await page.goto(site.url, {
          waitUntil: 'domcontentloaded', // Less strict than networkidle
          timeout: site.timeout,
        });

        const loadTime = Date.now() - startTime;
        console.log(chalk.green(`✅ Success! Loaded in ${loadTime}ms`));

        // Try to get page title
        try {
          const title = await page.title();
          console.log(chalk.cyan(`   Title: ${title}`));
        } catch (e) {
          console.log(chalk.yellow('   Could not get title'));
        }

        // Check for common popup indicators
        try {
          await page.act(
            'Look for any popup, cookie banner, or modal and describe what you see',
          );
          console.log(chalk.blue('   Popup check completed'));
        } catch (e) {
          console.log(
            chalk.yellow('   No popups detected or AI action failed'),
          );
        }

        // Wait a bit before next test
        await new Promise((resolve) => setTimeout(resolve, 2000));
      } catch (error) {
        console.log(chalk.red(`❌ Failed: ${error.message}`));

        if (error.message.includes('Timeout')) {
          console.log(
            chalk.yellow(
              '   This suggests the site may be blocking automated access or is slow',
            ),
          );
        } else if (error.message.includes('net::ERR_')) {
          console.log(
            chalk.yellow('   This suggests a network connectivity issue'),
          );
        }
      }
    }
  } catch (error) {
    console.error(chalk.red(`Failed to initialize browser: ${error.message}`));
  } finally {
    try {
      await stagehand.close();
      console.log(chalk.blue('\n🔚 Browser closed'));
    } catch (e) {
      // Ignore cleanup errors
    }
  }
}

// Run the test
testConnectivity().catch(console.error);
