#!/bin/bash

echo "🧪 Testing Kafka Client with RedPanda"
echo "======================================"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Check if RedPanda is running
echo "🔍 Checking RedPanda connection..."
if ! nc -z localhost 19092; then
    echo "❌ RedPanda is not running on port 19092"
    echo "   Please start with: docker-compose up -d panda"
    exit 1
fi

echo "✅ RedPanda is running"

# Test sequence
echo "🚀 Starting Kafka tests..."

echo "1️⃣ Creating topic..."
tsx kafka-client.ts create test-topic

echo "2️⃣ Listing topics..."
tsx kafka-client.ts list

echo "3️⃣ Publishing messages..."
tsx kafka-client.ts publish test-topic

echo "4️⃣ Running full test (publish + subscribe)..."
echo "   This will run for 15 seconds..."
tsx kafka-client.ts test test-topic

echo "5️⃣ Running unit tests..."
npm run test:kafka

echo "✅ All tests completed!"
