'use client';

import { Globe } from 'lucide-react';
import { useLanguage } from '@/lib/language-context';
import { cn } from '@/lib/utils';

interface LanguageSwitcherProps {
  className?: string;
  variant?: 'button' | 'dropdown';
}

export function LanguageSwitcher({
  className,
  variant = 'button',
}: LanguageSwitcherProps) {
  const { language, setLanguage } = useLanguage();

  const toggleLanguage = () => {
    setLanguage(language === 'en' ? 'bm' : 'en');
  };

  if (variant === 'button') {
    return (
      <button
        type="button"
        onClick={toggleLanguage}
        className={cn(
          'flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors',
          'text-gray-600 hover:text-primary-green hover:bg-bg-light-green',
          'focus:outline-none focus:ring-2 focus:ring-primary-green focus:ring-offset-2',
          className,
        )}
        aria-label={`Switch to ${language === 'en' ? 'Bahasa Malaysia' : 'English'}`} // cspell:ignore Bahasa
      >
        <Globe className="w-4 h-4" />
        <span className="font-semibold">{language === 'en' ? 'EN' : 'BM'}</span>
      </button>
    );
  }

  return (
    <div className={cn('relative group', className)}>
      <button
        type="button"
        className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-green focus:ring-offset-2"
        aria-label={`Current language: ${language === 'en' ? 'English' : 'Bahasa Malaysia'}`}
      >
        <Globe className="w-4 h-4" />
        <span>{language === 'en' ? 'English' : 'Bahasa Malaysia'}</span>
        <svg
          className="w-4 h-4 ml-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-label="Dropdown icon for language selection"
        >
          <title>Dropdown icon for language selection</title>
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>
      <div className="absolute left-0 mt-2 w-48 bg-white border border-gray-300 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
        <button
          type="button"
          onClick={() => setLanguage('en')}
          className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-t-md"
          disabled={language === 'en'}
        >
          English
        </button>
        <button
          type="button"
          onClick={() => setLanguage('bm')}
          className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-b-md"
          disabled={language === 'bm'}
        >
          Bahasa Malaysia
        </button>
      </div>
    </div>
  );
}

// Language indicator component for showing current language
export function LanguageIndicator({ className }: { className?: string }) {
  const { language } = useLanguage();

  return (
    <div
      className={cn('flex items-center gap-2 text-sm text-gray-600', className)}
    >
      <Globe className="w-4 h-4" />
      <span className="font-medium">
        {language === 'en' ? 'English' : 'Bahasa Malaysia'}
      </span>
    </div>
  );
}

// Compact language toggle for mobile
export function MobileLanguageToggle({ className }: { className?: string }) {
  const { language, setLanguage } = useLanguage();

  return (
    <button
      type="button"
      onClick={() => setLanguage(language === 'en' ? 'bm' : 'en')}
      className={cn(
        'inline-flex items-center justify-center w-10 h-10 rounded-full',
        'bg-gray-100 text-gray-600 hover:bg-primary-green hover:text-white',
        'transition-colors duration-200',
        'focus:outline-none focus:ring-2 focus:ring-primary-green focus:ring-offset-2',
        className,
      )}
      aria-label={`Switch to ${language === 'en' ? 'Bahasa Malaysia' : 'English'}`}
    >
      <span className="text-sm font-bold">
        {language === 'en' ? 'BM' : 'EN'}
      </span>
    </button>
  );
}
