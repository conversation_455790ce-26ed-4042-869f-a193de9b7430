'use client';

import {
  ArrowDownRight,
  ArrowUpRight,
  Clock,
  Minus,
  Search,
  Target,
  TrendingDown,
  TrendingUp,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { AdminLayout } from '@/components/admin/AdminLayout';
import {
  generateDashboardOverview,
  generateGeographicData,
  generateTemporalData,
  generateTopKeywords,
} from '@/lib/mockData';

export default function AdminDashboard() {
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [topKeywords, setTopKeywords] = useState<any[]>([]);
  const [geoData, setGeoData] = useState<any[]>([]);
  const [timeData, setTimeData] = useState<any>(null);

  useEffect(() => {
    // Generate mock data
    setDashboardData(generateDashboardOverview());
    setTopKeywords(generateTopKeywords().slice(0, 5));
    setGeoData(generateGeographicData().slice(0, 5));
    setTimeData(generateTemporalData());
  }, []);

  if (!dashboardData) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto" />
            <p className="mt-4 text-gray-600">Loading dashboard...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <ArrowUpRight className="h-4 w-4 text-green-600" />;
      case 'down':
        return <ArrowDownRight className="h-4 w-4 text-red-600" />;
      default:
        return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Analytics Dashboard
          </h1>
          <p className="text-gray-600">
            Halal Selangor Search Analytics Overview
          </p>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Searches
                </p>
                <p className="text-3xl font-bold text-gray-900">
                  {dashboardData.totalSearches.toLocaleString()}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Search className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              {getTrendIcon(dashboardData.recentTrends.searchVolume)}
              <span
                className={`ml-1 text-sm font-medium ${getTrendColor(dashboardData.recentTrends.searchVolume)}`}
              >
                {dashboardData.recentTrends.searchVolumeChange}% vs last month
              </span>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Click Rate</p>
                <p className="text-3xl font-bold text-gray-900">
                  {(dashboardData.successRate * 90).toFixed(1)}%
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Target className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              {getTrendIcon(dashboardData.recentTrends.successRate)}
              <span
                className={`ml-1 text-sm font-medium ${getTrendColor(dashboardData.recentTrends.successRate)}`}
              >
                {dashboardData.recentTrends.successRateChange}% vs last month
              </span>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Avg Response Time
                </p>
                <p className="text-3xl font-bold text-gray-900">
                  {dashboardData.avgResponseTime.toFixed(1)}s
                </p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <ArrowDownRight className="h-4 w-4 text-green-600" />
              <span className="ml-1 text-sm font-medium text-green-600">
                12% faster than industry avg
              </span>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Successful Searches
                </p>
                <p className="text-3xl font-bold text-gray-900">
                  {dashboardData.successfulSearches.toLocaleString()}
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <ArrowUpRight className="h-4 w-4 text-green-600" />
              <span className="ml-1 text-sm font-medium text-green-600">
                8% increase this month
              </span>
            </div>
          </div>
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Search Queries */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Top Search Queries
            </h3>
            <div className="space-y-4">
              {topKeywords.map((keyword, index) => (
                <div
                  key={keyword.query}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-500 w-6">
                      #{index + 1}
                    </span>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {keyword.query}
                      </p>
                      <p className="text-xs text-gray-500 capitalize">
                        {keyword.category}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {keyword.count}
                    </p>
                    <div className="flex items-center">
                      {getTrendIcon(keyword.trend)}
                      <span
                        className={`ml-1 text-xs ${getTrendColor(keyword.trend)}`}
                      >
                        {keyword.trendPercentage}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Search Categories */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Search Categories
            </h3>
            <div className="space-y-4">
              {dashboardData.topCategories.map(
                (category: any, index: number) => (
                  <div
                    key={category.category}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className={`w-3 h-3 rounded-full ${
                          index === 0
                            ? 'bg-blue-500'
                            : index === 1
                              ? 'bg-green-500'
                              : index === 2
                                ? 'bg-yellow-500'
                                : 'bg-purple-500'
                        }`}
                      />
                      <span className="text-sm font-medium text-gray-900">
                        {category.category}
                      </span>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        {category.count}
                      </p>
                      <p className="text-xs text-gray-500">
                        {category.percentage}%
                      </p>
                    </div>
                  </div>
                ),
              )}
            </div>
          </div>
        </div>

        {/* Geographic and Temporal Data */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Locations */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Top Search Locations
            </h3>
            <div className="space-y-3">
              {geoData.map((location, index) => (
                <div
                  key={location.location}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-500 w-6">
                      #{index + 1}
                    </span>
                    <span className="text-sm font-medium text-gray-900">
                      {location.location}
                    </span>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {location.searchCount}
                    </p>
                    <p className="text-xs text-gray-500">
                      {location.percentage}%
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Peak Hours */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Peak Search Hours
            </h3>
            <div className="space-y-3">
              {timeData?.peakHours
                .slice(0, 5)
                .map((hour: any, index: number) => (
                  <div
                    key={hour.hour}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-sm font-medium text-gray-500 w-6">
                        #{index + 1}
                      </span>
                      <span className="text-sm font-medium text-gray-900">
                        {hour.hour}:00 - {hour.hour + 1}:00
                      </span>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        {hour.count} searches
                      </p>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
