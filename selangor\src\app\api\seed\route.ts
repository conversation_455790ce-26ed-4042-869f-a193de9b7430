import { NextResponse } from 'next/server';
import { db, news, pages, people } from '@/lib/db';

// Remove edge runtime to use Node.js runtime for database access

export async function POST() {
  try {
    // Seed pages
    const pageData = [
      {
        title: 'Home',
        slug: 'home',
        content:
          'Welcome to Halal International Selangor - Your trusted partner for halal certification and services.',
      },
      {
        title: 'About Us',
        slug: 'about-us',
        content:
          'Halal International Selangor is dedicated to providing comprehensive halal certification services to ensure the highest standards of halal compliance.',
      },
      {
        title: 'Our People',
        slug: 'our-people',
        content:
          'Meet our dedicated team of halal certification experts and professionals.',
      },
      {
        title: 'Contact Us',
        slug: 'contact-us',
        content: 'Get in touch with us for all your halal certification needs.',
      },
    ];

    await db.insert(pages).values(pageData);

    // Seed sample news/posts
    const newsData = [
      {
        title: 'New Halal Certification Guidelines Released',
        slug: 'new-halal-certification-guidelines',
        content:
          'We are pleased to announce the release of updated halal certification guidelines to ensure compliance with the latest standards.',
      },
      {
        title: 'Halal Food Industry Conference 2024',
        slug: 'halal-food-industry-conference-2024',
        content:
          'Join us at the upcoming Halal Food Industry Conference 2024 to learn about the latest developments in halal certification.',
      },
    ];

    await db.insert(news).values(newsData);

    // Seed sample people
    const peopleData = [
      {
        name: 'Dr. Ahmad Hassan',
        role: 'Chief Executive Officer',
        bio: 'Dr. Ahmad Hassan leads our organization with over 20 years of experience in halal certification and Islamic jurisprudence.',
        slug: 'dr-ahmad-hassan',
      },
      {
        name: 'Fatimah Abdullah',
        role: 'Head of Certification',
        bio: 'Fatimah oversees all certification processes and ensures compliance with international halal standards.',
        slug: 'fatimah-abdullah',
      },
    ];

    await db.insert(people).values(peopleData);

    return NextResponse.json({
      message: 'Database seeded successfully',
      seeded: {
        pages: pageData.length,
        news: newsData.length,
        people: peopleData.length,
      },
    });
  } catch (error) {
    console.error('Seeding error:', error);
    return NextResponse.json(
      { error: 'Failed to seed database' },
      { status: 500 },
    );
  }
}
