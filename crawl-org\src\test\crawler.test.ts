import { afterAll, beforeAll, describe, expect, test } from 'bun:test';
import { DatabaseService } from '../database/service';
import type { ScrapedCompany } from '../types';
import { RetryManager } from '../utils/retry';
import { <PERSON>Quality<PERSON><PERSON><PERSON>, DataValidator } from '../utils/validation';

describe('Halal Crawler Tests', () => {
  let dbService: DatabaseService;

  beforeAll(async () => {
    // Initialize database service
    dbService = new DatabaseService();
  });

  afterAll(async () => {
    // Cleanup if needed
  });

  describe('Database Service', () => {
    test('should connect to database', async () => {
      const isConnected = await dbService.testConnection();
      expect(isConnected).toBe(true);
    });

    test('should handle company search', async () => {
      const companies = await dbService.searchCompaniesByName(1, 'test', 10);
      expect(Array.isArray(companies)).toBe(true);
    });

    test('should get company count', async () => {
      const count = await dbService.getCompanyCount(1);
      expect(typeof count).toBe('number');
      expect(count).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Data Validation', () => {
    test('should validate valid company data', () => {
      const validCompany: ScrapedCompany = {
        companyName: 'Test Company Sdn Bhd',
        address: '123 Test Street, Kuala Lumpur',
        phone: '+60123456789',
        email: '<EMAIL>',
        state: 'Kuala Lumpur',
        country: 'Malaysia',
        postcode: '50000',
        website: 'https://www.testcompany.com',
      };

      const result = DataValidator.validateCompany(validCompany);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should reject invalid company data', () => {
      const invalidCompany: ScrapedCompany = {
        companyName: 'A', // Too short
        email: 'invalid-email', // Invalid format
        phone: 'abc123', // Invalid format
        postcode: '123', // Invalid Malaysian postcode
        website: 'not-a-url', // Invalid URL
      };

      const result = DataValidator.validateCompany(invalidCompany);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should generate consistent data hash', () => {
      const company: ScrapedCompany = {
        companyName: 'Test Company',
        address: '123 Test Street',
        phone: '+60123456789',
        email: '<EMAIL>',
      };

      const hash1 = DataValidator.generateDataHash(company);
      const hash2 = DataValidator.generateDataHash(company);

      expect(hash1).toBe(hash2);
      expect(hash1).toHaveLength(64); // SHA-256 hash length
    });

    test('should detect duplicate candidates', () => {
      const company1: ScrapedCompany = {
        companyName: 'ABC Company Sdn Bhd',
        address: '123 Main Street',
        phone: '+60123456789',
      };

      const company2: ScrapedCompany = {
        companyName: 'ABC Company Sdn Bhd',
        address: '123 Main Street',
        phone: '+60123456789',
      };

      const isDuplicate = DataValidator.isDuplicateCandidate(
        company1,
        company2,
      );
      expect(isDuplicate).toBe(true);
    });

    test('should normalize company data', () => {
      const company: ScrapedCompany = {
        companyName: '  Test Company  ',
        address: 'Multiple   spaces   here',
        email: '<EMAIL>',
        website: 'www.testcompany.com',
      };

      const normalized = DataValidator.normalizeCompany(company);

      expect(normalized.companyName).toBe('Test Company');
      expect(normalized.address).toBe('Multiple spaces here');
      expect(normalized.email).toBe('<EMAIL>');
      expect(normalized.website).toBe('https://www.testcompany.com');
    });
  });

  describe('Data Quality Checker', () => {
    test('should assess data quality', () => {
      const companies: ScrapedCompany[] = [
        {
          companyName: 'Complete Company',
          address: '123 Street',
          phone: '+60123456789',
          email: '<EMAIL>',
          state: 'Selangor',
          category: 'Food',
        },
        {
          companyName: 'Incomplete Company',
          // Missing other fields
        },
        {
          companyName: '', // Invalid
          email: 'invalid-email', // Invalid
        },
      ];

      const quality = DataQualityChecker.assessDataQuality(companies);

      expect(quality.totalRecords).toBe(3);
      expect(quality.validRecords).toBe(2); // First two should be valid
      expect(quality.invalidRecords).toBe(1);
      expect(quality.completenessScore).toBeGreaterThan(0);
      expect(quality.qualityIssues.length).toBeGreaterThan(0);
    });
  });

  describe('Retry Manager', () => {
    test('should retry failed operations', async () => {
      let attempts = 0;
      const maxAttempts = 3;

      const failingFunction = async () => {
        attempts++;
        if (attempts < maxAttempts) {
          throw new Error('Temporary failure');
        }
        return 'success';
      };

      const result = await RetryManager.retry(
        failingFunction,
        { maxAttempts, delayMs: 100 },
        'test-operation',
      );

      expect(result).toBe('success');
      expect(attempts).toBe(maxAttempts);
    });

    test('should identify retryable errors', () => {
      const retryableErrors = [
        { code: 'ECONNRESET' },
        { code: 'ENOTFOUND' },
        { response: { status: 500 } },
        { response: { status: 429 } },
        { message: 'Navigation timeout' },
      ];

      const nonRetryableErrors = [
        { response: { status: 404 } },
        { response: { status: 401 } },
        { message: 'Validation error' },
      ];

      retryableErrors.forEach((error) => {
        expect(RetryManager.isRetryableError(error)).toBe(true);
      });

      nonRetryableErrors.forEach((error) => {
        expect(RetryManager.isRetryableError(error)).toBe(false);
      });
    });
  });

  describe('Integration Tests', () => {
    test('should save and retrieve companies', async () => {
      const testCompanies: ScrapedCompany[] = [
        {
          companyName: `Test Company ${Date.now()}`,
          address: '123 Test Street',
          phone: '+60123456789',
          email: '<EMAIL>',
          state: 'Selangor',
          category: 'Food',
          country: 'Malaysia',
        },
      ];

      const saveResult = await dbService.saveCompanies(testCompanies, 1);

      expect(saveResult.saved).toBeGreaterThan(0);
      expect(saveResult.errors).toBe(0);

      // Search for the saved company
      const searchResults = await dbService.searchCompaniesByName(
        1,
        testCompanies[0].companyName!,
        1,
      );

      expect(searchResults.length).toBeGreaterThan(0);
      expect(searchResults[0].companyName).toBe(testCompanies[0].companyName);
    });

    test('should detect and handle duplicates', async () => {
      const duplicateCompany: ScrapedCompany = {
        companyName: `Duplicate Test ${Date.now()}`,
        address: '456 Duplicate Street',
        phone: '+60987654321',
        email: '<EMAIL>',
      };

      // Save the same company twice
      const firstSave = await dbService.saveCompanies([duplicateCompany], 1);
      const secondSave = await dbService.saveCompanies([duplicateCompany], 1);

      expect(firstSave.saved).toBe(1);
      expect(firstSave.duplicates).toBe(0);

      expect(secondSave.saved).toBe(0);
      expect(secondSave.duplicates).toBe(1);
    });
  });
});
