'use client';

import { AlertCircle, CheckCircle, Loader2, Send } from 'lucide-react';
import { useState } from 'react';
import { useLanguage } from '@/lib/language-context';

interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  company: string;
  subject: string;
  category: string;
  message: string;
}

interface ContactFormProps {
  onSubmit?: (data: ContactFormData) => Promise<void>;
  className?: string;
  variant?: 'default' | 'compact';
}

export function ContactForm({
  onSubmit,
  className,
  variant = 'default',
}: ContactFormProps) {
  const { language } = useLanguage();
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    subject: '',
    category: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<
    'idle' | 'success' | 'error'
  >('idle');
  const [errors, setErrors] = useState<Partial<ContactFormData>>({});

  const categories = [
    {
      value: 'general',
      label: language === 'en' ? 'General Inquiry' : 'Pertanyaan Am',
    },
    {
      value: 'certification',
      label: language === 'en' ? 'Certification Process' : 'Proses Pensijilan',
    },
    {
      value: 'technical',
      label: language === 'en' ? 'Technical Support' : 'Sokongan Teknikal',
    },
    { value: 'complaint', label: language === 'en' ? 'Complaint' : 'Aduan' },
    {
      value: 'media',
      label: language === 'en' ? 'Media Inquiry' : 'Pertanyaan Media',
    },
    {
      value: 'partnership',
      label: language === 'en' ? 'Partnership' : 'Perkongsian',
    },
  ];

  const validateForm = (): boolean => {
    const newErrors: Partial<ContactFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name =
        language === 'en' ? 'Name is required' : 'Nama diperlukan';
    }

    if (!formData.email.trim()) {
      newErrors.email =
        language === 'en' ? 'Email is required' : 'E-mel diperlukan';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email =
        language === 'en' ? 'Invalid email format' : 'Format e-mel tidak sah';
    }

    if (!formData.subject.trim()) {
      newErrors.subject =
        language === 'en' ? 'Subject is required' : 'Subjek diperlukan';
    }

    if (!formData.category) {
      newErrors.category =
        language === 'en' ? 'Category is required' : 'Kategori diperlukan';
    }

    if (!formData.message.trim()) {
      newErrors.message =
        language === 'en' ? 'Message is required' : 'Mesej diperlukan';
    } else if (formData.message.trim().length < 10) {
      newErrors.message =
        language === 'en'
          ? 'Message must be at least 10 characters'
          : 'Mesej mesti sekurang-kurangnya 10 aksara';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof ContactFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      if (onSubmit) {
        await onSubmit(formData);
      } else {
        // Default submission - simulate API call
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }

      setSubmitStatus('success');
      setFormData({
        name: '',
        email: '',
        phone: '',
        company: '',
        subject: '',
        category: '',
        message: '',
      });
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const isCompact = variant === 'compact';

  return (
    <div className={className}>
      {submitStatus === 'success' && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center gap-2 text-green-800">
            <CheckCircle className="w-5 h-5" />
            <span className="font-medium">
              {language === 'en'
                ? 'Message Sent Successfully!'
                : 'Mesej Berjaya Dihantar!'}
            </span>
          </div>
          <p className="text-green-700 text-sm mt-1">
            {language === 'en'
              ? 'Thank you for contacting us. We will respond within 24-48 hours.'
              : 'Terima kasih kerana menghubungi kami. Kami akan membalas dalam masa 24-48 jam.'}
          </p>
        </div>
      )}

      {submitStatus === 'error' && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 text-red-800">
            <AlertCircle className="w-5 h-5" />
            <span className="font-medium">
              {language === 'en' ? 'Submission Failed' : 'Penghantaran Gagal'}
            </span>
          </div>
          <p className="text-red-700 text-sm mt-1">
            {language === 'en'
              ? 'There was an error sending your message. Please try again or contact us directly.'
              : 'Terdapat ralat semasa menghantar mesej anda. Sila cuba lagi atau hubungi kami secara langsung.'}
          </p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div
          className={
            isCompact ? 'grid grid-cols-1 md:grid-cols-2 gap-4' : 'space-y-6'
          }
        >
          {/* Name */}
          <div>
            <label
              htmlFor="name"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              {language === 'en' ? 'Full Name' : 'Nama Penuh'} *
            </label>
            <input
              type="text"
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-green focus:border-transparent bg-white text-gray-800 ${
                errors.name ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder={
                language === 'en'
                  ? 'Enter your full name'
                  : 'Masukkan nama penuh anda'
              }
            />
            {errors.name && (
              <p className="text-red-600 text-sm mt-1">{errors.name}</p>
            )}
          </div>

          {/* Email */}
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              {language === 'en' ? 'Email Address' : 'Alamat E-mel'} *
            </label>
            <input
              type="email"
              id="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-green focus:border-transparent bg-white text-gray-800 ${
                errors.email ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder={
                language === 'en'
                  ? 'Enter your email address'
                  : 'Masukkan alamat e-mel anda'
              }
            />
            {errors.email && (
              <p className="text-red-600 text-sm mt-1">{errors.email}</p>
            )}
          </div>

          {!isCompact && (
            <>
              {/* Phone */}
              <div>
                <label
                  htmlFor="phone"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  {language === 'en' ? 'Phone Number' : 'Nombor Telefon'}
                </label>
                <input
                  type="tel"
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-transparent bg-white text-gray-800"
                  placeholder={
                    language === 'en'
                      ? 'Enter your phone number'
                      : 'Masukkan nombor telefon anda'
                  }
                />
              </div>

              {/* Company */}
              <div>
                <label
                  htmlFor="company"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  {language === 'en'
                    ? 'Company/Organization'
                    : 'Syarikat/Organisasi'}
                </label>
                <input
                  type="text"
                  id="company"
                  value={formData.company}
                  onChange={(e) => handleInputChange('company', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-green focus:border-transparent bg-white text-gray-800"
                  placeholder={
                    language === 'en'
                      ? 'Enter your company name'
                      : 'Masukkan nama syarikat anda'
                  }
                />
              </div>
            </>
          )}

          {/* Category */}
          <div>
            <label
              htmlFor="category"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              {language === 'en' ? 'Inquiry Category' : 'Kategori Pertanyaan'} *
            </label>
            <select
              id="category"
              value={formData.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-green focus:border-transparent bg-white text-gray-800 ${
                errors.category ? 'border-red-300' : 'border-gray-300'
              }`}
            >
              <option value="">
                {language === 'en' ? 'Select a category' : 'Pilih kategori'}
              </option>
              {categories.map((category) => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
            {errors.category && (
              <p className="text-red-600 text-sm mt-1">{errors.category}</p>
            )}
          </div>

          {/* Subject */}
          <div className={isCompact ? 'md:col-span-2' : ''}>
            <label
              htmlFor="subject"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              {language === 'en' ? 'Subject' : 'Subjek'} *
            </label>
            <input
              type="text"
              id="subject"
              value={formData.subject}
              onChange={(e) => handleInputChange('subject', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-green focus:border-transparent bg-white text-gray-800 ${
                errors.subject ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder={
                language === 'en'
                  ? 'Enter the subject of your inquiry'
                  : 'Masukkan subjek pertanyaan anda'
              }
            />
            {errors.subject && (
              <p className="text-red-600 text-sm mt-1">{errors.subject}</p>
            )}
          </div>
        </div>

        {/* Message */}
        <div>
          <label
            htmlFor="message"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            {language === 'en' ? 'Message' : 'Mesej'} *
          </label>
          <textarea
            id="message"
            rows={isCompact ? 4 : 6}
            value={formData.message}
            onChange={(e) => handleInputChange('message', e.target.value)}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-green focus:border-transparent resize-vertical bg-white text-gray-800 ${
              errors.message ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder={
              language === 'en'
                ? 'Please provide details about your inquiry...'
                : 'Sila berikan butiran mengenai pertanyaan anda...'
            }
          />
          {errors.message && (
            <p className="text-red-600 text-sm mt-1">{errors.message}</p>
          )}
          <p className="text-gray-500 text-sm mt-1">
            {formData.message.length}/500{' '}
            {language === 'en' ? 'characters' : 'aksara'}
          </p>
        </div>

        {/* Submit Button */}
        <div>
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                {language === 'en' ? 'Sending...' : 'Menghantar...'}
              </>
            ) : (
              <>
                <Send className="w-4 h-4" />
                {language === 'en' ? 'Send Message' : 'Hantar Mesej'}
              </>
            )}
          </button>
        </div>

        {/* Privacy Notice */}
        <div className="text-xs text-gray-500">
          {language === 'en'
            ? 'By submitting this form, you agree to our privacy policy. Your information will be used solely to respond to your inquiry.'
            : 'Dengan menghantar borang ini, anda bersetuju dengan dasar privasi kami. Maklumat anda akan digunakan semata-mata untuk membalas pertanyaan anda.'}
        </div>
      </form>
    </div>
  );
}
