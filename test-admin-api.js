#!/usr/bin/env node

const API_BASE_URL = 'http://localhost:16001';
const DEFAULT_SITE_ID = '1';

// Test admin login first
async function testAdminLogin() {
  console.log('🔐 Testing admin login...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      }),
    });

    const data = await response.json();
    console.log('Login response:', { status: response.status, data });
    
    if (response.ok && data.token) {
      return data.token;
    }
    return null;
  } catch (error) {
    console.error('Login error:', error.message);
    return null;
  }
}

// Test API endpoint with authentication
async function testApiEndpoint(endpoint, token, method = 'GET', body = null) {
  console.log(`📡 Testing ${method} ${endpoint}...`);
  
  try {
    const url = endpoint.includes('/login') 
      ? `${API_BASE_URL}/api${endpoint}`
      : `${API_BASE_URL}/api/sites/${DEFAULT_SITE_ID}${endpoint}`;
    
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };
    
    if (token) {
      options.headers.Authorization = `Bearer ${token}`;
    }
    
    if (body) {
      options.body = JSON.stringify(body);
    }

    const response = await fetch(url, options);
    const data = await response.json();
    
    console.log(`${endpoint}:`, { 
      status: response.status, 
      ok: response.ok,
      dataType: Array.isArray(data) ? `array[${data.length}]` : typeof data,
      error: data.error || null
    });
    
    return { response, data };
  } catch (error) {
    console.error(`${endpoint} error:`, error.message);
    return { error: error.message };
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Admin Dashboard API Tests\n');
  
  // Test login
  const token = await testAdminLogin();
  if (!token) {
    console.log('❌ Login failed, cannot proceed with authenticated tests');
    return;
  }
  
  console.log('✅ Login successful, token received\n');
  
  // Test admin endpoints
  const endpoints = [
    '/admin/me',
    '/admin/users',
    '/admin/collections',
    '/admin/services',
    '/admin/bots',
    '/admin/sessions',
    '/admin/s3-configurations',
  ];
  
  console.log('📋 Testing authenticated endpoints:');
  for (const endpoint of endpoints) {
    await testApiEndpoint(endpoint, token);
  }
  
  console.log('\n🧪 Testing create operations:');
  
  // Test creating a bot with unique name
  const timestamp = Date.now();
  await testApiEndpoint('/admin/bots', token, 'POST', {
    name: `Test Bot ${timestamp}`,
    slug: `test-bot-${timestamp}`,
    provider: 'openai',
    model: 'gpt-4',
    temperature: 0.5,
    isDefault: false,
    systemPrompt: 'You are a helpful assistant.'
  });

  // Test creating a collection with unique name
  await testApiEndpoint('/admin/collections', token, 'POST', {
    name: `Test Collection ${timestamp}`,
    status: 'ACTIVE'
  });
  
  console.log('\n✅ API tests completed');
}

// Run the tests
runTests().catch(console.error);
