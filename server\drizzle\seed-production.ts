/// <reference types="node" />
import bcrypt from 'bcryptjs';
import { eq } from 'drizzle-orm';
import { initializeDatabase } from '../src/db/connection';
import {
  collections,
  s3Configurations,
  sites,
  twilioConfigs,
  users,
} from '../src/db/schema';

async function seedProduction() {
  console.log('🌱 Starting PRODUCTION database seeding...');

  // Use production environment
  const db = initializeDatabase();

  try {
    // Check if database is already seeded
    const existingUsers = await db.select().from(users).limit(1);
    if (existingUsers.length > 0) {
      console.log(
        '⚠️  Database already contains data. Skipping seed to prevent data loss.',
      );
      console.log(
        '   If you want to re-seed, manually clear the database first.',
      );
      return;
    }

    console.log('📊 Creating essential production data...');

    // Create default site first
    console.log('🏢 Creating default site...');
    const existingSites = await db.select().from(sites).limit(1);
    let siteId = 1;

    if (existingSites.length === 0) {
      const [newSite] = await db
        .insert(sites)
        .values({
          name: 'Default Site',
          code: 'default',
          domains: ['localhost', '127.0.0.1', 'halal.primalcom.com'],
          status: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();
      siteId = newSite.id;
    } else {
      siteId = existingSites[0].id;
    }

    // Create secure passwords for production users
    const adminPasswordHash = bcrypt.hashSync('admin123', 12); // Higher salt rounds for production
    const agentPasswordHash = bcrypt.hashSync('agent123', 12);
    const supervisorPasswordHash = bcrypt.hashSync('supervisor123', 12);

    console.log('👤 Creating admin user...');
    await db.insert(users).values([
      {
        siteId,
        username: 'admin',
        email: '<EMAIL>',
        passwordHash: adminPasswordHash,
        firstName: 'System',
        lastName: 'Administrator',
        roles: ['ADMIN'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ]);

    // Create sample agent and supervisor users for production
    console.log('🤖 Creating sample agent and supervisor users...');
    await db.insert(users).values([
      {
        siteId,
        username: 'agent1',
        email: '<EMAIL>',
        passwordHash: agentPasswordHash,
        firstName: 'John',
        lastName: 'Agent',
        roles: ['AGENT'],
        isActive: true,
        isOnline: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        siteId,
        username: 'supervisor1',
        email: '<EMAIL>',
        passwordHash: supervisorPasswordHash,
        firstName: 'Sarah',
        lastName: 'Supervisor',
        roles: ['SUPERVISOR'],
        isActive: true,
        isOnline: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ]);

    // Create default collection
    await db.insert(collections).values([
      {
        siteId,
        name: 'default',
        status: 'ACTIVE',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ]);

    // Create S3 configurations
    console.log('☁️ Checking for existing S3 configurations...');
    const existingS3Configs = await db.select().from(s3Configurations).limit(1);

    if (!existingS3Configs[0]) {
      // Note: This seed script runs in Node.js environment, not Cloudflare Workers
      // For Workers deployment, S3 config should be set via admin interface or direct DB insert
      const {
        S3_ACCESS_KEY_ID,
        S3_SECRET_ACCESS_KEY,
        S3_BUCKET_NAME,
        S3_REGION,
        S3_ENDPOINT_URL,
        DEFAULT_S3_SERVICE_NAME,
      } = process.env;

      const s3ConfigsToInsert: any[] = [];

      // Add real S3 config if environment variables are provided
      if (S3_ACCESS_KEY_ID && S3_SECRET_ACCESS_KEY && S3_BUCKET_NAME) {
        console.log(
          '☁️ Creating S3 configuration from environment variables...',
        );
        s3ConfigsToInsert.push({
          serviceName: DEFAULT_S3_SERVICE_NAME || 'AWS S3',
          accessKeyId: S3_ACCESS_KEY_ID,
          secretAccessKey: S3_SECRET_ACCESS_KEY,
          bucketName: S3_BUCKET_NAME,
          region: S3_REGION || 'us-east-1',
          endpointUrl: S3_ENDPOINT_URL || null,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      } else {
        console.warn(
          '⚠️ Missing S3 environment variables. Skipping S3 config for production.',
        );
        console.log(
          '   Add S3_ACCESS_KEY_ID, S3_SECRET_ACCESS_KEY, S3_BUCKET_NAME to .env.production',
        );
      }

      if (s3ConfigsToInsert.length > 0) {
        console.log('☁️ Creating S3 configurations...');
        await db.insert(s3Configurations).values(s3ConfigsToInsert);
      }
    } else {
      console.log('✅ S3 configurations already exist. Skipping creation.');
    }

    // Create Twilio configuration
    console.log('📞 Checking for existing Twilio configuration...');
    const existingTwilioConfig = await db
      .select()
      .from(twilioConfigs)
      .where(eq(twilioConfigs.siteId, 1))
      .limit(1);

    if (!existingTwilioConfig[0]) {
      // Note: This seed script runs in Node.js environment, not Cloudflare Workers
      // For Workers deployment, Twilio config should be set via admin interface or direct DB insert
      const {
        TWILIO_ACCOUNT_SID,
        TWILIO_AUTH_TOKEN,
        TWILIO_PHONE_NUMBER,
        TWILIO_WEBHOOK_URL,
      } = process.env;

      if (!TWILIO_ACCOUNT_SID || !TWILIO_AUTH_TOKEN || !TWILIO_PHONE_NUMBER) {
        console.warn(
          '⚠️ Missing Twilio environment variables. Skipping Twilio config for production.',
        );
        console.log(
          '   Add TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_PHONE_NUMBER to .env.production',
        );
      } else {
        console.log('📞 Creating Twilio configuration...');
        await db.insert(twilioConfigs).values({
          siteId: 1,
          accountSid: TWILIO_ACCOUNT_SID,
          authToken: TWILIO_AUTH_TOKEN,
          phoneNumber: TWILIO_PHONE_NUMBER,
          webhookUrl: TWILIO_WEBHOOK_URL,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }
    } else {
      console.log('✅ Twilio configuration already exists. Skipping creation.');
    }

    console.log('✅ Production database seeding completed successfully!');
    console.log('');
    console.log('📋 Created accounts:');
    console.log('  👤 Admin: username=admin, password=admin123');
    console.log('  🤖 Agent: username=agent1, password=agent123');
    console.log(
      '  👨‍💼 Supervisor: username=supervisor1, password=supervisor123',
    );
    console.log('');
    console.log('📊 Created data:');
    console.log('  🏢 Production site');
    console.log('  📁 Default collection');
    console.log('  ☁️ S3 configuration (if env vars provided)');
    console.log('  📞 Twilio configuration (if env vars provided)');
    console.log('');
    console.log(
      '⚠️  IMPORTANT: Change all default passwords after first login!',
    );
    console.log('');
    console.log('📝 Configuration Notes:');
    console.log('  • S3 credentials should be in .env.production file');
    console.log('  • Twilio credentials should be in .env.production file');
    console.log(
      '  • Use admin interface to manage configurations after deployment',
    );
    console.log(
      '  • Sensitive data is stored in database, not as Workers secrets',
    );
    console.log('');
    console.log('🔗 Next steps:');
    console.log('  1. Deploy to Cloudflare Workers: pnpm deploy:prod');
    console.log(
      '  2. Set Workers secrets: npx wrangler secret put DATABASE_URL --env production',
    );
    console.log('  3. Configure webhook URLs in Twilio Console');
    console.log('  4. Test integrations via admin interface');
  } catch (error) {
    console.error('❌ Error during production seeding:', error);
    process.exit(1);
  } finally {
    // Always close database connection
    await db.$client.end();
  }
}

// Run the seed function when executed directly
seedProduction()
  .then(() => {
    console.log('🎉 Production seed completed');
  })
  .catch((error) => {
    console.error('💥 Production seed failed:', error);
    process.exit(1);
  });

export { seedProduction };
