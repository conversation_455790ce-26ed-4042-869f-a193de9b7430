import { NextRequest } from 'next/server';
import {
  extractAnalyticsFromRequest,
  getHalalSelangorSiteId,
  trackSearchAnalytics,
} from '@/lib/analytics';
import R2RService from '@/lib/r2r';
import {
  detectLanguageAndTranslate,
  type TranslationResult,
} from '@/lib/translation';
import type { ParseR2rOptions, SearchResponse } from '@/types/search';
import { createSuccessResponse, createErrorResponse, handleApiError } from '@/lib/api-response';

// Remove edge runtime to use Node.js runtime for database access

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('q');
  const page = Number.parseInt(searchParams.get('page') || '1');
  const limit = Number.parseInt(searchParams.get('limit') || '10');
  const minScore = Number.parseFloat(searchParams.get('minScore') || '0.2');
  const maxWordCount = Number.parseInt(
    searchParams.get('maxWordCount') || '3000',
  );
  const retrieveDocument = searchParams.get('retrieveDocument') !== 'false';

  console.log('Received search query:', query);

  if (!query) {
    return createErrorResponse('Query parameter is missing', undefined, 400);
  }

  const { R2R_URL, R2R_DOCUMENT_COLLECTION_ID } = process.env;

  if (!R2R_URL) {
    console.error('R2R credentials not configured');
    return createErrorResponse('R2R credentials not configured');
  }

  try {
    const r2rService = new R2RService();

    // Calculate offset for pagination
    const offset = (page - 1) * limit;
    const searchLimit = limit + offset; // Get more results to handle pagination

    // Detect language and generate translated terms using OpenAI
    const translationResult = await detectLanguageAndTranslate(query);
    const allQueries = [query, ...translationResult.translatedTerms];

    console.log('Search queries:', {
      original: query,
      detectedLanguage: translationResult.detectedLanguage,
      confidence: translationResult.confidence,
      translatedTerms: translationResult.translatedTerms,
      allQueries,
    });

    // Perform searches for all queries in parallel
    const searchPromises = allQueries.map((searchQuery) =>
      r2rService.search(searchQuery, {
        retrieveDocument,
        maxWordCount: maxWordCount * 2,
        collectionId: R2R_DOCUMENT_COLLECTION_ID,
        limit: searchLimit,
      }),
    );

    const searchResults = await Promise.all(searchPromises);

    // Combine and deduplicate chunks from all searches
    const allChunks = new Map();
    let totalChunksFound = 0;

    searchResults.forEach((result, index) => {
      console.log(
        `Search ${index + 1} (${allQueries[index]}) found ${result.chunks.length} chunks`,
      );
      totalChunksFound += result.chunks.length;

      result.chunks.forEach((chunk) => {
        const key = `${chunk.document_id}-${chunk.text.substring(0, 100)}`;
        if (!allChunks.has(key) || allChunks.get(key).score < chunk.score) {
          // Keep the chunk with the highest score if duplicates exist
          allChunks.set(key, {
            ...chunk,
            searchQuery: allQueries[index], // Track which query found this chunk
          });
        }
      });
    });

    const chunks = Array.from(allChunks.values());
    console.log(
      `Combined ${totalChunksFound} total chunks into ${chunks.length} unique chunks`,
    );

    // Parse options
    const options: ParseR2rOptions = {
      retrieveDocument,
      maxWordCount,
      includeGraph: false, // Explicitly exclude graph search as requested
      minScore,
      limit: searchLimit,
    };

    // Parse and filter results
    const r2rResult = await r2rService.parseR2rResult(chunks, options);
    console.log('Parsed R2R result:', { minScore, r2rResult });
    // Apply pagination to the filtered results
    const totalResults = r2rResult.texts.length;
    const paginatedResults = r2rResult.texts.slice(offset, offset + limit);
    const hasMore = offset + limit < totalResults;

    const searchResponse: SearchResponse = {
      query,
      results: paginatedResults,
      totalChunks: chunks.length,
      totalGraphResults: 0, // No graph search
      options,
      pagination: {
        page,
        limit,
        total: totalResults,
        hasMore,
      },
      // Add information about multilingual search
      ...(translationResult.translatedTerms.length > 0 && {
        translatedTerms: translationResult.translatedTerms,
        detectedLanguage: translationResult.detectedLanguage,
        confidence: translationResult.confidence,
        searchType: 'multilingual' as const,
      }),
    };

    console.log('Final multilingual search response:', {
      originalQuery: query,
      detectedLanguage: translationResult.detectedLanguage,
      translatedTerms: translationResult.translatedTerms,
      resultsCount: paginatedResults.length,
      totalChunks: chunks.length,
      uniqueChunks: chunks.length,
      page,
      hasMore,
      totalResults,
    });

    // Track search analytics
    const startTime = Date.now();
    const analyticsData = extractAnalyticsFromRequest(request);
    await trackSearchAnalytics({
      siteId: getHalalSelangorSiteId(),
      searchQuery: query,
      searchType: 'web',
      resultsCount: paginatedResults.length,
      hasResults: paginatedResults.length > 0,
      responseTime: Date.now() - startTime,
      searchFilters: JSON.stringify({
        minScore,
        maxWordCount,
        retrieveDocument,
        page,
        limit,
      }),
      ...analyticsData,
    });

    return createSuccessResponse(searchResponse);
  } catch (error) {
    return handleApiError(error, 'Failed to fetch search results');
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      query,
      page = 1,
      limit = 10,
      minScore = 0.2,
      maxWordCount = 3000,
      retrieveDocument = true,
    } = body;

    if (!query) {
      return NextResponse.json({ error: 'Query is required' }, { status: 400 });
    }

    // Use the same logic as GET but with POST body parameters
    const searchParams = new URLSearchParams({
      q: query,
      page: page.toString(),
      limit: limit.toString(),
      minScore: minScore.toString(),
      maxWordCount: maxWordCount.toString(),
      retrieveDocument: retrieveDocument.toString(),
    });

    // Create a new request with the search params
    const getRequest = new NextRequest(
      `${request.url}?${searchParams.toString()}`,
      { method: 'GET' },
    );

    return await GET(getRequest);
  } catch (error) {
    return handleApiError(error, 'Invalid request body', 400);
  }
}
