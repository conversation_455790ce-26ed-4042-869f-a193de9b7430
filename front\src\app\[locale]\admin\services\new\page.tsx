'use client';

export const runtime = 'edge';

import { ArrowLeft, Save } from 'lucide-react';
import { useState } from 'react';
import { Link, useRouter } from '@/i18n/navigation';
import { api } from '@/lib/api';
import type {
  ExternalAPIConfiguration,
  R2RConfiguration,
  ServiceCreationRequest,
  ServiceType,
  SMTPConfiguration,
} from '@/types';

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

export default function NewServicePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [name, setName] = useState('');
  const [type, setType] = useState<ServiceType>('R2R_RAG' as ServiceType);
  const [description, setDescription] = useState('');
  const [isActive, setIsActive] = useState(true);

  // R2R Configuration
  const [r2rConfig, setR2rConfig] = useState<R2RConfiguration>({
    url: '',
    username: '',
    password: '',
    collectionId: '',
    searchLimit: 10,
    minScore: 0.2,
  });

  // SMTP Configuration
  const [smtpConfig, setSmtpConfig] = useState<SMTPConfiguration>({
    host: '',
    port: 587,
    secure: false,
    username: '',
    password: '',
    fromEmail: '',
    fromName: '',
  });

  // External API Configuration
  const [apiConfig, setApiConfig] = useState<ExternalAPIConfiguration>({
    baseUrl: '',
    apiKey: '',
    headers: {},
    timeout: 30000,
    retryAttempts: 3,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      let configuration;
      switch (type) {
        case 'R2R_RAG':
          configuration = r2rConfig;
          break;
        case 'SMTP_PROVIDER':
          configuration = smtpConfig;
          break;
        case 'EXTERNAL_API':
          configuration = apiConfig;
          break;
        default:
          throw new Error('Invalid service type');
      }

      const serviceData: ServiceCreationRequest = {
        name,
        type,
        description: description || undefined,
        isActive,
        configuration,
      };

      await api.admin.createService(serviceData);
      router.push('/admin/services');
    } catch (err: any) {
      setError(err.message || 'Failed to create service');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const renderConfigurationForm = () => {
    switch (type) {
      case 'R2R_RAG':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">
              R2R RAG Configuration
            </h3>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                URL *
              </label>
              <input
                type="url"
                required
                value={r2rConfig.url}
                onChange={(e) =>
                  setR2rConfig({ ...r2rConfig, url: e.target.value })
                }
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="https://r2r-api.example.com"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Username
                </label>
                <input
                  type="text"
                  value={r2rConfig.username || ''}
                  onChange={(e) =>
                    setR2rConfig({ ...r2rConfig, username: e.target.value })
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <input
                  type="password"
                  value={r2rConfig.password || ''}
                  onChange={(e) =>
                    setR2rConfig({ ...r2rConfig, password: e.target.value })
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Collection ID
              </label>
              <input
                type="text"
                value={r2rConfig.collectionId || ''}
                onChange={(e) =>
                  setR2rConfig({ ...r2rConfig, collectionId: e.target.value })
                }
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Search Limit
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={r2rConfig.searchLimit || 10}
                  onChange={(e) =>
                    setR2rConfig({
                      ...r2rConfig,
                      searchLimit: Number.parseInt(e.target.value),
                    })
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Min Score
                </label>
                <input
                  type="number"
                  min="0"
                  max="1"
                  step="0.1"
                  value={r2rConfig.minScore || 0.2}
                  onChange={(e) =>
                    setR2rConfig({
                      ...r2rConfig,
                      minScore: Number.parseFloat(e.target.value),
                    })
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        );

      case 'SMTP_PROVIDER':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">
              SMTP Provider Configuration
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Host *
                </label>
                <input
                  type="text"
                  required
                  value={smtpConfig.host}
                  onChange={(e) =>
                    setSmtpConfig({ ...smtpConfig, host: e.target.value })
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="smtp.gmail.com"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Port *
                </label>
                <input
                  type="number"
                  required
                  min="1"
                  max="65535"
                  value={smtpConfig.port}
                  onChange={(e) =>
                    setSmtpConfig({
                      ...smtpConfig,
                      port: Number.parseInt(e.target.value),
                    })
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={smtpConfig.secure}
                  onChange={(e) =>
                    setSmtpConfig({ ...smtpConfig, secure: e.target.checked })
                  }
                  className="mr-2"
                />
                <span className="text-sm font-medium text-gray-700">
                  Use SSL/TLS (port 465)
                </span>
              </label>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Username *
                </label>
                <input
                  type="text"
                  required
                  value={smtpConfig.username}
                  onChange={(e) =>
                    setSmtpConfig({ ...smtpConfig, username: e.target.value })
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Password *
                </label>
                <input
                  type="password"
                  required
                  value={smtpConfig.password}
                  onChange={(e) =>
                    setSmtpConfig({ ...smtpConfig, password: e.target.value })
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  From Email *
                </label>
                <input
                  type="email"
                  required
                  value={smtpConfig.fromEmail}
                  onChange={(e) =>
                    setSmtpConfig({ ...smtpConfig, fromEmail: e.target.value })
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  From Name
                </label>
                <input
                  type="text"
                  value={smtpConfig.fromName || ''}
                  onChange={(e) =>
                    setSmtpConfig({ ...smtpConfig, fromName: e.target.value })
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        );

      case 'EXTERNAL_API':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">
              External API Configuration
            </h3>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Base URL *
              </label>
              <input
                type="url"
                required
                value={apiConfig.baseUrl}
                onChange={(e) =>
                  setApiConfig({ ...apiConfig, baseUrl: e.target.value })
                }
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="https://api.example.com"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                API Key
              </label>
              <input
                type="password"
                value={apiConfig.apiKey || ''}
                onChange={(e) =>
                  setApiConfig({ ...apiConfig, apiKey: e.target.value })
                }
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Timeout (ms)
                </label>
                <input
                  type="number"
                  min="1000"
                  max="300000"
                  value={apiConfig.timeout || 30000}
                  onChange={(e) =>
                    setApiConfig({
                      ...apiConfig,
                      timeout: Number.parseInt(e.target.value),
                    })
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Retry Attempts
                </label>
                <input
                  type="number"
                  min="0"
                  max="10"
                  value={apiConfig.retryAttempts || 3}
                  onChange={(e) =>
                    setApiConfig({
                      ...apiConfig,
                      retryAttempts: Number.parseInt(e.target.value),
                    })
                  }
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="mb-6">
        <h1 className="text-2xl md:text-3xl font-semibold text-gray-800">
          Add New Service
        </h1>
      </div>

      {error && (
        <div className="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow-md rounded-lg p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">
              Basic Information
            </h3>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Service Name *
              </label>
              <input
                type="text"
                required
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter service name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Service Type *
              </label>
              <select
                required
                value={type}
                onChange={(e) => setType(e.target.value as ServiceType)}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="R2R_RAG">R2R RAG</option>
                <option value="SMTP_PROVIDER">SMTP Provider</option>
                <option value="EXTERNAL_API">External API</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter service description"
              />
            </div>
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={isActive}
                  onChange={(e) => setIsActive(e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm font-medium text-gray-700">
                  Active
                </span>
              </label>
            </div>
          </div>

          {/* Configuration */}
          {renderConfigurationForm()}

          {/* Actions */}
          <div className="flex justify-between pt-6">
            <Link
              href="/admin/services"
              className="text-gray-500 hover:text-gray-700 inline-flex items-center"
            >
              <ArrowLeft size={18} className="mr-1" /> Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white font-medium py-2 px-4 rounded-lg inline-flex items-center transition duration-150 ease-in-out"
            >
              <Save size={18} className="mr-2" />
              {loading ? 'Creating...' : 'Create Service'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
