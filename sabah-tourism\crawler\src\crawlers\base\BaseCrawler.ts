import { Stagehand } from '@browserbasehq/stagehand';
import { EventEmitter } from 'events';
import {
  type CrawlerConfig,
  CrawlerEvents,
  type CrawlerOptions,
  type CrawlProgress,
  type CrawlSummary,
  type SearchOptions,
} from '../../types/crawler.js';
import type { CrawlAttempt, CrawlSession } from '../../types/database.js';
import type {
  PlatformConfig,
  SearchResult,
  SocialMediaPost,
} from '../../types/social-media.js';
import {
  crawlerLogger,
  logCrawlEnd,
  logCrawlStart,
  logError,
} from '../../utils/logger.js';
import {
  createRetryConfig,
  RateLimiter,
  withRetry,
} from '../../utils/retry.js';
import {
  validateCrawlerConfig,
  validateSocialMediaPost,
} from '../../utils/validation.js';
import type { DatabaseManager } from './DatabaseManager.js';
import { type DownloadConfig, MediaDownloader } from './MediaDownloader.js';

export abstract class BaseCrawler extends EventEmitter {
  protected config: CrawlerConfig;
  protected platformConfig: PlatformConfig;
  protected database: DatabaseManager;
  protected mediaDownloader: MediaDownloader;
  protected stagehand: Stagehand;
  protected rateLimiter: RateLimiter;

  private currentSession: CrawlSession | null = null;
  private isRunning = false;
  private isPaused = false;
  private shouldStop = false;

  constructor(config: CrawlerConfig, database: DatabaseManager) {
    super();

    // Validate configuration
    this.config = validateCrawlerConfig(config);
    this.database = database;

    // Initialize platform-specific configuration
    this.platformConfig = this.getPlatformConfig();

    // Initialize rate limiter
    this.rateLimiter = new RateLimiter(
      this.config.rateLimiting.requestsPerMinute,
      60000, // 1 minute window
    );

    // Initialize media downloader
    this.initializeMediaDownloader();
  }

  // Abstract methods that must be implemented by platform-specific crawlers
  protected abstract getPlatformConfig(): PlatformConfig;
  protected abstract validateConfig(): boolean;
  public abstract search(
    keyword: string,
    options?: SearchOptions,
  ): Promise<SearchResult>;
  public abstract extractPost(url: string): Promise<SocialMediaPost | null>;

  private initializeMediaDownloader(): void {
    const downloadConfig: DownloadConfig = {
      outputDir: this.config.outputDir,
      maxFileSize: 100 * 1024 * 1024, // 100MB
      allowedTypes: [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'video/mp4',
        'video/webm',
        'audio/mp3',
        'audio/wav',
      ],
      concurrency: this.config.rateLimiting.downloadConcurrency,
      timeout: 30000,
      generateThumbnails: true,
      thumbnailSize: { width: 300, height: 300 },
    };

    this.mediaDownloader = new MediaDownloader(downloadConfig);
  }

  private async initializeStagehand(): Promise<void> {
    this.stagehand = new Stagehand({
      env: 'LOCAL',
      headless: this.config.browserConfig.headless,
      verbose: this.config.browserConfig.headless ? 0 : 1, // Enable verbose logging when not headless
      debugDom: !this.config.browserConfig.headless, // Enable DOM debugging when visible
    });

    await this.stagehand.init();
  }

  public async crawl(options?: CrawlerOptions): Promise<CrawlProgress> {
    if (this.isRunning) {
      throw new Error('Crawler is already running');
    }

    try {
      this.isRunning = true;
      this.shouldStop = false;
      this.isPaused = false;

      // Initialize Stagehand
      await this.initializeStagehand();

      // Create or resume session
      const sessionId = options?.resumeSession || (await this.createSession());
      this.currentSession = this.database.getSession(sessionId);

      if (!this.currentSession) {
        throw new Error(`Session ${sessionId} not found`);
      }

      logCrawlStart(this.config.platform, this.config.keywords, sessionId);

      // Update session status
      this.database.updateSession(sessionId, {
        status: 'running',
        started_at: new Date(),
      });

      const progress = await this.executeCrawl(sessionId, options);

      // Mark session as completed
      this.database.updateSession(sessionId, {
        status: 'completed',
        completed_at: new Date(),
        successful_posts: progress.progress.successfulPosts,
        failed_posts: progress.progress.failedPosts,
        total_posts: progress.progress.totalPosts,
      });

      const summary: CrawlSummary = {
        sessionId,
        platform: this.config.platform,
        keywords: this.config.keywords,
        duration:
          Date.now() -
          (this.currentSession.started_at?.getTime() || Date.now()),
        totalPosts: progress.progress.totalPosts,
        successfulPosts: progress.progress.successfulPosts,
        failedPosts: progress.progress.failedPosts,
        totalMedia: progress.progress.totalMedia,
        downloadedMedia: progress.progress.downloadedMedia,
        totalSize: 0, // TODO: Calculate from downloaded files
        errors: progress.errors.length,
        startTime: this.currentSession.started_at || new Date(),
        endTime: new Date(),
      };

      logCrawlEnd(sessionId, summary);
      this.emit('completed', summary);

      return progress;
    } catch (error) {
      logError(error as Error, { sessionId: this.currentSession?.id });

      if (this.currentSession) {
        this.database.updateSession(this.currentSession.id!, {
          status: 'failed',
          error_message: (error as Error).message,
          completed_at: new Date(),
        });
      }

      this.emit('error', error);
      throw error;
    } finally {
      this.isRunning = false;
      this.currentSession = null;
    }
  }

  private async createSession(): Promise<number> {
    const sessionData: Omit<CrawlSession, 'id'> = {
      platform: this.config.platform,
      keywords: JSON.stringify(this.config.keywords),
      status: 'running',
      config: JSON.stringify(this.config),
    };

    return this.database.createSession(sessionData);
  }

  private async executeCrawl(
    sessionId: number,
    options?: CrawlerOptions,
  ): Promise<CrawlProgress> {
    const progress: CrawlProgress = {
      sessionId,
      platform: this.config.platform,
      keywords: this.config.keywords,
      status: 'initializing',
      progress: {
        totalPosts: 0,
        processedPosts: 0,
        successfulPosts: 0,
        failedPosts: 0,
        downloadedMedia: 0,
        totalMedia: 0,
      },
      errors: [],
    };

    this.emit('progress', progress);

    try {
      // Get pending attempts or create new ones
      let attempts = this.database.getPendingAttempts(sessionId);

      if (attempts.length === 0 && !options?.resumeSession) {
        progress.status = 'searching';
        this.emit('progress', progress);

        // Create new attempts for each keyword
        attempts = await this.createCrawlAttempts(sessionId);
      } else if (options?.resumeSession) {
        crawlerLogger.info('Resuming session with pending attempts', {
          sessionId,
          pendingAttempts: attempts.length,
        });
      }

      progress.status = 'extracting';
      progress.progress.totalPosts = attempts.length;
      this.emit('progress', progress);

      // Process attempts in batches for better performance
      const batchSize = 10;
      const batches = this.chunkArray(attempts, batchSize);

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        if (this.shouldStop) break;

        const batch = batches[batchIndex];
        crawlerLogger.info('Processing batch', {
          batchIndex: batchIndex + 1,
          totalBatches: batches.length,
          batchSize: batch.length,
        });

        // Process batch with controlled concurrency
        await this.processBatch(batch, progress, options);

        // Save progress periodically
        this.database.updateSession(sessionId, {
          successful_posts: progress.progress.successfulPosts,
          failed_posts: progress.progress.failedPosts,
          total_posts: progress.progress.totalPosts,
        });

        // Brief pause between batches
        if (batchIndex < batches.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }

      progress.status = 'completed';
      this.emit('progress', progress);

      return progress;
    } catch (error) {
      progress.status = 'failed';
      progress.errors.push({
        type: 'unknown',
        message: (error as Error).message,
        timestamp: new Date(),
        retryable: false,
      });

      this.emit('progress', progress);
      throw error;
    }
  }

  private async processBatch(
    attempts: CrawlAttempt[],
    progress: CrawlProgress,
    options?: CrawlerOptions,
  ): Promise<void> {
    for (const attempt of attempts) {
      if (this.shouldStop) break;

      while (this.isPaused) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      await this.rateLimiter.waitIfNeeded();

      try {
        progress.currentActivity = `Processing ${attempt.post_url}`;
        this.emit('progress', progress);

        const result = await this.processAttempt(attempt, options);

        if (result.success) {
          progress.progress.successfulPosts++;
          if (result.mediaCount) {
            progress.progress.totalMedia += result.mediaCount;
            progress.progress.downloadedMedia += result.downloadedMedia || 0;
          }
        } else {
          progress.progress.failedPosts++;
          if (result.error) {
            progress.errors.push(result.error);
          }
        }
      } catch (error) {
        logError(error as Error, {
          attemptId: attempt.id,
          url: attempt.post_url,
        });
        progress.progress.failedPosts++;

        progress.errors.push({
          type: 'extraction',
          message: (error as Error).message,
          url: attempt.post_url,
          timestamp: new Date(),
          retryable: false,
        });
      }

      progress.progress.processedPosts++;
      this.emit('progress', progress);
    }
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  private async createCrawlAttempts(
    sessionId: number,
  ): Promise<CrawlAttempt[]> {
    const attempts: CrawlAttempt[] = [];
    const seenUrls = new Set<string>();

    for (const keyword of this.config.keywords) {
      try {
        crawlerLogger.info('Creating crawl attempts for keyword', {
          keyword,
          sessionId,
        });

        // Update keyword tracking
        this.database.upsertKeyword({
          keyword,
          platform: this.config.platform,
          last_crawled: new Date(),
        });

        const searchResult = await this.search(keyword, {
          maxResults: Math.ceil(
            this.config.maxPosts / this.config.keywords.length,
          ),
        });

        let addedForKeyword = 0;
        for (const post of searchResult.posts) {
          // Skip duplicates within this session
          if (seenUrls.has(post.url)) {
            continue;
          }

          // Check if post already exists in database
          const existingPost = this.database.getPost(post.platform, post.id);
          if (existingPost && !this.shouldRecrawlPost(existingPost)) {
            crawlerLogger.debug('Skipping existing post', {
              postId: post.id,
              url: post.url,
            });
            continue;
          }

          const attemptData: Omit<CrawlAttempt, 'id'> = {
            session_id: sessionId,
            post_url: post.url,
            keyword,
            status: 'pending',
          };

          const attemptId = this.database.insertAttempt(attemptData);
          attempts.push({ ...attemptData, id: attemptId });
          seenUrls.add(post.url);
          addedForKeyword++;
        }

        crawlerLogger.info('Created attempts for keyword', {
          keyword,
          attempts: addedForKeyword,
          totalFound: searchResult.posts.length,
        });

        // Update keyword statistics
        this.database.upsertKeyword({
          keyword,
          platform: this.config.platform,
          last_crawled: new Date(),
          total_posts_found: searchResult.posts.length,
        });
      } catch (error) {
        logError(error as Error, { keyword, sessionId });
      }
    }

    crawlerLogger.info('Total crawl attempts created', {
      sessionId,
      totalAttempts: attempts.length,
      uniqueUrls: seenUrls.size,
    });

    return attempts;
  }

  private shouldRecrawlPost(existingPost: any): boolean {
    // Recrawl if post is older than 7 days
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);

    const crawledAt = new Date(existingPost.crawled_at);
    return crawledAt < weekAgo;
  }

  private async processAttempt(
    attempt: CrawlAttempt,
    options?: CrawlerOptions,
  ): Promise<{
    success: boolean;
    mediaCount?: number;
    downloadedMedia?: number;
    error?: any;
  }> {
    try {
      // Extract post data
      const post = await this.extractPost(attempt.post_url);

      if (!post) {
        // Update attempt as failed
        this.database.updateAttempt(attempt.id!, {
          status: 'failed',
          error_message: 'Failed to extract post data',
          completed_at: new Date(),
        });

        return {
          success: false,
          error: {
            type: 'extraction',
            message: 'Failed to extract post data',
            url: attempt.post_url,
            timestamp: new Date(),
            retryable: true,
          },
        };
      }

      // Validate and save post
      const validatedPost = validateSocialMediaPost(post);
      const postId = this.savePost(validatedPost);

      let downloadedMedia = 0;
      const totalMedia = validatedPost.media.length;

      // Download media if enabled
      if (this.config.downloadMedia && !options?.dryRun && totalMedia > 0) {
        downloadedMedia = await this.downloadPostMedia(
          validatedPost,
          postId.toString(),
        );
      }

      // Update attempt as successful
      this.database.updateAttempt(attempt.id!, {
        status: 'success',
        completed_at: new Date(),
      });

      crawlerLogger.debug('Successfully processed attempt', {
        attemptId: attempt.id,
        postId: validatedPost.id,
        mediaCount: totalMedia,
        downloadedMedia,
      });

      return {
        success: true,
        mediaCount: totalMedia,
        downloadedMedia,
      };
    } catch (error) {
      // Update attempt as failed
      this.database.updateAttempt(attempt.id!, {
        status: 'failed',
        error_message: (error as Error).message,
        completed_at: new Date(),
      });

      return {
        success: false,
        error: {
          type: 'processing',
          message: (error as Error).message,
          url: attempt.post_url,
          timestamp: new Date(),
          retryable: true,
        },
      };
    }
  }

  private savePost(post: SocialMediaPost): number {
    const postData = {
      platform: post.platform,
      post_id: post.id,
      url: post.url,
      title: post.title,
      content: post.content,
      author_username: post.author.username,
      author_display_name: post.author.displayName,
      author_avatar_url: post.author.avatarUrl,
      posted_at: post.publishedAt,
      tags: JSON.stringify(post.hashtags),
      likes_count: post.engagement.likes,
      comments_count: post.engagement.comments,
      shares_count: post.engagement.shares,
      views_count: post.engagement.views,
      metadata: JSON.stringify(post.metadata),
    };

    return this.database.insertPost(postData);
  }

  private async downloadPostMedia(
    post: SocialMediaPost,
    postId: string,
  ): Promise<number> {
    let downloadedCount = 0;

    for (const media of post.media) {
      try {
        const result = await this.mediaDownloader.downloadMedia(
          media,
          postId,
          this.config.platform,
          (progress) => this.emit('media-downloaded', progress),
        );

        if (result.success) {
          downloadedCount++;

          // Save media file record to database
          this.database.insertMediaFile({
            post_id: Number.parseInt(postId),
            media_type: media.type,
            file_name: result.fileName || 'unknown',
            file_path: result.filePath || '',
            original_url: media.url,
            file_size: result.fileSize,
            duration: media.duration,
            width: media.dimensions?.width,
            height: media.dimensions?.height,
          });
        }
      } catch (error) {
        logError(error as Error, { postId, mediaUrl: media.url });
      }
    }

    return downloadedCount;
  }

  public async pause(): Promise<void> {
    this.isPaused = true;

    if (this.currentSession) {
      this.database.updateSession(this.currentSession.id!, {
        status: 'paused',
      });
    }

    this.emit('paused');
    crawlerLogger.info('Crawler paused');
  }

  public async resume(): Promise<void> {
    this.isPaused = false;

    if (this.currentSession) {
      this.database.updateSession(this.currentSession.id!, {
        status: 'running',
      });
    }

    this.emit('resumed');
    crawlerLogger.info('Crawler resumed');
  }

  public async stop(): Promise<void> {
    this.shouldStop = true;

    if (this.currentSession) {
      this.database.updateSession(this.currentSession.id!, {
        status: 'completed',
        completed_at: new Date(),
      });
    }

    crawlerLogger.info('Crawler stopped');
  }

  public getStatus(): {
    isRunning: boolean;
    isPaused: boolean;
    currentSession: CrawlSession | null;
  } {
    return {
      isRunning: this.isRunning,
      isPaused: this.isPaused,
      currentSession: this.currentSession,
    };
  }

  public async cleanup(): Promise<void> {
    if (this.stagehand) {
      await this.stagehand.close();
    }

    if (this.mediaDownloader) {
      await this.mediaDownloader.waitForCompletion();
    }
  }
}
