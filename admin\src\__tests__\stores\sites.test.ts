import { act, renderHook } from '@testing-library/react';
import { useAuthStore } from '@/stores/auth';
import { useSitesStore } from '@/stores/sites';

// Mock fetch
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe('Sites Store', () => {
  beforeEach(() => {
    // Reset store states
    useSitesStore.setState({
      sites: [],
      currentSite: null,
      isLoading: false,
      error: null,
      pagination: {
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      },
    });

    useAuthStore.setState({
      token: 'mock-token',
      isAuthenticated: true,
    });

    mockFetch.mockClear();
  });

  describe('fetchSites', () => {
    it('should fetch sites successfully', async () => {
      const mockSites = [
        {
          id: 1,
          name: 'Test Site',
          code: 'test',
          domains: ['test.com'],
          status: true,
          createdAt: '2023-01-01',
          updatedAt: '2023-01-01',
        },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          data: mockSites,
          total: 1,
          page: 1,
          limit: 10,
          totalPages: 1,
        }),
      } as Response);

      const { result } = renderHook(() => useSitesStore());

      await act(async () => {
        await result.current.fetchSites();
      });

      expect(result.current.sites).toEqual(mockSites);
      expect(result.current.pagination.total).toBe(1);
      expect(result.current.error).toBeNull();
    });

    it('should handle fetch error', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: async () => ({ error: 'Failed to fetch' }),
      } as Response);

      const { result } = renderHook(() => useSitesStore());

      await act(async () => {
        await result.current.fetchSites();
      });

      expect(result.current.sites).toEqual([]);
      expect(result.current.error).toBe('Failed to fetch');
    });
  });

  describe('createSite', () => {
    it('should create site successfully', async () => {
      const newSite = {
        name: 'New Site',
        code: 'new',
        domains: ['new.com'],
        status: true,
      };

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ id: 1, ...newSite }),
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            data: [{ id: 1, ...newSite }],
            total: 1,
            page: 1,
            limit: 10,
            totalPages: 1,
          }),
        } as Response);

      const { result } = renderHook(() => useSitesStore());

      await act(async () => {
        const success = await result.current.createSite(newSite);
        expect(success).toBe(true);
      });

      expect(result.current.error).toBeNull();
    });

    it('should handle create error', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: async () => ({ error: 'Site code already exists' }),
      } as Response);

      const { result } = renderHook(() => useSitesStore());

      await act(async () => {
        const success = await result.current.createSite({
          name: 'Test',
          code: 'test',
          domains: ['test.com'],
        });
        expect(success).toBe(false);
      });

      expect(result.current.error).toBe('Site code already exists');
    });
  });

  describe('updateSite', () => {
    it('should update site successfully', async () => {
      const updateData = { name: 'Updated Site' };

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ id: 1, ...updateData }),
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            data: [],
            total: 0,
            page: 1,
            limit: 10,
            totalPages: 0,
          }),
        } as Response);

      const { result } = renderHook(() => useSitesStore());

      await act(async () => {
        const success = await result.current.updateSite(1, updateData);
        expect(success).toBe(true);
      });

      expect(result.current.error).toBeNull();
    });
  });

  describe('deleteSite', () => {
    it('should delete site successfully', async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ message: 'Site deleted successfully' }),
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            data: [],
            total: 0,
            page: 1,
            limit: 10,
            totalPages: 0,
          }),
        } as Response);

      const { result } = renderHook(() => useSitesStore());

      await act(async () => {
        const success = await result.current.deleteSite(1);
        expect(success).toBe(true);
      });

      expect(result.current.error).toBeNull();
    });

    it('should handle delete error', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: async () => ({ error: 'Cannot delete site with existing users' }),
      } as Response);

      const { result } = renderHook(() => useSitesStore());

      await act(async () => {
        const success = await result.current.deleteSite(1);
        expect(success).toBe(false);
      });

      expect(result.current.error).toBe(
        'Cannot delete site with existing users',
      );
    });
  });
});
