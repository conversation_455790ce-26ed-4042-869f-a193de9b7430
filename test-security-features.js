#!/usr/bin/env node

/**
 * Test script to verify security features implementation
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:8787';

// Test configuration
const testConfig = {
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': 'SecurityTestScript/1.0',
  },
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: [],
};

function logTest(name, passed, details = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  console.log(`${status}: ${name}`);
  if (details) {
    console.log(`   ${details}`);
  }

  testResults.tests.push({ name, passed, details });
  if (passed) {
    testResults.passed++;
  } else {
    testResults.failed++;
  }
}

async function testHealthEndpoint() {
  try {
    const response = await axios.get(`${BASE_URL}/health`, testConfig);
    const isHealthy = response.status === 200 && response.data.status === 'OK';
    logTest(
      'Health endpoint',
      isHealthy,
      `Status: ${response.status}, Response: ${JSON.stringify(response.data)}`,
    );
    return isHealthy;
  } catch (error) {
    logTest('Health endpoint', false, `Error: ${error.message}`);
    return false;
  }
}

async function testSecurityHealthEndpoint() {
  try {
    const response = await axios.get(
      `${BASE_URL}/api/security/health`,
      testConfig,
    );
    const isHealthy = response.status === 200 && response.data.status;
    logTest(
      'Security health endpoint',
      isHealthy,
      `Status: ${response.status}, Health status: ${response.data.status}`,
    );
    return response.data;
  } catch (error) {
    logTest('Security health endpoint', false, `Error: ${error.message}`);
    return null;
  }
}

async function testSessionCreation() {
  try {
    const response = await axios.post(
      `${BASE_URL}/api/chat/session`,
      {},
      testConfig,
    );
    const hasSessionId = response.status === 200 && response.data.sessionId;
    logTest(
      'Session creation',
      hasSessionId,
      `Status: ${response.status}, SessionId: ${response.data.sessionId?.substring(0, 20)}...`,
    );
    return response.data.sessionId;
  } catch (error) {
    logTest('Session creation', false, `Error: ${error.message}`);
    return null;
  }
}

async function testChatRateLimit() {
  try {
    // First create a session
    const sessionResponse = await axios.post(
      `${BASE_URL}/api/chat/session`,
      {},
      testConfig,
    );
    const sessionId = sessionResponse.data.sessionId;

    if (!sessionId) {
      logTest(
        'Chat rate limiting test',
        false,
        'Could not create session for rate limit test',
      );
      return;
    }

    // Send multiple messages quickly to trigger rate limit
    const promises = [];
    for (let i = 0; i < 65; i++) {
      // Exceed the 60 per minute limit
      promises.push(
        axios
          .post(
            `${BASE_URL}/api/chat/message`,
            {
              sessionId,
              message: `Test message ${i}`,
              model: 'gpt-3.5-turbo',
            },
            testConfig,
          )
          .catch((err) => err.response),
      );
    }

    const responses = await Promise.all(promises);
    const rateLimitedResponses = responses.filter((r) => r && r.status === 429);
    const hasRateLimit = rateLimitedResponses.length > 0;

    logTest(
      'Chat rate limiting',
      hasRateLimit,
      `Rate limited responses: ${rateLimitedResponses.length}/65`,
    );
  } catch (error) {
    logTest('Chat rate limiting', false, `Error: ${error.message}`);
  }
}

async function testInputValidation() {
  try {
    // Test with invalid session ID
    const response = await axios
      .post(
        `${BASE_URL}/api/chat/message`,
        {
          sessionId: 'invalid-session-id',
          message: 'Test message',
          model: 'gpt-3.5-turbo',
        },
        testConfig,
      )
      .catch((err) => err.response);

    const isValidated = response && response.status === 404; // Session not found
    logTest(
      'Input validation (invalid session)',
      isValidated,
      `Status: ${response?.status}, Message: ${response?.data?.message}`,
    );

    // Test with extremely long message
    const longMessage = 'A'.repeat(5000); // Exceed 4000 char limit
    const sessionResponse = await axios.post(
      `${BASE_URL}/api/chat/session`,
      {},
      testConfig,
    );
    const sessionId = sessionResponse.data.sessionId;

    const longMessageResponse = await axios
      .post(
        `${BASE_URL}/api/chat/message`,
        {
          sessionId,
          message: longMessage,
          model: 'gpt-3.5-turbo',
        },
        testConfig,
      )
      .catch((err) => err.response);

    const isLongMessageValidated =
      longMessageResponse && longMessageResponse.status === 400;
    logTest(
      'Input validation (long message)',
      isLongMessageValidated,
      `Status: ${longMessageResponse?.status}`,
    );
  } catch (error) {
    logTest('Input validation', false, `Error: ${error.message}`);
  }
}

async function testSessionStats() {
  try {
    // This endpoint requires authentication, so we expect 401
    const response = await axios
      .get(`${BASE_URL}/api/chat/stats`, testConfig)
      .catch((err) => err.response);
    const requiresAuth = response && response.status === 401;
    logTest(
      'Session stats authentication',
      requiresAuth,
      `Status: ${response?.status}, Message: ${response?.data?.message}`,
    );
  } catch (error) {
    logTest('Session stats authentication', false, `Error: ${error.message}`);
  }
}

async function testSecurityHeaders() {
  try {
    const response = await axios.get(`${BASE_URL}/health`, testConfig);
    const headers = response.headers;

    const hasSecurityHeaders =
      headers['x-content-type-options'] === 'nosniff' ||
      headers['x-frame-options'] ||
      headers['x-xss-protection'] ||
      headers['strict-transport-security'];

    logTest(
      'Security headers',
      hasSecurityHeaders,
      `Headers present: ${Object.keys(headers)
        .filter((h) => h.startsWith('x-'))
        .join(', ')}`,
    );
  } catch (error) {
    logTest('Security headers', false, `Error: ${error.message}`);
  }
}

async function runAllTests() {
  console.log('🔒 Starting Security Features Test Suite\n');
  console.log(`Testing server at: ${BASE_URL}\n`);

  // Basic connectivity test
  const isHealthy = await testHealthEndpoint();
  if (!isHealthy) {
    console.log('\n❌ Server is not healthy. Stopping tests.');
    return;
  }

  // Run all security tests
  await testSecurityHealthEndpoint();
  await testSessionCreation();
  await testChatRateLimit();
  await testInputValidation();
  await testSessionStats();
  await testSecurityHeaders();

  // Print summary
  console.log('\n📊 Test Summary:');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(
    `📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`,
  );

  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests
      .filter((t) => !t.passed)
      .forEach((test) => {
        console.log(`   - ${test.name}: ${test.details}`);
      });
  }

  console.log('\n🏁 Test suite completed.');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests, testResults };
