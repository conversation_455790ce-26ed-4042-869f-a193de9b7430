import chalk from 'chalk';
import { Command } from 'commander';
import { HalalCrawler } from '../crawler/main';
import { DatabaseService } from '../database/service';
import type { ScrapingConfig } from '../types';
import { Logger, LogLevel } from '../utils/logger';
import { <PERSON>QualityChecker } from '../utils/validation';

export function createCLI(): Command {
  const program = new Command();

  program
    .name('halal-crawler')
    .description('Malaysian Halal Companies Directory Crawler')
    .version('1.0.0');

  // Main crawl command
  program
    .command('crawl')
    .description('Start crawling the Malaysian halal companies directory')
    .option('-p, --pages <number>', 'Maximum number of pages to crawl',  process.env.MAX_PAGES || '10')
    .option('-c, --concurrent <number>', 'Number of concurrent pages', process.env.CONCURRENT_PAGES || '3')
    .option('-d, --delay <number>', 'Delay between requests in ms', process.env.DELAY_BETWEEN_REQUESTS || '1000')
    .option('-r, --retries <number>', 'Number of retry attempts', process.env.RETRY_ATTEMPTS || '3')
    .option('-t, --timeout <number>', 'Timeout in milliseconds', process.env.TIMEOUT_MS || '50000')
    .option('--headless', 'Run browser in headless mode', true)
    .option('--no-headless', 'Run browser in non-headless mode')
    .option('--debug', 'Enable debug logging', false)
    .option('-s, --site-id <number>', 'Site ID for database', process.env.SITE_ID || '1')
    .option(
      '--start-url <url>',
      'Custom start URL',
      'https://myehalal.halal.gov.my/portal-halal/v1/index.php?data=ZGlyZWN0b3J5L2luZGV4X2RpcmVjdG9yeTs7Ozs=',
    )
    .action(async (options) => {
      try {
        Logger.header('Malaysian Halal Companies Crawler');

        if (options.debug) {
          Logger.setLevel(LogLevel.DEBUG);
        }

        const config: ScrapingConfig = {
          maxPages: Number.parseInt(options.pages),
          concurrentPages: Number.parseInt(options.concurrent),
          delayBetweenRequests: Number.parseInt(options.delay),
          retryAttempts: Number.parseInt(options.retries),
          timeoutMs: Number.parseInt(options.timeout),
          headless: options.noHeadless ? false : options.headless,
          debug: options.debug,
          siteId: Number.parseInt(options.siteId),
        };

        Logger.section('Configuration');
        Logger.stats('Crawling Configuration', {
          'Max Pages': config.maxPages,
          'Concurrent Pages': config.concurrentPages,
          'Delay (ms)': config.delayBetweenRequests,
          'Retry Attempts': config.retryAttempts,
          'Timeout (ms)': config.timeoutMs,
          'Headless Mode': config.headless,
          'Debug Mode': config.debug,
          'Site ID': config.siteId,
        });

        // Test database connection
        Logger.section('Database Connection');
        const dbService = new DatabaseService();
        const dbConnected = await dbService.testConnection();

        if (!dbConnected) {
          Logger.error(
            'Failed to connect to database. Please check your DATABASE_URL.',
          );
          process.exit(1);
        }

        Logger.success('Database connection successful');

        // Start crawling
        const crawler = new HalalCrawler(config);
        const result = await crawler.crawl(options.startUrl);

        // Display results
        Logger.section('Crawling Results');
        Logger.stats('Final Statistics', {
          'Total Pages': result.stats.totalPages,
          'Processed Pages': result.stats.processedPages,
          'Total Companies': result.stats.totalCompanies,
          'Saved Companies': result.stats.savedCompanies,
          Duplicates: result.stats.duplicates,
          Errors: result.stats.errors,
          'Duration (s)': Math.round((result.stats.duration || 0) / 1000),
          'Success Rate': `${Math.round((result.stats.savedCompanies / Math.max(result.stats.totalCompanies, 1)) * 100)}%`,
        });

        if (result.errors.length > 0) {
          Logger.section('Errors Encountered');
          result.errors.forEach((error) => Logger.error(error));
        }

        Logger.footer();

        if (result.success) {
          Logger.success('Crawling completed successfully!');
          process.exit(0);
        } else {
          Logger.error('Crawling completed with errors');
          process.exit(1);
        }
      } catch (error) {
        Logger.error('Fatal error during crawling:', error);
        process.exit(1);
      }
    });

  // Stats command
  program
    .command('stats')
    .description('Show database statistics')
    .option('-s, --site-id <number>', 'Site ID', '1')
    .action(async (options) => {
      try {
        Logger.header('Database Statistics');

        const dbService = new DatabaseService();
        const siteId = Number.parseInt(options.siteId);

        const stats = await dbService.getScrapingStats(siteId);

        Logger.section('Overall Statistics');
        Logger.stats('Database Overview', {
          'Total Companies': stats.totalCompanies,
          'Recently Added (24h)': stats.recentlyAdded,
        });

        if (Object.keys(stats.companiesByState).length > 0) {
          Logger.section('Companies by State');
          Logger.stats('State Distribution', stats.companiesByState);
        }

        if (Object.keys(stats.companiesByCategory).length > 0) {
          Logger.section('Companies by Category');
          Logger.stats('Category Distribution', stats.companiesByCategory);
        }

        Logger.footer();
      } catch (error) {
        Logger.error('Error retrieving statistics:', error);
        process.exit(1);
      }
    });

  // Search command
  program
    .command('search <term>')
    .description('Search companies by name')
    .option('-s, --site-id <number>', 'Site ID', '1')
    .option('-l, --limit <number>', 'Maximum results', '10')
    .action(async (term, options) => {
      try {
        Logger.header(`Search Results for "${term}"`);

        const dbService = new DatabaseService();
        const siteId = Number.parseInt(options.siteId);
        const limit = Number.parseInt(options.limit);

        const companies = await dbService.searchCompaniesByName(
          siteId,
          term,
          limit,
        );

        if (companies.length === 0) {
          Logger.warn('No companies found matching the search term');
          return;
        }

        Logger.section(`Found ${companies.length} companies`);

        companies.forEach((company, index) => {
          console.log(chalk.cyan(`\n${index + 1}. ${company.companyName}`));
          if (company.address) console.log(`   Address: ${company.address}`);
          if (company.phone) console.log(`   Phone: ${company.phone}`);
          if (company.email) console.log(`   Email: ${company.email}`);
          if (company.state) console.log(`   State: ${company.state}`);
          if (company.category) console.log(`   Category: ${company.category}`);
        });

        Logger.footer();
      } catch (error) {
        Logger.error('Error searching companies:', error);
        process.exit(1);
      }
    });

  // Test command
  program
    .command('test')
    .description('Test database connection and configuration')
    .action(async () => {
      try {
        Logger.header('System Test');

        // Test database connection
        Logger.section('Database Connection Test');
        const dbService = new DatabaseService();
        const dbConnected = await dbService.testConnection();

        if (dbConnected) {
          Logger.success('Database connection: OK');
        } else {
          Logger.error('Database connection: FAILED');
        }

        // Test environment variables
        Logger.section('Environment Variables');
        const envVars = {
          DATABASE_URL: process.env.DATABASE_URL ? 'Set' : 'Missing',
          MAX_PAGES: process.env.MAX_PAGES || 'Default (10)',
          SITE_ID: process.env.SITE_ID || 'Default (1)',
        };

        Logger.stats('Environment Configuration', envVars);

        Logger.footer();
      } catch (error) {
        Logger.error('Error during system test:', error);
        process.exit(1);
      }
    });

  return program;
}
