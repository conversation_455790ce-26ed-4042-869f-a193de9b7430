import type { Metadata } from 'next';

export interface SEOConfig {
  title: string;
  titleBM?: string;
  description: string;
  descriptionBM?: string;
  keywords?: string[];
  keywordsBM?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'profile';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tags?: string[];
  locale?: 'en_MY' | 'ms_MY';
  alternateLocale?: 'en_MY' | 'ms_MY';
}

export interface StructuredDataConfig {
  type:
    | 'Article'
    | 'NewsArticle'
    | 'WebPage'
    | 'Organization'
    | 'BreadcrumbList';
  title?: string;
  description?: string;
  image?: string;
  url?: string;
  datePublished?: string;
  dateModified?: string;
  author?: {
    name: string;
    type: 'Person' | 'Organization';
  };
  publisher?: {
    name: string;
    logo: string;
  };
  breadcrumbs?: Array<{
    name: string;
    url: string;
  }>;
}

const DEFAULT_CONFIG = {
  siteName: 'Halal Malaysia Portal',
  siteUrl: 'https://myehalal.halal.gov.my',
  defaultImage: '/images/logos/jakim_logo.png',
  twitterHandle: '@halal_malaysia',
  organization: {
    name: 'JAKIM - Jabatan Kemajuan Islam Malaysia',
    logo: '/images/logos/jakim_logo.png',
  },
};

export function generateMetadata(
  config: SEOConfig,
  language: 'en' | 'bm' = 'en',
): Metadata {
  const title =
    language === 'bm' && config.titleBM ? config.titleBM : config.title;
  const description =
    language === 'bm' && config.descriptionBM
      ? config.descriptionBM
      : config.description;
  const keywords =
    language === 'bm' && config.keywordsBM
      ? config.keywordsBM
      : config.keywords;

  const fullTitle = title.includes(DEFAULT_CONFIG.siteName)
    ? title
    : `${title} | ${DEFAULT_CONFIG.siteName}`;

  const metadata: Metadata = {
    title: fullTitle,
    description,
    keywords: keywords?.join(', '),
    authors: [{ name: config.author || DEFAULT_CONFIG.organization.name }],
    creator: DEFAULT_CONFIG.organization.name,
    publisher: DEFAULT_CONFIG.organization.name,
    openGraph: {
      title: fullTitle,
      description,
      url: config.url
        ? `${DEFAULT_CONFIG.siteUrl}${config.url}`
        : DEFAULT_CONFIG.siteUrl,
      siteName: DEFAULT_CONFIG.siteName,
      locale: config.locale || (language === 'bm' ? 'ms_MY' : 'en_MY'),
      type: config.type || 'website',
      images: config.image
        ? [
            {
              url: config.image.startsWith('http')
                ? config.image
                : `${DEFAULT_CONFIG.siteUrl}${config.image}`,
              width: 1200,
              height: 630,
              alt: title,
            },
          ]
        : [
            {
              url: `${DEFAULT_CONFIG.siteUrl}${DEFAULT_CONFIG.defaultImage}`,
              width: 1200,
              height: 630,
              alt: DEFAULT_CONFIG.siteName,
            },
          ],
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      creator: DEFAULT_CONFIG.twitterHandle,
      images: config.image ? [config.image] : [DEFAULT_CONFIG.defaultImage],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };

  // Add article-specific metadata
  if (config.type === 'article' && config.publishedTime) {
    metadata.openGraph = {
      ...metadata.openGraph,
      type: 'article',
      publishedTime: config.publishedTime,
      modifiedTime: config.modifiedTime,
      authors: config.author ? [config.author] : undefined,
      section: config.section,
      tags: config.tags,
    };
  }

  // Add alternate language
  if (config.alternateLocale) {
    metadata.alternates = {
      languages: {
        [config.alternateLocale]: config.url || '/',
      },
    };
  }

  return metadata;
}

export function generateStructuredData(config: StructuredDataConfig): object {
  const baseData = {
    '@context': 'https://schema.org',
    '@type': config.type,
    name: config.title,
    description: config.description,
    url: config.url ? `${DEFAULT_CONFIG.siteUrl}${config.url}` : undefined,
    image: config.image
      ? config.image.startsWith('http')
        ? config.image
        : `${DEFAULT_CONFIG.siteUrl}${config.image}`
      : undefined,
  };

  switch (config.type) {
    case 'Article':
    case 'NewsArticle':
      return {
        ...baseData,
        headline: config.title,
        datePublished: config.datePublished,
        dateModified: config.dateModified || config.datePublished,
        author: config.author
          ? {
              '@type': config.author.type,
              name: config.author.name,
            }
          : undefined,
        publisher: config.publisher
          ? {
              '@type': 'Organization',
              name: config.publisher.name,
              logo: {
                '@type': 'ImageObject',
                url: `${DEFAULT_CONFIG.siteUrl}${config.publisher.logo}`,
              },
            }
          : {
              '@type': 'Organization',
              name: DEFAULT_CONFIG.organization.name,
              logo: {
                '@type': 'ImageObject',
                url: `${DEFAULT_CONFIG.siteUrl}${DEFAULT_CONFIG.organization.logo}`,
              },
            },
      };

    case 'BreadcrumbList':
      return {
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: config.breadcrumbs?.map((crumb, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          name: crumb.name,
          item: `${DEFAULT_CONFIG.siteUrl}${crumb.url}`,
        })),
      };

    case 'Organization':
      return {
        ...baseData,
        '@type': 'Organization',
        name: DEFAULT_CONFIG.organization.name,
        url: DEFAULT_CONFIG.siteUrl,
        logo: `${DEFAULT_CONFIG.siteUrl}${DEFAULT_CONFIG.organization.logo}`,
        contactPoint: {
          '@type': 'ContactPoint',
          telephone: '+60-3-8892-5000',
          contactType: 'customer service',
          availableLanguage: ['English', 'Malay'],
        },
        sameAs: [
          'https://www.facebook.com/HabHalalJakim/',
          'https://x.com/halal_malaysia',
        ],
      };

    default:
      return baseData;
  }
}

export function generateSitemap(
  pages: Array<{
    url: string;
    lastModified?: string;
    changeFrequency?:
      | 'always'
      | 'hourly'
      | 'daily'
      | 'weekly'
      | 'monthly'
      | 'yearly'
      | 'never';
    priority?: number;
  }>,
): string {
  const urls = pages
    .map(
      (page) => `
    <url>
      <loc>${DEFAULT_CONFIG.siteUrl}${page.url}</loc>
      ${page.lastModified ? `<lastmod>${page.lastModified}</lastmod>` : ''}
      ${page.changeFrequency ? `<changefreq>${page.changeFrequency}</changefreq>` : ''}
      ${page.priority ? `<priority>${page.priority}</priority>` : ''}
    </url>
  `,
    )
    .join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  ${urls}
</urlset>`;
}

export function generateRobotsTxt(): string {
  return `User-agent: *
Allow: /

Sitemap: ${DEFAULT_CONFIG.siteUrl}/sitemap.xml

# Disallow admin areas (if any)
Disallow: /admin/
Disallow: /api/

# Allow important pages
Allow: /api/search
Allow: /api/announcements
Allow: /api/news`;
}

// SEO utilities for content optimization
export const seoUtils = {
  // Generate meta description from content
  generateMetaDescription(content: string, maxLength = 160): string {
    const cleanContent = content.replace(/<[^>]*>/g, '').trim();
    if (cleanContent.length <= maxLength) {
      return cleanContent;
    }

    const truncated = cleanContent.substring(0, maxLength);
    const lastSpace = truncated.lastIndexOf(' ');
    return lastSpace > 0
      ? `${truncated.substring(0, lastSpace)}...`
      : `${truncated}...`;
  },

  // Extract keywords from content
  extractKeywords(content: string, maxKeywords = 10): string[] {
    const words = content
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter((word) => word.length > 3);

    const frequency: Record<string, number> = {};
    words.forEach((word) => {
      frequency[word] = (frequency[word] || 0) + 1;
    });

    return Object.entries(frequency)
      .sort(([, a], [, b]) => b - a)
      .slice(0, maxKeywords)
      .map(([word]) => word);
  },

  // Validate SEO requirements
  validateSEO(config: SEOConfig): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    if (!config.title || config.title.length < 10) {
      issues.push('Title should be at least 10 characters long');
    }
    if (config.title && config.title.length > 60) {
      issues.push(
        'Title should be less than 60 characters for optimal display',
      );
    }
    if (!config.description || config.description.length < 120) {
      issues.push('Description should be at least 120 characters long');
    }
    if (config.description && config.description.length > 160) {
      issues.push(
        'Description should be less than 160 characters for optimal display',
      );
    }
    if (!config.keywords || config.keywords.length === 0) {
      issues.push('Keywords are required for SEO');
    }

    return {
      isValid: issues.length === 0,
      issues,
    };
  },
};
