'use client';

import type { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveContainerProps {
  children: ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  as?: 'div' | 'section' | 'article' | 'main';
}

export function ResponsiveContainer({
  children,
  className,
  size = 'lg',
  padding = 'md',
  as: Component = 'div',
}: ResponsiveContainerProps) {
  const sizeClasses = {
    sm: 'max-w-2xl',
    md: 'max-w-4xl',
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    full: 'max-w-full',
  };

  const paddingClasses = {
    none: '',
    sm: 'px-4 sm:px-6',
    md: 'px-4 sm:px-6 lg:px-8',
    lg: 'px-4 sm:px-6 lg:px-8 xl:px-12',
  };

  return (
    <Component
      className={cn(
        'mx-auto w-full',
        sizeClasses[size],
        paddingClasses[padding],
        className,
      )}
    >
      {children}
    </Component>
  );
}

interface ResponsiveGridProps {
  children: ReactNode;
  className?: string;
  cols?: {
    default: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: 'sm' | 'md' | 'lg';
}

export function ResponsiveGrid({
  children,
  className,
  cols = { default: 1, md: 2, lg: 3 },
  gap = 'md',
}: ResponsiveGridProps) {
  const gapClasses = {
    sm: 'gap-4',
    md: 'gap-6',
    lg: 'gap-8',
  };

  const getGridCols = () => {
    const classes = [`grid-cols-${cols.default}`];

    if (cols.sm) {
      classes.push(`sm:grid-cols-${cols.sm}`);
    }
    if (cols.md) {
      classes.push(`md:grid-cols-${cols.md}`);
    }
    if (cols.lg) {
      classes.push(`lg:grid-cols-${cols.lg}`);
    }
    if (cols.xl) {
      classes.push(`xl:grid-cols-${cols.xl}`);
    }

    return classes.join(' ');
  };

  return (
    <div className={cn('grid', getGridCols(), gapClasses[gap], className)}>
      {children}
    </div>
  );
}

interface ResponsiveTextProps {
  children: ReactNode;
  className?: string;
  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  color?: 'primary' | 'secondary' | 'muted' | 'accent';
  as?: 'p' | 'span' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

export function ResponsiveText({
  children,
  className,
  size = 'base',
  weight = 'normal',
  color = 'primary',
  as: Component = 'p',
}: ResponsiveTextProps) {
  const sizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    base: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-xl sm:text-2xl',
    '3xl': 'text-2xl sm:text-3xl',
    '4xl': 'text-2xl sm:text-3xl lg:text-4xl',
  };

  const weightClasses = {
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold',
  };

  const colorClasses = {
    primary: 'text-gray-900',
    secondary: 'text-gray-600',
    muted: 'text-gray-500',
    accent: 'text-primary-green',
  };

  return (
    <Component
      className={cn(
        sizeClasses[size],
        weightClasses[weight],
        colorClasses[color],
        className,
      )}
    >
      {children}
    </Component>
  );
}

interface ResponsiveSpacingProps {
  children: ReactNode;
  className?: string;
  y?: 'sm' | 'md' | 'lg' | 'xl';
  x?: 'sm' | 'md' | 'lg' | 'xl';
}

export function ResponsiveSpacing({
  children,
  className,
  y = 'md',
  x,
}: ResponsiveSpacingProps) {
  const yClasses = {
    sm: 'py-6 sm:py-8',
    md: 'py-8 sm:py-12 lg:py-16',
    lg: 'py-12 sm:py-16 lg:py-20',
    xl: 'py-16 sm:py-20 lg:py-24',
  };

  const xClasses = x
    ? {
        sm: 'px-4 sm:px-6',
        md: 'px-4 sm:px-6 lg:px-8',
        lg: 'px-4 sm:px-6 lg:px-8 xl:px-12',
        xl: 'px-6 sm:px-8 lg:px-12 xl:px-16',
      }
    : {};

  return (
    <div className={cn(yClasses[y], x && xClasses[x], className)}>
      {children}
    </div>
  );
}

// Hook for responsive breakpoint detection
export function useResponsive() {
  // This would typically use window.matchMedia in a real implementation
  // For now, we'll return a simple object
  return {
    isMobile: false, // window.innerWidth < 768
    isTablet: false, // window.innerWidth >= 768 && window.innerWidth < 1024
    isDesktop: false, // window.innerWidth >= 1024
    breakpoint: 'lg' as 'sm' | 'md' | 'lg' | 'xl',
  };
}
