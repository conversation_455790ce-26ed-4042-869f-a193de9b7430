import { DatabaseService } from '../database/service';
import { BrowserManager } from '../scraper/browser';
import { DataExtractor } from '../scraper/extractor';
import type {
  ScrapedCompany,
  ScrapingConfig,
  ScrapingResult,
  ScrapingStats,
} from '../types';
import { Logger, ProgressTracker } from '../utils/logger';
import { <PERSON><PERSON>r<PERSON><PERSON><PERSON>, RetryManager } from '../utils/retry';
import { DataQualityChecker } from '../utils/validation';

export class HalalCrawler {
  private config: ScrapingConfig;
  private browserManager: BrowserManager;
  private dataExtractor: DataExtractor;
  private databaseService: DatabaseService;
  private stats: ScrapingStats;

  constructor(config: ScrapingConfig) {
    this.config = config;
    this.browserManager = new BrowserManager(config);
    this.dataExtractor = new DataExtractor(config);
    this.databaseService = new DatabaseService();
    this.stats = {
      totalPages: 0,
      processedPages: 0,
      totalCompanies: 0,
      savedCompanies: 0,
      duplicates: 0,
      errors: 0,
      startTime: new Date(),
    };
  }

  async crawl(startUrl: string): Promise<ScrapingResult> {
    const errors: string[] = [];

    try {
      Logger.section('Initializing Crawler');

      // Initialize browser
      await this.browserManager.initialize();
      Logger.success('Browser initialized');

      // Test database connection
      const dbConnected = await this.databaseService.testConnection();
      if (!dbConnected) {
        throw new Error('Database connection failed');
      }
      Logger.success('Database connection verified');

      // Start crawling process
      Logger.section('Starting Crawl Process');
      const result = await this.performCrawling(startUrl);

      // Calculate final stats
      this.stats.endTime = new Date();
      this.stats.duration =
        this.stats.endTime.getTime() - this.stats.startTime.getTime();

      // Display data quality report
      await this.displayDataQualityReport();

      return {
        success: result.success,
        stats: this.stats,
        errors: [...errors, ...result.errors],
      };
    } catch (error) {
      const errorMessage = `Fatal crawler error: ${error}`;
      Logger.error(errorMessage);
      errors.push(errorMessage);

      return {
        success: false,
        stats: this.stats,
        errors,
      };
    } finally {
      // Cleanup
      await this.cleanup();
    }
  }

  private async performCrawling(
    startUrl: string,
  ): Promise<{ success: boolean; errors: string[] }> {
    const errors: string[] = [];
    const currentUrl = startUrl;
    let pageNumber = 1;

    const progressTracker = new ProgressTracker(
      this.config.maxPages,
      'Crawling Pages',
    );

    try {
      while (pageNumber <= this.config.maxPages && currentUrl) {
        Logger.info(`Processing page ${pageNumber}/${this.config.maxPages}`);

        const pageResult = await this.processPage(currentUrl, pageNumber);

        if (pageResult.success) {
          this.stats.processedPages++;
          this.stats.totalCompanies += pageResult.companies.length;

          // Companies are already saved individually during extraction
          // Just update the stats from the page result
          this.stats.savedCompanies += pageResult.savedCompanies;
          this.stats.duplicates += pageResult.duplicates;
          this.stats.errors += pageResult.saveErrors;

          if (pageResult.errorMessages.length > 0) {
            errors.push(...pageResult.errorMessages);
          }

          // Check if there's a next page
          if (pageResult.hasNextPage && pageNumber < this.config.maxPages) {
            // For POST-based pagination, we keep the same base URL
            // The actual pagination is handled in the processPage method
            pageNumber++;

            // Add delay between requests
            if (this.config.delayBetweenRequests > 0) {
              await this.delay(this.config.delayBetweenRequests);
            }
          } else {
            Logger.info('No more pages to process or reached maximum pages');
            break;
          }
        } else {
          this.stats.errors++;
          errors.push(
            `Failed to process page ${pageNumber}: ${pageResult.error}`,
          );

          // Check if we should abort due to too many errors
          if (ErrorHandler.shouldAbort()) {
            Logger.error('Aborting crawl due to too many errors');
            break;
          }

          pageNumber++;
        }

        progressTracker.update();

        // Update total pages if we know there are more
        if (pageNumber > this.stats.totalPages) {
          this.stats.totalPages = pageNumber;
        }
      }

      progressTracker.complete();
      this.stats.totalPages = Math.max(this.stats.totalPages, pageNumber - 1);

      return {
        success: this.stats.processedPages > 0,
        errors,
      };
    } catch (error) {
      const errorMessage = `Error during crawling process: ${error}`;
      Logger.error(errorMessage);
      errors.push(errorMessage);

      return {
        success: false,
        errors,
      };
    }
  }

  private async processPage(
    url: string,
    pageNumber: number,
  ): Promise<{
    success: boolean;
    companies: ScrapedCompany[];
    savedCompanies: number;
    duplicates: number;
    saveErrors: number;
    errorMessages: string[];
    hasNextPage: boolean;
    error?: string;
  }> {
    return await RetryManager.retry(
      async () => {
        const page = await this.browserManager.createPage();

        try {
          Logger.debug(`Navigating to: ${url}`);

          // Navigate to page using different methods for page 1 vs subsequent pages
          if (pageNumber === 1) {
            // First page - navigate to base URL and use POST form with hdnCounter
            const baseUrl =
              'https://myehalal.halal.gov.my/portal-halal/v1/index.php?data=ZGlyZWN0b3J5L2luZGV4X2RpcmVjdG9yeTs7Ozs=';

            await page.goto(baseUrl, {
              waitUntil: 'networkidle0',
              timeout: this.config.timeoutMs,
            });

            // Wait for page to load completely
            await this.delay(3000);

            // Submit POST form with hdnCounter for page 1
            Logger.debug('Submitting POST form with hdnCounter for page 1');
            await page.evaluate(() => {
              const form = document.createElement('form');
              form.method = 'POST';
              form.action = window.location.href;

              // Add form fields for page 1
              const fields = {
                hdnCounter: '1', // Start with 1 for first page
                t: '',
                a: '',
                ty: 'CO',
              };

              for (const [name, value] of Object.entries(fields)) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = name;
                input.value = value;
                form.appendChild(input);
              }

              document.body.appendChild(form);
              form.submit();
            });

            // Wait for the POST response to load
            await page.waitForNavigation({
              waitUntil: 'networkidle0',
              timeout: this.config.timeoutMs,
            });
          } else {
            // Subsequent pages - use page number clicking
            Logger.debug(
              `Navigating to page ${pageNumber} using page number clicking`,
            );

            // First navigate to the base URL to ensure we're on the right page
            const baseUrl =
              'https://myehalal.halal.gov.my/portal-halal/v1/index.php?data=ZGlyZWN0b3J5L2luZGV4X2RpcmVjdG9yeTs7Ozs=';
            await page.goto(baseUrl, {
              waitUntil: 'networkidle0',
              timeout: this.config.timeoutMs,
            });

            // Wait for page to load
            await this.delay(3000);

            // Use the data extractor's page clicking method
            const pageClicked = await this.dataExtractor.clickPageNumber(
              page,
              pageNumber,
            );

            if (!pageClicked) {
              throw new Error(
                `Could not navigate to page ${pageNumber} using page number clicking`,
              );
            }
          }

          // Track saving stats for this page
          let savedCompanies = 0;
          let duplicates = 0;
          let saveErrors = 0;
          const errorMessages: string[] = [];

          // Create callback to save each company as it's extracted
          const onCompanyFound = async (company: ScrapedCompany) => {
            try {
              if (this.config.debug) {
                Logger.debug(
                  `Attempting to save company: ${JSON.stringify(company, null, 2)}`,
                );
              }

              const saveResult = await this.databaseService.saveCompany(
                company,
                this.config.siteId,
              );

              if (saveResult.saved) {
                savedCompanies++;
                Logger.info(`✅ Saved: ${company.companyName}`);
              } else if (saveResult.duplicate) {
                duplicates++;
                Logger.debug(`🔄 Duplicate: ${company.companyName}`);
              } else if (saveResult.error) {
                saveErrors++;
                errorMessages.push(saveResult.error);
                Logger.warn(
                  `❌ Error saving ${company.companyName}: ${saveResult.error}`,
                );
              }
            } catch (error) {
              saveErrors++;
              const errorMsg = `Failed to save ${company.companyName}: ${error}`;
              errorMessages.push(errorMsg);
              Logger.error(errorMsg);
            }
          };

          // Debug: Take a screenshot and check page content
          if (this.config.debug) {
            try {
              await page.screenshot({
                path: `debug-page-${pageNumber}.png`,
                fullPage: true,
              });
              Logger.debug(`Screenshot saved as debug-page-${pageNumber}.png`);

              // Check page title and URL
              const title = await page.title();
              const url = page.url();
              Logger.debug(`Page title: ${title}`);
              Logger.debug(`Page URL: ${url}`);

              // Check if there are any tables
              const tableCount = await page.evaluate(() => {
                return document.querySelectorAll('table').length;
              });
              Logger.debug(`Found ${tableCount} tables on the page`);
            } catch (error) {
              Logger.debug(`Debug screenshot/info failed: ${error}`);
            }
          }

          // Extract companies from page with individual saving
          const companies = await this.dataExtractor.extractCompaniesFromPage(
            page,
            pageNumber,
            onCompanyFound,
          );

          // Check for next page
          const hasNextPage = await this.dataExtractor.hasNextPage(page);

          Logger.success(
            `Extracted ${companies.length} companies from page ${pageNumber} (Saved: ${savedCompanies}, Duplicates: ${duplicates}, Errors: ${saveErrors})`,
          );

          return {
            success: true,
            companies,
            savedCompanies,
            duplicates,
            saveErrors,
            errorMessages,
            hasNextPage,
          };
        } catch (error) {
          const errorInfo = ErrorHandler.handleError(
            error,
            `page-${pageNumber}`,
          );

          return {
            success: false,
            companies: [],
            savedCompanies: 0,
            duplicates: 0,
            saveErrors: 0,
            errorMessages: [],
            hasNextPage: false,
            error: errorInfo.message,
          };
        } finally {
          await this.browserManager.closePage(page);
        }
      },
      RetryManager.createRetryOptions('navigation'),
      `page-${pageNumber}`,
    );
  }

  private async displayDataQualityReport(): Promise<void> {
    try {
      Logger.section('Data Quality Report');

      // Get recent companies for quality assessment
      const recentCompanies = await this.databaseService.getCompaniesBySite(
        this.config.siteId,
        100,
        0,
      );

      if (recentCompanies.length > 0) {
        // Convert database companies to scraped format for quality check
        const scrapedFormat = recentCompanies.map((company) => ({
          companyName: company.companyName,
          address: company.address,
          phone: company.phone,
          email: company.email,
          state: company.state,
          category: company.category,
        })) as ScrapedCompany[];

        const qualityReport =
          DataQualityChecker.assessDataQuality(scrapedFormat);

        Logger.stats('Data Quality Metrics', {
          'Total Records': qualityReport.totalRecords,
          'Valid Records': qualityReport.validRecords,
          'Invalid Records': qualityReport.invalidRecords,
          'Completeness Score': `${Math.round(qualityReport.completenessScore)}%`,
          'Quality Issues': qualityReport.qualityIssues.length,
        });

        if (qualityReport.qualityIssues.length > 0) {
          Logger.warn('Quality Issues Found:');
          qualityReport.qualityIssues.slice(0, 5).forEach((issue) => {
            Logger.warn(`  • ${issue}`);
          });

          if (qualityReport.qualityIssues.length > 5) {
            Logger.warn(
              `  ... and ${qualityReport.qualityIssues.length - 5} more issues`,
            );
          }
        }
      }

      // Display error statistics
      const errorStats = ErrorHandler.getErrorStats();
      if (Object.keys(errorStats).length > 0) {
        Logger.stats('Error Statistics', errorStats);
      }
    } catch (error) {
      Logger.error('Error generating data quality report:', error);
    }
  }

  private async cleanup(): Promise<void> {
    try {
      await this.browserManager.close();
      Logger.debug('Cleanup completed');
    } catch (error) {
      Logger.error('Error during cleanup:', error);
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
