#!/usr/bin/env node

const FRONTEND_URL = 'http://localhost:16000';

async function testDashboardEndToEnd() {
  console.log('🚀 Testing Admin Dashboard End-to-End\n');
  
  // Step 1: Test login flow
  console.log('📋 Step 1: Testing login flow');
  
  try {
    // Login via API
    const loginResponse = await fetch(`${FRONTEND_URL}/api/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      }),
    });

    const loginData = await loginResponse.json();
    
    if (!loginResponse.ok || !loginData.token) {
      console.log('❌ Login failed');
      return;
    }
    
    console.log('✅ Login successful, token received');
    const token = loginData.token;
    
    // Step 2: Test all admin data endpoints
    console.log('\n📋 Step 2: Testing all admin data endpoints');
    
    const endpoints = [
      { name: 'Users', path: '/api/admin/users' },
      { name: 'Collections', path: '/api/admin/collections' },
      { name: 'Bo<PERSON>', path: '/api/admin/bots' },
      { name: 'Services', path: '/api/admin/services' },
      { name: 'Sessions', path: '/api/admin/sessions' },
      { name: 'S3 Configurations', path: '/api/admin/s3-configurations' },
    ];
    
    const results = {};
    
    for (const endpoint of endpoints) {
      try {
        const response = await fetch(`${FRONTEND_URL}${endpoint.path}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
        
        if (response.ok) {
          const data = await response.json();
          const count = Array.isArray(data) ? data.length : 'N/A';
          results[endpoint.name] = { status: 'success', count };
          console.log(`✅ ${endpoint.name}: ${count} items`);
        } else {
          results[endpoint.name] = { status: 'error', code: response.status };
          console.log(`❌ ${endpoint.name}: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        results[endpoint.name] = { status: 'error', message: error.message };
        console.log(`❌ ${endpoint.name}: ${error.message}`);
      }
    }
    
    // Step 3: Test create operations
    console.log('\n📋 Step 3: Testing create operations');
    
    const timestamp = Date.now();
    
    // Test creating a bot
    try {
      const botResponse = await fetch(`${FRONTEND_URL}/api/admin/bots`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: `E2E Test Bot ${timestamp}`,
          slug: `e2e-test-bot-${timestamp}`,
          provider: 'openai',
          model: 'gpt-4',
          temperature: 0.7,
          isDefault: false,
          systemPrompt: 'You are a test assistant for E2E testing.'
        }),
      });
      
      if (botResponse.ok) {
        const botData = await botResponse.json();
        console.log(`✅ Bot creation: Success (ID: ${botData.id})`);
      } else {
        const errorData = await botResponse.json();
        console.log(`❌ Bot creation: ${botResponse.status} - ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`❌ Bot creation: ${error.message}`);
    }
    
    // Test creating a collection
    try {
      const collectionResponse = await fetch(`${FRONTEND_URL}/api/admin/collections`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: `E2E Test Collection ${timestamp}`,
          status: 'ACTIVE'
        }),
      });
      
      if (collectionResponse.ok) {
        const collectionData = await collectionResponse.json();
        console.log(`✅ Collection creation: Success (ID: ${collectionData.id})`);
      } else {
        const errorData = await collectionResponse.json();
        console.log(`❌ Collection creation: ${collectionResponse.status} - ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`❌ Collection creation: ${error.message}`);
    }
    
    // Step 4: Test frontend pages
    console.log('\n📋 Step 4: Testing frontend pages');
    
    const pages = [
      { name: 'Admin Login', path: '/en/admin' },
      { name: 'Collections Page', path: '/en/admin/collections' },
      { name: 'Users Page', path: '/en/admin/users' },
      { name: 'Bots Page', path: '/en/admin/bots' },
      { name: 'Services Page', path: '/en/admin/services' },
      { name: 'Sessions Page', path: '/en/admin/sessions' },
      { name: 'S3 Configurations Page', path: '/en/admin/s3-configurations' },
      { name: 'New Bot Form', path: '/en/admin/bots/new' },
      { name: 'New Collection Form', path: '/en/admin/collections/new' },
    ];
    
    for (const page of pages) {
      try {
        const response = await fetch(`${FRONTEND_URL}${page.path}`);
        const statusColor = response.ok ? '✅' : '❌';
        console.log(`${statusColor} ${page.name}: ${response.status} ${response.statusText}`);
      } catch (error) {
        console.log(`❌ ${page.name}: ${error.message}`);
      }
    }
    
    // Step 5: Summary
    console.log('\n📋 Step 5: Test Summary');
    console.log('='.repeat(50));
    
    const successfulEndpoints = Object.values(results).filter(r => r.status === 'success').length;
    const totalEndpoints = Object.keys(results).length;
    
    console.log(`API Endpoints: ${successfulEndpoints}/${totalEndpoints} working`);
    console.log('Data Summary:');
    
    for (const [name, result] of Object.entries(results)) {
      if (result.status === 'success') {
        console.log(`  - ${name}: ${result.count} items`);
      } else {
        console.log(`  - ${name}: ERROR`);
      }
    }
    
    if (successfulEndpoints === totalEndpoints) {
      console.log('\n🎉 ALL TESTS PASSED! Admin Dashboard is fully functional!');
    } else {
      console.log('\n⚠️ Some tests failed. Check the errors above.');
    }
    
  } catch (error) {
    console.error('❌ E2E test failed:', error);
  }
}

testDashboardEndToEnd().catch(console.error);
