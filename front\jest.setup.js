import '@testing-library/jest-dom';

// Only mock Next.js router when absolutely necessary
// Most tests should use actual router behavior when possible
const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  prefetch: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
};

// Only mock when MOCK_ROUTER environment variable is set
if (process.env.MOCK_ROUTER === 'true') {
  jest.mock('next/navigation', () => ({
    useRouter: () => mockRouter,
    useSearchParams: () => new URLSearchParams(),
    usePathname: () => '/',
    useParams: () => ({}),
  }));
}

// Only mock Next.js Image when MOCK_IMAGE environment variable is set
if (process.env.MOCK_IMAGE === 'true') {
  jest.mock('next/image', () => ({
    __esModule: true,
    default: (props) => {
      return <div {...props} />;
    },
  }));
}

// Essential browser API mocks (these are needed for jsdom environment)
global.IntersectionObserver = class IntersectionObserver {
  constructor(callback, options) {
    this.callback = callback;
    this.options = options;
  }
  disconnect() {}
  observe() {}
  unobserve() {}
};

global.ResizeObserver = class ResizeObserver {
  constructor(callback) {
    this.callback = callback;
  }
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock matchMedia only when needed
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Create a more realistic localStorage mock
const createLocalStorageMock = () => {
  let store = {};
  return {
    getItem: jest.fn((key) => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn((key) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    }),
    get length() {
      return Object.keys(store).length;
    },
    key: jest.fn((index) => Object.keys(store)[index] || null),
  };
};

global.localStorage = createLocalStorageMock();
global.sessionStorage = createLocalStorageMock();

// Only mock fetch when MOCK_FETCH environment variable is set
if (process.env.MOCK_FETCH !== 'false') {
  global.fetch = jest.fn();
}

// Setup console error suppression for expected errors in tests
const originalError = console.error;
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});
