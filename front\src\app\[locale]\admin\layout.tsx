'use client';

import {
  Award,
  Bot,
  Briefcase,
  Building,
  Contact,
  Facebook,
  File,
  FileText,
  GitBranch,
  LayoutDashboard,
  Menu,
  MessageSquare,
  Package2,
  Settings,
  Smartphone,
  Users,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Link, usePathname } from '@/i18n/navigation';
import { cn } from '@/lib/utils';

const getAdminNavItems = () => [
  {
    href: '/admin/dashboard',
    label: 'Dashboard',
    icon: LayoutDashboard,
  },
  {
    href: '/admin/corporate',
    label: 'Corporate',
    icon: Building,
  },
  // {
  //   href: '/admin/certification',
  //   label: 'Certification',
  //   icon: Award,
  // },
  // {
  //   href: '/admin/procedure',
  //   label: 'Procedure',
  //   icon: File,
  // },
  {
    href: '/admin/contact',
    label: 'Contact Us',
    icon: Contact,
  },
  {
    href: '/admin/bots',
    label: 'Bots',
    icon: Bot,
  },
  {
    href: '/admin/users',
    label: 'Users',
    icon: Users,
  },
  {
    href: '/admin/agents',
    label: 'Agents',
    icon: Briefcase,
  },
  {
    href: '/admin/sessions',
    label: 'Sessions',
    icon: MessageSquare,
  },
  {
    href: '/admin/collections',
    label: 'Document Collections',
    icon: FileText,
  },
  {
    href: '/admin/documents',
    label: 'Documents',
    icon: File,
  },
  {
    href: '/admin/s3-configurations',
    label: 'S3 Configurations',
    icon: Settings,
  },
  {
    href: '/admin/services',
    label: 'Services',
    icon: GitBranch,
  },
  {
    href: '/admin/facebook',
    label: 'Facebook',
    icon: Facebook,
  },
  {
    href: '/admin/whatsapp',
    label: 'WhatsApp',
    icon: Smartphone,
  },
];

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const adminNavItems = getAdminNavItems();

  const renderNavLinks = () =>
    adminNavItems.map((item) => (
      <Link
        key={item.label}
        href={item.href}
        className={cn(
          'flex items-center gap-3 rounded-lg px-3 py-2 text-gray-800 transition-all hover:text-green-600 ',
          {
            'bg-green-100 text-green-900 dark:bg-green-800 dark:text-green-50':
              pathname.startsWith(item.href),
          },
        )}
      >
        <item.icon className="h-4 w-4" />
        {item.label}
      </Link>
    ));

  return (
    <div className="grid min-h-screen w-full lg:grid-cols-[280px_1fr]">
      <div className="hidden border-r bg-green-50/80 lg:block dark:bg-green-900/20">
        <div className="flex h-full max-h-screen flex-col gap-2">
          <div className="flex h-[60px] items-center border-b px-6">
            <Link
              href="/admin"
              className="flex items-center gap-2 font-semibold"
            >
              <Package2 className="h-6 w-6" />
              <span>Admin Dashboard</span>
            </Link>
          </div>
          <div className="flex-1 overflow-auto py-2">
            <nav className="grid items-start px-4 text-sm font-medium">
              {renderNavLinks()}
            </nav>
          </div>
        </div>
      </div>
      <div className="flex flex-col">
        <header className="flex h-14 items-center gap-4 border-b bg-green-50/30 px-6 dark:bg-green-900/10 lg:h-[60px]">
          <Sheet>
            <SheetTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className="shrink-0 lg:hidden"
              >
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-[280px]">
              <nav className="grid gap-6 text-lg font-medium pt-8">
                <Link
                  href="/admin"
                  className="flex items-center gap-2 text-lg font-semibold mb-4"
                >
                  <Package2 className="h-6 w-6" />
                  <span>Admin Dashboard</span>
                </Link>
                {renderNavLinks()}
              </nav>
            </SheetContent>
          </Sheet>
          <div className="w-full flex-1">
            {/* Can add breadcrumbs or other header content here */}
          </div>
        </header>
        <main className="flex-1 p-4 sm:p-6">{children}</main>
      </div>
    </div>
  );
}
