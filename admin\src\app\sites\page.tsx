'use client';

import { Edit, Globe, Plus, Search, Trash2 } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useSitesStore } from '@/stores/sites';

export default function SitesPage() {
  const { sites, isLoading, error, fetchSites, deleteSite, clearError } =
    useSitesStore();
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    fetchSites();
  }, [fetchSites]);

  const handleDelete = async (id: number, name: string) => {
    if (
      confirm(
        `Are you sure you want to delete the site "${name}"? This action cannot be undone.`,
      )
    ) {
      const success = await deleteSite(id);
      if (success) {
        // Refresh the list
        fetchSites();
      }
    }
  };

  const filteredSites = sites.filter(
    (site) =>
      site.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      site.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
      site.domains.some((domain) =>
        domain.toLowerCase().includes(searchQuery.toLowerCase()),
      ),
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Sites</h1>
            <p className="text-gray-600">Manage all sites in the system</p>
          </div>
          <Link href="/sites/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Site
            </Button>
          </Link>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <div className="flex items-center justify-between">
              <span>{error}</span>
              <Button variant="ghost" size="sm" onClick={clearError}>
                ×
              </Button>
            </div>
          </div>
        )}

        <Card>
          <CardHeader>
            <CardTitle>All Sites</CardTitle>
            <CardDescription>
              View and manage all sites in the system
            </CardDescription>
            <div className="flex items-center space-x-2">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search sites..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <p className="text-gray-500">Loading sites...</p>
              </div>
            ) : filteredSites.length === 0 ? (
              <div className="text-center py-8">
                <Globe className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  No sites found
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchQuery
                    ? 'No sites match your search.'
                    : 'Get started by creating a new site.'}
                </p>
                {!searchQuery && (
                  <div className="mt-6">
                    <Link href="/sites/new">
                      <Button>
                        <Plus className="mr-2 h-4 w-4" />
                        Add Site
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredSites.map((site) => (
                  <div
                    key={site.id}
                    className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-lg font-medium text-gray-900">
                            {site.name}
                          </h3>
                          <span className="text-sm text-gray-500">
                            ({site.code})
                          </span>
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              site.status
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}
                          >
                            {site.status ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                        <div className="mt-2">
                          <p className="text-sm text-gray-600">
                            <strong>Domains:</strong> {site.domains.join(', ')}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            Created:{' '}
                            {new Date(site.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Link href={`/sites/${site.id}/edit`}>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(site.id, site.name)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
