import { z } from 'zod';
import type { CrawlerConfig } from '../types/crawler.js';
import type { MediaItem, SocialMediaPost } from '../types/social-media.js';

// Zod schemas for validation
export const MediaItemSchema = z.object({
  type: z.enum(['image', 'video', 'audio']),
  url: z.string().url(),
  thumbnailUrl: z.string().url().optional(),
  duration: z.number().positive().optional(),
  dimensions: z
    .object({
      width: z.number().positive(),
      height: z.number().positive(),
    })
    .optional(),
  fileSize: z.number().positive().optional(),
  format: z.string().optional(),
  quality: z.string().optional(),
});

export const SocialMediaPostSchema = z.object({
  id: z.string(),
  platform: z.enum(['douyin', 'tiktok', 'instagram', 'facebook', 'youtube']),
  url: z.string().url(),
  title: z.string().optional(),
  content: z.string().optional(),
  author: z.object({
    username: z.string(),
    displayName: z.string().optional(),
    avatarUrl: z.string().url().optional(),
    verified: z.boolean().optional(),
    followerCount: z.number().nonnegative().optional(),
  }),
  publishedAt: z.date().optional(),
  engagement: z.object({
    likes: z.number().nonnegative(),
    comments: z.number().nonnegative(),
    shares: z.number().nonnegative(),
    views: z.number().nonnegative().optional(),
  }),
  hashtags: z.array(z.string()),
  mentions: z.array(z.string()),
  media: z.array(MediaItemSchema),
  location: z
    .object({
      name: z.string(),
      coordinates: z
        .object({
          latitude: z.number(),
          longitude: z.number(),
        })
        .optional(),
    })
    .optional(),
  metadata: z.record(z.any()),
});

export const CrawlerConfigSchema = z.object({
  platform: z.enum(['douyin', 'tiktok', 'instagram', 'facebook', 'youtube']),
  keywords: z.array(z.string()).min(1),
  maxPosts: z.number().positive(),
  downloadMedia: z.boolean(),
  mediaTypes: z.array(z.enum(['image', 'video', 'audio'])),
  outputDir: z.string(),
  rateLimiting: z.object({
    requestsPerMinute: z.number().positive(),
    downloadConcurrency: z.number().positive(),
  }),
  retryConfig: z.object({
    maxRetries: z.number().nonnegative(),
    backoffMultiplier: z.number().positive(),
    initialDelay: z.number().positive(),
  }),
  browserConfig: z.object({
    headless: z.boolean(),
    timeout: z.number().positive(),
    userAgent: z.string().optional(),
    viewport: z
      .object({
        width: z.number().positive(),
        height: z.number().positive(),
      })
      .optional(),
  }),
  filters: z
    .object({
      minLikes: z.number().nonnegative().optional(),
      minViews: z.number().nonnegative().optional(),
      dateRange: z
        .object({
          from: z.date(),
          to: z.date(),
        })
        .optional(),
      authors: z.array(z.string()).optional(),
      excludeAuthors: <AUTHORS>
    })
    .optional(),
});

// Validation functions
export function validateCrawlerConfig(config: unknown): CrawlerConfig {
  try {
    return CrawlerConfigSchema.parse(config);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors
        .map((err) => `${err.path.join('.')}: ${err.message}`)
        .join(', ');
      throw new Error(`Invalid crawler configuration: ${errorMessages}`);
    }
    throw error;
  }
}

export function validateSocialMediaPost(post: unknown): SocialMediaPost {
  try {
    return SocialMediaPostSchema.parse(post);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors
        .map((err) => `${err.path.join('.')}: ${err.message}`)
        .join(', ');
      throw new Error(`Invalid social media post: ${errorMessages}`);
    }
    throw error;
  }
}

export function validateMediaItem(media: unknown): MediaItem {
  try {
    return MediaItemSchema.parse(media);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors
        .map((err) => `${err.path.join('.')}: ${err.message}`)
        .join(', ');
      throw new Error(`Invalid media item: ${errorMessages}`);
    }
    throw error;
  }
}

// URL validation helpers
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export function isValidMediaUrl(
  url: string,
  mediaType: 'image' | 'video' | 'audio',
): boolean {
  if (!isValidUrl(url)) return false;

  const urlObj = new URL(url);
  const pathname = urlObj.pathname.toLowerCase();

  switch (mediaType) {
    case 'image':
      return /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(pathname);
    case 'video':
      return /\.(mp4|avi|mov|wmv|flv|webm|mkv)$/i.test(pathname);
    case 'audio':
      return /\.(mp3|wav|flac|aac|ogg|m4a)$/i.test(pathname);
    default:
      return false;
  }
}

// File validation helpers
export function validateFileName(fileName: string): boolean {
  // Check for invalid characters
  const invalidChars = /[<>:"/\\|?*]/;
  if (invalidChars.test(fileName)) return false;

  // Check length
  if (fileName.length === 0 || fileName.length > 255) return false;

  // Check for reserved names (Windows)
  const reservedNames = [
    'CON',
    'PRN',
    'AUX',
    'NUL',
    'COM1',
    'COM2',
    'COM3',
    'COM4',
    'COM5',
    'COM6',
    'COM7',
    'COM8',
    'COM9',
    'LPT1',
    'LPT2',
    'LPT3',
    'LPT4',
    'LPT5',
    'LPT6',
    'LPT7',
    'LPT8',
    'LPT9',
  ];
  const nameWithoutExt = fileName.split('.')[0]?.toUpperCase();
  if (nameWithoutExt && reservedNames.includes(nameWithoutExt)) return false;

  return true;
}

export function sanitizeFileName(fileName: string): string {
  // Replace invalid characters with underscores
  let sanitized = fileName.replace(/[<>:"/\\|?*]/g, '_');

  // Trim whitespace and dots
  sanitized = sanitized.trim().replace(/^\.+|\.+$/g, '');

  // Ensure it's not empty
  if (sanitized.length === 0) {
    sanitized = 'unnamed_file';
  }

  // Truncate if too long
  if (sanitized.length > 255) {
    const ext = sanitized.split('.').pop();
    const nameWithoutExt = sanitized.substring(0, sanitized.lastIndexOf('.'));
    sanitized =
      nameWithoutExt.substring(0, 255 - (ext ? ext.length + 1 : 0)) +
      (ext ? `.${ext}` : '');
  }

  return sanitized;
}
