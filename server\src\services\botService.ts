import { and, eq } from 'drizzle-orm';
import type { Database } from '@/db/connection';
import { bots } from '@/db/schema';

export interface BotConfig {
  id: number;
  name: string;
  slug: string;
  provider: string;
  model: string;
  temperature: number;
  isDefault: boolean;
  systemPrompt?: string;
  siteId: number;
}

export class BotService {
  constructor(private db: Database) {}

  /**
   * Get the default bot for a site
   */
  async getDefaultBot(siteId: number): Promise<BotConfig | null> {
    const [defaultBot] = await this.db
      .select()
      .from(bots)
      .where(and(eq(bots.siteId, siteId), eq(bots.isDefault, true)))
      .limit(1);

    return defaultBot || null;
  }

  /**
   * Get a bot by slug for a specific site
   */
  async getBotBySlug(slug: string, siteId: number): Promise<BotConfig | null> {
    const [bot] = await this.db
      .select()
      .from(bots)
      .where(and(eq(bots.slug, slug), eq(bots.siteId, siteId)))
      .limit(1);

    return bot || null;
  }

  /**
   * Get a bot by ID for a specific site
   */
  async getBotById(id: number, siteId: number): Promise<BotConfig | null> {
    const [bot] = await this.db
      .select()
      .from(bots)
      .where(and(eq(bots.id, id), eq(bots.siteId, siteId)))
      .limit(1);

    return bot || null;
  }

  /**
   * Get all bots for a site
   */
  async getBotsBySite(siteId: number): Promise<BotConfig[]> {
    return await this.db.select().from(bots).where(eq(bots.siteId, siteId));
  }

  /**
   * Get bot configuration for message handling
   * Falls back to default bot if specific bot not found
   */
  async getBotConfigForChat(
    siteId: number,
    botSlug?: string,
  ): Promise<BotConfig | null> {
    // Try to get specific bot first
    if (botSlug) {
      const bot = await this.getBotBySlug(botSlug, siteId);
      if (bot) return bot;
    }

    // Fall back to default bot
    return await this.getDefaultBot(siteId);
  }
}
