import { eq } from 'drizzle-orm';
import { type NextRequest } from 'next/server';
import { db, news } from '@/lib/db';
import { createSuccessResponse, createErrorResponse, handleApiError } from '@/lib/api-response';

// Remove edge runtime to use Node.js runtime for database access

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const slug = searchParams.get('slug');

    if (slug) {
      // Get specific news article by slug
      const newsArticle = await db
        .select()
        .from(news)
        .where(eq(news.slug, slug));

      if (newsArticle.length === 0) {
        return createErrorResponse('News article not found', undefined, 404);
      }

      return createSuccessResponse(newsArticle[0]);
    }

    // Get all news articles
    const allNews = await db.select().from(news);
    return createSuccessResponse(allNews);
  } catch (error) {
    return handleApiError(error, 'Error fetching news');
  }
}
