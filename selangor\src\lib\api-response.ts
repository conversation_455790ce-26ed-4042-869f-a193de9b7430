import { NextResponse } from 'next/server';

/**
 * Standardized API response format
 */
export interface StandardApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

/**
 * Create a successful API response
 */
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  status: number = 200
): NextResponse {
  const response: StandardApiResponse<T> = {
    success: true,
    data,
    ...(message && { message })
  };
  
  return NextResponse.json(response, { status });
}

/**
 * Create an error API response
 */
export function createErrorResponse(
  error: string,
  message?: string,
  status: number = 500
): NextResponse {
  const response: StandardApiResponse = {
    success: false,
    error,
    ...(message && { message })
  };
  
  return NextResponse.json(response, { status });
}

/**
 * Handle API errors consistently
 */
export function handleApiError(
  error: unknown,
  defaultMessage: string = 'Internal server error',
  status: number = 500
): NextResponse {
  console.error('API Error:', error);
  
  const errorMessage = error instanceof Error ? error.message : defaultMessage;
  
  return createErrorResponse(errorMessage, undefined, status);
}
