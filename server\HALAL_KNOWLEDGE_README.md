# Halal Knowledge Service

An AI-powered halal knowledge search service that combines R2R (RAG to Riches) with OpenAI to provide informed answers about Islamic and halal-related topics.

## Features

- **Intelligent Query Detection**: Automatically detects halal-related queries using keyword matching
- **R2R Integration**: Searches through a curated halal knowledge base using R2R
- **AI-Powered Answers**: Generates contextual answers using OpenAI with retrieved knowledge
- **Source Citations**: Provides source references for transparency (when enabled)
- **Multi-Platform Support**: Integrated into WhatsApp, Facebook Messenger, and web chat
- **Fallback Mechanism**: Falls back to general AI if halal knowledge search fails

## API Endpoints

### 1. Ask Halal Question
**POST** `/api/halal-knowledge/ask`

Ask a halal-related question and get an AI-generated answer based on the knowledge base.

**Request Body:**
```json
{
  "query": "Is chicken halal?",
  "sessionId": "optional-session-id",
  "maxResults": 5,
  "minScore": 0.6,
  "includeContext": true
}
```

**Response:**
```json
{
  "success": true,
  "answer": "Based on Islamic guidelines, chicken is generally considered halal...",
  "sources": [
    {
      "text": "Relevant knowledge base content...",
      "score": 0.85,
      "document_id": "halal-guidelines-001"
    }
  ],
  "query": "Is chicken halal?",
  "sessionId": "optional-session-id",
  "usage": {
    "prompt_tokens": 150,
    "completion_tokens": 75,
    "total_tokens": 225
  }
}
```

### 2. Health Check
**GET** `/api/halal-knowledge/health`

Check the health status of the halal knowledge service.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-06-24T07:01:21.146Z",
  "services": {
    "r2r": "connected",
    "openai": "available"
  },
  "message": "Halal knowledge service is operational"
}
```

### 3. Service Information
**GET** `/api/halal-knowledge/info`

Get information about the service capabilities and supported topics.

**Response:**
```json
{
  "service": "Halal Knowledge Search",
  "description": "AI-powered halal knowledge search using R2R and OpenAI",
  "version": "1.0.0",
  "capabilities": [
    "Search halal knowledge base",
    "Generate AI-powered answers",
    "Provide source citations",
    "Filter by relevance score",
    "Support for multiple Islamic topics"
  ],
  "supportedTopics": [
    "Halal and Haram rulings",
    "Islamic jurisprudence (Fiqh)",
    "Food and dietary laws",
    "Business and finance",
    "Prayer and worship",
    "Marriage and family",
    "Clothing and modesty",
    "General Islamic guidance"
  ]
}
```

## Automatic Integration

The halal knowledge service is automatically integrated into:

### WhatsApp Bot
- Automatically detects halal-related messages
- Uses halal knowledge for relevant queries
- Falls back to general AI for non-halal topics

### Facebook Messenger Bot
- Same automatic detection and response as WhatsApp
- Provides halal-specific answers when appropriate

### Web Chat
- Integrated into the main chat system
- Includes source citations for web users
- Seamless experience with regular chat

## Supported Keywords

The service automatically detects halal-related queries using these keywords:
- halal, haram, islamic, islam, muslim
- quran, sunnah, prophet, muhammad, allah
- permissible, forbidden, makruh, mustahab
- fiqh, shariah, sharia, fatwa, imam, scholar
- prayer, salah, zakat, hajj, ramadan, fasting, sawm
- food, meat, pork, alcohol, wine, gambling, riba
- interest, usury, marriage, divorce, inheritance
- hijab, modest, clothing, music, art
- business, finance, banking, investment
- charity, sadaqah, wudu, ghusl, taharah
- najis, pure, impure, clean, unclean

## Environment Variables

Required environment variables for the service:

```bash
# R2R Configuration
R2R_URL=https://your-r2r-instance.com
R2R_USERNAME=your-username
R2R_PASSWORD=your-password
R2R_COLLECTION_ID=your-collection-id

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
```

## Usage Examples

### Direct API Call
```javascript
const response = await fetch('/api/halal-knowledge/ask', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    query: "What are the requirements for halal meat?",
    maxResults: 5,
    includeContext: true
  })
});

const data = await response.json();
console.log(data.answer);
```

### Testing
Run the included test scripts:
```bash
# Test the halal knowledge service
node test-halal-knowledge.js

# Test chat integration (requires chat routes to be enabled)
node test-chat-integration.js
```

## Architecture

1. **Query Detection**: Uses keyword matching to identify halal-related queries
2. **Knowledge Search**: Searches R2R collection for relevant content
3. **Context Preparation**: Formats search results for AI consumption
4. **Answer Generation**: Uses OpenAI with Islamic scholar persona and context
5. **Response Formatting**: Returns structured response with sources

## Error Handling

- **Invalid queries**: Returns validation errors for empty or invalid input
- **Non-halal topics**: Politely redirects users to ask halal-related questions
- **R2R failures**: Falls back to general AI when knowledge search fails
- **Service unavailable**: Returns appropriate error messages with status codes

## Future Enhancements

- Support for multiple languages (Arabic, Malay, etc.)
- Advanced query understanding with NLP
- User feedback collection for answer quality
- Integration with more Islamic knowledge sources
- Caching for frequently asked questions
