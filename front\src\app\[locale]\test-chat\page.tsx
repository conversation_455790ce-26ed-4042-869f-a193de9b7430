import TestChatWidget from '@/components/chat/TestChatWidget';

export default function TestChatPage() {
  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Chat Widget Test Page
        </h1>

        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Features to Test:
          </h2>
          <ul className="list-disc list-inside space-y-2 text-gray-600">
            <li>Click the chat button in the bottom right to open the chat</li>
            <li>
              Test the <strong>Maximize button</strong> - click the maximize
              icon in the header
            </li>
            <li>
              Test the <strong>Minimize button</strong> - click the minimize
              icon in the header
            </li>
            <li>
              Send a message and observe the{' '}
              <strong>auto-scroll behavior</strong> - it should scroll to show
              the latest message, not just to the bottom
            </li>
            <li>Try sending multiple messages to see the scroll behavior</li>
            <li>Test maximized mode with long conversations</li>
          </ul>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Implementation Details:
          </h2>
          <div className="space-y-4 text-gray-600">
            <div>
              <h3 className="font-medium text-gray-800">Maximize Button:</h3>
              <p>
                Added a maximize button next to the minimize button that expands
                the chat to full screen with proper positioning.
              </p>
            </div>
            <div>
              <h3 className="font-medium text-gray-800">
                Auto-scroll Behavior:
              </h3>
              <p>
                Modified the auto-scroll logic to scroll to show the latest
                message (using `block: 'start'`) instead of scrolling to the
                bottom. This ensures the message content is visible.
              </p>
            </div>
            <div>
              <h3 className="font-medium text-gray-800">State Management:</h3>
              <p>
                Added proper state management to reset maximize state when
                minimizing or closing the chat.
              </p>
            </div>
          </div>
        </div>
      </div>

      <TestChatWidget />
    </div>
  );
}
