import { act, renderHook } from '@testing-library/react';
import { useAuthStore } from '@/stores/auth';

// Mock fetch
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe('Auth Store', () => {
  beforeEach(() => {
    // Reset store state
    useAuthStore.setState({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });
    mockFetch.mockClear();
  });

  describe('login', () => {
    it('should login successfully with valid credentials', async () => {
      const mockUser = {
        id: 1,
        username: 'superadmin',
        roles: ['SUPERADMIN'],
        isActive: true,
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          token: 'mock-token',
          user: mockUser,
        }),
      } as Response);

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        const success = await result.current.login({
          username: 'superadmin',
          password: 'password',
        });
        expect(success).toBe(true);
      });

      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.user).toEqual(mockUser);
      expect(result.current.token).toBe('mock-token');
      expect(result.current.error).toBeNull();
    });

    it('should reject login for non-SUPERADMIN users', async () => {
      const mockUser = {
        id: 1,
        username: 'admin',
        roles: ['ADMIN'],
        isActive: true,
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          token: 'mock-token',
          user: mockUser,
        }),
      } as Response);

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        const success = await result.current.login({
          username: 'admin',
          password: 'password',
        });
        expect(success).toBe(false);
      });

      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.error).toBe(
        'Access denied. SUPERADMIN role required.',
      );
    });

    it('should handle login failure', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: false,
          error: 'Invalid credentials',
        }),
      } as Response);

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        const success = await result.current.login({
          username: 'invalid',
          password: 'invalid',
        });
        expect(success).toBe(false);
      });

      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.error).toBe('Invalid credentials');
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const { result } = renderHook(() => useAuthStore());

      await act(async () => {
        const success = await result.current.login({
          username: 'test',
          password: 'test',
        });
        expect(success).toBe(false);
      });

      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.error).toBe('Network error');
    });
  });

  describe('logout', () => {
    it('should clear user data on logout', () => {
      const { result } = renderHook(() => useAuthStore());

      // Set initial state
      act(() => {
        useAuthStore.setState({
          user: { id: 1, username: 'test', roles: ['SUPERADMIN'] } as any,
          token: 'token',
          isAuthenticated: true,
        });
      });

      act(() => {
        result.current.logout();
      });

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
    });
  });

  describe('clearError', () => {
    it('should clear error state', () => {
      const { result } = renderHook(() => useAuthStore());

      act(() => {
        useAuthStore.setState({ error: 'Test error' });
      });

      expect(result.current.error).toBe('Test error');

      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
    });
  });
});
