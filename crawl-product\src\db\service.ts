import { and, eq, or } from 'drizzle-orm';
import type { ScrapedProduct } from '../types';
import { Logger } from '../utils/logger';
import { initializeDatabase } from './connection';
import type { NewProduct, Product } from './schema';
import { products, sites } from './schema';

export class DatabaseService {
  private db: ReturnType<typeof initializeDatabase>;
  private logger: Logger;
  private siteId = 1; // Default site ID, can be configured

  constructor() {
    this.db = initializeDatabase();
    this.logger = Logger.getInstance();
  }

  async testConnection(): Promise<boolean> {
    try {
      // Test with a simple query
      await this.db.select().from(sites).limit(1);
      this.logger.success('Database connection successful');
      return true;
    } catch (error) {
      this.logger.error('Database connection failed:', error);
      return false;
    }
  }

  async setSiteId(siteId: number): Promise<void> {
    this.siteId = siteId;
    this.logger.info(`Site ID set to: ${siteId}`);
  }

  private convertScrapedToNewProduct(scraped: ScrapedProduct): NewProduct {
    return {
      siteId: this.siteId,
      productName: scraped.productName,
      companyName: scraped.companyName,
      certificateNumber: scraped.certificateNumber || null,
      certificateType: scraped.certificateType || null,
      issuedDate: scraped.issuedDate || null,
      expiryDate: scraped.expiryDate || null,
      status: scraped.status || null,
      category: scraped.category || null,
      subcategory: scraped.subcategory || null,
      address: scraped.address || null,
      state: scraped.state || null,
      country: scraped.country || 'Malaysia', // Default to Malaysia
      contactInfo: scraped.contactInfo || null,
      website: scraped.website || null,
      sourceUrl: scraped.sourceUrl,
      rawData: scraped.rawData,
    };
  }

  async findDuplicateProduct(product: ScrapedProduct): Promise<Product | null> {
    try {
      // Check for duplicates based on company name, product name, and certificate number
      const conditions = [
        and(
          eq(products.companyName, product.companyName),
          eq(products.productName, product.productName),
        ),
      ];

      // If certificate number exists, also check for that
      if (product.certificateNumber) {
        conditions.push(
          eq(products.certificateNumber, product.certificateNumber),
        );
      }

      const existing = await this.db
        .select()
        .from(products)
        .where(or(...conditions))
        .limit(1);

      return existing.length > 0 ? existing[0] : null;
    } catch (error) {
      this.logger.error('Error checking for duplicate product:', error);
      return null;
    }
  }

  async saveProduct(scrapedProduct: ScrapedProduct): Promise<{
    success: boolean;
    productId?: number;
    isDuplicate?: boolean;
    isUpdate?: boolean;
  }> {
    try {
      // Check for existing product
      const existingProduct = await this.findDuplicateProduct(scrapedProduct);

      if (existingProduct) {
        // Update existing product
        const updatedProduct = this.convertScrapedToNewProduct(scrapedProduct);

        const result = await this.db
          .update(products)
          .set({
            ...updatedProduct,
            updatedAt: new Date(), // Update the timestamp
          })
          .where(eq(products.id, existingProduct.id))
          .returning({ id: products.id });

        if (result.length > 0) {
          this.logger.debug(
            `Updated existing product: ${scrapedProduct.companyName} - ${scrapedProduct.productName}`,
          );

          // TODO: Trigger vectorization asynchronously for updated product
          // this.vectorizationService.vectorizeProductAsync(result[0].id, {
          //   productName: scrapedProduct.productName,
          //   companyName: scrapedProduct.companyName,
          //   category: scrapedProduct.category,
          //   subcategory: scrapedProduct.subcategory,
          //   certificateType: scrapedProduct.certificateType,
          //   status: scrapedProduct.status,
          //   address: scrapedProduct.address,
          // });

          return {
            success: true,
            productId: result[0].id,
            isDuplicate: true,
            isUpdate: true,
          };
        }
        this.logger.error('Failed to update product - no result returned');
        return { success: false };
      }

      // Convert and insert new product
      const newProduct = this.convertScrapedToNewProduct(scrapedProduct);
      const result = await this.db
        .insert(products)
        .values(newProduct)
        .returning({ id: products.id });

      if (result.length > 0) {
        this.logger.debug(
          `Saved new product: ${scrapedProduct.companyName} - ${scrapedProduct.productName}`,
        );

        // TODO: Trigger vectorization asynchronously for new product
        // this.vectorizationService.vectorizeProductAsync(result[0].id, {
        //   productName: scrapedProduct.productName,
        //   companyName: scrapedProduct.companyName,
        //   category: scrapedProduct.category,
        //   subcategory: scrapedProduct.subcategory,
        //   certificateType: scrapedProduct.certificateType,
        //   status: scrapedProduct.status,
        //   address: scrapedProduct.address,
        // });

        return {
          success: true,
          productId: result[0].id,
          isDuplicate: false,
          isUpdate: false,
        };
      }
      this.logger.error('Failed to insert product - no result returned');
      return { success: false };
    } catch (error) {
      this.logger.error('Error saving product:', error);
      return { success: false };
    }
  }

  async saveProducts(scrapedProducts: ScrapedProduct[]): Promise<{
    totalProcessed: number;
    successfulSaves: number;
    duplicates: number;
    updates: number;
    errors: number;
  }> {
    this.logger.info(
      `Starting to save ${scrapedProducts.length} products to database...`,
    );

    let successfulSaves = 0;
    let duplicates = 0;
    let updates = 0;
    let errors = 0;

    for (let i = 0; i < scrapedProducts.length; i++) {
      const product = scrapedProducts[i];

      if (i % 10 === 0) {
        this.logger.progress(
          i + 1,
          scrapedProducts.length,
          'Saving products to database',
        );
      }

      const result = await this.saveProduct(product);

      if (result.success) {
        if (result.isDuplicate) {
          if (result.isUpdate) {
            updates++;
          } else {
            duplicates++;
          }
        } else {
          successfulSaves++;
        }
      } else {
        errors++;
      }

      // Small delay to avoid overwhelming the database
      if (i % 50 === 0 && i > 0) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    }

    const summary = {
      totalProcessed: scrapedProducts.length,
      successfulSaves,
      duplicates,
      updates,
      errors,
    };

    this.logger.success('Database save completed:', summary);
    return summary;
  }

  async getProductCount(): Promise<number> {
    try {
      const result = await this.db
        .select({ count: products.id })
        .from(products)
        .where(eq(products.siteId, this.siteId));

      return result.length;
    } catch (error) {
      this.logger.error('Error getting product count:', error);
      return 0;
    }
  }

  async getRecentProducts(limit = 10): Promise<Product[]> {
    try {
      return await this.db
        .select()
        .from(products)
        .where(eq(products.siteId, this.siteId))
        .orderBy(products.createdAt)
        .limit(limit);
    } catch (error) {
      this.logger.error('Error getting recent products:', error);
      return [];
    }
  }
}
