@import "tailwindcss";

:root {
  /* Halal Malaysia Portal Color System */

  /* Primary Colors - Government Green */
  --primary-green: #2d5a27;
  --primary-green-light: #4a7c59;
  --primary-green-dark: #1a3d1a;

  /* Islamic Green */
  --islamic-green: #008000;
  --islamic-green-light: #33a033;
  --islamic-green-dark: #006600;

  /* Base Colors */
  --background: #ffffff;
  --foreground: #333333;
  --white: #ffffff;
  --off-white: #fafafa;

  /* Text Colors */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-muted: #999999;

  /* Interactive Colors */
  --link-blue: #0066cc;
  --link-blue-hover: #0052a3;

  /* Gray Scale */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Status Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Background Variants */
  --bg-light-green: #f0f9f0;
  --bg-light-blue: #f0f8ff;
  --border-light: #e2e8f0;

  /* Legacy mappings for compatibility */
  --primary: var(--primary-green);
  --primary-foreground: var(--white);
  --secondary: var(--gray-100);
  --secondary-foreground: var(--text-primary);
  --accent: var(--islamic-green);
  --accent-foreground: var(--white);
  --muted: var(--gray-50);
  --muted-foreground: var(--text-secondary);
  --border: var(--border-light);
  --input: var(--white);
  --ring: var(--primary-green);
  --popover: var(--white);
  --popover-foreground: var(--text-primary);
  --card: var(--white);
  --card-foreground: var(--text-primary);
}

@theme inline {
  /* Color System */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary-green);
  --color-primary-foreground: var(--white);
  --color-secondary: var(--gray-100);
  --color-secondary-foreground: var(--text-primary);
  --color-accent: var(--islamic-green);
  --color-accent-foreground: var(--white);
  --color-muted: var(--gray-50);
  --color-muted-foreground: var(--text-secondary);
  --color-border: var(--border-light);
  --color-popover: var(--white);
  --color-popover-foreground: var(--text-primary);
  --color-card: var(--white);
  --color-card-foreground: var(--text-primary);

  /* Custom Halal Portal Colors */
  --color-primary-green: var(--primary-green);
  --color-primary-green-light: var(--primary-green-light);
  --color-primary-green-dark: var(--primary-green-dark);
  --color-islamic-green: var(--islamic-green);
  --color-islamic-green-light: var(--islamic-green-light);
  --color-islamic-green-dark: var(--islamic-green-dark);
  --color-link-blue: var(--link-blue);
  --color-link-blue-hover: var(--link-blue-hover);
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-error: var(--error);
  --color-info: var(--info);

  /* Gray Scale */
  --color-gray-50: var(--gray-50);
  --color-gray-100: var(--gray-100);
  --color-gray-200: var(--gray-200);
  --color-gray-300: var(--gray-300);
  --color-gray-400: var(--gray-400);
  --color-gray-500: var(--gray-500);
  --color-gray-600: var(--gray-600);
  --color-gray-700: var(--gray-700);
  --color-gray-800: var(--gray-800);
  --color-gray-900: var(--gray-900);

  /* Typography */
  --font-sans:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue",
    Arial, sans-serif;
  --font-headings: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  --font-mono:
    "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, monospace;
}

* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-feature-settings:
    "rlig" 1,
    "calt" 1;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}

/* Halal Portal Component Styles */

/* Buttons */
.btn-primary {
  background-color: var(--primary-green);
  color: var(--white);
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  background-color: var(--primary-green-dark);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-green);
  border: 2px solid var(--primary-green);
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-secondary:hover {
  background-color: var(--primary-green);
  color: var(--white);
}

/* Cards */
.card {
  background-color: var(--white);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  border: 1px solid var(--border-light);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* Navigation */
.nav-link {
  color: var(--text-primary);
  font-weight: 500;
  padding: 0.5rem 1rem;
  text-decoration: none;
  transition: color 0.2s ease;
  border-radius: 0.25rem;
}

.nav-link:hover {
  color: var(--primary-green);
  background-color: var(--bg-light-green);
}

.nav-link.active {
  color: var(--primary-green);
  border-bottom: 2px solid var(--primary-green);
}

/* Animations */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Input and Form Styling */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
input[type="date"],
input[type="time"],
input[type="datetime-local"],
textarea,
select {
  color: var(--text-primary) !important;
  background-color: var(--white) !important;
}

input[type="text"]:disabled,
input[type="email"]:disabled,
input[type="password"]:disabled,
input[type="number"]:disabled,
input[type="tel"]:disabled,
input[type="url"]:disabled,
input[type="search"]:disabled,
input[type="date"]:disabled,
input[type="time"]:disabled,
input[type="datetime-local"]:disabled,
textarea:disabled,
select:disabled {
  color: var(--text-muted) !important;
  background-color: var(--gray-50) !important;
}

/* Placeholder text styling */
input::placeholder,
textarea::placeholder {
  color: var(--text-muted) !important;
  opacity: 1;
}

/* Ensure consistent input styling across browsers */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
input[type="date"],
input[type="time"],
input[type="datetime-local"],
textarea,
select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Fix for autofill styling */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px var(--white) inset !important;
  -webkit-text-fill-color: var(--text-primary) !important;
}

/* Fix for file input styling */
input[type="file"] {
  color: var(--text-primary) !important;
}

/* Fix for checkbox and radio styling */
input[type="checkbox"],
input[type="radio"] {
  accent-color: var(--primary-green);
}

/* Button styling - ensure white text on colored backgrounds */
button[class*="bg-blue"],
button[class*="bg-green"],
button[class*="bg-red"],
button[class*="bg-purple"],
button[class*="bg-indigo"],
button[class*="bg-yellow"],
button[class*="bg-pink"],
button[class*="bg-gray-"],
a[class*="bg-blue"],
a[class*="bg-green"],
a[class*="bg-red"],
a[class*="bg-purple"],
a[class*="bg-indigo"],
a[class*="bg-yellow"],
a[class*="bg-pink"],
a[class*="bg-gray-"] {
  color: white !important;
}

/* Exception for light backgrounds that need dark text */
button[class*="bg-gray-50"],
button[class*="bg-gray-100"],
button[class*="bg-gray-200"],
button[class*="bg-blue-50"],
button[class*="bg-blue-100"],
button[class*="bg-green-50"],
button[class*="bg-green-100"],
button[class*="bg-red-50"],
button[class*="bg-red-100"],
button[class*="bg-yellow-50"],
button[class*="bg-yellow-100"],
a[class*="bg-gray-50"],
a[class*="bg-gray-100"],
a[class*="bg-gray-200"],
a[class*="bg-blue-50"],
a[class*="bg-blue-100"],
a[class*="bg-green-50"],
a[class*="bg-green-100"],
a[class*="bg-red-50"],
a[class*="bg-red-100"],
a[class*="bg-yellow-50"],
a[class*="bg-yellow-100"] {
  color: var(--text-primary) !important;
}

/* Ensure primary green buttons have white text */
button[class*="bg-primary"],
a[class*="bg-primary"] {
  color: white !important;
}

/* Tailwind utility class overrides for buttons */
.bg-blue-500,
.bg-blue-600,
.bg-blue-700,
.bg-blue-800,
.bg-blue-900,
.bg-green-500,
.bg-green-600,
.bg-green-700,
.bg-green-800,
.bg-green-900,
.bg-red-500,
.bg-red-600,
.bg-red-700,
.bg-red-800,
.bg-red-900,
.bg-purple-500,
.bg-purple-600,
.bg-purple-700,
.bg-purple-800,
.bg-purple-900,
.bg-indigo-500,
.bg-indigo-600,
.bg-indigo-700,
.bg-indigo-800,
.bg-indigo-900,
.bg-yellow-500,
.bg-yellow-600,
.bg-yellow-700,
.bg-yellow-800,
.bg-yellow-900,
.bg-pink-500,
.bg-pink-600,
.bg-pink-700,
.bg-pink-800,
.bg-pink-900,
.bg-gray-500,
.bg-gray-600,
.bg-gray-700,
.bg-gray-800,
.bg-gray-900 {
  color: white !important;
}

/* Light background utilities that need dark text */
.bg-blue-50,
.bg-blue-100,
.bg-blue-200,
.bg-green-50,
.bg-green-100,
.bg-green-200,
.bg-red-50,
.bg-red-100,
.bg-red-200,
.bg-yellow-50,
.bg-yellow-100,
.bg-yellow-200,
.bg-purple-50,
.bg-purple-100,
.bg-purple-200,
.bg-indigo-50,
.bg-indigo-100,
.bg-indigo-200,
.bg-pink-50,
.bg-pink-100,
.bg-pink-200,
.bg-gray-50,
.bg-gray-100,
.bg-gray-200,
.bg-gray-300,
.bg-gray-400 {
  color: var(--text-primary) !important;
}

/* Site-specific button colors */
.btn-primary,
.bg-primary-green,
.bg-islamic-green {
  color: white !important;
}

/* Hover states should maintain white text */
button:hover[class*="bg-blue"],
button:hover[class*="bg-green"],
button:hover[class*="bg-red"],
button:hover[class*="bg-purple"],
button:hover[class*="bg-indigo"],
button:hover[class*="bg-yellow"],
button:hover[class*="bg-pink"],
button:hover[class*="bg-gray-"],
a:hover[class*="bg-blue"],
a:hover[class*="bg-green"],
a:hover[class*="bg-red"],
a:hover[class*="bg-purple"],
a:hover[class*="bg-indigo"],
a:hover[class*="bg-yellow"],
a:hover[class*="bg-pink"],
a:hover[class*="bg-gray-"] {
  color: white !important;
}

/* Disabled button states */
button:disabled[class*="bg-blue"],
button:disabled[class*="bg-green"],
button:disabled[class*="bg-red"],
button:disabled[class*="bg-purple"],
button:disabled[class*="bg-indigo"],
button:disabled[class*="bg-yellow"],
button:disabled[class*="bg-pink"],
button:disabled[class*="bg-gray-"],
a:disabled[class*="bg-blue"],
a:disabled[class*="bg-green"],
a:disabled[class*="bg-red"],
a:disabled[class*="bg-purple"],
a:disabled[class*="bg-indigo"],
a:disabled[class*="bg-yellow"],
a:disabled[class*="bg-pink"],
a:disabled[class*="bg-gray-"] {
  color: white !important;
  opacity: 0.5;
}

/* Default button styling */
button {
  color: inherit;
}

/* Ensure labels have proper text color */
label {
  color: var(--text-primary);
}

/* Fix for option elements in select */
option {
  color: var(--text-primary);
  background-color: var(--white);
}

/* Focus States for Accessibility */
.focus-visible {
  outline: 2px solid var(--primary-green);
  outline-offset: 2px;
}

/* High Contrast Support */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --primary-green: #004d00;
    --border-light: #666666;
  }
}

/* Responsive Design Enhancements */

/* Mobile-first responsive typography */
@media (max-width: 640px) {
  .text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
  .text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }
  .text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  /* Improved mobile spacing */
  .py-16 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
  .py-12 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
  .px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Mobile-optimized cards */
  .card {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  /* Mobile button improvements */
  .btn-primary,
  .btn-secondary {
    width: 100%;
    padding: 0.875rem 1rem;
    font-size: 1rem;
  }
}

/* Tablet optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  /* Tablet grid adjustments */
  .grid-cols-1.md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-1.md\:grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* Desktop enhancements */
@media (min-width: 1025px) {
  .container {
    max-width: 1200px;
  }

  /* Desktop hover effects */
  .card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Larger touch targets */
  .nav-link {
    padding: 0.75rem 1rem;
    min-height: 44px;
    display: flex;
    align-items: center;
  }

  button,
  .btn-primary,
  .btn-secondary {
    min-height: 44px;
    padding: 0.75rem 1.5rem;
  }

  /* Remove hover effects on touch devices */
  .card:hover {
    transform: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .card:hover {
    transform: none;
  }
}

/* Light mode only - dark mode disabled */
html {
  color-scheme: light only;
}

/* Force light mode regardless of system preference */
@media (prefers-color-scheme: dark) {
  html {
    color-scheme: light only;
  }

  body {
    background-color: var(--white) !important;
    color: var(--text-primary) !important;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  /* Print-specific optimizations */
  .card {
    box-shadow: none;
    border: 1px solid #ccc;
    break-inside: avoid;
  }

  .btn-primary,
  .btn-secondary {
    border: 1px solid #000;
    background: transparent !important;
    color: #000 !important;
  }
}
