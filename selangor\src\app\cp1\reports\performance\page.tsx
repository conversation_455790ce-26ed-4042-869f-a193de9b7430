'use client';

import {
  Al<PERSON><PERSON>riangle,
  CheckCircle,
  Clock,
  Download,
  Filter,
  Target,
  TrendingUp,
  XCircle,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { generateSearchPerformance } from '@/lib/mockData';

export default function SearchPerformanceReport() {
  const [performanceData, setPerformanceData] = useState<any>(null);
  const [timeRange, setTimeRange] = useState<string>('30d');

  useEffect(() => {
    setPerformanceData(generateSearchPerformance());
  }, []);

  if (!performanceData) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto" />
            <p className="mt-4 text-gray-600">Loading performance data...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'above':
        return 'text-green-600';
      case 'below':
        return 'text-red-600';
      default:
        return 'text-yellow-600';
    }
  };

  const getPerformanceIcon = (performance: string) => {
    switch (performance) {
      case 'above':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'below':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Target className="h-5 w-5 text-yellow-600" />;
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Search Performance & User Experience
            </h1>
            <p className="text-gray-600">
              Analysis of search efficiency and user experience metrics
            </p>
          </div>
          <button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </button>
        </div>

        {/* Filters */}
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                Time Range:
              </span>
            </div>

            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Average Search Duration
                </p>
                <p className="text-3xl font-bold text-gray-900">
                  {performanceData.avgSearchDuration.toFixed(1)}s
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  Time from query to result click
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Clock className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Zero Results Queries
                </p>
                <p className="text-3xl font-bold text-gray-900">
                  {performanceData.zeroResultsQueries.length}
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  Queries with no results
                </p>
              </div>
              <div className="p-3 bg-red-100 rounded-full">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Industry Benchmark
                </p>
                <div className="flex items-center mt-1">
                  {getPerformanceIcon(
                    performanceData.benchmarkComparison.performance,
                  )}
                  <span
                    className={`ml-2 text-lg font-bold ${getPerformanceColor(performanceData.benchmarkComparison.performance)}`}
                  >
                    {performanceData.benchmarkComparison.performance === 'above'
                      ? 'Above'
                      : performanceData.benchmarkComparison.performance ===
                          'below'
                        ? 'Below'
                        : 'Meeting'}{' '}
                    Standard
                  </span>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  Our: {performanceData.benchmarkComparison.ourAverage}s vs
                  Industry:{' '}
                  {performanceData.benchmarkComparison.industryStandard}s
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Benchmark Comparison Chart */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Performance Benchmark Comparison
          </h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">
                  Our Average Response Time
                </span>
                <span className="text-sm font-bold text-green-600">
                  {performanceData.benchmarkComparison.ourAverage}s
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-green-600 h-3 rounded-full"
                  style={{
                    width: `${(performanceData.benchmarkComparison.ourAverage / 5) * 100}%`,
                  }}
                />
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">
                  Industry Standard
                </span>
                <span className="text-sm font-bold text-gray-600">
                  {performanceData.benchmarkComparison.industryStandard}s
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-gray-500 h-3 rounded-full"
                  style={{
                    width: `${(performanceData.benchmarkComparison.industryStandard / 5) * 100}%`,
                  }}
                />
              </div>
            </div>

            <div className="mt-4 p-4 bg-green-50 rounded-lg">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <span className="text-sm font-medium text-green-800">
                  Performance is{' '}
                  {(
                    ((performanceData.benchmarkComparison.industryStandard -
                      performanceData.benchmarkComparison.ourAverage) /
                      performanceData.benchmarkComparison.industryStandard) *
                    100
                  ).toFixed(1)}
                  % better than industry standard
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Zero Results Analysis */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              Zero Results Analysis
            </h3>
            <p className="text-sm text-gray-600">
              Popular queries that returned no results - opportunities for
              content improvement
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rank
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Search Query
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Search Volume
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {performanceData.zeroResultsQueries.map(
                  (query: any, index: number) => (
                    <tr key={query.query} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        #{index + 1}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {query.query}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            query.category === 'products'
                              ? 'bg-green-100 text-green-800'
                              : query.category === 'services'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-purple-100 text-purple-800'
                          }`}
                        >
                          {query.category}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {query.count} searches
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            query.count > 30
                              ? 'bg-red-100 text-red-800'
                              : query.count > 20
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-green-100 text-green-800'
                          }`}
                        >
                          {query.count > 30
                            ? 'High'
                            : query.count > 20
                              ? 'Medium'
                              : 'Low'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <button className="text-green-600 hover:text-green-900 font-medium">
                          Add Content
                        </button>
                      </td>
                    </tr>
                  ),
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Performance Insights */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Performance Insights & Recommendations
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Strengths</h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                  <span className="text-sm text-gray-700">
                    Search response time is 12% faster than industry average
                  </span>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                  <span className="text-sm text-gray-700">
                    High success rate for restaurant and product searches
                  </span>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                  <span className="text-sm text-gray-700">
                    Consistent performance across different search types
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">
                Improvement Opportunities
              </h4>
              <div className="space-y-2">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <span className="text-sm text-gray-700">
                    Add content for high-volume zero-result queries
                  </span>
                </div>
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <span className="text-sm text-gray-700">
                    Improve search suggestions for specialty products
                  </span>
                </div>
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <span className="text-sm text-gray-700">
                    Enhance pharmaceutical and cosmetics coverage
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
