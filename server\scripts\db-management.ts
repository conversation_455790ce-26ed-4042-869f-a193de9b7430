#!/usr/bin/env ts-node

import { sql } from 'drizzle-orm';
import { initializeDatabase } from '../src/db/connection';

/**
 * Check if database exists and has tables
 */
async function checkDatabaseStatus() {
  try {
    const db = initializeDatabase();

    // Check if main tables exist
    const result = await db.execute(sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      ORDER BY table_name;
    `);

    const tables = result.map((row: any) => row.table_name);

    console.log('📊 Database Status:');
    console.log(`   Tables found: ${tables.length}`);

    if (tables.length === 0) {
      console.log('   Status: Empty database (needs initialization)');
      return 'empty';
    }
    console.log('   Tables:', tables.join(', '));
    console.log('   Status: Initialized');
    return 'initialized';
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return 'error';
  }
}

/**
 * Check migration status
 */
async function checkMigrationStatus() {
  try {
    const db = initializeDatabase();

    // Check if drizzle migrations table exists
    const result = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = '__drizzle_migrations'
      );
    `);

    const migrationsTableExists = result[0]?.exists;

    if (!migrationsTableExists) {
      console.log('📋 Migration Status: No migrations applied');
      return 'none';
    }

    // Get applied migrations
    const migrations = await db.execute(sql`
      SELECT hash, created_at 
      FROM __drizzle_migrations 
      ORDER BY created_at DESC;
    `);

    console.log('📋 Migration Status:');
    console.log(`   Applied migrations: ${migrations.length}`);
    migrations.forEach((migration: any, index: number) => {
      console.log(
        `   ${index + 1}. ${migration.hash} (${migration.created_at})`,
      );
    });

    return 'applied';
  } catch (error) {
    console.error('❌ Migration status check failed:', error);
    return 'error';
  }
}

/**
 * Backup database schema
 */
async function backupSchema() {
  try {
    const db = initializeDatabase();

    // Get all table schemas
    const result = await db.execute(sql`
      SELECT 
        table_name,
        column_name,
        data_type,
        is_nullable,
        column_default
      FROM information_schema.columns 
      WHERE table_schema = 'public'
      ORDER BY table_name, ordinal_position;
    `);

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = `schema-backup-${timestamp}.json`;

    const fs = require('node:fs');
    fs.writeFileSync(backupFile, JSON.stringify(result, null, 2));

    console.log(`💾 Schema backup saved to: ${backupFile}`);
    return backupFile;
  } catch (error) {
    console.error('❌ Schema backup failed:', error);
    throw error;
  }
}

// CLI interface
async function main() {
  const command = process.argv[2];

  switch (command) {
    case 'status':
      await checkDatabaseStatus();
      await checkMigrationStatus();
      break;

    case 'backup':
      await backupSchema();
      break;

    // biome-ignore lint/suspicious/noFallthroughSwitchClause: <explanation>
    case 'check': {
      const status = await checkDatabaseStatus();
      process.exit(status === 'error' ? 1 : 0);
      // break;
    }

    default:
      console.log('Usage: ts-node scripts/db-management.ts <command>');
      console.log('Commands:');
      console.log('  status  - Check database and migration status');
      console.log('  backup  - Backup current schema');
      console.log('  check   - Quick health check (exit code 0/1)');
      break;
  }
}

if (require.main === module) {
  main().catch(console.error);
}

export { checkDatabaseStatus, checkMigrationStatus, backupSchema };
