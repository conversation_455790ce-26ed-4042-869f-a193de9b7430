#!/usr/bin/env node

/**
 * Test Hono.js Twilio Integration
 *
 * This test simulates a real Twilio webhook call to test:
 * 1. Hono.js webhook endpoint response
 * 2. AI message processing with R2R halal knowledge
 * 3. End-to-end message flow
 *
 * Usage: node src/tests/test-hono-twilio-integration.mjs
 */

import 'dotenv/config';

const WEBHOOK_URL = 'http://localhost:16001/api/twilio/webhook';

console.log('🧪 Testing Hono.js Twilio Integration');
console.log('='.repeat(50));
console.log(
  '🎯 Goal: Verify AI responds with halal knowledge, not generic errors',
);
console.log('');

// Test 1: Basic webhook connectivity
async function testWebhookConnectivity() {
  console.log('🔍 Test 1: Webhook Connectivity');

  try {
    const response = await fetch('http://localhost:16001/api/twilio/webhook');
    if (response.status === 200) {
      const text = await response.text();
      console.log(`   ✅ GET webhook: ${text}`);
      return true;
    }
    console.log(`   ❌ GET webhook failed: ${response.status}`);
    return false;
  } catch (error) {
    console.log(`   ❌ Webhook connectivity error: ${error.message}`);
    return false;
  }
}

// Test 2: Simulate real Twilio webhook with halal question
async function testHalalQuestionWebhook() {
  console.log('\n🔍 Test 2: Halal Question Processing');

  try {
    // Create realistic Twilio webhook payload
    const testPayload = {
      MessageSid: 'SM_test_' + Date.now(),
      AccountSid: process.env.TWILIO_ACCOUNT_SID || 'AC_test_account',
      From: 'whatsapp:+***********',
      To: process.env.TWILIO_PHONE_NUMBER || 'whatsapp:+***********',
      Body: 'Assalamualaikum, boleh saya tanya tentang makanan halal? Adakah daging lembu halal dalam Islam?',
      NumMedia: '0',
      MessageStatus: 'received',
      ApiVersion: '2010-04-01',
    };

    console.log('   📨 Sending halal question to webhook...');
    console.log(`   📝 Question: "${testPayload.Body}"`);

    // Convert to URL-encoded format (as Twilio sends)
    const payloadString = Object.keys(testPayload)
      .map(
        (key) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(testPayload[key] || '')}`,
      )
      .join('&');

    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-Twilio-Signature': 'test-signature',
        'User-Agent': 'TwilioProxy/1.1',
        'x-site-id': '1',
        'x-test-mode': 'true', // Bypass signature verification
      },
      body: payloadString,
    });

    const responseText = await response.text();

    if (response.status === 200) {
      console.log('   ✅ Webhook processed successfully');
      console.log(`   📨 Response: ${responseText}`);

      // Wait a moment for async processing
      console.log('   ⏳ Waiting for AI processing...');
      await new Promise((resolve) => setTimeout(resolve, 5000));

      return true;
    }
    console.log(`   ❌ Webhook failed: ${response.status} - ${responseText}`);
    return false;
  } catch (error) {
    console.log(`   ❌ Webhook test error: ${error.message}`);
    return false;
  }
}

// Test 3: Test different types of halal questions
async function testVariousHalalQuestions() {
  console.log('\n🔍 Test 3: Various Halal Questions');

  const questions = [
    'Adakah daging lembu halal dalam Islam?',
    'Apa yang menjadikan makanan halal dalam Islam?',
    'Bolehkah Muslim makan ayam?',
    'Apakah keperluan pensijilan halal?',
  ];

  let successCount = 0;

  for (let i = 0; i < questions.length; i++) {
    const question = questions[i];
    console.log(`\n   📝 Question ${i + 1}: "${question}"`);

    try {
      const testPayload = {
        MessageSid: 'SM_test_' + Date.now() + '_' + i,
        AccountSid: process.env.TWILIO_ACCOUNT_SID || 'AC_test_account',
        From: `whatsapp:+**********${i}`,
        To: process.env.TWILIO_PHONE_NUMBER || 'whatsapp:+***********',
        Body: question,
        NumMedia: '0',
        MessageStatus: 'received',
        ApiVersion: '2010-04-01',
      };

      const payloadString = Object.keys(testPayload)
        .map(
          (key) =>
            `${encodeURIComponent(key)}=${encodeURIComponent(testPayload[key] || '')}`,
        )
        .join('&');

      const response = await fetch(WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Twilio-Signature': 'test-signature',
          'User-Agent': 'TwilioProxy/1.1',
          'x-site-id': '1',
          'x-test-mode': 'true',
        },
        body: payloadString,
      });

      if (response.status === 200) {
        console.log(`   ✅ Question ${i + 1}: Processed successfully`);
        successCount++;
      } else {
        console.log(`   ❌ Question ${i + 1}: Failed (${response.status})`);
      }

      // Wait between requests
      await new Promise((resolve) => setTimeout(resolve, 2000));
    } catch (error) {
      console.log(`   ❌ Question ${i + 1}: Error - ${error.message}`);
    }
  }

  console.log(
    `\n   📊 Results: ${successCount}/${questions.length} questions processed`,
  );
  return successCount === questions.length;
}

// Test 4: Check server logs for AI responses
async function checkServerLogs() {
  console.log('\n🔍 Test 4: Server Response Analysis');
  console.log('   💡 Check your server terminal for AI responses');
  console.log('   🔍 Look for:');
  console.log('     - R2R search tool calls');
  console.log('     - OpenAI responses with halal knowledge');
  console.log('     - Twilio message sending confirmations');
  console.log('   ❌ Avoid seeing: "I encountered an error" generic messages');

  return true;
}

// Main test runner
async function runHonoTwilioTests() {
  console.log('🚀 Starting Hono.js Twilio Integration Tests...\n');

  const tests = [
    { name: 'Webhook Connectivity', fn: testWebhookConnectivity },
    { name: 'Halal Question Processing', fn: testHalalQuestionWebhook },
    { name: 'Various Halal Questions', fn: testVariousHalalQuestions },
    { name: 'Server Response Analysis', fn: checkServerLogs },
  ];

  let passedTests = 0;
  const results = [];

  for (const test of tests) {
    try {
      console.log(`\n⏳ Running: ${test.name}...`);
      const success = await test.fn();
      results.push({ name: test.name, success });
      if (success) passedTests++;

      console.log(`📊 ${test.name}: ${success ? '✅ PASSED' : '❌ FAILED'}`);
    } catch (error) {
      console.log(`❌ ${test.name}: UNEXPECTED ERROR - ${error.message}`);
      results.push({ name: test.name, success: false });
    }
  }

  // Final summary
  console.log('\n' + '='.repeat(50));
  console.log('📊 HONO.JS TWILIO INTEGRATION TEST RESULTS');
  console.log('='.repeat(50));

  results.forEach((result) => {
    console.log(`${result.success ? '✅' : '❌'} ${result.name}`);
  });

  console.log(
    `\n🎯 Overall Score: ${passedTests}/${tests.length} tests passed`,
  );

  if (passedTests === tests.length) {
    console.log('\n🎉 HONO.JS TWILIO INTEGRATION WORKING!');
    console.log('✅ Your Hono.js server is properly handling Twilio webhooks');
    console.log('✅ AI should be responding with halal knowledge from R2R');
  } else {
    console.log('\n⚠️  Some tests failed. Check the details above.');
    console.log('\n💡 Next steps:');
    console.log('   1. Check server logs for error details');
    console.log('   2. Verify R2R service is accessible');
    console.log('   3. Confirm OpenAI API key is working');
    console.log('   4. Test individual components separately');
  }

  console.log('\n🔍 To debug further:');
  console.log('   - Watch server terminal for detailed logs');
  console.log('   - Check if R2R search tool is being called');
  console.log('   - Verify AI responses contain halal knowledge');
  console.log('   - Look for Twilio message sending confirmations');
}

// Run the tests
runHonoTwilioTests().catch(console.error);
