'use client';

export const runtime = 'edge';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ArrowLeft,
  CheckCircle,
  Clock,
  Eye,
  MessageSquare,
  RefreshCw,
  User,
  Users,
} from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { NotificationCenter } from '@/components/notifications/NotificationCenter';
import { SessionDetailView } from '@/components/sessions/SessionDetailView';
import { SessionHistory } from '@/components/sessions/SessionHistory';
import { useAdminAuthGuard } from '@/hooks/useAuthGuard';
import { Link, useRouter } from '@/i18n/navigation';
import { api } from '@/lib/api';
import { UserRole } from '@/types/roles';

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

interface ChatSession {
  id: string;
  userId?: string;
  platform: string;
  platformId?: string;
  status: string;
  isHandedOver: boolean;
  createdAt: string;
  lastMessageAt?: string;
  messageCount?: number;
  agentId?: number;
  agentName?: string;
}

interface HandoverRequest {
  id: number;
  sessionId: string;
  requestedBy: string;
  reason?: string;
  priority: 'low' | 'normal' | 'high';
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: string;
}

interface SessionStats {
  totalSessions: number;
  activeSessions: number;
  pendingHandovers: number;
  completedToday: number;
}

export default function AdminSessionsPage() {
  const router = useRouter();
  const { user, loading } = useAdminAuthGuard([
    UserRole.ADMIN,
    UserRole.AGENT,
    UserRole.SUPERVISOR,
  ]);

  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [handoverRequests, setHandoverRequests] = useState<HandoverRequest[]>(
    [],
  );
  const [stats, setStats] = useState<SessionStats>({
    totalSessions: 0,
    activeSessions: 0,
    pendingHandovers: 0,
    completedToday: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch sessions
      const sessionsResponse = await api.admin.getSessions();
      if (sessionsResponse.success && sessionsResponse.data) {
        setSessions(sessionsResponse.data);
      }

      // Fetch handover requests
      const handoversResponse = await api.admin.getHandoverRequests();
      if (handoversResponse.success && handoversResponse.data) {
        setHandoverRequests(handoversResponse.data);
      }

      // Fetch stats
      const statsResponse = await api.admin.getSessionStats();
      if (statsResponse.success && statsResponse.data) {
        setStats(statsResponse.data);
      }
    } catch (err) {
      console.error('Error fetching sessions data:', err);
      setError('Failed to load sessions data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!loading && user) {
      fetchData();
    }
  }, [loading, user]);

  const handleViewSession = (sessionId: string) => {
    router.push(`/admin/sessions/${sessionId}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'normal':
        return 'bg-blue-100 text-blue-800';
      case 'low':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Link
                href="/admin/dashboard"
                className="mr-4 p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
              <MessageSquare className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">
                Chat Sessions
              </h1>
            </div>
            <button
              onClick={fetchData}
              disabled={isLoading}
              className="flex items-center px-4 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md disabled:opacity-50"
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`}
              />
              Refresh
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <MessageSquare className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Total Sessions
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stats.totalSessions}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Active Sessions
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stats.activeSessions}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Pending Handovers
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stats.pendingHandovers}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircle className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Completed Today
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stats.completedToday}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Active Sessions */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                Active Sessions
              </h2>
            </div>
            <div className="p-6">
              {sessions.length === 0 ? (
                <div className="text-center py-8">
                  <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    No active sessions
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    All chat sessions will appear here when users start
                    conversations.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {sessions.slice(0, 5).map((session) => (
                    <div
                      key={session.id}
                      className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <User className="h-5 w-5 text-gray-400" />
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {session.platformId ||
                                session.userId ||
                                'Anonymous'}
                            </p>
                            <p className="text-xs text-gray-500">
                              {session.platform} •{' '}
                              {formatDate(session.createdAt)}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}
                          >
                            {session.status}
                          </span>
                          <button
                            onClick={() => handleViewSession(session.id)}
                            className="p-1 text-gray-400 hover:text-gray-600"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                      {session.messageCount && (
                        <div className="mt-2 text-xs text-gray-500">
                          {session.messageCount} messages
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Pending Handover Requests */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                Pending Handover Requests
              </h2>
            </div>
            <div className="p-6">
              {handoverRequests.length === 0 ? (
                <div className="text-center py-8">
                  <AlertTriangle className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    No pending requests
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Handover requests from users will appear here.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {handoverRequests.map((request) => (
                    <div
                      key={request.id}
                      className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            Session: {request.sessionId}
                          </p>
                          <p className="text-xs text-gray-500">
                            Requested by: {request.requestedBy}
                          </p>
                          {request.reason && (
                            <p className="text-xs text-gray-600 mt-1">
                              Reason: {request.reason}
                            </p>
                          )}
                        </div>
                        <div className="flex flex-col items-end space-y-1">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(request.priority)}`}
                          >
                            {request.priority}
                          </span>
                          <span className="text-xs text-gray-500">
                            {formatDate(request.createdAt)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
