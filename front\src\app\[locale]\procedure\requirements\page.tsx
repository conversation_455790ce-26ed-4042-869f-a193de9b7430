'use client';

export const runtime = 'edge';

import type { BreadcrumbItem } from '@/components/breadcrumb';
import { PageWrapper } from '@/components/page-wrapper';
import { useLanguage } from '@/lib/language-context';

export default function RequirementsPage() {
  const { language } = useLanguage();

  const breadcrumbs: BreadcrumbItem[] = [
    {
      label: language === 'en' ? 'Home' : 'Laman <PERSON>',
      href: '/',
    },
    {
      label: language === 'en' ? 'Procedure' : 'Prosedur',
      href: '/procedure',
    },
    {
      label: language === 'en' ? 'Requirements' : 'Keperluan',
      href: '/procedure/requirements',
    },
  ];

  return (
    <PageWrapper
      title="Requirements"
      titleBM="Keperluan"
      description="Detailed requirements for Halal certification including documentation, facility standards, and compliance criteria."
      descriptionBM="Keperluan terperinci untuk pensijilan Halal termasuk dokumentasi, piawaian kemudahan, dan kriteria pematuhan."
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-8">
        {/* Introduction */}
        <div className="card">
          <h2 className="text-2xl font-bold mb-4 text-gray-900">
            {language === 'en'
              ? 'Halal Certification Requirements'
              : 'Keperluan Pensijilan Halal'}
          </h2>
          <p className="text-gray-600 leading-relaxed">
            {language === 'en'
              ? 'To obtain Halal certification from JAKIM, applicants must meet specific requirements covering documentation, facility standards, ingredient compliance, and operational procedures. These requirements ensure that products and services comply with Islamic principles and Malaysian Halal standards.'
              : 'Untuk mendapatkan pensijilan Halal daripada JAKIM, pemohon mesti memenuhi keperluan khusus yang meliputi dokumentasi, piawaian kemudahan, pematuhan ramuan, dan prosedur operasi. Keperluan ini memastikan produk dan perkhidmatan mematuhi prinsip Islam dan piawaian Halal Malaysia.'}
          </p>
        </div>

        {/* General Requirements */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'General Requirements' : 'Keperluan Am'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en'
                      ? 'Company Registration'
                      : 'Pendaftaran Syarikat'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Valid business registration with relevant authorities'
                      : 'Pendaftaran perniagaan yang sah dengan pihak berkuasa berkaitan'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en' ? 'Halal Policy' : 'Polisi Halal'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Written commitment to maintain Halal integrity'
                      : 'Komitmen bertulis untuk mengekalkan integriti Halal'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en'
                      ? 'Management Commitment'
                      : 'Komitmen Pengurusan'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Top management support for Halal compliance'
                      : 'Sokongan pengurusan atasan untuk pematuhan Halal'}
                  </p>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en' ? 'Halal Executive' : 'Eksekutif Halal'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Appointed Muslim executive responsible for Halal matters'
                      : 'Eksekutif Muslim yang dilantik bertanggungjawab untuk hal ehwal Halal'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en' ? 'Training Records' : 'Rekod Latihan'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Staff training on Halal requirements and procedures'
                      : 'Latihan kakitangan mengenai keperluan dan prosedur Halal'}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {language === 'en'
                      ? 'Documentation System'
                      : 'Sistem Dokumentasi'}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {language === 'en'
                      ? 'Proper documentation and record keeping system'
                      : 'Sistem dokumentasi dan penyimpanan rekod yang betul'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Ingredient Requirements */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en' ? 'Ingredient Requirements' : 'Keperluan Ramuan'}
          </h3>
          <div className="space-y-6">
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-3">
                {language === 'en' ? 'Halal Ingredients' : 'Ramuan Halal'}
              </h4>
              <ul className="space-y-2 text-sm text-green-700">
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'All ingredients must be Halal-certified or from approved sources'
                    : 'Semua ramuan mesti disijilkan Halal atau daripada sumber yang diluluskan'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Supplier certificates and declarations required'
                    : 'Sijil dan pengisytiharan pembekal diperlukan'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Regular verification of ingredient status'
                    : 'Pengesahan berkala status ramuan'}
                </li>
              </ul>
            </div>
            <div className="bg-red-50 p-4 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-3">
                {language === 'en'
                  ? 'Prohibited Ingredients'
                  : 'Ramuan Dilarang'}
              </h4>
              <ul className="space-y-2 text-sm text-red-700">
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Pork and pork derivatives'
                    : 'Daging babi dan terbitan babi'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Alcohol and alcoholic beverages'
                    : 'Alkohol dan minuman beralkohol'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Non-Halal animal derivatives'
                    : 'Terbitan haiwan bukan Halal'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Ingredients from doubtful sources'
                    : 'Ramuan daripada sumber yang meragukan'}
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Facility Requirements */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Facility Requirements'
              : 'Keperluan Kemudahan'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">
                {language === 'en'
                  ? 'Physical Requirements'
                  : 'Keperluan Fizikal'}
              </h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Separate storage for Halal ingredients'
                    : 'Penyimpanan berasingan untuk ramuan Halal'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Dedicated production lines (if applicable)'
                    : 'Barisan pengeluaran khusus (jika berkenaan)'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Proper cleaning and sanitization procedures'
                    : 'Prosedur pembersihan dan sanitasi yang betul'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Pest control measures'
                    : 'Langkah kawalan perosak'}
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">
                {language === 'en'
                  ? 'Operational Requirements'
                  : 'Keperluan Operasi'}
              </h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Standard Operating Procedures (SOPs)'
                    : 'Prosedur Operasi Standard (SOP)'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Traceability system for ingredients'
                    : 'Sistem kebolehsurihan untuk ramuan'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Quality control procedures'
                    : 'Prosedur kawalan kualiti'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Regular internal audits'
                    : 'Audit dalaman berkala'}
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Documentation Requirements */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Documentation Requirements'
              : 'Keperluan Dokumentasi'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en' ? 'Company Documents' : 'Dokumen Syarikat'}
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Business registration'
                    : 'Pendaftaran perniagaan'}
                </li>
                <li>
                  • {language === 'en' ? 'Factory license' : 'Lesen kilang'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Halal policy statement'
                    : 'Kenyataan polisi Halal'}
                </li>
              </ul>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en' ? 'Product Documents' : 'Dokumen Produk'}
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Ingredient specifications'
                    : 'Spesifikasi ramuan'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Product formulations'
                    : 'Formulasi produk'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Process flow charts'
                    : 'Carta aliran proses'}
                </li>
              </ul>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-2">
                {language === 'en' ? 'Supplier Documents' : 'Dokumen Pembekal'}
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>
                  • {language === 'en' ? 'Halal certificates' : 'Sijil Halal'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Supplier declarations'
                    : 'Pengisytiharan pembekal'}
                </li>
                <li>
                  •{' '}
                  {language === 'en'
                    ? 'Ingredient certificates'
                    : 'Sijil ramuan'}
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Compliance Monitoring */}
        <div className="card">
          <h3 className="text-xl font-bold mb-6 text-gray-900">
            {language === 'en'
              ? 'Compliance Monitoring'
              : 'Pemantauan Pematuhan'}
          </h3>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-gray-900 mb-1">
                  {language === 'en'
                    ? 'Regular Surveillance'
                    : 'Pengawasan Berkala'}
                </h4>
                <p className="text-gray-600 text-sm">
                  {language === 'en'
                    ? 'Periodic surveillance visits to ensure continued compliance with Halal requirements.'
                    : 'Lawatan pengawasan berkala untuk memastikan pematuhan berterusan dengan keperluan Halal.'}
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-gray-900 mb-1">
                  {language === 'en'
                    ? 'Complaint Investigation'
                    : 'Siasatan Aduan'}
                </h4>
                <p className="text-gray-600 text-sm">
                  {language === 'en'
                    ? 'Investigation of any complaints or non-compliance issues reported.'
                    : 'Siasatan terhadap sebarang aduan atau isu ketidakpatuhan yang dilaporkan.'}
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-primary-green rounded-full mt-2 flex-shrink-0" />
              <div>
                <h4 className="font-semibold text-gray-900 mb-1">
                  {language === 'en'
                    ? 'Corrective Actions'
                    : 'Tindakan Pembetulan'}
                </h4>
                <p className="text-gray-600 text-sm">
                  {language === 'en'
                    ? 'Implementation of corrective actions for any identified non-conformities.'
                    : 'Pelaksanaan tindakan pembetulan untuk sebarang ketidakakuran yang dikenal pasti.'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Contact for Assistance */}
        <div className="card bg-bg-light-green">
          <h3 className="text-xl font-bold mb-4 text-gray-900">
            {language === 'en' ? 'Need Assistance?' : 'Perlukan Bantuan?'}
          </h3>
          <p className="text-gray-600 mb-4">
            {language === 'en'
              ? 'For detailed guidance on specific requirements or clarification on any aspect of the certification process, please contact our team.'
              : 'Untuk panduan terperinci mengenai keperluan khusus atau penjelasan mengenai sebarang aspek proses pensijilan, sila hubungi pasukan kami.'}
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="text-sm">
              <span className="font-medium">
                {language === 'en' ? 'Phone:' : 'Telefon:'}
              </span>{' '}
              03-8892 5000
            </div>
            <div className="text-sm">
              <span className="font-medium">
                {language === 'en' ? 'Email:' : 'E-mel:'}
              </span>{' '}
              <EMAIL>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}
