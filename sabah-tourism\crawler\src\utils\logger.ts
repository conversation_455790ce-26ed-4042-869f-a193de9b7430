import fs from 'fs';
import path from 'path';
import winston from 'winston';

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for console output
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.colorize(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const metaStr = Object.keys(meta).length
      ? JSON.stringify(meta, null, 2)
      : '';
    return `${timestamp} [${level}]: ${message} ${metaStr}`;
  }),
);

// Custom format for file output
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
);

// Create the logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: fileFormat,
  defaultMeta: { service: 'sabah-tourism-crawler' },
  transports: [
    // Write all logs with importance level of `error` or less to `error.log`
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // Write all logs with importance level of `info` or less to `combined.log`
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
  ],
});

// If we're not in production, log to the console as well
if (process.env.NODE_ENV !== 'production') {
  logger.add(
    new winston.transports.Console({
      format: consoleFormat,
    }),
  );
}

// Create specialized loggers for different components
export const crawlerLogger = logger.child({ component: 'crawler' });
export const databaseLogger = logger.child({ component: 'database' });
export const downloaderLogger = logger.child({ component: 'downloader' });
export const reportLogger = logger.child({ component: 'report' });

// Helper functions for common logging patterns
export const logCrawlStart = (
  platform: string,
  keywords: string[],
  sessionId: number,
) => {
  crawlerLogger.info('Crawl session started', {
    platform,
    keywords,
    sessionId,
    timestamp: new Date().toISOString(),
  });
};

export const logCrawlEnd = (sessionId: number, summary: any) => {
  crawlerLogger.info('Crawl session completed', {
    sessionId,
    summary,
    timestamp: new Date().toISOString(),
  });
};

export const logError = (error: Error, context?: any) => {
  logger.error('Error occurred', {
    error: {
      message: error.message,
      stack: error.stack,
      name: error.name,
    },
    context,
    timestamp: new Date().toISOString(),
  });
};

export const logProgress = (sessionId: number, progress: any) => {
  crawlerLogger.info('Crawl progress update', {
    sessionId,
    progress,
    timestamp: new Date().toISOString(),
  });
};

export const logMediaDownload = (
  url: string,
  fileName: string,
  status: string,
  error?: string,
) => {
  downloaderLogger.info('Media download', {
    url,
    fileName,
    status,
    error,
    timestamp: new Date().toISOString(),
  });
};

export default logger;
