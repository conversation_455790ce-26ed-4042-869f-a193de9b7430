#!/usr/bin/env bun

import puppeteer from 'puppeteer';

async function debugWebsite() {
  console.log('🔍 Debugging website structure...');

  const browser = await puppeteer.launch({
    headless: false, // Show browser for debugging
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });

  const page = await browser.newPage();
  await page.setViewport({ width: 1920, height: 1080 });

  try {
    const url =
      'https://myehalal.halal.gov.my/portal-halal/v1/index.php?data=ZGlyZWN0b3J5L2luZGV4X2RpcmVjdG9yeTs7Ozs=&negeri=&category=&cari=';
    console.log('📍 Navigating to:', url);

    await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 60000 });

    // Wait a bit for the page to fully load
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Get page title
    const title = await page.title();
    console.log('📄 Page title:', title);

    // Check for tables
    const tables = await page.evaluate(() => {
      const tables = document.querySelectorAll('table');
      return Array.from(tables).map((table, index) => ({
        index,
        rowCount: table.querySelectorAll('tr').length,
        columnCount:
          table.querySelectorAll('tr')[0]?.querySelectorAll('td, th').length ||
          0,
        hasData:
          table.textContent?.includes('Nama') ||
          table.textContent?.includes('Company') ||
          false,
        sampleText: table.textContent?.substring(0, 200) || '',
      }));
    });

    console.log('📊 Found tables:', tables);

    // Check for pagination
    const pagination = await page.evaluate(() => {
      const links = document.querySelectorAll('a');
      const paginationLinks = Array.from(links).filter((link) => {
        const text = link.textContent?.toLowerCase() || '';
        const href = link.getAttribute('href') || '';
        return (
          (text.includes('next') ||
            text.includes('seterusnya') ||
            text.includes('>') ||
            text.includes('previous') ||
            text.includes('sebelum') ||
            text.includes('<') ||
            /^\d+$/.test(text.trim())) &&
          href
        );
      });

      return paginationLinks.map((link) => ({
        text: link.textContent?.trim(),
        href: link.getAttribute('href'),
      }));
    });

    console.log('🔗 Pagination links:', pagination);

    // Get all text content to understand structure
    const bodyText = await page.evaluate(() => {
      return document.body.textContent?.substring(0, 1000) || '';
    });

    console.log('📝 Page content sample:', bodyText);

    // Take a screenshot for manual inspection
    await page.screenshot({ path: 'debug-screenshot.png', fullPage: true });
    console.log('📸 Screenshot saved as debug-screenshot.png');

    // Keep browser open for manual inspection
    console.log(
      '🔍 Browser will stay open for 30 seconds for manual inspection...',
    );
    await new Promise((resolve) => setTimeout(resolve, 30000));
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await browser.close();
  }
}

debugWebsite();
