import { sql } from 'drizzle-orm';
import {
  boolean,
  integer,
  pgTable,
  serial,
  text,
  timestamp,
  varchar,
  vector,
  unique
} from 'drizzle-orm/pg-core';

// Sites table (from server schema)
export const sites = pgTable('sites', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  code: varchar('code', { length: 50 }).unique().notNull(),
  domains: text('domains').array().notNull().default(sql`'{}'::text[]`), // Array of domains
  status: boolean('status').notNull().default(true), // 1 = active, 0 = inactive
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Products table (from server schema)
export const products = pgTable('products', {
  id: serial('id').primaryKey(),
  siteId: integer('site_id').notNull(),
  productName: varchar('product_name', { length: 500 }).notNull(),
  companyName: varchar('company_name', { length: 500 }).notNull(),
  certificateNumber: varchar('certificate_number', { length: 255 }),
  certificateType: varchar('certificate_type', { length: 255 }),
  issuedDate: varchar('issued_date', { length: 255 }),
  expiryDate: varchar('expiry_date', { length: 255 }),
  status: varchar('status', { length: 100 }),
  category: varchar('category', { length: 255 }),
  subcategory: varchar('subcategory', { length: 255 }),
  address: text('address'),
  state: varchar('state', { length: 255 }),
  country: varchar('country', { length: 255 }),
  contactInfo: text('contact_info'),
  website: varchar('website', { length: 500 }),
  sourceUrl: varchar('source_url', { length: 1000 }),
  rawData: text('raw_data'), // Store original scraped data as JSON
  r2rDocumentId: varchar('r2r_document_id', { length: 255 }),
  embedding: vector('embedding', { dimensions: 3072 }),
  vectorizedAt: timestamp('vectorized_at'),
  vectorizationModel: varchar('vectorization_model', { length: 100 }),
  searchableText: text('searchable_text'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Pages table
export const pages = pgTable('pages', {
  id: serial('id').primaryKey(),
  title: varchar('title', { length: 255 }).notNull(),
  content: text('content').notNull(),
  slug: varchar('slug', { length: 255 }).unique().notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Posts table
export const posts = pgTable('posts', {
  id: serial('id').primaryKey(),
  title: varchar('title', { length: 255 }).notNull(),
  content: text('content').notNull(),
  slug: varchar('slug', { length: 255 }).unique().notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// People table
export const people = pgTable('people', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  role: varchar('role', { length: 255 }),
  bio: text('bio'),
  slug: varchar('slug', { length: 255 }).unique().notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// News table
export const news = pgTable('news', {
  id: serial('id').primaryKey(),
  title: varchar('title', { length: 255 }).notNull(),
  content: text('content').notNull(),
  slug: varchar('slug', { length: 255 }).unique().notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Companies table (from server schema)
export const companies = pgTable('companies', {
  id: serial('id').primaryKey(),
  siteId: integer('site_id').notNull(),
  companyName: varchar('company_name', { length: 500 }).notNull(),
  registrationNumber: varchar('registration_number', { length: 255 }),
  businessType: varchar('business_type', { length: 255 }),
  category: varchar('category', { length: 255 }),
  subcategory: varchar('subcategory', { length: 255 }),
  address: text('address'),
  state: varchar('state', { length: 255 }),
  postcode: varchar('postcode', { length: 20 }),
  city: varchar('city', { length: 255 }),
  country: varchar('country', { length: 255 }).default('Malaysia'),
  phone: varchar('phone', { length: 50 }),
  fax: varchar('fax', { length: 50 }),
  email: varchar('email', { length: 255 }),
  website: varchar('website', { length: 500 }),
  contactPerson: varchar('contact_person', { length: 255 }),
  certificateNumber: varchar('certificate_number', { length: 255 }),
  certificateType: varchar('certificate_type', { length: 255 }),
  certificateStatus: varchar('certificate_status', { length: 100 }),
  issuedDate: varchar('issued_date', { length: 255 }),
  expiryDate: varchar('expiry_date', { length: 255 }),
  sourceUrl: varchar('source_url', { length: 1000 }),
  pageNumber: integer('page_number'),
  rawData: text('raw_data'),
  dataHash: varchar('data_hash', { length: 64 }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Search Analytics table for tracking user search activities
export const searchAnalytics = pgTable('search_analytics', {
  id: serial('id').primaryKey(),
  siteId: integer('site_id').notNull(),
  searchQuery: text('search_query').notNull(),
  searchType: varchar('search_type', { length: 50 }).notNull(), // 'web', 'products', 'companies', 'semantic'
  resultsCount: integer('results_count').notNull().default(0),
  userAgent: text('user_agent'),
  ipAddress: varchar('ip_address', { length: 45 }), // IPv6 compatible
  sessionId: varchar('session_id', { length: 255 }),
  userId: integer('user_id'), // Optional - for logged in users
  responseTime: integer('response_time'), // Response time in milliseconds
  hasResults: boolean('has_results').notNull().default(false),
  searchFilters: text('search_filters'), // JSON string of applied filters
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

// Categories table for product and company categorization
export const categories = pgTable('categories', {
  id: serial('id').primaryKey(),
  categoryName: varchar('category_name', { length: 255 }).notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Product-Category junction table (many-to-many relationship)
export const productCategories = pgTable('product_categories', {
  id: serial('id').primaryKey(),
  productId: integer('product_id').notNull().references(() => products.id, { onDelete: 'cascade' }),
  categoryId: integer('category_id').notNull().references(() => categories.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
}, (table) => ({
  // Ensure unique product-category combinations
  uniqueProductCategory: unique('unique_product_category').on(table.productId, table.categoryId),
}));

// Company-Category junction table (many-to-many relationship)
export const companyCategories = pgTable('company_categories', {
  id: serial('id').primaryKey(),
  companyId: integer('company_id').notNull().references(() => companies.id, { onDelete: 'cascade' }),
  categoryId: integer('category_id').notNull().references(() => categories.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
}, (table) => ({
  // Ensure unique company-category combinations
  uniqueCompanyCategory: unique('unique_company_category').on(table.companyId, table.categoryId),
}));

