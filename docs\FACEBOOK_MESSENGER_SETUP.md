# Facebook Messenger Integration Setup

This guide will help you set up Facebook Messenger integration for your Halal Malaysia chat system.

## Prerequisites

1. A Facebook Page for your business
2. A Facebook Developer Account
3. Access to Meta for Developers platform
4. Your deployed chat system with admin access

## Step 1: Create a Facebook App

1. Go to [Meta for Developers](https://developers.facebook.com/)
2. Click "Create App"
3. Select "Business" as the app type
4. Fill in your app details:
   - App Name: "Halal Malaysia Chat Bot"
   - App Contact Email: Your business email
   - Business Account: Select your business account (optional)

## Step 2: Add Messenger Product

1. In your app dashboard, click "Add Product"
2. Find "Messenger" and click "Set Up"
3. This will add Messenger to your app's products

## Step 3: Generate Page Access Token

1. In the Messenger settings, scroll to "Access Tokens"
2. Click "Add or Remove Pages"
3. Select your Facebook Page and grant necessary permissions
4. Copy the generated Page Access Token
5. **Important**: Keep this token secure and never share it publicly

## Step 4: Get Your Page ID

1. Go to your Facebook Page
2. Click "About" in the left sidebar
3. Scroll down to find "Page ID" or "Facebook Page ID"
4. Copy this ID

## Step 5: Get Your App Secret

1. In your Facebook App dashboard, go to "Settings" > "Basic"
2. Copy the "App Secret"
3. **Important**: Keep this secret secure and never share it publicly

## Step 6: Configure Webhook

1. In your chat system admin panel, go to Facebook Configuration
2. Enter the following information:
   - **Page Access Token**: The token from Step 3
   - **Page ID**: The ID from Step 4
   - **App Secret**: The secret from Step 5
   - **Verify Token**: Create a secure random string (e.g., "halal_malaysia_webhook_2024")

3. Save the configuration
4. Copy the webhook URL provided by the system

## Step 7: Set Up Facebook Webhook

1. In your Facebook App dashboard, go to Messenger > Settings
2. In the "Webhooks" section, click "Add Callback URL"
3. Enter the webhook URL from your admin panel
4. Enter the verify token you created in Step 6
5. Subscribe to the following webhook events:
   - `messages`
   - `messaging_postbacks`
   - `messaging_optins`
6. Click "Verify and Save"

## Step 8: Subscribe to Page Events

1. In the "Webhooks" section, find your page
2. Click "Subscribe" next to your page name
3. Select the following events:
   - `messages`
   - `messaging_postbacks`
   - `messaging_optins`

## Step 9: Test the Integration

1. Go to your admin panel's Facebook test page
2. Send a test message using a Facebook User ID (PSID)
3. Verify the message is received and processed correctly

## Step 10: Submit for Review (Production)

For production use, you'll need to submit your app for review:

1. In your Facebook App dashboard, go to "App Review"
2. Submit for review of the following permissions:
   - `pages_messaging`
   - `pages_show_list`
3. Provide detailed information about your use case
4. Wait for Facebook's approval (usually 1-7 business days)

## Getting Facebook User IDs (PSIDs)

To send messages to users, you need their Page-Scoped IDs (PSIDs):

1. Users must first message your page
2. The PSID will be included in the webhook payload
3. You can find PSIDs in your message logs or webhook data

## Troubleshooting

### Common Issues

1. **Webhook verification fails**
   - Check that your verify token matches exactly
   - Ensure your webhook URL is accessible via HTTPS
   - Verify your server is responding to GET requests

2. **Messages not being received**
   - Check webhook subscriptions are active
   - Verify your app has the necessary permissions
   - Check Facebook's webhook logs in the developer console

3. **Cannot send messages**
   - Ensure your page access token is valid
   - Check that the recipient has messaged your page first
   - Verify your app is approved for production (if needed)

### Testing Tips

1. Use Facebook's Webhook Test Tool in the developer console
2. Check the webhook logs for error messages
3. Test with a personal Facebook account first
4. Use the admin panel's test functionality

## Security Best Practices

1. **Never expose tokens**: Keep access tokens and app secrets secure
2. **Use HTTPS**: Always use HTTPS for webhook URLs
3. **Verify signatures**: Always verify webhook signatures for security
4. **Rotate tokens**: Regularly rotate your access tokens
5. **Monitor usage**: Keep track of API usage and rate limits

## Rate Limits

Facebook Messenger has rate limits:
- Standard messaging: 1000 messages per second
- Broadcast messaging: Lower limits apply
- Always implement proper error handling for rate limit responses

## Support

For additional help:
- [Facebook Messenger Platform Documentation](https://developers.facebook.com/docs/messenger-platform/)
- [Facebook Developer Community](https://developers.facebook.com/community/)
- Your system admin panel for configuration and testing

## Environment Variables

Make sure these environment variables are set in your deployment:

```bash
# Optional: Set custom webhook base URL
WEBHOOK_BASE_URL=https://your-domain.com

# Database configuration (if using persistent storage)
DATABASE_URL=your_database_connection_string
```

## Next Steps

After successful setup:
1. Monitor message delivery and response times
2. Set up message analytics and logging
3. Configure automated responses for common queries
4. Train your AI assistant with Halal-specific knowledge
5. Set up customer service workflows
