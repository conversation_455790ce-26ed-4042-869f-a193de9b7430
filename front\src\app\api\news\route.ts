import { type NextRequest, NextResponse } from 'next/server';
import { news } from '@/data/content';
export const runtime = 'edge';
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const page = Number.parseInt(searchParams.get('page') || '1');
    const limit = Number.parseInt(searchParams.get('limit') || '10');
    const sortBy = searchParams.get('sortBy') || 'date';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const search = searchParams.get('search');

    // Filter news
    const filteredNews = news.filter((item) => {
      const matchesCategory =
        !category ||
        item.category.toLowerCase().includes(category.toLowerCase()) ||
        item.categoryBM.toLowerCase().includes(category.toLowerCase());

      const matchesSearch =
        !search ||
        item.title.toLowerCase().includes(search.toLowerCase()) ||
        item.titleBM.toLowerCase().includes(search.toLowerCase()) ||
        item.excerpt.toLowerCase().includes(search.toLowerCase()) ||
        item.excerptBM.toLowerCase().includes(search.toLowerCase());

      return matchesCategory && matchesSearch;
    });

    // Sort news
    filteredNews.sort((a, b) => {
      let aValue: any = a[sortBy as keyof typeof a];
      let bValue: any = b[sortBy as keyof typeof b];

      // Handle date sorting
      if (sortBy === 'date') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
      }
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
    });

    // Paginate results
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedNews = filteredNews.slice(startIndex, endIndex);

    return NextResponse.json({
      success: true,
      data: paginatedNews,
      pagination: {
        page,
        limit,
        total: filteredNews.length,
        totalPages: Math.ceil(filteredNews.length / limit),
      },
    });
  } catch (error) {
    console.error('News API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch news',
      },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      title,
      titleBM,
      excerpt,
      excerptBM,
      content,
      contentBM,
      category,
      categoryBM,
      image,
    } = body;

    // Validate required fields
    if (!title || !excerpt || !content || !category) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation error',
          message: 'Title, excerpt, content, and category are required',
        },
        { status: 400 },
      );
    }

    // Create new news item
    const newNewsItem = {
      id: `${Date.now()}${Math.random().toString(36).substr(2, 9)}`,
      title,
      titleBM: titleBM || title,
      excerpt,
      excerptBM: excerptBM || excerpt,
      content,
      contentBM: contentBM || content,
      date: new Date().toISOString().split('T')[0],
      category,
      categoryBM: categoryBM || category,
      ...(image && { image }),
    };

    // In a real application, save to database
    // For now, we'll just return the created news item

    return NextResponse.json(
      {
        success: true,
        data: newNewsItem,
        message: 'News item created successfully',
      },
      { status: 201 },
    );
  } catch (error) {
    console.error('Create news API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to create news item',
      },
      { status: 500 },
    );
  }
}
