// Global setup for Jest tests
// This runs once before all tests

module.exports = async () => {
  console.log('🧪 Setting up test environment...');

  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.DATABASE_URL =
    process.env.TEST_DATABASE_URL || 'sqlite://test.db';

  // You can add database setup here if needed
  // For now, we'll use in-memory or file-based testing

  console.log('✅ Test environment setup complete');
};
