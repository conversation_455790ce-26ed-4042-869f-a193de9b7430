import React from 'react';

// Mock all lucide-react icons
export const ExternalLink = ({ className, ...props }: any) => (
  <svg className={className} data-testid="external-link-icon" {...props}>
    <title>External Link</title>
  </svg>
);

export const FileText = ({ className, ...props }: any) => (
  <svg className={className} data-testid="file-text-icon" {...props}>
    <title>File Text</title>
  </svg>
);

export const Star = ({ className, ...props }: any) => (
  <svg className={className} data-testid="star-icon" {...props}>
    <title>Star</title>
  </svg>
);

export const ChevronLeft = ({ className, ...props }: any) => (
  <svg className={className} data-testid="chevron-left-icon" {...props}>
    <title>Chevron Left</title>
  </svg>
);

export const ChevronRight = ({ className, ...props }: any) => (
  <svg className={className} data-testid="chevron-right-icon" {...props}>
    <title>Chevron Right</title>
  </svg>
);

export const File = ({ className, ...props }: any) => (
  <svg className={className} data-testid="file-icon" {...props}>
    <title>File</title>
  </svg>
);

export const Globe = ({ className, ...props }: any) => (
  <svg className={className} data-testid="globe-icon" {...props}>
    <title>Globe</title>
  </svg>
);
