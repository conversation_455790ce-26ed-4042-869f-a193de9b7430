'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { Edit, MoreHorizontal, Trash2, Eye, Copy } from 'lucide-react';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Link } from '@/i18n/navigation';
import type { Collection } from '@/types/collection';
import { useCollectionsStore } from '@/stores/collections';

function CollectionActionsCell({
  collection,
  onDelete,
}: {
  collection: Collection;
  onDelete?: () => void;
}) {
  const params = useParams();
  const locale = params.locale as string;
  const { deleteCollection } = useCollectionsStore();

  const handleDelete = async () => {
    if (window.confirm(`Are you sure you want to delete "${collection.name}"? This action cannot be undone.`)) {
      const success = await deleteCollection(collection.id);
      if (success && onDelete) {
        onDelete();
      }
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem
          onClick={() => navigator.clipboard.writeText(collection.id.toString())}
        >
          <Copy className="mr-2 h-4 w-4" />
          Copy ID
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href={`/${locale}/admin/collections/${collection.id}`}>
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/${locale}/admin/collections/${collection.id}/edit`}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Collection
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/${locale}/admin/collections/${collection.id}/clone`}>
            <Copy className="mr-2 h-4 w-4" />
            Clone Collection
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={handleDelete}
          className="text-red-600 hover:text-red-700"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete Collection
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export const createColumns = (onDelete?: () => void): ColumnDef<Collection>[] => [
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) => {
      const params = useParams();
      const locale = params.locale as string;
      return (
        <Link
          href={`/${locale}/admin/collections/${row.original.id}`}
          className="font-medium text-blue-600 hover:text-blue-800"
        >
          {row.getValue('name')}
        </Link>
      );
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      return (
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium ${
            status === 'ACTIVE'
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          }`}
        >
          {status}
        </span>
      );
    },
  },
  {
    accessorKey: '_count.documents',
    header: 'Documents',
    cell: ({ row }) => {
      const count = row.original._count?.documents || 0;
      return <span className="text-gray-600">{count}</span>;
    },
  },
  {
    accessorKey: 'createdAt',
    header: 'Created At',
    cell: ({ row }) => {
      const date = new Date(row.getValue('createdAt'));
      return date.toLocaleDateString();
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => (
      <div className="flex items-center justify-end space-x-2">
        <Link href={`/admin/collections/${row.original.id}/edit`}>
          <Button variant="outline" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
            <Edit className="h-4 w-4" />
          </Button>
        </Link>
        <CollectionActionsCell collection={row.original} onDelete={onDelete} />
      </div>
    ),
  },
];