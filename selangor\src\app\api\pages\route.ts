import { eq } from 'drizzle-orm';
import { db, pages } from '@/lib/db';
import { createSuccessResponse, createErrorResponse, handleApiError } from '@/lib/api-response';

// Remove edge runtime to use Node.js runtime for database access

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const slug = searchParams.get('slug');

    if (slug) {
      // Get specific page by slug
      const page = await db
        .select()
        .from(pages)
        .where(eq(pages.slug, slug))
        .limit(1);

      if (page.length === 0) {
        return createErrorResponse('Page not found', undefined, 404);
      }

      return createSuccessResponse(page[0]);
    }

    // Get all pages
    const allPages = await db.select().from(pages);
    return createSuccessResponse(allPages);
  } catch (error) {
    return handleApiError(error, 'Failed to fetch pages');
  }
}
