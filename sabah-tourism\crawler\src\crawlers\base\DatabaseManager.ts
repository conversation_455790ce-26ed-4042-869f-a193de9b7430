import Database from 'better-sqlite3';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

import type {
  CrawlAttempt,
  CrawlSession,
  DatabaseConfig,
  DatabaseStats,
  MediaFile,
  SearchKeyword,
  SocialPost,
} from '../../types/database.js';
import { databaseLogger } from '../../utils/logger.js';

export class DatabaseManager {
  private db: Database.Database;
  private config: DatabaseConfig;

  constructor(config: DatabaseConfig) {
    this.config = config;
    this.initializeDatabase();
  }

  private initializeDatabase(): void {
    try {
      // Ensure database directory exists
      const dbDir = path.dirname(this.config.path);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      // Initialize database connection
      this.db = new Database(this.config.path, {
        verbose: this.config.verbose
          ? databaseLogger.debug.bind(databaseLogger)
          : undefined,
        timeout: this.config.timeout || 5000,
      });

      // Enable WAL mode for better performance
      if (this.config.enableWAL !== false) {
        this.db.pragma('journal_mode = WAL');
      }

      // Set other pragmas for performance
      this.db.pragma('synchronous = NORMAL');
      this.db.pragma('cache_size = 1000000');
      this.db.pragma('temp_store = memory');

      // Create tables
      this.createTables();

      databaseLogger.info('Database initialized successfully', {
        path: this.config.path,
      });
    } catch (error) {
      databaseLogger.error('Failed to initialize database', {
        error,
        config: this.config,
      });
      throw error;
    }
  }

  private createTables(): void {
    const schemaPath = path.join(__dirname, '../../database/schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf-8');

    // Execute schema
    this.db.exec(schema);

    databaseLogger.info('Database tables created/verified');
  }

  // Social Posts operations
  public insertPost(post: Omit<SocialPost, 'id'>): number {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO social_posts (
        platform, post_id, url, title, content, author_username, 
        author_display_name, author_avatar_url, posted_at, tags,
        likes_count, comments_count, shares_count, views_count, metadata
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      post.platform,
      post.post_id,
      post.url,
      post.title,
      post.content,
      post.author_username,
      post.author_display_name,
      post.author_avatar_url,
      post.posted_at?.toISOString(),
      post.tags,
      post.likes_count || 0,
      post.comments_count || 0,
      post.shares_count || 0,
      post.views_count || 0,
      post.metadata,
    );

    return result.lastInsertRowid as number;
  }

  public getPost(platform: string, postId: string): SocialPost | null {
    const stmt = this.db.prepare(
      'SELECT * FROM social_posts WHERE platform = ? AND post_id = ?',
    );
    return stmt.get(platform, postId) as SocialPost | null;
  }

  public getPostsBySession(sessionId: number): SocialPost[] {
    const stmt = this.db.prepare(`
      SELECT sp.* FROM social_posts sp
      JOIN crawl_attempts ca ON sp.url = ca.post_url
      WHERE ca.session_id = ? AND ca.status = 'success'
    `);
    return stmt.all(sessionId) as SocialPost[];
  }

  // Media Files operations
  public insertMediaFile(media: Omit<MediaFile, 'id'>): number {
    const stmt = this.db.prepare(`
      INSERT INTO media_files (
        post_id, media_type, file_name, file_path, original_url,
        file_size, duration, width, height
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      media.post_id,
      media.media_type,
      media.file_name,
      media.file_path,
      media.original_url,
      media.file_size,
      media.duration,
      media.width,
      media.height,
    );

    return result.lastInsertRowid as number;
  }

  public getMediaFiles(postId: number): MediaFile[] {
    const stmt = this.db.prepare('SELECT * FROM media_files WHERE post_id = ?');
    return stmt.all(postId) as MediaFile[];
  }

  // Crawl Sessions operations
  public createSession(session: Omit<CrawlSession, 'id'>): number {
    const stmt = this.db.prepare(`
      INSERT INTO crawl_sessions (
        platform, keywords, status, config
      ) VALUES (?, ?, ?, ?)
    `);

    const result = stmt.run(
      session.platform,
      session.keywords,
      session.status,
      session.config,
    );

    return result.lastInsertRowid as number;
  }

  public updateSession(id: number, updates: Partial<CrawlSession>): void {
    const fields = Object.keys(updates)
      .map((key) => `${key} = ?`)
      .join(', ');
    const values = Object.values(updates).map((value) => {
      // Convert Date objects to ISO strings for SQLite
      if (value instanceof Date) {
        return value.toISOString();
      }
      return value;
    });

    const stmt = this.db.prepare(
      `UPDATE crawl_sessions SET ${fields} WHERE id = ?`,
    );
    stmt.run(...values, id);
  }

  public getSession(id: number): CrawlSession | null {
    const stmt = this.db.prepare('SELECT * FROM crawl_sessions WHERE id = ?');
    const result = stmt.get(id) as any;

    if (!result) return null;

    // Convert date strings back to Date objects
    return {
      ...result,
      created_at: result.created_at ? new Date(result.created_at) : undefined,
      started_at: result.started_at ? new Date(result.started_at) : undefined,
      completed_at: result.completed_at
        ? new Date(result.completed_at)
        : undefined,
    } as CrawlSession;
  }

  public getActiveSessions(): CrawlSession[] {
    const stmt = this.db.prepare(
      'SELECT * FROM crawl_sessions WHERE status IN (?, ?)',
    );
    return stmt.all('running', 'paused') as CrawlSession[];
  }

  // Crawl Attempts operations
  public insertAttempt(attempt: Omit<CrawlAttempt, 'id'>): number {
    const stmt = this.db.prepare(`
      INSERT INTO crawl_attempts (
        session_id, post_url, keyword, status, error_message
      ) VALUES (?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      attempt.session_id,
      attempt.post_url,
      attempt.keyword,
      attempt.status,
      attempt.error_message,
    );

    return result.lastInsertRowid as number;
  }

  public updateAttempt(id: number, updates: Partial<CrawlAttempt>): void {
    const fields = Object.keys(updates)
      .map((key) => `${key} = ?`)
      .join(', ');
    const values = Object.values(updates);

    const stmt = this.db.prepare(
      `UPDATE crawl_attempts SET ${fields} WHERE id = ?`,
    );
    stmt.run(...values, id);
  }

  public getPendingAttempts(sessionId: number): CrawlAttempt[] {
    const stmt = this.db.prepare(
      'SELECT * FROM crawl_attempts WHERE session_id = ? AND status = ?',
    );
    return stmt.all(sessionId, 'pending') as CrawlAttempt[];
  }

  // Search Keywords operations
  public upsertKeyword(keyword: SearchKeyword): void {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO search_keywords (
        keyword, platform, last_crawled, total_posts_found
      ) VALUES (?, ?, ?, ?)
    `);

    stmt.run(
      keyword.keyword,
      keyword.platform,
      keyword.last_crawled?.toISOString(),
      keyword.total_posts_found || 0,
    );
  }

  // Advanced query methods
  public getPostsByKeyword(keyword: string, limit?: number): SocialPost[] {
    const stmt = this.db.prepare(`
      SELECT sp.* FROM social_posts sp
      JOIN crawl_attempts ca ON sp.url = ca.post_url
      WHERE ca.keyword = ?
      ORDER BY sp.crawled_at DESC
      ${limit ? 'LIMIT ?' : ''}
    `);

    return limit
      ? (stmt.all(keyword, limit) as SocialPost[])
      : (stmt.all(keyword) as SocialPost[]);
  }

  public getPostsByAuthor(
    authorUsername: string,
    limit?: number,
  ): SocialPost[] {
    const stmt = this.db.prepare(`
      SELECT * FROM social_posts
      WHERE author_username = ?
      ORDER BY crawled_at DESC
      ${limit ? 'LIMIT ?' : ''}
    `);

    return limit
      ? (stmt.all(authorUsername, limit) as SocialPost[])
      : (stmt.all(authorUsername) as SocialPost[]);
  }

  public getTopAuthors(
    platform?: string,
    limit = 10,
  ): Array<{
    author_username: string;
    post_count: number;
    total_likes: number;
    total_comments: number;
    avg_likes: number;
  }> {
    let query = `
      SELECT
        author_username,
        COUNT(*) as post_count,
        SUM(likes_count) as total_likes,
        SUM(comments_count) as total_comments,
        AVG(likes_count) as avg_likes
      FROM social_posts
      WHERE author_username IS NOT NULL
    `;

    const params: any[] = [];
    if (platform) {
      query += ' AND platform = ?';
      params.push(platform);
    }

    query += `
      GROUP BY author_username
      ORDER BY total_likes DESC
      LIMIT ?
    `;
    params.push(limit);

    const stmt = this.db.prepare(query);
    return stmt.all(...params) as any[];
  }

  public getEngagementStats(platform?: string): {
    totalLikes: number;
    totalComments: number;
    totalShares: number;
    totalViews: number;
    avgLikes: number;
    avgComments: number;
  } {
    let query = `
      SELECT
        SUM(likes_count) as totalLikes,
        SUM(comments_count) as totalComments,
        SUM(shares_count) as totalShares,
        SUM(views_count) as totalViews,
        AVG(likes_count) as avgLikes,
        AVG(comments_count) as avgComments
      FROM social_posts
      WHERE 1=1
    `;

    const params: any[] = [];
    if (platform) {
      query += ' AND platform = ?';
      params.push(platform);
    }

    const stmt = this.db.prepare(query);
    const result = stmt.get(...params) as any;

    return {
      totalLikes: result.totalLikes || 0,
      totalComments: result.totalComments || 0,
      totalShares: result.totalShares || 0,
      totalViews: result.totalViews || 0,
      avgLikes: Math.round(result.avgLikes || 0),
      avgComments: Math.round(result.avgComments || 0),
    };
  }

  // Statistics and reporting
  public getStats(): DatabaseStats {
    const totalPosts = this.db
      .prepare('SELECT COUNT(*) as count FROM social_posts')
      .get() as { count: number };
    const totalMediaFiles = this.db
      .prepare('SELECT COUNT(*) as count FROM media_files')
      .get() as { count: number };
    const totalSessions = this.db
      .prepare('SELECT COUNT(*) as count FROM crawl_sessions')
      .get() as { count: number };
    const activeSessions = this.db
      .prepare(
        'SELECT COUNT(*) as count FROM crawl_sessions WHERE status IN (?, ?)',
      )
      .get('running', 'paused') as { count: number };

    const platformBreakdown = this.db
      .prepare(`
      SELECT platform, COUNT(*) as count
      FROM social_posts
      GROUP BY platform
    `)
      .all() as { platform: string; count: number }[];

    const recentActivity = {
      postsToday: this.db
        .prepare(`
        SELECT COUNT(*) as count FROM social_posts
        WHERE DATE(crawled_at) = DATE('now')
      `)
        .get() as { count: number },
      postsThisWeek: this.db
        .prepare(`
        SELECT COUNT(*) as count FROM social_posts
        WHERE DATE(crawled_at) >= DATE('now', '-7 days')
      `)
        .get() as { count: number },
      postsThisMonth: this.db
        .prepare(`
        SELECT COUNT(*) as count FROM social_posts
        WHERE DATE(crawled_at) >= DATE('now', '-30 days')
      `)
        .get() as { count: number },
    };

    return {
      totalPosts: totalPosts.count,
      totalMediaFiles: totalMediaFiles.count,
      totalSessions: totalSessions.count,
      activeSessions: activeSessions.count,
      platformBreakdown: Object.fromEntries(
        platformBreakdown.map((p) => [p.platform, p.count]),
      ),
      recentActivity: {
        postsToday: recentActivity.postsToday.count,
        postsThisWeek: recentActivity.postsThisWeek.count,
        postsThisMonth: recentActivity.postsThisMonth.count,
      },
    };
  }

  public close(): void {
    if (this.db) {
      this.db.close();
      databaseLogger.info('Database connection closed');
    }
  }

  // Transaction support
  public transaction<T>(fn: () => T): T {
    return this.db.transaction(fn)();
  }
}
