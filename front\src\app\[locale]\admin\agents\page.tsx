'use client';

export const runtime = 'edge';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>cle,
  Clock,
  Edit,
  MessageSquare,
  Refresh<PERSON>w,
  Trash2,
  UserPlus,
  Users,
  Wifi,
  WifiOff,
  X,
  XCircle,
} from 'lucide-react';
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';
import { NotificationCenter } from '@/components/notifications/NotificationCenter';
import { AgentSearchFilter } from '@/components/search/AgentSearchFilter';
import { useAgentSearch } from '@/hooks/useAgentSearch';
import { useAdminAuthGuard } from '@/hooks/useAuthGuard';
import { Link } from '@/i18n/navigation';
import { api } from '@/lib/api';
import { useNotifications } from '@/lib/notifications';
import { useWebSocket } from '@/lib/websocket';
import { UserRole } from '@/types/roles';

interface AgentUser {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'agent' | 'supervisor';
  isActive: boolean;
  isOnline: boolean;
  lastSeenAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface HandoverRequest {
  id: number;
  sessionId: string;
  requestedBy: string;
  reason?: string;
  priority: string;
  status: string;
  requestedAt: string;
  agent?: AgentUser;
}

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

export default function AgentManagementPage() {
  const { user, loading } = useAdminAuthGuard([
    UserRole.ADMIN,
    UserRole.SUPERVISOR,
  ]);
  const notifications = useNotifications();

  const [agents, setAgents] = useState<AgentUser[]>([]);
  const [_handoverRequests, setHandoverRequests] = useState<HandoverRequest[]>(
    [],
  );
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    totalAgents: 0,
    onlineAgents: 0,
    activeSessions: 0,
    pendingHandovers: 0,
  });
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<AgentUser | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Search and filtering
  const {
    filteredAgents,
    isSearching,
    totalResults,
    handleFilterChange,
    searchStats,
  } = useAgentSearch({ agents });

  const loadData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);



      // Load agents data and stats in parallel
      const [agentsResponse, statsResponse] = await Promise.all([
        api.admin.listAgents(),
        api.admin.getAgentStats().catch(() => ({ success: false, data: null })),
      ]);

      if (agentsResponse.success) {
        const agentsData: AgentUser[] = agentsResponse.data || [];
        setAgents(agentsData);

        // Use real stats if available, otherwise calculate from agents data
        if (statsResponse.success && statsResponse.data) {
          setStats(statsResponse.data);
        } else {
          // Fallback to calculated stats
          setStats({
            totalAgents: agentsData.length,
            onlineAgents: agentsData.filter((agent) => agent.isOnline).length,
            activeSessions: 0, // Will be updated when session API is available
            pendingHandovers: 0, // Will be updated when handover API is available
          });
        }
      } else {
        setError('Failed to load agents data');
      }

      // Load handover requests if sessions API is available
      try {
        const handoversResponse = await api.sessions.getPendingHandovers();
        if (handoversResponse.success) {
          setHandoverRequests(handoversResponse.data || []);
        }
      } catch (error) {
        console.log('Handover requests not available yet');
        setHandoverRequests([]);
      }
    } catch (error) {
      console.error('Error loading agent data:', error);
      setError('Failed to load agents data');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Memoized WebSocket callbacks to prevent infinite reconnections
  const onConnect = useCallback(() => {
    console.log('Admin agents WebSocket connected');
  }, []);

  const onDisconnect = useCallback(() => {
    console.log('Admin agents WebSocket disconnected');
  }, []);

  const onAgentStatusUpdate = useCallback((data: any) => {
    console.log('Agent status update:', data);
    // Update agent status in real-time
    setAgents((prevAgents) =>
      prevAgents.map((agent) =>
        agent.id === data.agentId
          ? {
              ...agent,
              isOnline: data.isOnline,
              lastSeenAt: data.lastSeenAt,
            }
          : agent,
      ),
    );

    // Update stats
    setStats((prevStats) => ({
      ...prevStats,
      onlineAgents: prevStats.onlineAgents + (data.isOnline ? 1 : -1),
    }));
  }, []);

  const onNotification = useCallback((data: any) => {
    console.log('Received notification:', data);
    notifications.addNotification({
      type: data.type,
      title: data.title,
      message: data.message,
      urgent: data.urgent,
      actionUrl: data.actionUrl,
    });
  }, [notifications]);

  // WebSocket connection for real-time agent updates
  const { isConnected: wsConnected } = useWebSocket(
    `${process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:16001'}/ws`,
    {
      onConnect,
      onDisconnect,
      onAgentStatusUpdate,
      onNotification,
    },
  );

  useEffect(() => {
    if (!loading && user) {
      loadData();
    }
  }, [loading, user, loadData]);

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    if (!loading && user) {
      const interval = setInterval(() => {
        loadData();
      }, 30000);

      return () => clearInterval(interval);
    }
  }, [loading, user, loadData]);

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'supervisor':
        return 'bg-purple-100 text-purple-800';
      case 'agent':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (isOnline: boolean) => {
    return isOnline ? 'text-green-600' : 'text-gray-400';
  };

  const formatLastSeen = (lastSeenAt?: string) => {
    if (!lastSeenAt) {
      return 'Never';
    }
    const date = new Date(lastSeenAt);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) {
      return 'Just now';
    }
    if (diffMins < 60) {
      return `${diffMins}m ago`;
    }
    if (diffMins < 1440) {
      return `${Math.floor(diffMins / 60)}h ago`;
    }
    return date.toLocaleDateString();
  };

  const handleAddAgent = () => {
    setSelectedAgent(null);
    setShowAddModal(true);
  };

  const handleEditAgent = (agent: AgentUser) => {
    setSelectedAgent(agent);
    setShowEditModal(true);
  };

  const handleDeleteAgent = async (agent: AgentUser) => {
    if (
      !confirm(
        `Are you sure you want to delete agent ${agent.firstName} ${agent.lastName}?`,
      )
    ) {
      return;
    }

    try {
      const response = await api.admin.deleteAgent(agent.id);
      if (response.success) {
        // Reload data
        await loadData();
      } else {
        setError('Failed to delete agent');
      }
    } catch (error) {
      console.error('Error deleting agent:', error);
      setError('Failed to delete agent. Please try again.');
    }
  };

  const handleSubmitAgent = async (formData: any) => {
    setIsSubmitting(true);
    try {
      setError(null);

      if (selectedAgent) {
        // Update existing agent
        const response = await api.admin.updateAgent(
          selectedAgent.id,
          formData,
        );
        if (!response.success) {
          const errorMessage = response.error || 'Failed to update agent';
          setError(errorMessage);
          return;
        }
      } else {
        // Create new agent
        const response = await api.admin.createAgent(formData);
        if (!response.success) {
          const errorMessage = response.error || 'Failed to create agent';
          setError(errorMessage);
          return;
        }
      }

      // Close modals and reload data
      setShowAddModal(false);
      setShowEditModal(false);
      setSelectedAgent(null);
      await loadData();
    } catch (error: any) {
      console.error('Error saving agent:', error);
      const errorMessage =
        error?.response?.data?.error ||
        error?.message ||
        'Failed to save agent';
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" />
          <p className="mt-4 text-gray-600">Loading agent data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Link
                href="/admin/dashboard"
                className="p-2 hover:bg-gray-100 rounded-md"
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600 mr-3" />
                <h1 className="text-2xl font-bold text-gray-900">
                  Agent Management
                </h1>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {/* WebSocket Status */}
              <div className="flex items-center space-x-1">
                {wsConnected ? (
                  <Wifi className="h-4 w-4 text-green-500" />
                ) : (
                  <WifiOff className="h-4 w-4 text-red-500" />
                )}
                <span className="text-xs text-gray-500">
                  {wsConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>

              {/* Notification Center */}
              <NotificationCenter />

              <button
                type="button"
                onClick={loadData}
                disabled={isLoading}
                className="flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50"
                title="Refresh data"
              >
                <RefreshCw
                  className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
                />
              </button>
              <button
                type="button"
                onClick={handleAddAgent}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Add Agent
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">


        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <XCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Search and Filter */}
        <AgentSearchFilter
          onFilterChange={handleFilterChange}
          totalResults={totalResults}
          isLoading={isSearching}
        />
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Total Agents
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {searchStats.totalAgents}
                    </dd>
                    <dd className="text-xs text-gray-500">
                      {searchStats.filteredCount} shown
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Online Now
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {searchStats.onlineAgents}
                    </dd>
                    <dd className="text-xs text-gray-500">
                      {Math.round(
                        (searchStats.onlineAgents / searchStats.totalAgents) *
                          100,
                      )}
                      % online
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <MessageSquare className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Active Sessions
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stats.activeSessions}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Avg Performance
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {searchStats.averagePerformance.toFixed(1)}/5.0
                    </dd>
                    <dd className="text-xs text-gray-500">Filtered results</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Agents Table */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Agent Users
            </h3>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Agent
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Role
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Seen
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredAgents.map((agent) => (
                    <tr key={agent.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                              <span className="text-sm font-medium text-gray-700">
                                {agent.firstName[0]}
                                {agent.lastName[0]}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {agent.firstName} {agent.lastName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {agent.email}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(agent.role)}`}
                        >
                          {agent.role}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {agent.isOnline ? (
                            <CheckCircle
                              className={`h-4 w-4 mr-2 ${getStatusColor(agent.isOnline)}`}
                            />
                          ) : (
                            <XCircle
                              className={`h-4 w-4 mr-2 ${getStatusColor(agent.isOnline)}`}
                            />
                          )}
                          <span
                            className={`text-sm ${getStatusColor(agent.isOnline)}`}
                          >
                            {agent.isOnline ? 'Online' : 'Offline'}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatLastSeen(agent.lastSeenAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleEditAgent(agent)}
                            className="text-blue-600 hover:text-blue-900"
                            title="Edit Agent"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            type="button"
                            onClick={() => handleDeleteAgent(agent)}
                            className="text-red-600 hover:text-red-900"
                            title="Delete Agent"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>

      {/* Add Agent Modal */}
      {showAddModal && (
        <AgentModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSubmit={handleSubmitAgent}
          isSubmitting={isSubmitting}
          title="Add New Agent"
        />
      )}

      {/* Edit Agent Modal */}
      {showEditModal && selectedAgent && (
        <AgentModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          onSubmit={handleSubmitAgent}
          isSubmitting={isSubmitting}
          title="Edit Agent"
          agent={selectedAgent}
        />
      )}
    </div>
  );
}

// Agent Modal Component
interface AgentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  isSubmitting: boolean;
  title: string;
  agent?: AgentUser;
}

function AgentModal({
  isOpen,
  onClose,
  onSubmit,
  isSubmitting,
  title,
  agent,
}: AgentModalProps) {
  const [formData, setFormData] = useState({
    username: agent?.username || '',
    email: agent?.email || '',
    password: '',
    firstName: agent?.firstName || '',
    lastName: agent?.lastName || '',
    role: agent?.role || 'agent',
    isActive: agent?.isActive ?? true,
  });

  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.username.trim()) {
      errors.username = 'Username is required';
    }

    if (!formData.role) {
      errors.role = 'Role is required';
    }

    if (!agent && !formData.password) {
      errors.password = 'Password is required for new agents';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Prepare data for submission
    const submitData: any = {
      username: formData.username.trim(),
      email: formData.email?.trim() || null,
      firstName: formData.firstName?.trim() || null,
      lastName: formData.lastName?.trim() || null,
      role: formData.role.toUpperCase(),
      isActive: formData.isActive,
    };

    // Only include password if it's provided
    if (formData.password) {
      submitData.password = formData.password;
    }

    onSubmit(submitData);
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]:
        type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900">{title}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={isSubmitting}
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label
              htmlFor="username"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Username *
            </label>
            <input
              id="username"
              type="text"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                validationErrors.username
                  ? 'border-red-300 focus:ring-red-500'
                  : 'border-gray-300 focus:ring-blue-500'
              }`}
              required
              disabled={isSubmitting}
            />
            {validationErrors.username && (
              <p className="mt-1 text-sm text-red-600">
                {validationErrors.username}
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Email
            </label>
            <input
              id="email"
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                validationErrors.email
                  ? 'border-red-300 focus:ring-red-500'
                  : 'border-gray-300 focus:ring-blue-500'
              }`}
              disabled={isSubmitting}
            />
            {validationErrors.email && (
              <p className="mt-1 text-sm text-red-600">
                {validationErrors.email}
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Password {!agent && '*'}
            </label>
            <input
              id="password"
              type="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                validationErrors.password
                  ? 'border-red-300 focus:ring-red-500'
                  : 'border-gray-300 focus:ring-blue-500'
              }`}
              required={!agent}
              disabled={isSubmitting}
              placeholder={agent ? 'Leave blank to keep current password' : ''}
            />
            {validationErrors.password && (
              <p className="mt-1 text-sm text-red-600">
                {validationErrors.password}
              </p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="firstName"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                First Name
              </label>
              <input
                id="firstName"
                type="text"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isSubmitting}
              />
            </div>

            <div>
              <label
                htmlFor="lastName"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Last Name
              </label>
              <input
                id="lastName"
                type="text"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isSubmitting}
              />
            </div>
          </div>

          <div>
            <label
              htmlFor="role"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Role *
            </label>
            <select
              id="role"
              name="role"
              value={formData.role}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
              disabled={isSubmitting}
            >
              <option value="agent">Agent</option>
              <option value="supervisor">Supervisor</option>
            </select>
          </div>

          <div className="flex items-center">
            <input
              id="isActive"
              type="checkbox"
              name="isActive"
              checked={formData.isActive}
              onChange={handleInputChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              disabled={isSubmitting}
            />
            <label
              htmlFor="isActive"
              className="ml-2 block text-sm text-gray-700"
            >
              Active
            </label>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : agent ? 'Update' : 'Create'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
