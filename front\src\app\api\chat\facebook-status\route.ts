import { type NextRequest, NextResponse } from 'next/server';
export const runtime = 'edge';

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8787';
const DEFAULT_SITE_ID = process.env.NEXT_PUBLIC_DEFAULT_SITE_ID || '1';

export async function GET(_request: NextRequest) {
  try {
    const response = await fetch(
      `${API_BASE_URL}/api/sites/${DEFAULT_SITE_ID}/chat/facebook-status`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

    const data = await response.json();

    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Facebook status proxy error:', error);
    return NextResponse.json(
      {
        facebookEnabled: false,
        pageId: null,
        message: 'Facebook Messenger integration is not available.',
      },
      { status: 200 },
    );
  }
}
