#!/usr/bin/env node

const FRONTEND_URL = 'http://localhost:16000';
const API_BASE_URL = 'http://localhost:16001';
const DEFAULT_SITE_ID = '1';

async function testFrontendAPIIntegration() {
  console.log('🚀 Testing Frontend API Integration\n');
  
  // Test 1: Login through frontend API proxy
  console.log('📋 Test 1: Login through frontend API proxy');
  try {
    const loginResponse = await fetch(`${FRONTEND_URL}/api/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      }),
    });

    const loginData = await loginResponse.json();
    console.log(`✅ Frontend Login API: ${loginResponse.status} ${loginResponse.statusText}`);
    
    if (loginResponse.ok && loginData.token) {
      console.log('✅ Token received from frontend API');
      
      // Test 2: Test authenticated endpoints through frontend
      console.log('\n📋 Test 2: Testing authenticated endpoints through frontend');
      
      const token = loginData.token;
      const endpoints = [
        '/api/admin/me',
        '/api/admin/users', 
        '/api/admin/collections',
        '/api/admin/bots',
        '/api/admin/services',
        '/api/admin/sessions',
        '/api/admin/s3-configurations'
      ];
      
      for (const endpoint of endpoints) {
        try {
          const response = await fetch(`${FRONTEND_URL}${endpoint}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });
          
          const statusColor = response.ok ? '✅' : '❌';
          console.log(`${statusColor} ${endpoint}: ${response.status} ${response.statusText}`);
          
          if (!response.ok) {
            const errorText = await response.text();
            console.log(`   Error: ${errorText.substring(0, 100)}...`);
          }
        } catch (error) {
          console.log(`❌ ${endpoint}: ${error.message}`);
        }
      }
      
    } else {
      console.log('❌ No token received from frontend API');
      console.log('Response:', loginData);
    }
    
  } catch (error) {
    console.log(`❌ Frontend Login API: ${error.message}`);
  }
  
  // Test 3: Direct API calls (bypass frontend)
  console.log('\n📋 Test 3: Direct API calls (for comparison)');
  try {
    const directLoginResponse = await fetch(`${API_BASE_URL}/api/admin/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      }),
    });

    const directLoginData = await directLoginResponse.json();
    console.log(`✅ Direct API Login: ${directLoginResponse.status} ${directLoginResponse.statusText}`);
    
    if (directLoginResponse.ok && directLoginData.token) {
      const token = directLoginData.token;
      
      // Test a few direct API endpoints
      const directEndpoints = [
        '/api/sites/1/admin/collections',
        '/api/sites/1/admin/users',
        '/api/sites/1/admin/bots'
      ];
      
      for (const endpoint of directEndpoints) {
        try {
          const response = await fetch(`${API_BASE_URL}${endpoint}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });
          
          const statusColor = response.ok ? '✅' : '❌';
          console.log(`${statusColor} Direct ${endpoint}: ${response.status} ${response.statusText}`);
          
        } catch (error) {
          console.log(`❌ Direct ${endpoint}: ${error.message}`);
        }
      }
    }
    
  } catch (error) {
    console.log(`❌ Direct API Login: ${error.message}`);
  }
  
  // Test 4: Check frontend environment variables
  console.log('\n📋 Test 4: Frontend API Configuration');
  console.log(`Frontend URL: ${FRONTEND_URL}`);
  console.log(`Expected API Base URL: ${API_BASE_URL}`);
  console.log(`Expected Site ID: ${DEFAULT_SITE_ID}`);
  
  // Test if frontend has the right API routes
  console.log('\n📋 Test 5: Frontend API Route Availability');
  const frontendApiRoutes = [
    '/api/admin/login',
    '/api/admin/me',
    '/api/admin/verify'
  ];
  
  for (const route of frontendApiRoutes) {
    try {
      const response = await fetch(`${FRONTEND_URL}${route}`, {
        method: route.includes('login') ? 'POST' : 'GET',
        headers: { 'Content-Type': 'application/json' },
        body: route.includes('login') ? JSON.stringify({ username: 'test', password: 'test' }) : undefined,
      });
      
      const statusColor = response.status !== 404 ? '✅' : '❌';
      console.log(`${statusColor} Frontend route ${route}: ${response.status} ${response.statusText}`);
      
    } catch (error) {
      console.log(`❌ Frontend route ${route}: ${error.message}`);
    }
  }
  
  console.log('\n✅ Frontend API integration tests completed');
}

testFrontendAPIIntegration().catch(console.error);
