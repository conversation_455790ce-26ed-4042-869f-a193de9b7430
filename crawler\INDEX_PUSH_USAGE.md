# Index Push to Qdrant - Usage Guide

This guide shows you how to push crawled files (.md, .pdf, .docx, .txt) from the `output/` directory to a Qdrant vector database using Docker.

## 🚀 Quick Start

### Method 1: Using Makefile (Recommended)

```bash
# Push files to a collection named "my_documents"
make index-push COLLECTION=my_documents

# Push to a collection with custom Qdrant URL
make index-push COLLECTION=my_documents QDRANT_URL=http://localhost:6333
```

### Method 2: Using Shell Script

```bash
# Basic usage
./run_index_push.sh my_documents

# With custom Qdrant URL
./run_index_push.sh my_documents http://localhost:6333
```

### Method 3: Direct Docker Command

```bash
# Basic usage (uses default Qdrant URL)
docker run --rm \
  -v "$(pwd)/output:/app/output:ro" \
  --network host \
  website-crawler \
  python push_to_qdrant.py my_documents

# With custom Qdrant URL
docker run --rm \
  -v "$(pwd)/output:/app/output:ro" \
  --network host \
  website-crawler \
  python push_to_qdrant.py my_documents http://*************:6333
```

## 📋 Prerequisites

1. **Qdrant Server Running**: Start Qdrant on your host machine:
   ```bash
   docker run -d -p 6333:6333 qdrant/qdrant
   ```

2. **Crawled Files**: Ensure you have files in the `output/` directory from previous crawls

3. **Docker**: Make sure Docker is installed and running

## 🔧 Configuration Options

### Collection Name
- **Required**: You must specify a collection name
- **Format**: Use alphanumeric characters and underscores
- **Examples**: `halal_docs`, `my_collection`, `website_content`

### Qdrant URL Options
- **Default**: `http://host.docker.internal:6333` (for Docker Desktop)
- **Local**: `http://localhost:6333`
- **Remote**: `http://your-server-ip:6333`
- **Custom Port**: `http://localhost:9333`

## 📊 What Gets Processed

The system automatically finds and processes:
- **Markdown files**: `*.md`
- **PDF files**: `*.pdf` 
- **Word documents**: `*.docx`
- **Text files**: `*.txt`

### File Processing Features
- ✅ **Recursive scanning** of all subdirectories in `output/`
- ✅ **Duplicate detection** using file hash and size
- ✅ **Content extraction** from all supported file types
- ✅ **Semantic embeddings** using sentence-transformers
- ✅ **Batch processing** for efficiency
- ✅ **Error handling** with detailed logging

## 📈 Example Output

```
🚀 Pushing crawled files to Qdrant...
📊 Collection: halal_documents
🔗 Qdrant URL: http://host.docker.internal:6333
📁 Output directory: output

=== PUSH RESULTS ===
Collection: halal_documents
Total files found: 3
Files processed: 3
Files skipped (unchanged): 0
Files updated: 3
Errors: 0
Start time: 2025-06-18T16:58:27.112719
End time: 2025-06-18T16:58:45.301951

✅ Successfully pushed 3 files to collection 'halal_documents'
```

## 🛠️ Troubleshooting

### Common Issues

1. **"Collection parameter required"**
   ```bash
   # ❌ Wrong
   make index-push
   
   # ✅ Correct
   make index-push COLLECTION=my_docs
   ```

2. **"Output directory does not exist"**
   ```bash
   # Run crawler first to generate files
   make run
   ```

3. **"Connection refused"**
   ```bash
   # Start Qdrant server
   docker run -d -p 6333:6333 qdrant/qdrant
   ```

4. **"Permission denied" for shell script**
   ```bash
   chmod +x run_index_push.sh
   ```

### Debug Mode

For detailed logging, check the Docker container output which includes:
- File discovery and processing status
- Qdrant connection details
- Embedding generation progress
- Upload statistics

## 🔍 Verifying Results

After pushing files, you can verify the collection was created by:

1. **Qdrant Web UI**: Visit `http://localhost:6333/dashboard`
2. **API Check**: 
   ```bash
   curl http://localhost:6333/collections
   ```

## 🚀 Integration Examples

### Automated Pipeline
```bash
# 1. Crawl websites
make run

# 2. Push to Qdrant
make index-push COLLECTION=latest_crawl

# 3. Query the collection (using your application)
```

### Multiple Collections
```bash
# Separate collections for different domains
make index-push COLLECTION=halal_gov_my
make index-push COLLECTION=hdc_global
make index-push COLLECTION=jsm_gov_my
```

## 📚 Next Steps

After pushing files to Qdrant, you can:
1. **Query the collection** using the Qdrant API or client libraries
2. **Build search applications** using semantic similarity
3. **Create RAG systems** for document Q&A
4. **Implement recommendation engines** based on content similarity

For querying examples, see the `qdrant_manager.py` file which includes query functions.
