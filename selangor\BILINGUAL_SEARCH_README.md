# Multilingual Search Functionality

This document describes the multilingual search functionality implemented in the Halal Selangor search API that automatically detects languages and performs additional searches with AI-powered translations.

## Features

- **AI-Powered Language Detection**: Uses OpenAI to detect the language of search queries (supports Malay, Arabic, English, and more)
- **Smart Translation**: Generates contextually relevant English translations for non-English queries
- **Parallel Search Execution**: Performs multiple searches simultaneously for better performance
- **Result Deduplication**: Combines and deduplicates results from different language searches
- **Score-based Ranking**: Maintains the highest scoring results when duplicates are found
- **Graceful Fallback**: Continues to work even if OpenAI API is unavailable

## How It Works

### 1. Query Analysis
When a search query is received, the system:
- Uses OpenAI to detect the primary language of the query
- Generates 3-5 contextually relevant English translations for non-English queries
- Focuses on halal/food/certification related terms rather than literal translations

### 2. Multi-Language Search
The system performs searches for:
- The original query (as entered by the user)
- AI-generated English translations for non-English queries

### 3. Result Processing
- Combines results from all searches
- Deduplicates based on document ID and content similarity
- Keeps the highest-scoring version of duplicate results
- Returns combined results with pagination support

## Language Support

The AI-powered translation supports:

- **Malay (ms)**: Traditional halal/food terminology
- **Arabic (ar)**: Islamic and halal-related terms
- **English (en)**: No translation needed
- **Other languages**: Automatic detection and translation to English

The system intelligently focuses on:
- Halal and food certification terminology
- Religious and Islamic terms
- Business and regulatory language
- Contextually relevant translations rather than literal word-by-word translations

## API Response

The search API now includes additional fields in the response when multilingual translation is used:

```json
{
  "query": "sijil makanan",
  "results": [...],
  "totalChunks": 5,
  "totalGraphResults": 0,
  "options": {...},
  "pagination": {...},
  "detectedLanguage": "ms",
  "confidence": 0.95,
  "translatedTerms": ["halal certificate", "food certification", "certificate food"],
  "searchType": "multilingual"
}
```

New fields:
- `detectedLanguage`: Language code detected by AI (e.g., "ms", "ar", "en")
- `confidence`: AI confidence score for language detection (0-1)
- `translatedTerms`: Array of AI-generated English translations
- `searchType`: "multilingual" when translations are used, otherwise omitted

## Examples

### Example 1: Malay Query (AI Translation)
**Input**: `sijil makanan`
**AI Detection**:
- Language: Malay (ms)
- Confidence: 0.95
- Translations: ["halal certificate", "food certification", "certificate food"]

**Searches Performed**:
1. `sijil makanan` (original)
2. `halal certificate` (AI translation)
3. `food certification` (AI translation)
4. `certificate food` (AI translation)

**Result**: Comprehensive multilingual search results

### Example 2: Arabic Query (AI Translation)
**Input**: `شهادة حلال`
**AI Detection**:
- Language: Arabic (ar)
- Confidence: 0.90
- Translations: ["halal certificate", "halal certification", "islamic certification"]

**Searches Performed**:
1. `شهادة حلال` (original)
2. `halal certificate` (AI translation)
3. `halal certification` (AI translation)
4. `islamic certification` (AI translation)

**Result**: Cross-language halal certification results

### Example 3: English Query (No Translation)
**Input**: `organic certification`
**AI Detection**:
- Language: English (en)
- Confidence: 0.98
- Translations: [] (none needed)

**Searches Performed**:
1. `organic certification` (original only)

**Result**: Direct search, no translation overhead

## Performance Considerations

- **Parallel Execution**: Multiple searches run simultaneously using `Promise.all()`
- **AI-Powered Detection**: Only performs additional searches when non-English languages are detected
- **Efficient Deduplication**: Uses Map-based deduplication for O(1) lookup performance
- **Score Preservation**: Maintains relevance scoring across combined results
- **Graceful Fallback**: Continues working even if OpenAI API is unavailable
- **Smart Caching**: AI responses could be cached for frequently used queries

## Testing

The multilingual search functionality includes comprehensive tests covering:
- Basic English queries (no translation)
- Non-English queries (with AI translation)
- Mixed language queries
- Result deduplication
- OpenAI API error handling
- Empty result handling

Run tests with:
```bash
pnpm test src/app/api/products/search/__tests__/search.test.ts
```

## Configuration

### Environment Variables

The multilingual search requires OpenAI API access:

```env
# OpenAI Configuration (required for translation)
OPENAI_API_KEY=your-openai-api-key
```

### Translation Service

The translation logic is centralized in `/src/lib/translation.ts` and can be customized:

```typescript
// Modify the prompt to focus on specific domains
const prompt = `Analyze the following search query and:
1. Detect the primary language
2. Generate contextually relevant English translations
3. Focus on your-specific-domain terms
...`;
```

## Future Enhancements

Potential improvements for the multilingual search functionality:
- **Caching**: Cache AI translation results for performance
- **More Languages**: Expand beyond current language support
- **Context Awareness**: Use document context to improve translations
- **User Feedback**: Learn from user interactions to improve translation quality
- **Batch Processing**: Process multiple queries in single API calls
- **Fallback Dictionaries**: Hybrid approach with AI + static dictionaries
