import { afterEach, beforeEach, describe, expect, it } from 'bun:test';
import { LogLevel, logger, PerformanceTracker, RequestTracker } from './logger';

describe('WorkerLogger', () => {
  let consoleSpy: any;

  beforeEach(() => {
    // Spy on console methods
    consoleSpy = {
      log: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
    };

    // Replace console methods
    global.console.log = consoleSpy.log;
    global.console.warn = consoleSpy.warn;
    global.console.error = consoleSpy.error;
  });

  afterEach(() => {
    // Reset console
    jest.restoreAllMocks();
  });

  describe('Basic Logging', () => {
    it('should log info messages with proper format', () => {
      logger.initialize({ LOG_LEVEL: 'INFO' });

      logger.info('Test message', { sessionId: 'test123' });

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('[INFO] Test message [session:test123]'),
      );
    });

    it('should respect log levels', () => {
      logger.initialize({ LOG_LEVEL: 'ERROR' });

      logger.debug('Debug message');
      logger.info('Info message');
      logger.warn('Warn message');
      logger.error('Error message');

      expect(consoleSpy.log).not.toHaveBeenCalledWith(
        expect.stringContaining('Debug message'),
      );
      expect(consoleSpy.log).not.toHaveBeenCalledWith(
        expect.stringContaining('Info message'),
      );
      expect(consoleSpy.warn).not.toHaveBeenCalled();
      expect(consoleSpy.error).toHaveBeenCalledWith(
        expect.stringContaining('Error message'),
      );
    });

    it('should format context properly', () => {
      logger.initialize({ LOG_LEVEL: 'DEBUG' });

      const context = {
        sessionId: 'sess_123',
        userId: 'user_456',
        platform: 'web',
        requestId: 'req_789',
      };

      logger.info('Test with context', context);

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining(
          '[session:sess_123|user:user_456|platform:web|req:req_789]',
        ),
      );
    });
  });

  describe('Specialized Logging', () => {
    beforeEach(() => {
      logger.initialize({ LOG_LEVEL: 'DEBUG' });
    });

    it('should log message flow', () => {
      logger.messageFlow('VALIDATION', 'Validating input', {
        sessionId: 'test',
      });

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('🔄 [FLOW] [VALIDATION] Validating input'),
      );
    });

    it('should log tool calls', () => {
      logger.toolCall('search_halal', 'Starting search', { sessionId: 'test' });

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('🔧 [TOOL] [search_halal] Starting search'),
      );
    });

    it('should log session events', () => {
      logger.session('CREATE', 'sess_123', { userId: 'user_456' });

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('👤 [SESSION] [CREATE] Session: sess_123'),
      );
    });

    it('should log database operations', () => {
      logger.database('SELECT', 'Fetching user data', { table: 'users' });

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('🗄️ [DB] [SELECT] Fetching user data'),
      );
    });

    it('should log API calls', () => {
      logger.api('POST', '/api/chat', 200, 150, { sessionId: 'test' });

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('✅ [API] POST /api/chat 200 (150ms)'),
      );
    });
  });

  describe('Performance Tracking', () => {
    beforeEach(() => {
      logger.initialize({ LOG_LEVEL: 'DEBUG' });
    });

    it('should track operation performance', async () => {
      const tracker = new PerformanceTracker('test_operation', {
        sessionId: 'test',
      });

      // Simulate some work
      await new Promise((resolve) => setTimeout(resolve, 10));

      const duration = tracker.end();

      expect(duration).toBeGreaterThan(0);
      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('⚡ [PERF]'),
      );
    });

    it('should track checkpoints', () => {
      const tracker = new PerformanceTracker('test_operation', {
        sessionId: 'test',
      });

      tracker.checkpoint('step1');

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('[DEBUG] test_operation - step1'),
      );
    });
  });

  describe('Request Tracking', () => {
    beforeEach(() => {
      logger.initialize({ LOG_LEVEL: 'INFO' });
    });

    it('should track request lifecycle', () => {
      const tracker = new RequestTracker('sess_123', 'user_456', 'web');

      tracker.stage('PROCESSING', 'Processing message');
      tracker.complete(true, 'Success');

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining(
          '🔄 [FLOW] [START] Processing incoming message',
        ),
      );
      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('🔄 [FLOW] [PROCESSING] Processing message'),
      );
      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('✅ [SUCCESS] Request completed'),
      );
    });

    it('should handle failed requests', () => {
      const tracker = new RequestTracker('sess_123', 'user_456', 'web');

      tracker.complete(false, 'Something went wrong');

      expect(consoleSpy.error).toHaveBeenCalledWith(
        expect.stringContaining('❌ [ERROR] Request failed'),
      );
    });

    it('should provide context for other operations', () => {
      const tracker = new RequestTracker('sess_123', 'user_456', 'web');
      const context = tracker.getContext();

      expect(context).toMatchObject({
        sessionId: 'sess_123',
        userId: 'user_456',
        platform: 'web',
        requestId: expect.stringMatching(/^req_/),
      });
    });
  });

  describe('Environment Configuration', () => {
    it('should set development mode correctly', () => {
      logger.initialize({ NODE_ENV: 'development', LOG_LEVEL: 'DEBUG' });

      logger.info('Test message', { test: 'data' });

      // In development mode, should log additional context
      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('📋 Context:'),
      );
    });

    it('should handle missing environment gracefully', () => {
      logger.initialize();

      logger.info('Test message');

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('[INFO] Test message'),
      );
    });
  });
});

// Example usage demonstration
describe('Logger Usage Examples', () => {
  beforeEach(() => {
    logger.initialize({ LOG_LEVEL: 'DEBUG', NODE_ENV: 'development' });
  });

  it('should demonstrate message handler logging', async () => {
    // Simulate message handler flow
    const tracker = new RequestTracker('sess_abc123', 'user_456', 'web');

    try {
      tracker.stage('VALIDATION', 'Validating input');
      logger.info('Input validated successfully', tracker.getContext());

      tracker.stage('SESSION_SETUP', 'Getting session');
      logger.session('FOUND_MEMORY', 'sess_abc123', { messageCount: 5 });

      tracker.stage('AI_PROCESSING', 'Sending to OpenAI');
      const perfTracker = new PerformanceTracker(
        'openai_request',
        tracker.getContext(),
      );

      // Simulate AI processing
      await new Promise((resolve) => setTimeout(resolve, 100));

      perfTracker.end('OpenAI request completed');

      tracker.stage('RESPONSE_STORAGE', 'Storing response');
      logger.database(
        'INSERT',
        'Stored assistant response',
        tracker.getContext(),
      );

      tracker.complete(true, 'Message processed successfully');
    } catch (error) {
      logger.error('Message processing failed', tracker.getContext(), error);
      tracker.complete(false, 'Processing failed');
    }

    // Verify the logging flow
    expect(console.log).toHaveBeenCalledWith(
      expect.stringContaining('🔄 [FLOW] [START] Processing incoming message'),
    );
  });
});
