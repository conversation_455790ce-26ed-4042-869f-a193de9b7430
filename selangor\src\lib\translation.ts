/**
 * Shared translation utilities using OpenAI for language detection and translation
 */

export interface TranslationResult {
  originalQuery: string;
  detectedLanguage: string;
  translatedTerms: string[];
  confidence: number;
}

/**
 * Detects the language of a query using OpenAI and generates translated terms
 * @param originalQuery The search query to analyze
 * @param excludeWords Optional list of words to exclude from generated terms
 */
export async function detectLanguageAndTranslate(
  originalQuery: string,
  excludeWords: string[] = [],
): Promise<TranslationResult> {
  const { OPENAI_API_KEY } = process.env;

  if (!OPENAI_API_KEY) {
    console.log('OpenAI API key not configured, skipping translation');
    return {
      originalQuery,
      detectedLanguage: 'unknown',
      translatedTerms: [],
      confidence: 0,
    };
  }

  try {
    const excludeWordsText =
      excludeWords.length > 0
        ? `\n- Do NOT include any of these words in the translated terms: ${excludeWords.join(', ')}`
        : '';

    const prompt = `Analyze the following search query / question and:
- Detect the primary language (return language code like 'ms' for Malay, 'ar' for Arabic, 'en' for English, etc.)
- If the language is NOT English, provide 1-3 English translations/equivalent terms that would help find similar content
- Focus on halal/food/certification related terms
- Return only practical search terms, not literal word-by-word translations
- Always include exact translated term without any other word if it is not in english.
${excludeWordsText?.length > 0 ? `- Do not include any of these terms: ${excludeWordsText}` : ''}

Query: "${originalQuery}"

Respond in this exact JSON format:
{
  "language": "language_code",
  "confidence": 0.95,
  "translatedTerms": ["term1", "term2", "term3"]
}

If the query is already in English or contains only English terms, return with 1 to 3 other contextually relevant terms.`;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4.1-mini',
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: 300,
        temperature: 0.3,
      }),
    });

    if (!response.ok) {
      throw new Error(
        `OpenAI API error: ${response.status} ${response.statusText}`,
      );
    }

    const data = await response.json();
    const content = data.choices[0]?.message?.content?.trim();

    if (!content) {
      throw new Error('No response from OpenAI');
    }

    // Parse the JSON response
    const result = JSON.parse(content);

    return {
      originalQuery,
      detectedLanguage: result.language || 'unknown',
      translatedTerms: Array.isArray(result.translatedTerms)
        ? result.translatedTerms
        : [],
      confidence: result.confidence || 0,
    };
  } catch (error) {
    console.error('Error in language detection and translation:', error);
    return {
      originalQuery,
      detectedLanguage: 'unknown',
      translatedTerms: [],
      confidence: 0,
    };
  }
}
