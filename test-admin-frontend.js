#!/usr/bin/env node

const puppeteer = require('puppeteer');

const FRONTEND_URL = 'http://localhost:16000';
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123'
};

async function testAdminDashboard() {
  console.log('🚀 Starting Admin Dashboard Frontend Tests\n');
  
  const browser = await puppeteer.launch({ 
    headless: false, // Set to true for headless mode
    defaultViewport: { width: 1280, height: 720 }
  });
  
  try {
    const page = await browser.newPage();
    
    // Test 1: Navigate to admin login
    console.log('📋 Test 1: Navigate to admin login page');
    await page.goto(`${FRONTEND_URL}/admin`);
    await page.waitForSelector('input[name="username"], input[type="text"]', { timeout: 10000 });
    console.log('✅ Admin login page loaded');
    
    // Test 2: Login with admin credentials
    console.log('\n📋 Test 2: Login with admin credentials');
    await page.type('input[name="username"], input[type="text"]', ADMIN_CREDENTIALS.username);
    await page.type('input[name="password"], input[type="password"]', ADMIN_CREDENTIALS.password);
    await page.click('button[type="submit"], button:contains("Login")');
    
    // Wait for redirect to dashboard
    await page.waitForNavigation({ timeout: 10000 });
    console.log('✅ Login successful, redirected to dashboard');
    
    // Test 3: Test each admin section
    const adminSections = [
      { name: 'Collections', path: '/admin/collections' },
      { name: 'Users', path: '/admin/users' },
      { name: 'Bots', path: '/admin/bots' },
      { name: 'Services', path: '/admin/services' },
      { name: 'Sessions', path: '/admin/sessions' },
      { name: 'S3 Configurations', path: '/admin/s3-configurations' }
    ];
    
    for (const section of adminSections) {
      console.log(`\n📋 Test 3.${adminSections.indexOf(section) + 1}: Testing ${section.name} section`);
      
      try {
        await page.goto(`${FRONTEND_URL}${section.path}`);
        await page.waitForSelector('body', { timeout: 5000 });
        
        // Check for error messages
        const errorElements = await page.$$('[class*="error"], .error, [data-testid="error"]');
        const loadingElements = await page.$$('[class*="loading"], .loading, [data-testid="loading"]');
        
        if (errorElements.length > 0) {
          const errorText = await page.evaluate(el => el.textContent, errorElements[0]);
          console.log(`❌ ${section.name}: Error found - ${errorText}`);
        } else if (loadingElements.length > 0) {
          console.log(`⏳ ${section.name}: Still loading...`);
          await page.waitForTimeout(3000); // Wait a bit more
        } else {
          console.log(`✅ ${section.name}: Page loaded successfully`);
        }
        
        // Test create functionality if available
        const createButton = await page.$('button:contains("Create"), button:contains("New"), a[href*="/new"]');
        if (createButton) {
          console.log(`  📝 ${section.name}: Create button found`);
        }
        
      } catch (error) {
        console.log(`❌ ${section.name}: Failed to load - ${error.message}`);
      }
    }
    
    // Test 4: Test create forms
    console.log('\n📋 Test 4: Testing create forms');
    
    // Test creating a new bot
    try {
      await page.goto(`${FRONTEND_URL}/admin/bots/new`);
      await page.waitForSelector('form, input[name="name"]', { timeout: 5000 });
      console.log('✅ Bot creation form loaded');
      
      // Fill out the form
      await page.type('input[name="name"]', 'Test Bot Frontend');
      await page.type('input[name="slug"]', 'test-bot-frontend');
      
      // Try to submit (but don't actually submit to avoid creating test data)
      const submitButton = await page.$('button[type="submit"]');
      if (submitButton) {
        console.log('✅ Bot creation form has submit button');
      }
      
    } catch (error) {
      console.log(`❌ Bot creation form: ${error.message}`);
    }
    
    // Test creating a new collection
    try {
      await page.goto(`${FRONTEND_URL}/admin/collections/new`);
      await page.waitForSelector('form, input[name="name"]', { timeout: 5000 });
      console.log('✅ Collection creation form loaded');
      
    } catch (error) {
      console.log(`❌ Collection creation form: ${error.message}`);
    }
    
    console.log('\n✅ Frontend tests completed');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await browser.close();
  }
}

// Check if puppeteer is available
try {
  testAdminDashboard().catch(console.error);
} catch (error) {
  console.log('⚠️ Puppeteer not available. Install with: npm install puppeteer');
  console.log('Running basic URL tests instead...\n');
  
  // Fallback to basic fetch tests
  const testUrls = [
    '/admin',
    '/admin/collections',
    '/admin/users', 
    '/admin/bots',
    '/admin/services',
    '/admin/sessions',
    '/admin/s3-configurations'
  ];
  
  for (const url of testUrls) {
    try {
      const response = await fetch(`${FRONTEND_URL}${url}`);
      console.log(`${url}: ${response.status} ${response.statusText}`);
    } catch (error) {
      console.log(`${url}: Error - ${error.message}`);
    }
  }
}
