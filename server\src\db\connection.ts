import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import ConnectionManager from '../utils/connectionManager';
import * as schema from './schema';

/**
 * Initialize database connection for PostgreSQL with Drizzle ORM
 * @param env - Environment object containing DATABASE_URL (for Cloudflare Workers)
 * @returns Configured Drizzle database instance
 */
export function initializeDatabase(
  env?: Record<string, unknown>,
): ReturnType<typeof drizzle> {
  // Prioritize Workers env.DATABASE_URL, fallback to process.env for non-Workers environments
  const databaseUrl =
    (env?.DATABASE_URL as string) ||
    (typeof process !== 'undefined' ? process.env.DATABASE_URL : undefined);

  if (!databaseUrl) {
    throw new Error(
      'DATABASE_URL is required. For Cloudflare Workers, set it in wrangler.toml or via secrets. For local development, set it in .env file.',
    );
  }

  // Create PostgreSQL connection using connection manager with optimized settings
  const sql = ConnectionManager.getConnection(databaseUrl, {
    // Environment-specific settings are handled by ConnectionManager
    // Additional server-specific options can be added here
    onnotice: process.env.NODE_ENV === 'development' ? console.log : undefined,
  });

  // Initialize Drizzle with the postgres connection and schema
  const db = drizzle(sql, { schema });

  return db;
}

/**
 * Initialize database connection for local development
 * @returns Configured Drizzle database instance
 */
export function initializeDatabaseLocal(): ReturnType<typeof drizzle> {
  if (!process.env.DATABASE_URL) {
    throw new Error(
      'DATABASE_URL environment variable is required for local development.',
    );
  }

  // Create PostgreSQL connection using connection manager for local development
  const sql = ConnectionManager.getConnection(process.env.DATABASE_URL, {
    // Environment-specific settings are handled by ConnectionManager
    // Additional local development options can be added here
    onnotice: console.log, // Show notices in development
  });

  // Initialize Drizzle with the postgres connection and schema
  const db = drizzle(sql, { schema });

  return db;
}

/**
 * Test database connection
 * @param databaseUrl - Database URL to test
 * @param env - Environment object (for Workers compatibility)
 */
export async function testConnection(
  databaseUrl?: string,
  env?: Record<string, unknown>,
): Promise<boolean> {
  try {
    const url =
      databaseUrl ||
      (env?.DATABASE_URL as string) ||
      (typeof process !== 'undefined' ? process.env.DATABASE_URL : undefined);
    if (!url) {
      throw new Error('Database URL not provided');
    }

    const sql = postgres(url, { prepare: false });

    // Simple query to test connection
    await sql`SELECT 1`;
    await sql.end();
    return true;
  } catch (error) {
    console.error('Database connection test failed:', error);
    return false;
  }
}

// Export the schema for use in other files
export { schema };
export * from './schema';
