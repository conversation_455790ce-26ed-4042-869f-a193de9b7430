# API Documentation

This document describes the API endpoints and data structures used in the JAKIM Halal Portal.

## 🔗 Base URLs

### Production
- **Main API**: `https://api.halal.gov.my`
- **Search API**: `https://search.halal.gov.my`
- **MyeHalal Domestic**: `https://myehalal.halal.gov.my/domestik/v1/`
- **MyeHalal International**: `https://myehalal.halal.gov.my/international/v1/`

### Staging
- **Main API**: `https://staging-api.halal.gov.my`
- **Search API**: `https://staging-search.halal.gov.my`

## 🔍 Search API

### Search Certificates

Search for Halal certificates with various filters.

**Endpoint**: `GET /api/search`

**Parameters**:
```typescript
interface SearchParams {
  query?: string          // Search query
  status?: string         // Certificate status: 'valid' | 'expired' | 'suspended'
  category?: string       // Product category: 'food' | 'cosmetics' | 'pharmaceuticals' | 'services'
  country?: string        // Country code or name
  sortBy?: string         // Sort field: 'companyName' | 'issueDate' | 'expiryDate'
  sortOrder?: 'asc' | 'desc'
  page?: number           // Page number (default: 1)
  limit?: number          // Results per page (default: 20, max: 100)
}
```

**Response**:
```typescript
interface SearchResponse {
  results: SearchResult[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  filters: {
    availableStatuses: string[]
    availableCategories: string[]
    availableCountries: string[]
  }
}

interface SearchResult {
  certificateNumber: string
  companyName: string
  productName: string
  status: 'valid' | 'expired' | 'suspended'
  issueDate: string       // ISO date string
  expiryDate: string      // ISO date string
  country: string
  category: string
  address?: string
  phone?: string
  email?: string
}
```

**Example Request**:
```bash
curl "https://search.halal.gov.my/api/search?query=ABC%20Company&status=valid&page=1&limit=10"
```

**Example Response**:
```json
{
  "results": [
    {
      "certificateNumber": "JAKIM-001-2024",
      "companyName": "ABC Food Industries Sdn Bhd",
      "productName": "Halal Chicken Products",
      "status": "valid",
      "issueDate": "2024-01-15T00:00:00.000Z",
      "expiryDate": "2026-01-15T00:00:00.000Z",
      "country": "Malaysia",
      "category": "food",
      "address": "123 Industrial Area, Kuala Lumpur",
      "phone": "+60123456789",
      "email": "<EMAIL>"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "totalPages": 1
  },
  "filters": {
    "availableStatuses": ["valid", "expired", "suspended"],
    "availableCategories": ["food", "cosmetics", "pharmaceuticals", "services"],
    "availableCountries": ["Malaysia", "Singapore", "Thailand", "Indonesia"]
  }
}
```

### Get Certificate Details

Get detailed information about a specific certificate.

**Endpoint**: `GET /api/certificates/{certificateNumber}`

**Response**:
```typescript
interface CertificateDetails extends SearchResult {
  description: string
  certificationBody: string
  auditDate?: string
  renewalDate?: string
  documents: {
    certificate: string    // URL to certificate PDF
    audit?: string         // URL to audit report
  }
  products: {
    name: string
    description: string
    category: string
  }[]
  facility: {
    name: string
    address: string
    coordinates?: {
      lat: number
      lng: number
    }
  }
}
```

## 📰 Content API

### Get Announcements

Retrieve latest announcements.

**Endpoint**: `GET /api/announcements`

**Parameters**:
```typescript
interface AnnouncementParams {
  limit?: number          // Number of announcements (default: 10, max: 50)
  priority?: 'high' | 'medium' | 'low'
  language?: 'en' | 'bm'
}
```

**Response**:
```typescript
interface AnnouncementResponse {
  announcements: Announcement[]
}

interface Announcement {
  id: string
  title: string
  titleBM: string
  content: string
  contentBM: string
  date: string            // ISO date string
  priority: 'high' | 'medium' | 'low'
  category: string
  author: string
  tags: string[]
}
```

### Get News

Retrieve latest news articles.

**Endpoint**: `GET /api/news`

**Parameters**:
```typescript
interface NewsParams {
  limit?: number          // Number of articles (default: 10, max: 50)
  category?: string       // News category
  language?: 'en' | 'bm'
  page?: number
}
```

**Response**:
```typescript
interface NewsResponse {
  news: NewsArticle[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

interface NewsArticle {
  id: string
  title: string
  titleBM: string
  excerpt: string
  excerptBM: string
  content: string
  contentBM: string
  date: string            // ISO date string
  category: string
  author: string
  image?: string          // URL to featured image
  tags: string[]
  slug: string
}
```

## 📋 Forms API

### Submit Contact Form

Submit a contact form inquiry.

**Endpoint**: `POST /api/contact`

**Request Body**:
```typescript
interface ContactFormData {
  name: string
  email: string
  phone?: string
  company?: string
  subject: string
  category: 'general' | 'certification' | 'technical' | 'complaint' | 'media' | 'partnership'
  message: string
  language: 'en' | 'bm'
}
```

**Response**:
```typescript
interface ContactResponse {
  success: boolean
  message: string
  ticketId?: string       // Reference number for follow-up
}
```

### Submit E-Aduan

Submit a complaint through E-Aduan system.

**Endpoint**: `POST /api/eaduan`

**Request Body**:
```typescript
interface EAduanData {
  complainantName: string
  complainantEmail: string
  complainantPhone: string
  companyName: string
  complaintType: 'product' | 'service' | 'certification' | 'other'
  description: string
  evidence?: File[]       // Uploaded files
  location?: string
  incidentDate?: string
}
```

**Response**:
```typescript
interface EAduanResponse {
  success: boolean
  message: string
  referenceNumber: string
  estimatedResolution: string
}
```

## 📊 Statistics API

### Get Dashboard Statistics

Retrieve statistics for dashboard display.

**Endpoint**: `GET /api/statistics`

**Response**:
```typescript
interface StatisticsResponse {
  certificates: {
    total: number
    valid: number
    expired: number
    thisMonth: number
  }
  companies: {
    total: number
    active: number
    newThisMonth: number
  }
  categories: {
    food: number
    cosmetics: number
    pharmaceuticals: number
    services: number
  }
  countries: {
    [countryCode: string]: number
  }
  trends: {
    monthly: {
      month: string
      certificates: number
      companies: number
    }[]
  }
}
```

## 🔐 Authentication

### API Key Authentication

For internal API access, include API key in headers:

```bash
curl -H "X-API-Key: your-api-key" \
     -H "Content-Type: application/json" \
     "https://api.halal.gov.my/api/search"
```

### Rate Limiting

API endpoints are rate-limited:
- **Public endpoints**: 100 requests per minute per IP
- **Authenticated endpoints**: 1000 requests per minute per API key

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## 🚨 Error Handling

### Error Response Format

All API errors follow this format:

```typescript
interface ErrorResponse {
  error: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
  path: string
  requestId: string
}
```

### Common Error Codes

| Code | Status | Description |
|------|--------|-------------|
| `INVALID_REQUEST` | 400 | Invalid request parameters |
| `UNAUTHORIZED` | 401 | Missing or invalid authentication |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `RATE_LIMITED` | 429 | Rate limit exceeded |
| `INTERNAL_ERROR` | 500 | Internal server error |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable |

### Example Error Response

```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Invalid search parameters",
    "details": {
      "field": "status",
      "value": "invalid_status",
      "allowedValues": ["valid", "expired", "suspended"]
    }
  },
  "timestamp": "2024-01-15T10:30:00.000Z",
  "path": "/api/search",
  "requestId": "req_123456789"
}
```

## 📝 Data Validation

### Request Validation

All API requests are validated against schemas:

```typescript
// Search parameters validation
const searchSchema = {
  query: { type: 'string', maxLength: 255 },
  status: { type: 'string', enum: ['valid', 'expired', 'suspended'] },
  category: { type: 'string', enum: ['food', 'cosmetics', 'pharmaceuticals', 'services'] },
  page: { type: 'integer', minimum: 1, maximum: 1000 },
  limit: { type: 'integer', minimum: 1, maximum: 100 }
}

// Contact form validation
const contactSchema = {
  name: { type: 'string', minLength: 2, maxLength: 100 },
  email: { type: 'string', format: 'email' },
  phone: { type: 'string', pattern: '^\\+?[1-9]\\d{1,14}$' },
  message: { type: 'string', minLength: 10, maxLength: 2000 }
}
```

## 🔄 Webhooks

### Certificate Status Updates

Subscribe to certificate status change notifications:

**Endpoint**: `POST /api/webhooks/certificates`

**Payload**:
```typescript
interface CertificateWebhook {
  event: 'certificate.issued' | 'certificate.renewed' | 'certificate.expired' | 'certificate.suspended'
  data: {
    certificateNumber: string
    companyName: string
    previousStatus?: string
    newStatus: string
    timestamp: string
  }
}
```

## 📚 SDK and Libraries

### JavaScript/TypeScript SDK

```bash
npm install @jakim/halal-api-sdk
```

```typescript
import { HalalAPI } from '@jakim/halal-api-sdk'

const api = new HalalAPI({
  baseUrl: 'https://api.halal.gov.my',
  apiKey: 'your-api-key'
})

// Search certificates
const results = await api.search({
  query: 'ABC Company',
  status: 'valid'
})

// Get certificate details
const certificate = await api.getCertificate('JAKIM-001-2024')
```

### PHP SDK

```bash
composer require jakim/halal-api-sdk
```

```php
use Jakim\HalalAPI\Client;

$client = new Client([
    'base_url' => 'https://api.halal.gov.my',
    'api_key' => 'your-api-key'
]);

$results = $client->search([
    'query' => 'ABC Company',
    'status' => 'valid'
]);
```

## 🧪 Testing

### Test Environment

Use staging endpoints for testing:
- **Base URL**: `https://staging-api.halal.gov.my`
- **Test API Key**: Contact development team

### Sample Test Data

Test certificates available in staging:
- `TEST-001-2024`: Valid food certificate
- `TEST-002-2024`: Expired cosmetics certificate
- `TEST-003-2024`: Suspended pharmaceutical certificate

---

For additional API support, contact the development <NAME_EMAIL>
