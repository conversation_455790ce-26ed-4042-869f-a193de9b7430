import { r2rClient } from 'r2r-js';
import type { ParseR2rOptions, R2rSearchResult, TextResult } from '../types';

export class R2RService {
  private client: r2rClient | null = null;
  private isAuthenticated = false;

  public getClient(env?: any): r2rClient {
    if (this.client) {
      return this.client;
    }

    // Try to get credentials from environment (Cloudflare Workers) or process.env (Node.js)
    const url = env?.R2R_URL || process.env?.R2R_URL;

    if (!url) {
      console.error('R2R_URL is not defined', {
        env,
        processEnv: process.env,
      });
      throw new Error('R2R_URL is required');
    }

    this.client = new r2rClient(url, false, {
      enableAutoRefresh: true,
    });

    return this.client;
  }

  private async ensureAuthenticated(env?: any): Promise<void> {
    if (this.isAuthenticated) {
      return;
    }

    const client = this.getClient(env);
    const username = env?.R2R_USERNAME || process.env?.R2R_USERNAME;
    const password = env?.R2R_PASSWORD || process.env?.R2R_PASSWORD;

    if (!username || !password) {
      console.error('R2R authentication credentials missing', {
        hasUsername: !!username,
        hasPassword: !!password,
      });
      throw new Error('R2R_USERNAME and R2R_PASSWORD are required');
    }

    try {
      console.log('Authenticating with R2R service...');
      await client.users.login({
        email: username,
        password: password,
      });
      this.isAuthenticated = true;
      console.log('R2R authentication successful');
    } catch (error) {
      console.error('R2R authentication failed:', error);
      throw new Error(
        `R2R authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  async search(
    query: string,
    options: {
      retrieveDocument?: boolean;
      maxWordCount?: number;
      includeGraph?: boolean;
      collectionId?: string;
    } = {},
    env?: any,
  ): Promise<{
    chunks: TextResult[];
    graph: TextResult[];
  }> {
    // Ensure we're authenticated before making the search request
    await this.ensureAuthenticated(env);

    const client = this.getClient(env);
    const collectionId =
      options.collectionId ||
      env?.R2R_COLLECTION_ID ||
      process.env?.R2R_COLLECTION_ID;

    try {
      // Search with collection filter - let any errors bubble up
      const r2rSearchRes = await client.retrieval.search({
        query: query,
        searchSettings: {
          limit: 10,
          filters: collectionId
            ? {
                collection_ids: {
                  $in: [collectionId],
                },
              }
            : undefined,
        },
      });

      // Extract chunks and graph results from the search response
      const chunks: TextResult[] =
        r2rSearchRes.results?.chunkSearchResults?.map((c: any) => ({
          ...c,
          text: c.text,
          type: 'vector',
          document_id: c.document_id || c.documentId,
          score: c.score,
          wordCount: 0, // Will be calculated in parseR2rResult
        })) || [];

      const graph: TextResult[] =
        r2rSearchRes.results?.graphSearchResults?.map((g: any) => ({
          ...g,
          text: g.content?.description || g.content?.summary || '',
          type: g.result_type || g.resultType,
          document_id: null,
          score: g.score,
          wordCount: 0, // Will be calculated in parseR2rResult
        })) || [];

      return { chunks, graph };
    } catch (error) {
      console.error('R2R search error:', error);

      // Reset client connection on any error to prevent persistent issues
      this.resetAuthentication();

      // If authentication error, reset auth state and retry once
      if (
        error instanceof Error &&
        (error.message.includes('401') ||
          error.message.includes('unauthorized') ||
          error.message.includes('authentication'))
      ) {
        console.log(
          'Authentication error detected, resetting auth state and retrying...',
        );
        this.isAuthenticated = false;
        this.client = null;

        // Retry once with fresh authentication
        try {
          await this.ensureAuthenticated(env);
          const freshClient = this.getClient(env);
          const retryRes = await freshClient.retrieval.search({
            query: query,
            searchSettings: {
              limit: 10,
              filters: collectionId
                ? {
                    collection_ids: {
                      $in: [collectionId],
                    },
                  }
                : undefined,
            },
          });

          // Process retry response same as original
          const chunks: TextResult[] =
            retryRes.results?.chunkSearchResults?.map((c: any) => ({
              ...c,
              text: c.text,
              type: 'vector',
              document_id: c.document_id || c.documentId,
              score: c.score,
              wordCount: 0,
            })) || [];

          const graph: TextResult[] =
            retryRes.results?.graphSearchResults?.map((g: any) => ({
              ...g,
              text: g.content?.description || g.content?.summary || '',
              type: g.result_type || g.resultType,
              document_id: null,
              score: g.score,
              wordCount: 0,
            })) || [];

          return { chunks, graph };
        } catch (retryError) {
          console.error('R2R search retry failed:', retryError);
          throw retryError;
        }
      }

      throw error;
    }
  }

  public resetAuthentication(): void {
    this.isAuthenticated = false;
    this.client = null;
  }

  async healthCheck(
    env?: any,
  ): Promise<{ success: boolean; error?: string; message?: string }> {
    try {
      await this.ensureAuthenticated(env);

      // Try a simple search with minimal parameters
      const client = this.getClient(env);
      const testResult = await client.retrieval.search({
        query: 'test',
        searchSettings: {
          limit: 1,
        },
      });

      return {
        success: true,
        message: `R2R service is healthy. Found ${testResult.results?.chunkSearchResults?.length || 0} chunks.`,
      };
    } catch (error) {
      console.error('R2R health check failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async parseR2rResult(
    _client: r2rClient,
    chunks: TextResult[],
    graph: TextResult[],
    options: ParseR2rOptions,
  ): Promise<R2rSearchResult> {
    let allResults: TextResult[] = [];

    // Add chunks
    if (chunks.length > 0) {
      allResults = [...allResults, ...chunks];
    }

    // Add graph results if requested
    if (options.includeGraph && graph.length > 0) {
      allResults = [...allResults, ...graph];
    }

    // Filter by minimum score
    allResults = allResults.filter(
      (result) => result.score >= options.minScore,
    );

    // Sort by score (highest first)
    allResults.sort((a, b) => b.score - a.score);

    // Calculate word counts and apply word limit
    let totalWordCount = 0;
    const filteredResults: TextResult[] = [];

    for (const result of allResults) {
      const wordCount = result.text.split(/\s+/).length;
      result.wordCount = wordCount;

      if (totalWordCount + wordCount <= options.maxWordCount) {
        filteredResults.push(result);
        totalWordCount += wordCount;
      } else {
        // If adding this result would exceed the limit, truncate it
        const remainingWords = options.maxWordCount - totalWordCount;
        if (remainingWords > 0) {
          const words = result.text.split(/\s+/);
          const truncatedText = words.slice(0, remainingWords).join(' ');
          filteredResults.push({
            ...result,
            text: truncatedText,
            wordCount: remainingWords,
          });
          totalWordCount = options.maxWordCount;
        }
        break;
      }
    }

    // Apply limit
    const limitedResults = filteredResults.slice(0, options.limit);

    return {
      texts: limitedResults,
      totalWordCount,
    };
  }
}

export default R2RService;
