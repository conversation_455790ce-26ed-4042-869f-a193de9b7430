import { logError } from './logger.js';

export interface RetryConfig {
  maxRetries: number;
  backoffMultiplier: number;
  initialDelay: number;
  maxDelay?: number;
  retryCondition?: (error: Error) => boolean;
}

export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  attempts: number;
  totalTime: number;
}

export class RetryableError extends Error {
  constructor(
    message: string,
    public retryable = true,
  ) {
    super(message);
    this.name = 'RetryableError';
  }
}

export async function withRetry<T>(
  operation: () => Promise<T>,
  config: RetryConfig,
  context?: string,
): Promise<RetryResult<T>> {
  const startTime = Date.now();
  let lastError: Error;

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      const result = await operation();
      return {
        success: true,
        result,
        attempts: attempt + 1,
        totalTime: Date.now() - startTime,
      };
    } catch (error) {
      lastError = error as Error;

      // Check if we should retry this error
      if (config.retryCondition && !config.retryCondition(lastError)) {
        break;
      }

      // If this is the last attempt, don't wait
      if (attempt === config.maxRetries) {
        break;
      }

      // Calculate delay for next attempt
      const delay = Math.min(
        config.initialDelay * config.backoffMultiplier ** attempt,
        config.maxDelay || 30000,
      );

      logError(lastError, {
        context: context || 'retry-operation',
        attempt: attempt + 1,
        maxRetries: config.maxRetries,
        nextRetryIn: delay,
      });

      await sleep(delay);
    }
  }

  return {
    success: false,
    error: lastError,
    attempts: config.maxRetries + 1,
    totalTime: Date.now() - startTime,
  };
}

export function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export function isRetryableError(error: Error): boolean {
  // Network errors are generally retryable
  if (
    error.message.includes('ECONNRESET') ||
    error.message.includes('ENOTFOUND') ||
    error.message.includes('ETIMEDOUT') ||
    error.message.includes('ECONNREFUSED')
  ) {
    return true;
  }

  // Rate limiting errors are retryable
  if (
    error.message.includes('rate limit') ||
    error.message.includes('429') ||
    error.message.includes('Too Many Requests')
  ) {
    return true;
  }

  // Server errors (5xx) are retryable
  if (
    error.message.includes('500') ||
    error.message.includes('502') ||
    error.message.includes('503') ||
    error.message.includes('504')
  ) {
    return true;
  }

  // Custom retryable errors
  if (error instanceof RetryableError) {
    return error.retryable;
  }

  return false;
}

export function createRetryConfig(
  overrides?: Partial<RetryConfig>,
): RetryConfig {
  return {
    maxRetries: 3,
    backoffMultiplier: 2,
    initialDelay: 1000,
    maxDelay: 30000,
    retryCondition: isRetryableError,
    ...overrides,
  };
}

export class RateLimiter {
  private requests: number[] = [];

  constructor(
    private maxRequests: number,
    private windowMs: number,
  ) {}

  async waitIfNeeded(): Promise<void> {
    const now = Date.now();

    // Remove old requests outside the window
    this.requests = this.requests.filter((time) => now - time < this.windowMs);

    // If we're at the limit, wait until the oldest request expires
    if (this.requests.length >= this.maxRequests) {
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.windowMs - (now - oldestRequest);

      if (waitTime > 0) {
        await sleep(waitTime);
        return this.waitIfNeeded(); // Recursive call to check again
      }
    }

    // Record this request
    this.requests.push(now);
  }

  getStatus() {
    const now = Date.now();
    const activeRequests = this.requests.filter(
      (time) => now - time < this.windowMs,
    );

    return {
      activeRequests: activeRequests.length,
      maxRequests: this.maxRequests,
      windowMs: this.windowMs,
      canMakeRequest: activeRequests.length < this.maxRequests,
    };
  }
}
