import type { CrawlerConfig } from '../src/types/crawler.js';
import type { PlatformConfig } from '../src/types/social-media.js';

export const defaultCrawlerConfig: Partial<CrawlerConfig> = {
  maxPosts: Number.parseInt(process.env.DEFAULT_MAX_POSTS || '100'),
  downloadMedia: true,
  mediaTypes: ['image', 'video', 'audio'],
  outputDir: process.env.MEDIA_OUTPUT_DIR || './output',
  rateLimiting: {
    requestsPerMinute: Number.parseInt(
      process.env.DEFAULT_REQUESTS_PER_MINUTE || '30',
    ),
    downloadConcurrency: Number.parseInt(
      process.env.DEFAULT_DOWNLOAD_CONCURRENCY || '3',
    ),
  },
  retryConfig: {
    maxRetries: Number.parseInt(process.env.MAX_RETRIES || '3'),
    backoffMultiplier: Number.parseFloat(process.env.BACKOFF_MULTIPLIER || '2'),
    initialDelay: Number.parseInt(process.env.INITIAL_RETRY_DELAY || '1000'),
  },
  browserConfig: {
    headless: process.env.STAGEHAND_HEADLESS === 'true',
    timeout: Number.parseInt(process.env.BROWSER_TIMEOUT || '30000'),
    userAgent:
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    viewport: {
      width: 1920,
      height: 1080,
    },
  },
};

export const platformConfigs: Record<string, PlatformConfig> = {
  douyin: {
    name: 'Douyin',
    baseUrl: process.env.DOUYIN_BASE_URL || 'https://www.douyin.com',
    searchEndpoint: '/search',
    rateLimits: {
      requestsPerMinute: 20,
      downloadConcurrency: 2,
    },
    selectors: {
      searchBox: 'input[placeholder*="搜索"]',
      searchButton: 'button[type="submit"]',
      postContainer: '[data-e2e="search-result"]',
      postLink: 'a[href*="/video/"]',
      authorName: '[data-e2e="search-card-user-link"]',
      postContent: '[data-e2e="search-card-desc"]',
      mediaElements: 'video, img[src*="video"]',
      engagementStats:
        '[data-e2e="search-card-like"], [data-e2e="search-card-comment"]',
    },
    waitTimes: {
      pageLoad: Number.parseInt(process.env.PAGE_LOAD_TIMEOUT || '10000'),
      searchDelay: Number.parseInt(process.env.DOUYIN_SEARCH_DELAY || '2000'),
      scrollDelay: Number.parseInt(process.env.DOUYIN_SCROLL_DELAY || '1000'),
      elementTimeout: Number.parseInt(process.env.ELEMENT_TIMEOUT || '5000'),
    },
  },
  tiktok: {
    name: 'TikTok',
    baseUrl: 'https://www.tiktok.com',
    searchEndpoint: '/search',
    rateLimits: {
      requestsPerMinute: 25,
      downloadConcurrency: 3,
    },
    selectors: {
      searchBox: 'input[placeholder*="Search"]',
      searchButton: 'button[type="submit"]',
      postContainer: '[data-e2e="search_top-item"]',
      postLink: 'a[href*="/video/"]',
      authorName: '[data-e2e="search-card-user-link"]',
      postContent: '[data-e2e="search-video-desc"]',
      mediaElements: 'video',
      engagementStats: '[data-e2e="like-count"], [data-e2e="comment-count"]',
    },
    waitTimes: {
      pageLoad: 10000,
      searchDelay: 2000,
      scrollDelay: 1000,
      elementTimeout: 5000,
    },
  },
  instagram: {
    name: 'Instagram',
    baseUrl: 'https://www.instagram.com',
    searchEndpoint: '/explore/tags',
    rateLimits: {
      requestsPerMinute: 15,
      downloadConcurrency: 2,
    },
    selectors: {
      searchBox: 'input[placeholder*="Search"]',
      postContainer: 'article',
      postLink: 'a[href*="/p/"]',
      authorName: 'a[href*="/"]',
      postContent: 'span',
      mediaElements: 'img, video',
      engagementStats: 'span',
    },
    waitTimes: {
      pageLoad: 15000,
      searchDelay: 3000,
      scrollDelay: 2000,
      elementTimeout: 8000,
    },
  },
};

export function createCrawlerConfig(
  platform: string,
  keywords: string[],
  overrides?: Partial<CrawlerConfig>,
): CrawlerConfig {
  const platformConfig = platformConfigs[platform];
  if (!platformConfig) {
    throw new Error(`Unsupported platform: ${platform}`);
  }

  const config: CrawlerConfig = {
    platform: platform as any,
    keywords,
    ...defaultCrawlerConfig,
    ...overrides,
  } as CrawlerConfig;

  // Apply platform-specific rate limits if not overridden
  if (!overrides?.rateLimiting) {
    config.rateLimiting = {
      requestsPerMinute: platformConfig.rateLimits.requestsPerMinute,
      downloadConcurrency: platformConfig.rateLimits.downloadConcurrency,
    };
  }

  return config;
}

export function validatePlatform(platform: string): boolean {
  return platform in platformConfigs;
}

export function getSupportedPlatforms(): string[] {
  return Object.keys(platformConfigs);
}

export function getPlatformConfig(platform: string): PlatformConfig {
  const config = platformConfigs[platform];
  if (!config) {
    throw new Error(`Unsupported platform: ${platform}`);
  }
  return config;
}
